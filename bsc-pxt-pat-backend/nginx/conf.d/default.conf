# PXPAT Backend Services Nginx Configuration

# 上游服务定义
upstream token-service {
    server token-service:15004;
    keepalive 32;
}

upstream wallet-service {
    server wallet-service:15002;
    keepalive 32;
}

# HTTP 服务器配置
server {
    listen 80;
    server_name localhost;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # 通用配置
    client_max_body_size 10M;
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # Token Service 路由
    location /api/v1/tokens {
        proxy_pass http://token-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # Token Service 健康检查
    location /token/health {
        proxy_pass http://token-service/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Wallet Service 路由
    location /api/v1/wallet {
        proxy_pass http://wallet-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # Wallet Service 健康检查
    location /wallet/health {
        proxy_pass http://wallet-service/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 根路径重定向
    location / {
        return 200 '{"message": "PXPAT Backend Services", "services": {"token": "/api/v1/tokens", "wallet": "/api/v1/wallet"}}';
        add_header Content-Type application/json;
    }

    # 健康检查总览
    location /health {
        return 200 '{"status": "healthy", "timestamp": "$time_iso8601"}';
        add_header Content-Type application/json;
    }
}
