package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 服务配置
type Config struct {
	Server         ServerConfig         `yaml:"server"`
	Database       DatabaseConfig       `yaml:"database"`
	PointsService  PointsServiceConfig  `yaml:"points_service"`
	Transaction    TransactionConfig    `yaml:"transaction"`
	ServicePackage ServicePackageConfig `yaml:"service_package"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `yaml:"port"`
	Mode string `yaml:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Name   string `yaml:"name"`
	Schema string `yaml:"schema"`
}

// PointsServiceConfig 点数服务配置
type PointsServiceConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	GRPCPort int    `yaml:"grpc_port"`
}

// TransactionConfig 交易配置
type TransactionConfig struct {
	HistoryPageSize int `yaml:"history_page_size"`
	MaxHistoryDays  int `yaml:"max_history_days"`
}

// ServicePackageConfig 服务包配置
type ServicePackageConfig struct {
	ExpiryNotificationDays []int `yaml:"expiry_notification_days"`
}

// GlobalDatabaseConfig 定义全局数据库配置结构 (用于加载 global.prod.yaml)
type GlobalDatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	SSLMode  string `yaml:"ssl_mode"`
}

// GlobalDBConfig 全局数据库配置
type GlobalDBConfig struct {
	Host                   string `yaml:"host"`
	Port                   int    `yaml:"port"`
	User                   string `yaml:"user"`
	Password               string `yaml:"password"`
	DBName                 string `yaml:"dbname"`
	SSLMode                string `yaml:"ssl_mode"`
	MaxOpenConns           int    `yaml:"max_open_conns"`
	MaxIdleConns           int    `yaml:"max_idle_conns"`
	ConnMaxLifetimeMinutes string `yaml:"conn_max_lifetime_minutes"`
	AutoMigrate            bool   `yaml:"auto_migrate"`
	TimeZone               string `yaml:"time_zone"`
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	// 获取工作目录
	workDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("获取工作目录失败: %w", err)
	}

	// 设置配置文件路径
	configPath := os.Getenv("WALLET_SERVICE_CONFIG")
	if configPath == "" {
		// 根据环境变量选择配置文件路径
		env := os.Getenv("PXPAT_ENV")
		var envDir string
		switch env {
		case "dev", "development":
			envDir = "dev"
		case "prod", "production":
			envDir = "prod"
		default:
			envDir = "prod"
		}
		configPath = filepath.Join(workDir, "configs", envDir, "finance-cluster", "wallet-service", "config.yaml")
		log.Printf("WALLET_SERVICE_CONFIG 环境变量未设置，使用默认路径: %s", configPath)
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 打印加载的配置信息（调试用）
	log.Printf("加载的配置信息: Server.Port=%d", cfg.Server.Port)

	// 验证配置
	if cfg.Server.Port <= 0 {
		return nil, fmt.Errorf("服务器端口必须大于0")
	}
	if cfg.Database.Name == "" {
		return nil, fmt.Errorf("数据库名称不能为空")
	}
	if cfg.Database.Schema == "" {
		return nil, fmt.Errorf("数据库Schema不能为空")
	}

	return &cfg, nil
}

// LoadGlobalDBConfig 加载全局数据库配置
func LoadGlobalDBConfig() (*GlobalDBConfig, error) {
	// 根据环境变量选择配置文件路径
	env := os.Getenv("PXPAT_ENV")
	var envDir string
	switch env {
	case "dev", "development":
		envDir = "dev"
	case "prod", "production":
		envDir = "prod"
	default:
		envDir = "prod"
	}

	globalConfigPath := filepath.Join("configs", envDir, "global.yaml")
	data, err := os.ReadFile(globalConfigPath)
	if err != nil {
		return nil, fmt.Errorf("读取全局数据库配置文件失败 '%s': %w", globalConfigPath, err)
	}

	var globalCfg struct {
		Database GlobalDBConfig `yaml:"database"`
	}
	if err := yaml.Unmarshal(data, &globalCfg); err != nil {
		return nil, fmt.Errorf("解析全局数据库配置文件失败: %w", err)
	}

	// 验证配置
	if globalCfg.Database.Host == "" {
		return nil, fmt.Errorf("数据库主机地址不能为空")
	}
	if globalCfg.Database.Port <= 0 {
		return nil, fmt.Errorf("数据库端口必须大于0")
	}
	if globalCfg.Database.User == "" {
		return nil, fmt.Errorf("数据库用户名不能为空")
	}

	return &globalCfg.Database, nil
}
