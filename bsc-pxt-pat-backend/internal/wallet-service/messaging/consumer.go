package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"pxpat-backend/internal/wallet-service/dto"
	"pxpat-backend/internal/wallet-service/service"
	"pxpat-backend/pkg/messaging"

	amqp091 "github.com/rabbitmq/amqp091-go"
	"github.com/rs/zerolog/log"
)

// Consumer MQ消息消费者
type Consumer struct {
	consumer      *messaging.Consumer
	walletService *service.WalletService
}

// NewConsumer 创建新的消息消费者
func NewConsumer(amqpURL string, walletService *service.WalletService) (*Consumer, error) {
	// 创建消息处理器
	handler := &UserEventHandler{
		walletService: walletService,
	}

	log.Info().
		Str("amqp_url", amqpURL).
		Msg("开始创建钱包服务消息消费者")

	// 使用构建器创建消费者
	consumer, err := messaging.CreateWalletServiceUserRegisteredConsumer(amqpURL, handler)

	if err != nil {
		log.Error().
			Err(err).
			Str("amqp_url", amqpURL).
			Msg("创建消费者失败")
		return nil, fmt.Errorf("创建消费者失败: %w", err)
	}

	log.Info().Msg("钱包服务消息消费者创建成功")

	return &Consumer{
		consumer:      consumer,
		walletService: walletService,
	}, nil
}

// StartConsuming 开始消费消息
func (c *Consumer) StartConsuming(ctx context.Context) error {
	log.Info().Msg("开始消费用户注册事件消息")
	return c.consumer.StartConsuming(ctx)
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	log.Info().Msg("关闭钱包服务消息消费者")
	return c.consumer.Close()
}

// IsConnected 检查连接状态
func (c *Consumer) IsConnected() bool {
	return c.consumer.IsConnected()
}

// UserEventHandler 用户事件处理器
type UserEventHandler struct {
	walletService *service.WalletService
}

// UserRegisteredEvent 用户注册事件结构体
type UserRegisteredEvent struct {
	UserKSUID string    `json:"user_ksuid"`
	Email     string    `json:"email"`
	Region    string    `json:"region"`
	Timestamp time.Time `json:"timestamp"`
}

// HandleMessage 实现 MessageHandler 接口
func (h *UserEventHandler) HandleMessage(ctx context.Context, delivery amqp091.Delivery) error {
	var event UserRegisteredEvent
	if err := json.Unmarshal(delivery.Body, &event); err != nil {
		log.Error().
			Err(err).
			Str("body", string(delivery.Body)).
			Msg("解析用户注册事件失败")
		return fmt.Errorf("解析用户注册事件失败: %w", err)
	}

	log.Info().
		Str("user_ksuid", event.UserKSUID).
		Str("email", event.Email).
		Str("region", event.Region).
		Time("timestamp", event.Timestamp).
		Msg("收到用户注册事件")

	// 注意：这里我们依赖pkg/messaging中Consumer的防重复处理机制
	// 如果需要额外的业务层防重复处理，可以在这里添加

	log.Debug().
		Str("user_ksuid", event.UserKSUID).
		Str("event_body", string(delivery.Body)).
		Msg("用户注册事件详细内容")

	// 检查钱包是否已存在
	exists, err := h.walletService.WalletExists(event.UserKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", event.UserKSUID).
			Msg("检查钱包存在性失败")
		return fmt.Errorf("检查钱包存在性失败: %w", err)
	}

	if exists {
		log.Info().
			Str("user_ksuid", event.UserKSUID).
			Msg("用户钱包已存在，跳过创建")
		return nil
	}

	// 自动创建钱包
	createWalletReq := &dto.CreateWalletRequest{
		UserKSUID: event.UserKSUID,
		Region:    event.Region,
	}

	response, err := h.walletService.CreateWallet(createWalletReq)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", event.UserKSUID).
			Msg("创建钱包失败")
		return fmt.Errorf("为用户 %s 创建钱包失败: %w", event.UserKSUID, err)
	}

	log.Info().
		Str("user_ksuid", event.UserKSUID).
		Str("wallet_address", response.WalletAddress).
		Str("status", response.Status).
		Msg("成功为新用户创建钱包")

	return nil
}
