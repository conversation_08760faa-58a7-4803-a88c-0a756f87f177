package dto

import "time"

// CreateWalletRequest 创建钱包请求
type CreateWalletRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
	Region    string `json:"region" binding:"required"` // CN, GLOBAL
}

// CreateWalletResponse 创建钱包响应
type CreateWalletResponse struct {
	WalletID      uint64 `json:"wallet_id"`
	WalletAddress string `json:"wallet_address"`
	PublicKey     string `json:"public_key"`
	Status        string `json:"status"`
	IsAccessible  bool   `json:"is_accessible"`
	Message       string `json:"message"`
}

// GetWalletRequest 获取钱包请求
type GetWalletRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
}

// WalletInfo 钱包信息
type WalletInfo struct {
	ID            uint64     `json:"id"`
	UserKSUID     string     `json:"user_ksuid"`
	WalletAddress string     `json:"wallet_address"`
	PublicKey     string     `json:"public_key"`
	WalletType    string     `json:"wallet_type"`
	Status        string     `json:"status"`
	Region        string     `json:"region"`
	IsAccessible  bool       `json:"is_accessible"`
	ClaimableAt   *time.Time `json:"claimable_at"`
	CreatedAt     time.Time  `json:"created_at"`
}

// TokenBalanceInfo 代币余额信息
type TokenBalanceInfo struct {
	TokenSymbol   string    `json:"token_symbol"`
	TokenAddress  string    `json:"token_address"`
	Balance       string    `json:"balance"`
	LockedBalance string    `json:"locked_balance"`
	LastSyncAt    time.Time `json:"last_sync_at"`
}

// GetWalletResponse 获取钱包响应
type GetWalletResponse struct {
	Wallet        *WalletInfo         `json:"wallet"`
	TokenBalances []*TokenBalanceInfo `json:"token_balances"`
	AirdropCount  int64               `json:"airdrop_count"` // 待领取空投数量
	TotalValue    string              `json:"total_value"`   // 总价值（USD）
}

// AirdropInfo 空投信息
type AirdropInfo struct {
	ID          uint64     `json:"id"`
	TokenSymbol string     `json:"token_symbol"`
	Amount      string     `json:"amount"`
	AirdropType string     `json:"airdrop_type"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	ClaimableAt *time.Time `json:"claimable_at"`
	ClaimedAt   *time.Time `json:"claimed_at"`
	CreatedAt   time.Time  `json:"created_at"`
}

// GetAirdropsRequest 获取空投请求
type GetAirdropsRequest struct {
	UserKSUID string `form:"user_ksuid" binding:"required"`
	Status    string `form:"status"` // pending, completed, failed
	Page      int    `form:"page,default=1"`
	Size      int    `form:"size,default=20"`
}

// GetAirdropsResponse 获取空投响应
type GetAirdropsResponse struct {
	Airdrops []*AirdropInfo `json:"airdrops"`
	Total    int64          `json:"total"`
	Page     int            `json:"page"`
	Size     int            `json:"size"`
}

// CreateAirdropRequest 创建空投请求（内部使用）
type CreateAirdropRequest struct {
	UserKSUID   string `json:"user_ksuid" binding:"required"`
	TokenSymbol string `json:"token_symbol" binding:"required"`
	Amount      string `json:"amount" binding:"required"`
	AirdropType string `json:"airdrop_type" binding:"required"`
	Description string `json:"description"`
}

// CreateAirdropResponse 创建空投响应
type CreateAirdropResponse struct {
	AirdropID uint64 `json:"airdrop_id"`
	Message   string `json:"message"`
}

// EnableWalletAccessRequest 启用钱包访问请求（监管放宽后）
type EnableWalletAccessRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
}

// EnableWalletAccessResponse 启用钱包访问响应
type EnableWalletAccessResponse struct {
	WalletAddress string    `json:"wallet_address"`
	PrivateKey    string    `json:"private_key"` // 仅在启用时返回一次
	Mnemonic      string    `json:"mnemonic"`    // 仅在启用时返回一次
	EnabledAt     time.Time `json:"enabled_at"`
	Message       string    `json:"message"`
}

// GetWalletKeysRequest 获取钱包密钥请求
type GetWalletKeysRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
}

// GetWalletKeysResponse 获取钱包密钥响应
type GetWalletKeysResponse struct {
	WalletAddress string `json:"wallet_address"`
	PrivateKey    string `json:"private_key"`
	Mnemonic      string `json:"mnemonic"`
	PublicKey     string `json:"public_key"`
	IsAccessible  bool   `json:"is_accessible"`
	Region        string `json:"region"`
	Message       string `json:"message"`
}

// GetBalanceRequest 获取余额请求
type GetBalanceRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
}

// BalanceInfo 余额信息（统一的余额接口，支持积分和代币）
type BalanceInfo struct {
	// 积分余额（中国大陆版）
	PXTPoints float64 `json:"pxt_points"`
	PATPoints float64 `json:"pat_points"`

	// 代币余额（国际版）
	PXTTokens string `json:"pxt_tokens"`
	PATTokens string `json:"pat_tokens"`

	// 钱包信息
	WalletAddress string `json:"wallet_address,omitempty"`
	IsAccessible  bool   `json:"is_accessible"`
	Region        string `json:"region"`
}

// GetBalanceResponse 获取余额响应
type GetBalanceResponse struct {
	Balance *BalanceInfo `json:"balance"`
}

// GetUserBalanceRequest 定义获取用户余额请求的结构
type GetUserBalanceRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
}

// GetUserBalanceResponse 定义获取用户余额响应的结构
type GetUserBalanceResponse struct {
	UserKSUID string  `json:"user_ksuid"`
	PXTPoints float64 `json:"pxt_points"`
	PATPoints float64 `json:"pat_points"`
}

// GetUserPackagesRequest 定义获取用户服务包请求的结构
type GetUserPackagesRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
	Status    string `json:"status,omitempty"` // active, expired, cancelled
}

// GetUserPackagesResponse 定义获取用户服务包响应的结构
type GetUserPackagesResponse struct {
	// 根据实际需求定义用户服务包详情
	// 示例: []struct{ PackageID int64; Name string; Status string; ExpireTime time.Time }
	Packages interface{} `json:"packages"`
}
