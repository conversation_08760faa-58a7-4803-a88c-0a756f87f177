# 钱包服务 (Wallet Service)

## 概述

钱包服务是PXPAT金融服务集群的核心组件，负责管理用户的数字资产，包括积分系统（中国大陆版）和加密钱包系统（国际版）。

## 核心功能

### 🎯 **预创建冷钱包功能**

这是一个创新的设计，专门为中国大陆用户解决合规性问题：

#### **设计理念**
- **合规优先**：中国大陆用户无法直接访问冷钱包
- **福利保障**：确保用户能享受代币空投等福利
- **未来准备**：监管放宽后可无缝领取

#### **工作流程**
1. **用户注册** → 自动创建冷钱包（托管状态）
2. **系统空投** → 代币发送到托管钱包
3. **监管放宽** → 用户可领取钱包控制权

### 📊 **双重余额系统**

#### **中国大陆版（积分系统）**
- **PXT积分**：治理积分，用于平台治理
- **PAT积分**：功能积分，用于内容上传等

#### **国际版（代币系统）**
- **PXT代币**：BSC链上的治理代币
- **PAT代币**：BSC链上的功能代币

## API接口

### 🔐 **钱包管理**

#### 创建钱包
```http
POST /api/v1/wallet/create
Content-Type: application/json

{
  "ks_uid": "用户KSUID",
  "region": "CN|GLOBAL"
}
```

#### 获取钱包信息
```http
GET /api/v1/wallet/info/{ks_uid}
```

#### 获取统一余额
```http
GET /api/v1/wallet/unified-balance/{ks_uid}
```

### 🎁 **空投管理**

#### 获取空投列表
```http
GET /api/v1/wallet/airdrops?ks_uid={ks_uid}&status=pending&page=1&size=20
```

#### 创建空投（内部API）
```http
POST /api/v1/internal/airdrop
Content-Type: application/json

{
  "ks_uid": "用户KSUID",
  "token_symbol": "PXT",
  "amount": "100",
  "airdrop_type": "registration",
  "description": "注册奖励"
}
```

### 🔓 **钱包访问控制**

#### 启用钱包访问（监管放宽后）
```http
POST /api/v1/wallet/enable-access/{ks_uid}
```

## 数据库模型

### 核心表结构

#### `finance_crypto_wallets` - 加密钱包表
- 存储钱包地址、加密私钥、助记词
- 支持托管状态和访问控制
- 区域隔离（CN/GLOBAL）

#### `finance_token_balances` - 代币余额表
- 支持多种代币（PXT、PAT）
- 精确的余额计算
- 锁定余额支持

#### `finance_airdrop_records` - 空投记录表
- 空投历史追踪
- 状态管理（pending/completed/failed）
- 可领取时间控制

## 安全特性

### 🔒 **加密存储**
- 私钥和助记词加密存储
- 敏感信息不在API中暴露
- 分层访问控制

### 🌍 **区域隔离**
- 中国大陆用户：托管模式
- 国际用户：完全控制
- 灵活的状态转换

### 🛡️ **权限控制**
- 基于区域的功能限制
- 监管合规检查
- 安全的状态转换

## 集成说明

### 与用户服务集成
```go
// 用户注册时自动创建钱包
event := &UserRegisteredEvent{
    KsUID:  "user_ksuid",
    Region: "CN",
    Email:  "<EMAIL>",
}

// 事件监听器自动处理
eventListener.HandleUserRegisteredEvent(eventData)
```

### 与其他服务集成
- **内容服务**：内容奖励空投
- **社交服务**：互动奖励空投
- **治理服务**：投票权重查询

## 部署配置

### 快速部署

#### 使用Docker Compose（推荐）
```bash
# 一键启动（包括数据库、消息队列等）
./scripts/start_with_docker.sh start

# 启动并运行测试
./scripts/start_with_docker.sh test

# 仅构建镜像
./scripts/start_with_docker.sh build

# 仅启动基础设施
./scripts/start_with_docker.sh infrastructure
```

#### 使用完整部署脚本
```bash
# 完整部署
./scripts/deploy_wallet_service.sh deploy

# 查看服务状态
./scripts/deploy_wallet_service.sh status

# 查看日志
./scripts/deploy_wallet_service.sh logs

# 停止服务
./scripts/deploy_wallet_service.sh stop
```

### 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pxpat
DB_USER=postgres
DB_PASSWORD=password

# 服务配置
WALLET_SERVICE_PORT=15002
USER_SERVICE_URL=http://localhost:15001

# RabbitMQ配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# 加密配置
ENCRYPTION_KEY=your_encryption_key
```

### Docker Compose部署
```bash
# 使用主Docker Compose文件启动钱包服务
docker-compose up -d postgres rabbitmq redis wallet-service

# 查看服务状态
docker-compose ps

# 查看钱包服务日志
docker-compose logs -f wallet-service

# 停止钱包服务
docker-compose stop wallet-service
```

### 手动部署
```bash
# 构建服务
go build -o bin/wallet-service cmd/wallet-service/main.go

# 启动服务
./bin/wallet-service
```

## 监管合规

### 中国大陆合规策略
1. **托管模式**：钱包由平台托管，用户无法直接访问
2. **积分映射**：代币以积分形式展示
3. **合规转换**：监管放宽后可转为完全控制

### 国际版功能
1. **完全控制**：用户拥有私钥和助记词
2. **链上交互**：直接与BSC链交互
3. **DeFi集成**：支持各种DeFi协议

## 未来规划

### 短期目标
- [ ] 真实加密库集成
- [ ] 硬件钱包支持
- [ ] 多签钱包支持

### 长期目标
- [ ] 跨链桥接
- [ ] Layer 2 集成
- [ ] 自建公链迁移

## 测试

### 快速测试
```bash
# 运行完整的功能测试
./scripts/quick_test.sh
```

### 端到端测试
```bash
# 运行端到端测试（包括用户注册流程）
./scripts/test_end_to_end.sh
```

### 单元测试
```bash
go test ./...
```

### 手动API测试
```bash
# 测试钱包创建
curl -X POST http://localhost:15002/api/v1/wallet/create \
  -H "Content-Type: application/json" \
  -d '{"ks_uid":"test_user","region":"CN"}'

# 测试钱包信息获取
curl -X GET http://localhost:15002/api/v1/wallet/info/test_user

# 测试统一余额查询
curl -X GET http://localhost:15002/api/v1/wallet/unified-balance/test_user

# 测试空投创建
curl -X POST http://localhost:15002/api/v1/internal/airdrop \
  -H "Content-Type: application/json" \
  -d '{"ks_uid":"test_user","token_symbol":"PXT","amount":"100","airdrop_type":"registration","description":"注册奖励"}'

# 测试钱包访问启用
curl -X POST http://localhost:15002/api/v1/wallet/enable-access/test_user
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License
