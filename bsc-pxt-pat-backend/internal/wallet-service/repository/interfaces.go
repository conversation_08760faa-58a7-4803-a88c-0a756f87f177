package repository

import (
	"context"

	"pxpat-backend/internal/wallet-service/model"
)

// PointAccountRepository 点数账户数据访问接口
type PointAccountRepository interface {
	GetPointAccountByKsUID(ctx context.Context, ksUID string) (*model.PointAccount, error)
	// TODO: Add methods for updating point accounts if needed for wallet service
}

// CryptoWalletRepository 加密钱包数据访问接口
type CryptoWalletRepository interface {
	Create(ctx context.Context, wallet *model.CryptoWallet) error
	GetByUserKSUID(ctx context.Context, ksUID string) (*model.CryptoWallet, error)
	GetByID(ctx context.Context, id uint64) (*model.CryptoWallet, error)
	Update(ctx context.Context, wallet *model.CryptoWallet) error
	UpdateFields(ctx context.Context, id uint64, updates map[string]interface{}) error
}

// TokenBalanceRepository 代币余额数据访问接口
type TokenBalanceRepository interface {
	GetByWalletID(ctx context.Context, walletID uint64) ([]model.TokenBalance, error)
	GetByWalletIDAndToken(ctx context.Context, walletID uint64, tokenSymbol string) (*model.TokenBalance, error)
	Create(ctx context.Context, balance *model.TokenBalance) error
	Update(ctx context.Context, balance *model.TokenBalance) error
}

// AirdropRecordRepository 空投记录数据访问接口
type AirdropRecordRepository interface {
	Create(ctx context.Context, record *model.AirdropRecord) error
	GetByKsUID(ctx context.Context, ksUID string, status string, page, size int) ([]model.AirdropRecord, int64, error)
	CountPendingByUserKSUID(ctx context.Context, ksUID string) (int64, error)
	GetByID(ctx context.Context, id uint64) (*model.AirdropRecord, error)
	Update(ctx context.Context, record *model.AirdropRecord) error
}
