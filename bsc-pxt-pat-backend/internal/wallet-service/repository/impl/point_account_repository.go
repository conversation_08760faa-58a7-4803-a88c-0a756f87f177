package impl

import (
	"context"
	"errors"

	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"

	"gorm.io/gorm"
)

type pointAccountRepository struct {
	db *gorm.DB
}

// NewPointAccountRepository 创建新的点数账户数据访问层实现
func NewPointAccountRepository(db *gorm.DB) repository.PointAccountRepository {
	return &pointAccountRepository{
		db: db,
	}
}

// GetPointAccountByKsUID 根据用户KsUID获取点数账户
func (r *pointAccountRepository) GetPointAccountByKsUID(ctx context.Context, userKSUID string) (*model.PointAccount, error) {
	var account model.PointAccount
	if err := r.db.WithContext(ctx).Where("user_ksuid = ?", userKSUID).First(&account).Error; err != nil {
		// 如果记录未找到，返回nil和nil错误，或者gorm.ErrRecordNotFound
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &account, nil
}
