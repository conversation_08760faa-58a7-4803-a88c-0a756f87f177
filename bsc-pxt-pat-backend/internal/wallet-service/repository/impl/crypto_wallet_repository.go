package impl

import (
	"context"
	"errors"

	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"

	"gorm.io/gorm"
)

type cryptoWalletRepository struct {
	db *gorm.DB
}

// NewCryptoWalletRepository 创建新的加密钱包数据访问层实现
func NewCryptoWalletRepository(db *gorm.DB) repository.CryptoWalletRepository {
	return &cryptoWalletRepository{
		db: db,
	}
}

// Create 创建钱包记录
func (r *cryptoWalletRepository) Create(ctx context.Context, wallet *model.CryptoWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

// GetByUserKSUID 根据用户KsUID获取钱包
func (r *cryptoWalletRepository) GetByUserKSUID(ctx context.Context, ksUID string) (*model.CryptoWallet, error) {
	var wallet model.CryptoWallet
	if err := r.db.WithContext(ctx).Where("user_ksuid = ?", ksUID).First(&wallet).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &wallet, nil
}

// GetByID 根据ID获取钱包
func (r *cryptoWalletRepository) GetByID(ctx context.Context, id uint64) (*model.CryptoWallet, error) {
	var wallet model.CryptoWallet
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&wallet).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &wallet, nil
}

// Update 更新钱包记录
func (r *cryptoWalletRepository) Update(ctx context.Context, wallet *model.CryptoWallet) error {
	return r.db.WithContext(ctx).Save(wallet).Error
}

// UpdateFields 更新钱包指定字段
func (r *cryptoWalletRepository) UpdateFields(ctx context.Context, id uint64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.CryptoWallet{}).Where("id = ?", id).Updates(updates).Error
}
