package impl

import (
	"context"
	"errors"

	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"

	"gorm.io/gorm"
)

type airdropRecordRepository struct {
	db *gorm.DB
}

// NewAirdropRecordRepository 创建新的空投记录数据访问层实现
func NewAirdropRecordRepository(db *gorm.DB) repository.AirdropRecordRepository {
	return &airdropRecordRepository{
		db: db,
	}
}

// Create 创建空投记录
func (r *airdropRecordRepository) Create(ctx context.Context, record *model.AirdropRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

// GetByKsUID 根据用户KsUID获取空投记录列表（支持分页和状态过滤）
func (r *airdropRecordRepository) GetByKsUID(ctx context.Context, ksUID string, status string, page, size int) ([]model.AirdropRecord, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.AirdropRecord{}).Where("user_ksuid = ?", ksUID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	var records []model.AirdropRecord
	if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// CountPendingByKsUID 统计用户待领取空投数量
func (r *airdropRecordRepository) CountPendingByUserKSUID(ctx context.Context, ksUID string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.AirdropRecord{}).Where("user_ksuid = ? AND status = ?", ksUID, "pending").Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetByID 根据ID获取空投记录
func (r *airdropRecordRepository) GetByID(ctx context.Context, id uint64) (*model.AirdropRecord, error) {
	var record model.AirdropRecord
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// Update 更新空投记录
func (r *airdropRecordRepository) Update(ctx context.Context, record *model.AirdropRecord) error {
	return r.db.WithContext(ctx).Save(record).Error
}
