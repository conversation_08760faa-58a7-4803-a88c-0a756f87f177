package impl

import (
	"context"
	"errors"

	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"

	"gorm.io/gorm"
)

type tokenBalanceRepository struct {
	db *gorm.DB
}

// NewTokenBalanceRepository 创建新的代币余额数据访问层实现
func NewTokenBalanceRepository(db *gorm.DB) repository.TokenBalanceRepository {
	return &tokenBalanceRepository{
		db: db,
	}
}

// GetByWalletID 根据钱包ID获取所有代币余额
func (r *tokenBalanceRepository) GetByWalletID(ctx context.Context, walletID uint64) ([]model.TokenBalance, error) {
	var balances []model.TokenBalance
	if err := r.db.WithContext(ctx).Where("wallet_id = ?", walletID).Find(&balances).Error; err != nil {
		return nil, err
	}
	return balances, nil
}

// GetByWalletIDAndToken 根据钱包ID和代币符号获取特定代币余额
func (r *tokenBalanceRepository) GetByWalletIDAndToken(ctx context.Context, walletID uint64, tokenSymbol string) (*model.TokenBalance, error) {
	var balance model.TokenBalance
	if err := r.db.WithContext(ctx).Where("wallet_id = ? AND token_symbol = ?", walletID, tokenSymbol).First(&balance).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &balance, nil
}

// Create 创建代币余额记录
func (r *tokenBalanceRepository) Create(ctx context.Context, balance *model.TokenBalance) error {
	return r.db.WithContext(ctx).Create(balance).Error
}

// Update 更新代币余额记录
func (r *tokenBalanceRepository) Update(ctx context.Context, balance *model.TokenBalance) error {
	return r.db.WithContext(ctx).Save(balance).Error
}
