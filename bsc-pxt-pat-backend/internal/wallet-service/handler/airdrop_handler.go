package handler

import (
	"errors"

	"pxpat-backend/internal/wallet-service/dto"
	"pxpat-backend/internal/wallet-service/service"
	"pxpat-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// AirdropHandler 空投处理器接口
type AirdropHandler interface {
	GetAirdrops(c *gin.Context)
	CreateAirdrop(c *gin.Context)
}

type airdropHandler struct {
	airdropService *service.AirdropService
}

// NewAirdropHandler 创建新的空投处理器
func NewAirdropHandler(s *service.AirdropService) AirdropHandler {
	return &airdropHandler{
		airdropService: s,
	}
}

// GetAirdrops 获取空投列表
func (h *airdropHandler) GetAirdrops(c *gin.Context) {
	var req dto.GetAirdropsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, errors.New("请求参数错误: "+err.Error()))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	result, err := h.airdropService.GetAirdrops(&req)
	if err != nil {
		response.Error(c, errors.New("获取空投列表失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// CreateAirdrop 创建空投（内部API）
func (h *airdropHandler) CreateAirdrop(c *gin.Context) {
	var req dto.CreateAirdropRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, errors.New("请求参数错误: "+err.Error()))
		return
	}

	result, err := h.airdropService.CreateAirdrop(&req)
	if err != nil {
		response.Error(c, errors.New("创建空投失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}
