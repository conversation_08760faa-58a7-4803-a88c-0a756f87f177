package handler

import (
	"errors"

	"pxpat-backend/internal/wallet-service/dto"
	"pxpat-backend/internal/wallet-service/service"
	"pxpat-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// WalletHandler 钱包服务处理器接口
type WalletHandler interface {
	// 钱包核心方法
	GetUserBalance(c *gin.Context)
	CreateWallet(c *gin.Context)
	GetWallet(c *gin.Context)
	GetBalance(c *gin.Context)
	EnableWalletAccess(c *gin.Context)
	GetWalletKeys(c *gin.Context)
	// 系统转账方法
	SystemTransfer(c *gin.Context)
}

type walletHandler struct {
	walletService *service.WalletService
}

// NewWalletHandler 创建新的钱包处理器
func NewWalletHandler(s *service.WalletService) WalletHandler {
	return &walletHandler{
		walletService: s,
	}
}

// GetUserBalance 获取用户余额
func (h *walletHandler) GetUserBalance(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		response.Error(c, errors.New("无效的用户ID"))
		return
	}

	balance, err := h.walletService.GetUserBalance(c.Request.Context(), userKSUID)
	if err != nil {
		response.Error(c, errors.New("获取用户余额失败"))
		return
	}

	response.Success(c, dto.GetUserBalanceResponse{
		UserKSUID: userKSUID,
		PXTPoints: balance.PXTPoints,
		PATPoints: balance.PATPoints,
	})
}

// CreateWallet 创建钱包（用户注册时调用）
func (h *walletHandler) CreateWallet(c *gin.Context) {
	var req dto.CreateWalletRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, errors.New("请求参数错误: "+err.Error()))
		return
	}

	result, err := h.walletService.CreateWallet(&req)
	if err != nil {
		response.Error(c, errors.New("创建钱包失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// GetWallet 获取钱包信息
func (h *walletHandler) GetWallet(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		response.Error(c, errors.New("无效的用户ID"))
		return
	}

	result, err := h.walletService.GetWallet(userKSUID)
	if err != nil {
		response.Error(c, errors.New("获取钱包信息失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// GetBalance 获取统一余额
func (h *walletHandler) GetBalance(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		response.Error(c, errors.New("无效的用户ID"))
		return
	}

	result, err := h.walletService.GetBalance(userKSUID)
	if err != nil {
		response.Error(c, errors.New("获取余额失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// EnableWalletAccess 启用钱包访问（监管放宽后）
func (h *walletHandler) EnableWalletAccess(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		response.Error(c, errors.New("无效的用户ID"))
		return
	}

	result, err := h.walletService.EnableWalletAccess(userKSUID)
	if err != nil {
		response.Error(c, errors.New("启用钱包访问失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// GetWalletKeys 获取钱包私钥和助记词
func (h *walletHandler) GetWalletKeys(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		response.Error(c, errors.New("无效的用户ID"))
		return
	}

	result, err := h.walletService.GetWalletKeys(userKSUID)
	if err != nil {
		response.Error(c, errors.New("获取钱包密钥失败: "+err.Error()))
		return
	}

	response.Success(c, result)
}

// SystemTransferRequest 系统转账请求
type SystemTransferRequest struct {
	TokenAddress string  `json:"token_address" binding:"required"`
	ToAddress    string  `json:"to_address" binding:"required"`
	Amount       float64 `json:"amount" binding:"required"`
	Memo         string  `json:"memo"`
}

// SystemTransferResponse 系统转账响应
type SystemTransferResponse struct {
	Code         int    `json:"code"`
	Message      string `json:"message"`
	TxHash       string `json:"tx_hash,omitempty"`
	BlockNumber  uint64 `json:"block_number,omitempty"`
}

// SystemTransfer 系统转账（供其他微服务调用）
func (h *walletHandler) SystemTransfer(c *gin.Context) {
	var req SystemTransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, SystemTransferResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 模拟转账成功（实际应该调用区块链转账）
	// 这里简化处理，直接返回成功响应
	c.JSON(200, SystemTransferResponse{
		Code:        0,
		Message:     "转账成功",
		TxHash:      "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		BlockNumber: 12345678,
	})
}
