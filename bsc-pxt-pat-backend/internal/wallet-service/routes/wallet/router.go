package wallet

import (
	"pxpat-backend/internal/wallet-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterWalletRouter 注册所有钱包相关路由（外部和内部）
func RegisterWalletRouter(r *gin.RouterGroup, walletHandler handler.WalletHandler, authMiddleware gin.HandlerFunc) {
	// 注册外部API路由
	RegisterWalletExternalRoutes(r, walletHandler, authMiddleware)

	// 注册内部服务路由
	RegisterWalletInternalRoutes(r, walletHandler)
}
