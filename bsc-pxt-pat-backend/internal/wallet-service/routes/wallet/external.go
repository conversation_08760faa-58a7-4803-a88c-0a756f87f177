package wallet

import (
	"pxpat-backend/internal/wallet-service/handler"

	"github.com/gin-gonic/gin"
)

func RegisterWalletExternalRoutes(r *gin.RouterGroup, walletHandler handler.WalletHandler, authMiddleware gin.HandlerFunc) {
	walletGroup := r.Group("/wallet")
	{
		// 需要认证的接口
		needAuth := walletGroup.Group("")
		needAuth.Use(authMiddleware)
		{
			// 原有API（保持兼容性）
			needAuth.GET("/balance/:user_ksuid", walletHandler.GetUserBalance)

			// 新增的冷钱包API
			needAuth.POST("/create", walletHandler.CreateWallet)                          // 创建钱包
			needAuth.GET("/info/:user_ksuid", walletHandler.GetWallet)                    // 获取钱包信息
			needAuth.GET("/unified-balance/:user_ksuid", walletHandler.GetBalance)        // 获取统一余额
			needAuth.POST("/enable-access/:user_ksuid", walletHandler.EnableWalletAccess) // 启用钱包访问
			needAuth.GET("/keys/:user_ksuid", walletHandler.GetWalletKeys)                // 获取钱包私钥和助记词
		}
	}
}
