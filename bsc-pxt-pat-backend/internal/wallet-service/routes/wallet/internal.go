package wallet

import (
	"pxpat-backend/internal/wallet-service/handler"

	"github.com/gin-gonic/gin"
)

func RegisterWalletInternalRoutes(r *gin.RouterGroup, walletHandler handler.WalletHandler) {
	// 内部API（用于系统间调用）
	internalGroup := r.Group("/intra/wallet")
	{
		// 微服务内部接口 - 暂时为空，未来可能需要
		_ = internalGroup
	}

	// 系统转账API（供其他微服务调用）
	walletGroup := r.Group("/wallet")
	{
		walletGroup.POST("/transfer", walletHandler.SystemTransfer) // 系统转账接口
	}
}
