package airdrop

import (
	"pxpat-backend/internal/wallet-service/handler"

	"github.com/gin-gonic/gin"
)

func RegisterAirdropExternalRoutes(r *gin.RouterGroup, airdropHandler handler.AirdropHandler, authMiddleware gin.HandlerFunc) {
	walletGroup := r.Group("/wallet")
	{
		// 需要认证的接口
		needAuth := walletGroup.Group("")
		needAuth.Use(authMiddleware)
		{
			// 空投相关API
			needAuth.GET("/airdrops", airdropHandler.GetAirdrops) // 获取空投列表
		}
	}
}
