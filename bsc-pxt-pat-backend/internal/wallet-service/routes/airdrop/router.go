package airdrop

import (
	"pxpat-backend/internal/wallet-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterAirdropRouter 注册所有空投相关路由（外部和内部）
func RegisterAirdropRouter(r *gin.RouterGroup, airdropHandler handler.AirdropHandler, authMiddleware gin.HandlerFunc) {
	// 注册外部API路由
	RegisterAirdropExternalRoutes(r, airdropHandler, authMiddleware)

	// 注册内部服务路由
	RegisterAirdropInternalRoutes(r, airdropHandler)
}
