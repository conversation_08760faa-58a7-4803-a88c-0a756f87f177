package routes

import (
	"pxpat-backend/internal/wallet-service/handler"
	"pxpat-backend/internal/wallet-service/routes/airdrop"
	"pxpat-backend/internal/wallet-service/routes/wallet"
	"pxpat-backend/pkg/auth"
	authMiddleware "pxpat-backend/pkg/middleware/auth"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// RegisterRoutes 注册钱包服务路由
func RegisterRoutes(r *gin.Engine, jwtManager auth.Manager, walletHandler handler.WalletHandler, airdropHandler handler.AirdropHandler) {
	// 创建base路由
	v1 := r.Group("/api/v1")
	// 认证中间件
	userAuthMiddleware := authMiddleware.UserAuthMiddleware(jwtManager)

	// 注册各功能域路由
	wallet.RegisterWalletRouter(v1, walletHandler, userAuthMiddleware)
	airdrop.RegisterAirdropRouter(v1, airdropHandler, userAuthMiddleware)

	// Swagger文档路由
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}
