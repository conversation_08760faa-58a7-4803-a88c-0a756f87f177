package service

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/tyler-smith/go-bip39"

	"pxpat-backend/internal/wallet-service/dto"
	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"
)

// WalletService 钱包服务实现
type WalletService struct {
	pointAccountRepo repository.PointAccountRepository
	walletRepo       repository.CryptoWalletRepository
	tokenBalanceRepo repository.TokenBalanceRepository
	airdropRepo      repository.AirdropRecordRepository
}

// getEncryptionKey 获取加密密钥
func (s *WalletService) getEncryptionKey() []byte {
	key := os.Getenv("WALLET_ENCRYPTION_KEY")
	if key == "" {
		// 默认密钥（生产环境必须设置环境变量）
		key = "pxpat-wallet-default-key-32-bytes"
	}

	// 使用SHA-256生成32字节密钥
	hash := sha256.Sum256([]byte(key))
	return hash[:]
}

// encryptData 使用AES-GCM加密数据
func (s *WalletService) encryptData(plaintext string) (string, error) {
	key := s.getEncryptionKey()

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptData 使用AES-GCM解密数据
func (s *WalletService) decryptData(ciphertext string) (string, error) {
	key := s.getEncryptionKey()

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("密文长度不足")
	}

	nonce, ciphertextBytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertextBytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// NewWalletService 创建钱包服务
func NewWalletService(
	pointAccountRepo repository.PointAccountRepository,
	walletRepo repository.CryptoWalletRepository,
	tokenBalanceRepo repository.TokenBalanceRepository,
	airdropRepo repository.AirdropRecordRepository,
) *WalletService {
	return &WalletService{
		pointAccountRepo: pointAccountRepo,
		walletRepo:       walletRepo,
		tokenBalanceRepo: tokenBalanceRepo,
		airdropRepo:      airdropRepo,
	}
}

// GetUserBalance 获取用户余额（原有方法）
func (s *WalletService) GetUserBalance(ctx context.Context, ksUID string) (*model.PointAccount, error) {
	account, err := s.pointAccountRepo.GetPointAccountByKsUID(ctx, ksUID)
	if err != nil {
		return nil, fmt.Errorf("查询用户点数账户失败: %w", err)
	}

	// 如果账户不存在，返回空账户
	if account == nil {
		return &model.PointAccount{
			UserKSUID: ksUID,
			PXTPoints: 0.0,
			PATPoints: 0.0,
		}, nil
	}

	return account, nil
}

// CreateWallet 创建钱包（用户注册时自动调用）
func (s *WalletService) CreateWallet(req *dto.CreateWalletRequest) (*dto.CreateWalletResponse, error) {
	// 检查钱包是否已存在
	exists, err := s.WalletExists(req.UserKSUID)
	if err != nil {
		return nil, fmt.Errorf("检查钱包存在性失败: %w", err)
	}
	if exists {
		return nil, errors.New("钱包已存在")
	}

	// 生成真实的助记词
	entropy, err := bip39.NewEntropy(128) // 12个单词
	if err != nil {
		return nil, fmt.Errorf("生成熵失败: %w", err)
	}

	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return nil, fmt.Errorf("生成助记词失败: %w", err)
	}

	// 生成真实的私钥
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, fmt.Errorf("生成私钥失败: %w", err)
	}

	// 获取公钥和地址
	publicKey := privateKey.Public().(*ecdsa.PublicKey)
	address := crypto.PubkeyToAddress(*publicKey)

	// 真正加密私钥和助记词
	privateKeyHex := fmt.Sprintf("%x", crypto.FromECDSA(privateKey))
	encryptedPrivateKey, err := s.encryptData(privateKeyHex)
	if err != nil {
		return nil, fmt.Errorf("加密私钥失败: %w", err)
	}

	encryptedMnemonic, err := s.encryptData(mnemonic)
	if err != nil {
		return nil, fmt.Errorf("加密助记词失败: %w", err)
	}

	// 根据区域设置钱包状态
	status := model.WalletStatusActive
	isAccessible := true
	if req.Region == "CN" {
		status = model.WalletStatusCustodial
		isAccessible = false
	}

	// 创建钱包记录
	wallet := &model.CryptoWallet{
		UserKSUID:      req.UserKSUID,
		WalletAddress:  address.Hex(),
		PrivateKeyHash: encryptedPrivateKey,
		PublicKey:      fmt.Sprintf("0x%x", crypto.FromECDSAPub(publicKey)),
		Mnemonic:       encryptedMnemonic,
		WalletType:     model.WalletTypeCold,
		Status:         status,
		Region:         req.Region,
		IsAccessible:   isAccessible,
	}

	ctx := context.Background()
	if err := s.walletRepo.Create(ctx, wallet); err != nil {
		return nil, fmt.Errorf("创建钱包记录失败: %w", err)
	}

	// 初始化代币余额记录
	tokenBalances := []*model.TokenBalance{
		{
			WalletID:      wallet.ID,
			TokenSymbol:   "PXT",
			TokenAddress:  "******************************************", // 待部署合约后更新
			Balance:       "0",
			LockedBalance: "0",
			LastSyncAt:    time.Now(),
		},
		{
			WalletID:      wallet.ID,
			TokenSymbol:   "PAT",
			TokenAddress:  "******************************************", // 待部署合约后更新
			Balance:       "0",
			LockedBalance: "0",
			LastSyncAt:    time.Now(),
		},
	}

	for _, balance := range tokenBalances {
		if err := s.tokenBalanceRepo.Create(ctx, balance); err != nil {
			return nil, fmt.Errorf("创建代币余额记录失败: %w", err)
		}
	}

	// TODO: 为中国大陆用户创建注册空投 - 需要通过 AirdropService 处理

	message := "钱包创建成功"
	if req.Region == "CN" {
		message = "钱包已创建并处于托管状态，监管政策放宽后可领取"
	}

	return &dto.CreateWalletResponse{
		WalletID:      wallet.ID,
		WalletAddress: wallet.WalletAddress,
		PublicKey:     wallet.PublicKey,
		Status:        wallet.Status,
		IsAccessible:  wallet.IsAccessible,
		Message:       message,
	}, nil
}

// WalletExists 检查钱包是否存在
func (s *WalletService) WalletExists(ksUID string) (bool, error) {
	ctx := context.Background()
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, ksUID)
	if err != nil {
		return false, err
	}
	return wallet != nil, nil
}

// GetWallet 获取钱包信息
func (s *WalletService) GetWallet(userKSUID string) (*dto.GetWalletResponse, error) {
	ctx := context.Background()

	// 获取钱包信息
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		return nil, fmt.Errorf("查询钱包失败: %w", err)
	}
	if wallet == nil {
		return nil, errors.New("钱包不存在")
	}

	// 获取代币余额
	tokenBalances, err := s.tokenBalanceRepo.GetByWalletID(ctx, wallet.ID)
	if err != nil {
		return nil, fmt.Errorf("查询代币余额失败: %w", err)
	}

	// 转换为DTO
	balanceInfos := make([]*dto.TokenBalanceInfo, len(tokenBalances))
	for i, balance := range tokenBalances {
		balanceInfos[i] = &dto.TokenBalanceInfo{
			TokenSymbol:   balance.TokenSymbol,
			TokenAddress:  balance.TokenAddress,
			Balance:       balance.Balance,
			LockedBalance: balance.LockedBalance,
			LastSyncAt:    balance.LastSyncAt,
		}
	}

	// 获取待领取空投数量
	airdropCount, err := s.airdropRepo.CountPendingByUserKSUID(ctx, userKSUID)
	if err != nil {
		return nil, fmt.Errorf("查询空投数量失败: %w", err)
	}

	walletInfo := &dto.WalletInfo{
		ID:            wallet.ID,
		UserKSUID:     wallet.UserKSUID,
		WalletAddress: wallet.WalletAddress,
		PublicKey:     wallet.PublicKey,
		WalletType:    wallet.WalletType,
		Status:        wallet.Status,
		Region:        wallet.Region,
		IsAccessible:  wallet.IsAccessible,
		ClaimableAt:   wallet.ClaimableAt,
		CreatedAt:     wallet.CreatedAt,
	}

	return &dto.GetWalletResponse{
		Wallet:        walletInfo,
		TokenBalances: balanceInfos,
		AirdropCount:  airdropCount,
		TotalValue:    "0", // TODO: 计算总价值
	}, nil
}

// GetBalance 获取统一余额（积分+代币）
func (s *WalletService) GetBalance(ksUID string) (*dto.GetBalanceResponse, error) {
	// 获取积分余额
	pointAccount, err := s.pointAccountRepo.GetPointAccountByKsUID(context.Background(), ksUID)
	if err != nil {
		return nil, fmt.Errorf("查询积分账户失败: %w", err)
	}

	ctx := context.Background()

	// 获取钱包信息
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, ksUID)
	if err != nil {
		return nil, fmt.Errorf("查询钱包失败: %w", err)
	}

	balance := &dto.BalanceInfo{
		Region: "CN", // 默认中国大陆
	}

	// 设置积分余额
	if pointAccount != nil {
		balance.PXTPoints = pointAccount.PXTPoints
		balance.PATPoints = pointAccount.PATPoints
	}

	// 设置钱包信息
	if wallet != nil {
		balance.WalletAddress = wallet.WalletAddress
		balance.IsAccessible = wallet.IsAccessible
		balance.Region = wallet.Region

		// 获取代币余额
		tokenBalances, err := s.tokenBalanceRepo.GetByWalletID(ctx, wallet.ID)
		if err == nil {
			for _, tb := range tokenBalances {
				if tb.TokenSymbol == "PXT" {
					balance.PXTTokens = tb.Balance
				} else if tb.TokenSymbol == "PAT" {
					balance.PATTokens = tb.Balance
				}
			}
		}
	}

	return &dto.GetBalanceResponse{
		Balance: balance,
	}, nil
}

// EnableWalletAccess 启用钱包访问（监管放宽后）
func (s *WalletService) EnableWalletAccess(ksUID string) (*dto.EnableWalletAccessResponse, error) {
	ctx := context.Background()

	// 获取钱包信息
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, ksUID)
	if err != nil {
		return nil, fmt.Errorf("查询钱包失败: %w", err)
	}
	if wallet == nil {
		return nil, errors.New("钱包不存在")
	}

	// 检查是否为中国大陆用户
	if wallet.Region != "CN" {
		return nil, errors.New("只有中国大陆用户需要启用钱包访问")
	}

	// 检查当前状态
	if wallet.IsAccessible {
		return nil, errors.New("钱包已经可以访问")
	}

	// 更新钱包状态
	now := time.Now()
	updates := map[string]interface{}{
		"is_accessible":    true,
		"status":           model.WalletStatusActive,
		"claimable_at":     &now,
		"last_accessed_at": &now,
	}

	if err := s.walletRepo.UpdateFields(ctx, wallet.ID, updates); err != nil {
		return nil, fmt.Errorf("更新钱包状态失败: %w", err)
	}

	// 真正解密私钥和助记词
	privateKey, err := s.decryptData(wallet.PrivateKeyHash)
	if err != nil {
		return nil, fmt.Errorf("解密私钥失败: %w", err)
	}

	mnemonic, err := s.decryptData(wallet.Mnemonic)
	if err != nil {
		return nil, fmt.Errorf("解密助记词失败: %w", err)
	}

	return &dto.EnableWalletAccessResponse{
		WalletAddress: wallet.WalletAddress,
		PrivateKey:    privateKey,
		Mnemonic:      mnemonic,
		EnabledAt:     now,
		Message:       "钱包访问已启用，请妥善保管私钥和助记词",
	}, nil
}

// GetWalletKeys 获取钱包私钥和助记词（用户登录后可调用）
func (s *WalletService) GetWalletKeys(ksUID string) (*dto.GetWalletKeysResponse, error) {
	ctx := context.Background()

	// 获取钱包信息
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, ksUID)
	if err != nil {
		return nil, fmt.Errorf("查询钱包失败: %w", err)
	}
	if wallet == nil {
		return nil, errors.New("钱包不存在")
	}

	// 检查钱包是否可访问
	if !wallet.IsAccessible {
		if wallet.Region == "CN" {
			return &dto.GetWalletKeysResponse{
				WalletAddress: wallet.WalletAddress,
				PublicKey:     wallet.PublicKey,
				IsAccessible:  false,
				Region:        wallet.Region,
				Message:       "钱包处于托管状态，监管政策放宽后可获取私钥",
			}, nil
		}
		return nil, errors.New("钱包不可访问")
	}

	// 解密私钥和助记词
	privateKey, err := s.decryptData(wallet.PrivateKeyHash)
	if err != nil {
		return nil, fmt.Errorf("解密私钥失败: %w", err)
	}

	mnemonic, err := s.decryptData(wallet.Mnemonic)
	if err != nil {
		return nil, fmt.Errorf("解密助记词失败: %w", err)
	}

	return &dto.GetWalletKeysResponse{
		WalletAddress: wallet.WalletAddress,
		PrivateKey:    privateKey,
		Mnemonic:      mnemonic,
		PublicKey:     wallet.PublicKey,
		IsAccessible:  wallet.IsAccessible,
		Region:        wallet.Region,
		Message:       "请妥善保管您的私钥和助记词，不要泄露给任何人",
	}, nil
}
