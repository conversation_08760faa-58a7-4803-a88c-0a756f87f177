package service

import (
	"context"
	"errors"
	"fmt"

	"pxpat-backend/internal/wallet-service/dto"
	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository"
)

// AirdropService 空投服务实现
type AirdropService struct {
	walletRepo  repository.CryptoWalletRepository
	airdropRepo repository.AirdropRecordRepository
}

// NewAirdropService 创建空投服务
func NewAirdropService(walletRepo repository.CryptoWalletRepository, airdropRepo repository.AirdropRecordRepository) *AirdropService {
	return &AirdropService{
		walletRepo:  walletRepo,
		airdropRepo: airdropRepo,
	}
}

// CreateAirdrop 创建空投记录（系统内部调用）
func (s *AirdropService) CreateAirdrop(req *dto.CreateAirdropRequest) (*dto.CreateAirdropResponse, error) {
	ctx := context.Background()

	// 获取用户钱包
	wallet, err := s.walletRepo.GetByUserKSUID(ctx, req.UserKSUID)
	if err != nil {
		return nil, fmt.Errorf("查询钱包失败: %w", err)
	}
	if wallet == nil {
		return nil, errors.New("用户钱包不存在")
	}

	// 创建空投记录
	airdrop := &model.AirdropRecord{
		UserKSUID:   req.UserKSUID,
		WalletID:    wallet.ID,
		TokenSymbol: req.TokenSymbol,
		Amount:      req.Amount,
		AirdropType: req.AirdropType,
		Description: req.Description,
		Status:      "pending",
	}

	if err := s.airdropRepo.Create(ctx, airdrop); err != nil {
		return nil, fmt.Errorf("创建空投记录失败: %w", err)
	}

	return &dto.CreateAirdropResponse{
		AirdropID: airdrop.ID,
		Message:   "空投记录创建成功",
	}, nil
}

// GetAirdrops 获取空投列表
func (s *AirdropService) GetAirdrops(req *dto.GetAirdropsRequest) (*dto.GetAirdropsResponse, error) {
	ctx := context.Background()

	// 通过repository获取空投记录
	airdrops, total, err := s.airdropRepo.GetByKsUID(ctx, req.UserKSUID, req.Status, req.Page, req.Size)
	if err != nil {
		return nil, fmt.Errorf("查询空投列表失败: %w", err)
	}

	// 转换为DTO
	airdropInfos := make([]*dto.AirdropInfo, len(airdrops))
	for i, airdrop := range airdrops {
		airdropInfos[i] = &dto.AirdropInfo{
			ID:          airdrop.ID,
			TokenSymbol: airdrop.TokenSymbol,
			Amount:      airdrop.Amount,
			AirdropType: airdrop.AirdropType,
			Description: airdrop.Description,
			Status:      airdrop.Status,
			ClaimableAt: airdrop.ClaimableAt,
			ClaimedAt:   airdrop.ClaimedAt,
			CreatedAt:   airdrop.CreatedAt,
		}
	}

	return &dto.GetAirdropsResponse{
		Airdrops: airdropInfos,
		Total:    total,
		Page:     req.Page,
		Size:     req.Size,
	}, nil
}
