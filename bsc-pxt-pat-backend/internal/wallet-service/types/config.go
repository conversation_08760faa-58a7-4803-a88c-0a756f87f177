package types

import (
	globalTypes "pxpat-backend/pkg/types"
)

// Config 钱包服务配置
type Config struct {
	Server           globalTypes.GlobalServerConfig   `mapstructure:"server"`
	Database         globalTypes.GlobalDatabaseConfig `mapstructure:"database"`
	Redis            globalTypes.GlobalRedisConfig    `mapstructure:"redis"`
	Log              globalTypes.GlobalLogConfig      `mapstructure:"log"`
	RabbitMQ         globalTypes.GlobalRabbitMQConfig `mapstructure:"rabbitmq"`
	Storage          globalTypes.GlobalStorageConfig  `mapstructure:"storage"`
	JWT              globalTypes.GlobalJWTConfig      `mapstructure:"jwt"`
	ExternalServices ExternalServicesConfig           `mapstructure:"external_services"`
	WalletService    WalletServiceConfig              `mapstructure:"wallet_service"`
	Security         Security                         `mapstructure:"security"`
}

func (c Config) IsConfig() {}

// ExternalServicesConfig 外部服务配置
type ExternalServicesConfig struct {
	UserService   ServiceConfig `mapstructure:"user_service"`
	PointsService ServiceConfig `mapstructure:"points_service"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Host    string `mapstructure:"host"`
	Port    int    `mapstructure:"port"`
	Timeout int    `mapstructure:"timeout"`
}

// WalletServiceConfig 钱包服务特定配置
type WalletServiceConfig struct {
	Transaction    TransactionConfig    `mapstructure:"transaction"`
	ServicePackage ServicePackageConfig `mapstructure:"service_package"`
	Crypto         CryptoConfig         `mapstructure:"crypto"`
}

// TransactionConfig 交易配置
type TransactionConfig struct {
	MaxRetries     int     `mapstructure:"max_retries"`
	RetryDelay     int     `mapstructure:"retry_delay"`
	TimeoutSeconds int     `mapstructure:"timeout_seconds"`
	MinAmount      float64 `mapstructure:"min_amount"`
	MaxAmount      float64 `mapstructure:"max_amount"`
	DailyLimit     float64 `mapstructure:"daily_limit"`
	MonthlyLimit   float64 `mapstructure:"monthly_limit"`
	FeeRate        float64 `mapstructure:"fee_rate"`
	MinFee         float64 `mapstructure:"min_fee"`
	MaxFee         float64 `mapstructure:"max_fee"`
}

// ServicePackageConfig 服务包配置
type ServicePackageConfig struct {
	DefaultExpireDays  int     `mapstructure:"default_expire_days"`
	MaxPackagesPerUser int     `mapstructure:"max_packages_per_user"`
	RefundPolicy       string  `mapstructure:"refund_policy"`
	DiscountRate       float64 `mapstructure:"discount_rate"`
}

// CryptoConfig 加密货币配置
type CryptoConfig struct {
	SupportedChains []string `mapstructure:"supported_chains"`
	DefaultChain    string   `mapstructure:"default_chain"`
	MinConfirms     int      `mapstructure:"min_confirms"`
	GasLimit        int64    `mapstructure:"gas_limit"`
	GasPrice        string   `mapstructure:"gas_price"`
}

type Security struct {
	CORS globalTypes.GlobalCORSConfig `mapstructure:"cors"`
	JWT  globalTypes.GlobalJWTConfig  `mapstructure:"jwt"`
}
