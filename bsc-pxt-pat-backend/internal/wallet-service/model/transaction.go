package model

import (
	"time"
)

// Transaction 交易记录模型
type Transaction struct {
	ID          int64     `gorm:"primaryKey;autoIncrement"`
	UserKSUID   string    `gorm:"column:user_ksuid;type:varchar(64);not null;index"`
	Type        string    `gorm:"type:varchar(32);not null"` // 交易类型：充值、消费、奖励等
	Amount      float64   `gorm:"type:decimal(10,2);not null"`
	Balance     float64   `gorm:"type:decimal(10,2);not null"` // 交易后余额
	Description string    `gorm:"type:varchar(255)"`
	CreatedAt   time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (Transaction) TableName() string {
	return "finance_wallet_transactions"
}
