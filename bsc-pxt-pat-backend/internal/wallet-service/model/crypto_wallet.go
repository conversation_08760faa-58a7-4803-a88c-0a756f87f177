package model

import (
	"time"

	"gorm.io/gorm"
)

// WalletStatus 钱包状态枚举
const (
	WalletStatusActive    = "active"    // 激活状态（国际版用户）
	WalletStatusCustodial = "custodial" // 托管状态（中国大陆用户）
	WalletStatusSuspended = "suspended" // 暂停状态
	WalletStatusClosed    = "closed"    // 关闭状态
)

// WalletType 钱包类型枚举
const (
	WalletTypeHot  = "hot"  // 热钱包（在线）
	WalletTypeCold = "cold" // 冷钱包（离线）
)

// CryptoWallet 加密钱包模型（国际版 + 中国大陆预创建）
type CryptoWallet struct {
	ID             uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	UserKSUID      string         `gorm:"column:user_ksuid;type:varchar(27);not null;uniqueIndex" json:"user_ksuid"`
	WalletAddress  string         `gorm:"type:varchar(42);not null;uniqueIndex" json:"wallet_address"`                                                   // BSC钱包地址
	PrivateKeyHash string         `gorm:"type:varchar(128);not null" json:"-"`                                                                           // 加密后的私钥（不返回给前端）
	PublicKey      string         `gorm:"type:varchar(200);not null" json:"public_key"`                                                                  // 公钥
	Mnemonic       string         `gorm:"type:text" json:"-"`                                                                                            // 加密后的助记词（不返回给前端）
	WalletType     string         `gorm:"type:varchar(10);not null;default:'cold'" json:"wallet_type"`                                                   // 钱包类型：hot, cold
	Status         string         `gorm:"type:varchar(20);not null;default:'custodial';index:idx_crypto_wallets_region_status,priority:2" json:"status"` // 钱包状态
	Region         string         `gorm:"type:varchar(10);not null;index:idx_crypto_wallets_region_status,priority:1" json:"region"`                     // 用户区域：CN, GLOBAL
	IsAccessible   bool           `gorm:"default:false" json:"is_accessible"`                                                                            // 用户是否可以访问（中国大陆用户默认false）
	ClaimableAt    *time.Time     `gorm:"type:timestamp" json:"claimable_at"`                                                                            // 可领取时间（监管放宽后）
	LastAccessedAt *time.Time     `gorm:"type:timestamp" json:"last_accessed_at"`                                                                        // 最后访问时间
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (CryptoWallet) TableName() string {
	return "finance_wallet_crypto_wallets"
}
