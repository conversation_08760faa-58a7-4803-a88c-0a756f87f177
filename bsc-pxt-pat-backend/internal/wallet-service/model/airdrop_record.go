package model

import (
	"time"

	"gorm.io/gorm"
)

// AirdropRecord 空投记录模型
type AirdropRecord struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	UserKSUID   string         `gorm:"column:user_ksuid;type:varchar(27);not null;index:idx_airdrop_records_ks_uid_status,priority:1" json:"user_ksuid"`
	WalletID    uint64         `gorm:"not null;index" json:"wallet_id"`
	TokenSymbol string         `gorm:"type:varchar(10);not null" json:"token_symbol"`
	Amount      string         `gorm:"type:varchar(50);not null" json:"amount"`
	AirdropType string         `gorm:"type:varchar(30);not null;index:idx_airdrop_records_type_status,priority:1" json:"airdrop_type"`                                                                // registration, activity, special
	Description string         `gorm:"type:varchar(255)" json:"description"`                                                                                                                          // 空投描述
	TxHash      string         `gorm:"type:varchar(66)" json:"tx_hash"`                                                                                                                               // 交易哈希（如果已发放）
	Status      string         `gorm:"type:varchar(20);not null;default:'pending';index:idx_airdrop_records_ks_uid_status,priority:2;index:idx_airdrop_records_type_status,priority:2" json:"status"` // pending, completed, failed
	ClaimableAt *time.Time     `gorm:"type:timestamp" json:"claimable_at"`                                                                                                                            // 可领取时间
	ClaimedAt   *time.Time     `gorm:"type:timestamp" json:"claimed_at"`                                                                                                                              // 实际领取时间
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联
	Wallet *CryptoWallet `gorm:"foreignKey:WalletID" json:"wallet,omitempty"`
}

// TableName 指定表名
func (AirdropRecord) TableName() string {
	return "finance_wallet_airdrop_records"
}
