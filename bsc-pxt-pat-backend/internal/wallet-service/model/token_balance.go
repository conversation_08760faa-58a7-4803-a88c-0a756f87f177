package model

import (
	"time"

	"gorm.io/gorm"
)

// TokenBalance 代币余额模型
type TokenBalance struct {
	ID            uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	WalletID      uint64         `gorm:"not null;index:idx_token_balances_wallet_token,priority:1" json:"wallet_id"`
	TokenSymbol   string         `gorm:"type:varchar(10);not null;index:idx_token_balances_wallet_token,priority:2" json:"token_symbol"` // PXT, PAT
	TokenAddress  string         `gorm:"type:varchar(42);not null" json:"token_address"`                                                 // 代币合约地址
	Balance       string         `gorm:"type:varchar(50);not null;default:'0'" json:"balance"`                                           // 使用字符串存储大数值
	LockedBalance string         `gorm:"type:varchar(50);not null;default:'0'" json:"locked_balance"`                                    // 锁定余额（质押等）
	LastSyncAt    time.Time      `gorm:"type:timestamp" json:"last_sync_at"`                                                             // 最后同步时间
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联
	Wallet *CryptoWallet `gorm:"foreignKey:WalletID" json:"wallet,omitempty"`
}

// TableName 指定表名
func (TokenBalance) TableName() string {
	return "finance_wallet_token_balances"
}
