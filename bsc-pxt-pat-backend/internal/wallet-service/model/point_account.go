package model

import (
	"time"
)

// PointAccount 点数账户模型（中国大陆版）
type PointAccount struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	UserKSUID string    `gorm:"column:user_ksuid;type:varchar(27);uniqueIndex" json:"user_ksuid"`
	PXTPoints float64   `gorm:"column:pxt_points;type:decimal(20,2);default:0.00" json:"pxt_points"`
	PATPoints float64   `gorm:"column:pat_points;type:decimal(20,2);default:0.00" json:"pat_points"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName PointAccount 的表名
func (PointAccount) TableName() string {
	return "finance_wallet_point_accounts"
}
