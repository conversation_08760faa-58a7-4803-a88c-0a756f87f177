package blockchain

import (
	"context"
	"fmt"
	"math/big"
	"os"
	"strings"
	"time"

	"pxpat-backend/internal/token-service/config"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/rs/zerolog"
)

// Client 区块链客户端
type Client struct {
	client             *ethclient.Client
	stakingPoolAddress common.Address
	stakingPoolABI     abi.ABI
	config             config.BlockchainConfig
	logger             zerolog.Logger
}

// NewClient 创建新的区块链客户端
func NewClient(cfg config.BlockchainConfig) (*Client, error) {
	// 连接到以太坊节点
	client, err := ethclient.Dial(cfg.RPCURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ethereum client: %w", err)
	}

	// 验证连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = client.NetworkID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get network ID: %w", err)
	}

	// 解析合约地址
	stakingPoolAddress := common.HexToAddress(cfg.Contracts.StakingPool)

	// 解析ABI
	stakingPoolABI, err := abi.JSON(strings.NewReader(StakingPoolABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse staking pool ABI: %w", err)
	}

	logger := zerolog.New(os.Stdout).With().
		Str("component", "blockchain_client").
		Str("network", cfg.Network).
		Str("staking_pool", cfg.Contracts.StakingPool).
		Logger()

	return &Client{
		client:             client,
		stakingPoolAddress: stakingPoolAddress,
		stakingPoolABI:     stakingPoolABI,
		config:             cfg,
		logger:             logger,
	}, nil
}

// GetLatestBlockNumber 获取最新区块号
func (c *Client) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	header, err := c.client.HeaderByNumber(ctx, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to get latest block header: %w", err)
	}
	return header.Number.Uint64(), nil
}

// GetBlockByNumber 根据区块号获取区块信息
func (c *Client) GetBlockByNumber(ctx context.Context, blockNumber uint64) (*types.Block, error) {
	block, err := c.client.BlockByNumber(ctx, big.NewInt(int64(blockNumber)))
	if err != nil {
		return nil, fmt.Errorf("failed to get block %d: %w", blockNumber, err)
	}
	return block, nil
}

// FilterLogs 过滤日志
func (c *Client) FilterLogs(ctx context.Context, fromBlock, toBlock uint64) ([]types.Log, error) {
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: []common.Address{c.stakingPoolAddress},
		Topics: [][]common.Hash{
			{
				c.stakingPoolABI.Events["Staked"].ID,
				c.stakingPoolABI.Events["Withdrawn"].ID,
				c.stakingPoolABI.Events["RewardClaimed"].ID,
				c.stakingPoolABI.Events["StakingLevelUpdated"].ID,
				c.stakingPoolABI.Events["MiningPowerUpdated"].ID,
				c.stakingPoolABI.Events["UnlockRequested"].ID,
				c.stakingPoolABI.Events["EmergencyWithdrawal"].ID,
			},
		},
	}

	logs, err := c.client.FilterLogs(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to filter logs: %w", err)
	}

	c.logger.Debug().
		Uint64("from_block", fromBlock).
		Uint64("to_block", toBlock).
		Int("logs_count", len(logs)).
		Msg("Filtered logs")

	return logs, nil
}

// ParseStakedEvent 解析Staked事件
func (c *Client) ParseStakedEvent(log types.Log) (*StakedEvent, error) {
	event := &StakedEvent{}
	err := c.stakingPoolABI.UnpackIntoInterface(event, "Staked", log.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack Staked event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}

	event.Raw = log
	return event, nil
}

// ParseWithdrawnEvent 解析Withdrawn事件
func (c *Client) ParseWithdrawnEvent(log types.Log) (*WithdrawnEvent, error) {
	event := &WithdrawnEvent{}
	err := c.stakingPoolABI.UnpackIntoInterface(event, "Withdrawn", log.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack Withdrawn event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}

	event.Raw = log
	return event, nil
}

// ParseRewardClaimedEvent 解析RewardClaimed事件
func (c *Client) ParseRewardClaimedEvent(log types.Log) (*RewardClaimedEvent, error) {
	event := &RewardClaimedEvent{}
	err := c.stakingPoolABI.UnpackIntoInterface(event, "RewardClaimed", log.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack RewardClaimed event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}

	event.Raw = log
	return event, nil
}

// ParseStakingLevelUpdatedEvent 解析StakingLevelUpdated事件
func (c *Client) ParseStakingLevelUpdatedEvent(log types.Log) (*StakingLevelUpdatedEvent, error) {
	event := &StakingLevelUpdatedEvent{}
	err := c.stakingPoolABI.UnpackIntoInterface(event, "StakingLevelUpdated", log.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack StakingLevelUpdated event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}

	event.Raw = log
	return event, nil
}

// ParseMiningPowerUpdatedEvent 解析MiningPowerUpdated事件
func (c *Client) ParseMiningPowerUpdatedEvent(log types.Log) (*MiningPowerUpdatedEvent, error) {
	event := &MiningPowerUpdatedEvent{}
	err := c.stakingPoolABI.UnpackIntoInterface(event, "MiningPowerUpdated", log.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack MiningPowerUpdated event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}

	event.Raw = log
	return event, nil
}

// GetUserStakingInfo 获取用户质押信息
func (c *Client) GetUserStakingInfo(ctx context.Context, userAddress common.Address) (*UserStakingInfo, error) {
	// 调用合约的getUserStakingInfo方法
	callData, err := c.stakingPoolABI.Pack("getUserStakingInfo", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack getUserStakingInfo call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &c.stakingPoolAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getUserStakingInfo: %w", err)
	}

	// 解析返回结果
	var info UserStakingInfo
	err = c.stakingPoolABI.UnpackIntoInterface(&info, "getUserStakingInfo", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getUserStakingInfo result: %w", err)
	}

	return &info, nil
}

// GetUserTotalRewardsClaimed 获取用户累计奖励领取量
func (c *Client) GetUserTotalRewardsClaimed(ctx context.Context, userAddress common.Address) (*big.Int, error) {
	// 调用合约的totalRewardsClaimed方法
	callData, err := c.stakingPoolABI.Pack("totalRewardsClaimed", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalRewardsClaimed call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &c.stakingPoolAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalRewardsClaimed: %w", err)
	}

	// 解析返回结果
	var totalRewardsClaimed *big.Int
	err = c.stakingPoolABI.UnpackIntoInterface(&totalRewardsClaimed, "totalRewardsClaimed", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack totalRewardsClaimed result: %w", err)
	}

	return totalRewardsClaimed, nil
}

// GetTotalStaked 获取总质押量
func (c *Client) GetTotalStaked(ctx context.Context) (*big.Int, error) {
	callData, err := c.stakingPoolABI.Pack("totalStaked")
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalStaked call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &c.stakingPoolAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalStaked: %w", err)
	}

	totalStaked := new(big.Int)
	err = c.stakingPoolABI.UnpackIntoInterface(&totalStaked, "totalStaked", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack totalStaked result: %w", err)
	}

	return totalStaked, nil
}

// Close 关闭客户端连接
func (c *Client) Close() {
	if c.client != nil {
		c.client.Close()
	}
}

// IsHealthy 检查客户端健康状态
func (c *Client) IsHealthy(ctx context.Context) error {
	_, err := c.client.NetworkID(ctx)
	if err != nil {
		return fmt.Errorf("blockchain client unhealthy: %w", err)
	}
	return nil
}
