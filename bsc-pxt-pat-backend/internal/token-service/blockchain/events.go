package blockchain

import (
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// StakingLevel 质押等级枚举
type StakingLevel uint8

const (
	DingJi StakingLevel = iota
	ChengJi
	YiJi
	JiaJi
	ShiJue
	ShuangShiJue
	ZhiZun
)

// String 返回质押等级的字符串表示
func (s StakingLevel) String() string {
	switch s {
	case DingJi:
		return "丁级"
	case ChengJi:
		return "丙级"
	case YiJi:
		return "乙级"
	case JiaJi:
		return "甲级"
	case ShiJue:
		return "十绝"
	case ShuangShiJue:
		return "双十绝"
	case ZhiZun:
		return "至尊"
	default:
		return "未知"
	}
}

// StakedEvent Staked事件结构
type StakedEvent struct {
	User     common.Address `json:"user"`
	Amount   *big.Int       `json:"amount"`
	Level    StakingLevel   `json:"level"`
	Duration *big.Int       `json:"duration"`
	Raw      types.Log      `json:"raw"`
}

// WithdrawnEvent Withdrawn事件结构
type WithdrawnEvent struct {
	User   common.Address `json:"user"`
	Amount *big.Int       `json:"amount"`
	Raw    types.Log      `json:"raw"`
}

// RewardClaimedEvent RewardClaimed事件结构
type RewardClaimedEvent struct {
	User   common.Address `json:"user"`
	Amount *big.Int       `json:"amount"`
	Raw    types.Log      `json:"raw"`
}

// StakingLevelUpdatedEvent StakingLevelUpdated事件结构
type StakingLevelUpdatedEvent struct {
	User     common.Address `json:"user"`
	OldLevel StakingLevel   `json:"old_level"`
	NewLevel StakingLevel   `json:"new_level"`
	Raw      types.Log      `json:"raw"`
}

// MiningPowerUpdatedEvent MiningPowerUpdated事件结构
type MiningPowerUpdatedEvent struct {
	User     common.Address `json:"user"`
	OldPower *big.Int       `json:"old_power"`
	NewPower *big.Int       `json:"new_power"`
	Raw      types.Log      `json:"raw"`
}

// UnlockRequestedEvent UnlockRequested事件结构
type UnlockRequestedEvent struct {
	User       common.Address `json:"user"`
	Amount     *big.Int       `json:"amount"`
	UnlockTime *big.Int       `json:"unlock_time"`
	Raw        types.Log      `json:"raw"`
}

// EmergencyWithdrawalEvent EmergencyWithdrawal事件结构
type EmergencyWithdrawalEvent struct {
	User    common.Address `json:"user"`
	Amount  *big.Int       `json:"amount"`
	Penalty *big.Int       `json:"penalty"`
	Raw     types.Log      `json:"raw"`
}

// UserStakingInfo 用户质押信息结构
type UserStakingInfo struct {
	Amount                  *big.Int `json:"amount"`
	StartTime               *big.Int `json:"start_time"`
	Level                   uint8    `json:"level"`
	IsUnlocking             bool     `json:"is_unlocking"`
	UnlockTime              *big.Int `json:"unlock_time"`
	RequestedDuration       *big.Int `json:"requested_duration"`
	PendingReward           *big.Int `json:"pending_reward"`
	RandomSeed              *big.Int `json:"random_seed"`
	CurrentRandomMultiplier *big.Int `json:"current_random_multiplier"`
}

// StakingPoolABIJSON 质押池合约ABI JSON
const StakingPoolABIJSON = `[
	{
		"constant": true,
		"inputs": [],
		"name": "getTotalStaked",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "_user", "type": "address"}],
		"name": "getUserStakingInfo",
		"outputs": [
			{"name": "amount", "type": "uint256"},
			{"name": "startTime", "type": "uint256"},
			{"name": "level", "type": "uint8"},
			{"name": "isUnlocking", "type": "bool"},
			{"name": "unlockTime", "type": "uint256"},
			{"name": "requestedDuration", "type": "uint256"},
			{"name": "pendingReward", "type": "uint256"}
		],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "totalStaked",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			},
			{
				"indexed": false,
				"internalType": "enum StakingPool.StakingLevel",
				"name": "level",
				"type": "uint8"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "duration",
				"type": "uint256"
			}
		],
		"name": "Staked",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			}
		],
		"name": "Withdrawn",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			}
		],
		"name": "RewardClaimed",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "enum StakingPool.StakingLevel",
				"name": "oldLevel",
				"type": "uint8"
			},
			{
				"indexed": false,
				"internalType": "enum StakingPool.StakingLevel",
				"name": "newLevel",
				"type": "uint8"
			}
		],
		"name": "StakingLevelUpdated",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "oldPower",
				"type": "uint256"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "newPower",
				"type": "uint256"
			}
		],
		"name": "MiningPowerUpdated",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "unlockTime",
				"type": "uint256"
			}
		],
		"name": "UnlockRequested",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "user",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "penalty",
				"type": "uint256"
			}
		],
		"name": "EmergencyWithdrawal",
		"type": "event"
	},
	{
		"inputs": [
			{
				"internalType": "address",
				"name": "_user",
				"type": "address"
			}
		],
		"name": "getUserStakingInfo",
		"outputs": [
			{
				"internalType": "uint256",
				"name": "amount",
				"type": "uint256"
			},
			{
				"internalType": "uint256",
				"name": "startTime",
				"type": "uint256"
			},
			{
				"internalType": "enum StakingPool.StakingLevel",
				"name": "level",
				"type": "uint8"
			},
			{
				"internalType": "bool",
				"name": "isUnlocking",
				"type": "bool"
			},
			{
				"internalType": "uint256",
				"name": "unlockTime",
				"type": "uint256"
			},
			{
				"internalType": "uint256",
				"name": "requestedDuration",
				"type": "uint256"
			},
			{
				"internalType": "uint256",
				"name": "pendingReward",
				"type": "uint256"
			},
			{
				"internalType": "uint256",
				"name": "randomSeed",
				"type": "uint256"
			},
			{
				"internalType": "uint256",
				"name": "currentRandomMultiplier",
				"type": "uint256"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "totalStaked",
		"outputs": [
			{
				"internalType": "uint256",
				"name": "",
				"type": "uint256"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [
			{
				"internalType": "address",
				"name": "user",
				"type": "address"
			}
		],
		"name": "totalRewardsClaimed",
		"outputs": [
			{
				"internalType": "uint256",
				"name": "",
				"type": "uint256"
			}
		],
		"stateMutability": "view",
		"type": "function"
	}
]`

// ===== 代币相关事件 =====

// TransferEvent ERC20转账事件
type TransferEvent struct {
	From  common.Address `json:"from"`
	To    common.Address `json:"to"`
	Value *big.Int       `json:"value"`
	Raw   types.Log      `json:"raw"`
}

// ApprovalEvent ERC20授权事件
type ApprovalEvent struct {
	Owner   common.Address `json:"owner"`
	Spender common.Address `json:"spender"`
	Value   *big.Int       `json:"value"`
	Raw     types.Log      `json:"raw"`
}

// TokenMintEvent 代币铸造事件
type TokenMintEvent struct {
	To     common.Address `json:"to"`
	Amount *big.Int       `json:"amount"`
	Raw    types.Log      `json:"raw"`
}

// TokenBurnEvent 代币销毁事件
type TokenBurnEvent struct {
	From   common.Address `json:"from"`
	Amount *big.Int       `json:"amount"`
	Raw    types.Log      `json:"raw"`
}

// ===== 治理相关事件 =====

// ProposalCreatedEvent 提案创建事件
type ProposalCreatedEvent struct {
	ProposalId  *big.Int       `json:"proposal_id"`
	Proposer    common.Address `json:"proposer"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	StartBlock  *big.Int       `json:"start_block"`
	EndBlock    *big.Int       `json:"end_block"`
	Raw         types.Log      `json:"raw"`
}

// VoteCastEvent 投票事件
type VoteCastEvent struct {
	Voter      common.Address `json:"voter"`
	ProposalId *big.Int       `json:"proposal_id"`
	Support    uint8          `json:"support"` // 0=against, 1=for, 2=abstain
	Weight     *big.Int       `json:"weight"`
	Reason     string         `json:"reason"`
	Raw        types.Log      `json:"raw"`
}

// ProposalExecutedEvent 提案执行事件
type ProposalExecutedEvent struct {
	ProposalId *big.Int       `json:"proposal_id"`
	Executor   common.Address `json:"executor"`
	Raw        types.Log      `json:"raw"`
}

// ProposalCancelledEvent 提案取消事件
type ProposalCancelledEvent struct {
	ProposalId *big.Int       `json:"proposal_id"`
	Canceller  common.Address `json:"canceller"`
	Raw        types.Log      `json:"raw"`
}

// VotingPowerChangedEvent 投票权重变更事件
type VotingPowerChangedEvent struct {
	User     common.Address `json:"user"`
	OldPower *big.Int       `json:"old_power"`
	NewPower *big.Int       `json:"new_power"`
	Raw      types.Log      `json:"raw"`
}

// DelegationEvent 委托事件
type DelegationEvent struct {
	Delegator common.Address `json:"delegator"`
	Delegate  common.Address `json:"delegate"`
	Amount    *big.Int       `json:"amount"`
	Raw       types.Log      `json:"raw"`
}

// ===== 奖励分配相关事件 =====

// RewardDistributedEvent 奖励分配事件
type RewardDistributedEvent struct {
	Recipient common.Address `json:"recipient"`
	Amount    *big.Int       `json:"amount"`
	Raw       types.Log      `json:"raw"`
}

// ===== 国库相关事件 =====

// TreasuryTransferEvent 国库转账事件
type TreasuryTransferEvent struct {
	Token   common.Address `json:"token"`
	To      common.Address `json:"to"`
	Amount  *big.Int       `json:"amount"`
	Purpose string         `json:"purpose"`
	Raw     types.Log      `json:"raw"`
}
