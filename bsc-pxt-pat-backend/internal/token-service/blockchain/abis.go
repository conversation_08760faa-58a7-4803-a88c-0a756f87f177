package blockchain

// ERC20ABIJSON ERC20代币合约ABI
const ERC20ABIJSON = `[
	{
		"constant": true,
		"inputs": [],
		"name": "name",
		"outputs": [{"name": "", "type": "string"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "symbol",
		"outputs": [{"name": "", "type": "string"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "decimals",
		"outputs": [{"name": "", "type": "uint8"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "totalSupply",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "account", "type": "address"}],
		"name": "balanceOf",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "to", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "transfer",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [
			{"name": "owner", "type": "address"},
			{"name": "spender", "type": "address"}
		],
		"name": "allowance",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "spender", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "approve",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "from", "type": "address"},
			{"name": "to", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "transferFrom",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "from", "type": "address"},
			{"indexed": true, "name": "to", "type": "address"},
			{"indexed": false, "name": "value", "type": "uint256"}
		],
		"name": "Transfer",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "owner", "type": "address"},
			{"indexed": true, "name": "spender", "type": "address"},
			{"indexed": false, "name": "value", "type": "uint256"}
		],
		"name": "Approval",
		"type": "event"
	}
]`

// PXTOKENABIJSON PXT代币合约ABI (扩展ERC20)
const PXTOKENABIJSON = `[
	{
		"constant": true,
		"inputs": [],
		"name": "name",
		"outputs": [{"name": "", "type": "string"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "symbol",
		"outputs": [{"name": "", "type": "string"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "decimals",
		"outputs": [{"name": "", "type": "uint8"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "totalSupply",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "account", "type": "address"}],
		"name": "balanceOf",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "account", "type": "address"}],
		"name": "lockedBalanceOf",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "totalBurned",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "to", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "transfer",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [
			{"name": "owner", "type": "address"},
			{"name": "spender", "type": "address"}
		],
		"name": "allowance",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "spender", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "approve",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "from", "type": "address"},
			{"name": "to", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "transferFrom",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "from", "type": "address"},
			{"indexed": true, "name": "to", "type": "address"},
			{"indexed": false, "name": "value", "type": "uint256"}
		],
		"name": "Transfer",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "owner", "type": "address"},
			{"indexed": true, "name": "spender", "type": "address"},
			{"indexed": false, "name": "value", "type": "uint256"}
		],
		"name": "Approval",
		"type": "event"
	}
]`

// DAOABIJSON DAO治理合约ABI
const DAOABIJSON = `[
	{
		"constant": true,
		"inputs": [{"name": "proposalId", "type": "uint256"}],
		"name": "getProposal",
		"outputs": [
			{"name": "id", "type": "uint256"},
			{"name": "proposer", "type": "address"},
			{"name": "title", "type": "string"},
			{"name": "description", "type": "string"},
			{"name": "startBlock", "type": "uint256"},
			{"name": "endBlock", "type": "uint256"},
			{"name": "forVotes", "type": "uint256"},
			{"name": "againstVotes", "type": "uint256"},
			{"name": "abstainVotes", "type": "uint256"},
			{"name": "status", "type": "uint8"}
		],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "proposalCount",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "title", "type": "string"},
			{"name": "description", "type": "string"},
			{"name": "category", "type": "string"}
		],
		"name": "createProposal",
		"outputs": [{"name": "proposalId", "type": "uint256"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "proposalId", "type": "uint256"},
			{"indexed": true, "name": "proposer", "type": "address"},
			{"indexed": false, "name": "title", "type": "string"},
			{"indexed": false, "name": "description", "type": "string"}
		],
		"name": "ProposalCreated",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "proposalId", "type": "uint256"},
			{"indexed": false, "name": "status", "type": "uint8"}
		],
		"name": "ProposalStatusChanged",
		"type": "event"
	}
]`

// VotingABIJSON 投票合约ABI
const VotingABIJSON = `[
	{
		"constant": false,
		"inputs": [
			{"name": "proposalId", "type": "uint256"},
			{"name": "support", "type": "uint8"},
			{"name": "reason", "type": "string"}
		],
		"name": "castVote",
		"outputs": [],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [
			{"name": "account", "type": "address"},
			{"name": "blockNumber", "type": "uint256"}
		],
		"name": "getVotingPower",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [
			{"name": "proposalId", "type": "uint256"},
			{"name": "voter", "type": "address"}
		],
		"name": "hasVoted",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "voter", "type": "address"},
			{"indexed": true, "name": "proposalId", "type": "uint256"},
			{"indexed": false, "name": "support", "type": "uint8"},
			{"indexed": false, "name": "weight", "type": "uint256"},
			{"indexed": false, "name": "reason", "type": "string"}
		],
		"name": "VoteCast",
		"type": "event"
	}
]`

// ProposalManagerABIJSON 提案管理器合约ABI
const ProposalManagerABIJSON = `[
	{
		"constant": false,
		"inputs": [{"name": "proposalId", "type": "uint256"}],
		"name": "executeProposal",
		"outputs": [],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [{"name": "proposalId", "type": "uint256"}],
		"name": "cancelProposal",
		"outputs": [],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "proposalId", "type": "uint256"}],
		"name": "getProposalState",
		"outputs": [{"name": "", "type": "uint8"}],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "proposalId", "type": "uint256"},
			{"indexed": false, "name": "executor", "type": "address"}
		],
		"name": "ProposalExecuted",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "proposalId", "type": "uint256"},
			{"indexed": false, "name": "canceller", "type": "address"}
		],
		"name": "ProposalCancelled",
		"type": "event"
	}
]`

// TreasuryABIJSON 国库合约ABI
const TreasuryABIJSON = `[
	{
		"constant": true,
		"inputs": [{"name": "token", "type": "address"}],
		"name": "getBalance",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "token", "type": "address"},
			{"name": "to", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "transfer",
		"outputs": [],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [
			{"name": "token", "type": "address"},
			{"name": "amount", "type": "uint256"}
		],
		"name": "burn",
		"outputs": [],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "token", "type": "address"},
			{"indexed": true, "name": "to", "type": "address"},
			{"indexed": false, "name": "amount", "type": "uint256"},
			{"indexed": false, "name": "purpose", "type": "string"}
		],
		"name": "TreasuryTransfer",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "token", "type": "address"},
			{"indexed": false, "name": "amount", "type": "uint256"}
		],
		"name": "TokenBurned",
		"type": "event"
	}
]`

// RewardDistributorABIJSON 奖励分配器合约ABI
const RewardDistributorABIJSON = `[
	{
		"constant": false,
		"inputs": [
			{"name": "recipients", "type": "address[]"},
			{"name": "amounts", "type": "uint256[]"}
		],
		"name": "distributeRewards",
		"outputs": [],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "user", "type": "address"}],
		"name": "getPendingRewards",
		"outputs": [{"name": "", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": false,
		"inputs": [],
		"name": "claimRewards",
		"outputs": [],
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "recipient", "type": "address"},
			{"indexed": false, "name": "amount", "type": "uint256"}
		],
		"name": "RewardDistributed",
		"type": "event"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "user", "type": "address"},
			{"indexed": false, "name": "amount", "type": "uint256"}
		],
		"name": "RewardClaimed",
		"type": "event"
	}
]`

// ContentRegistryABIJSON 内容注册表合约ABI
const ContentRegistryABIJSON = `[
	{
		"inputs": [
			{"name": "contentType", "type": "string"},
			{"name": "title", "type": "string"},
			{"name": "ipfsHash", "type": "string"},
			{"name": "metadataURI", "type": "string"},
			{"name": "reviewers", "type": "string[]"}
		],
		"name": "registerContent",
		"outputs": [{"name": "contentId", "type": "uint256"}],
		"stateMutability": "nonpayable",
		"type": "function"
	},
	{
		"inputs": [{"name": "contentType", "type": "string"}],
		"name": "getContentFee",
		"outputs": [{"name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [{"name": "contentType", "type": "string"}],
		"name": "contentTypeExists",
		"outputs": [{"name": "", "type": "bool"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [{"name": "contentId", "type": "uint256"}],
		"name": "getContent",
		"outputs": [
			{"name": "contentId", "type": "uint256"},
			{"name": "creator", "type": "address"},
			{"name": "contentType", "type": "string"},
			{"name": "title", "type": "string"},
			{"name": "ipfsHash", "type": "string"},
			{"name": "metadataURI", "type": "string"},
			{"name": "patFee", "type": "uint256"},
			{"name": "timestamp", "type": "uint256"},
			{"name": "isLocked", "type": "bool"},
			{"name": "isActive", "type": "bool"},
			{"name": "mintCount", "type": "uint256"},
			{"name": "totalEarnings", "type": "uint256"}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"anonymous": false,
		"inputs": [
			{"indexed": true, "name": "contentId", "type": "uint256"},
			{"indexed": true, "name": "creator", "type": "address"},
			{"indexed": false, "name": "contentType", "type": "string"},
			{"indexed": false, "name": "ipfsHash", "type": "string"},
			{"indexed": false, "name": "patFee", "type": "uint256"}
		],
		"name": "ContentRegistered",
		"type": "event"
	}
]`
