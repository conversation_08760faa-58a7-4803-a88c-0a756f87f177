package blockchain

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/config"
)

// MultiContractClient 多合约区块链客户端
type MultiContractClient struct {
	client    *ethclient.Client
	config    *config.BlockchainConfig
	logger    zerolog.Logger
	contracts *ContractManager
}

// ContractManager 合约管理器
type ContractManager struct {
	Addresses *ContractAddresses
	ABIs      *ContractABIs
}

// ContractAddresses 合约地址集合
type ContractAddresses struct {
	// 核心代币合约
	PXTToken common.Address // PXT 治理代币合约地址
	PATToken common.Address // PAT 功能代币合约地址

	// 质押系统合约
	StakingPool       common.Address // 质押池合约地址
	StakingFactory    common.Address // 质押工厂合约地址
	RewardDistributor common.Address // 奖励分配器合约地址

	// 治理系统合约
	DAO             common.Address // DAO 主合约地址
	Voting          common.Address // 投票合约地址
	ProposalManager common.Address // 提案管理器合约地址
	Treasury        common.Address // 国库合约地址
	RoleManager     common.Address // 角色管理器合约地址

	// 其他系统合约
	TokenRegistry common.Address // 代币注册表合约地址
	PXTFactory    common.Address // PXT 工厂合约地址
	PATFactory    common.Address // PAT 工厂合约地址

	// 内容系统合约
	ContentRegistry common.Address // 内容注册表合约地址

	// 池子地址配置 - 重要：用于监听转账和奖励分发
	ChinaMainlandPool common.Address // 中国大陆池子地址
	GlobalPool        common.Address // 全球池子地址
}

// ContractABIs 合约ABI集合
type ContractABIs struct {
	ERC20Token        abi.ABI
	PXTToken          abi.ABI
	StakingPool       abi.ABI
	DAO               abi.ABI
	RewardDistributor abi.ABI
	Voting            abi.ABI
	ProposalManager   abi.ABI
	Treasury          abi.ABI
	ContentRegistry   abi.ABI
}

// UserStakeInfo 用户质押信息
type UserStakeInfo struct {
	Amount              *big.Int  `json:"amount"`
	Level               uint8     `json:"level"`
	MiningPower         *big.Int  `json:"mining_power"`
	TotalRewardsClaimed *big.Int  `json:"total_rewards_claimed"`
	StartTime           time.Time `json:"start_time"`
	LastUpdateTime      time.Time `json:"last_update_time"`
}

// NewMultiContractClient 创建新的多合约区块链客户端
func NewMultiContractClient(cfg *config.BlockchainConfig, logger zerolog.Logger) (*MultiContractClient, error) {
	// 🚀 构建RPC URL列表（主要 + 备用）
	rpcURLs := []string{cfg.RPCURL}
	rpcURLs = append(rpcURLs, cfg.BackupRPCURLs...)

	var client *ethclient.Client
	var err error
	var connectedURL string

	// 🚀 尝试连接每个RPC节点
	for _, rpcURL := range rpcURLs {
		logger.Info().
			Str("rpc_url", rpcURL).
			Msg("Attempting to connect to blockchain...")

		client, err = ethclient.Dial(rpcURL)
		if err == nil {
			connectedURL = rpcURL
			break
		}

		logger.Warn().
			Err(err).
			Str("rpc_url", rpcURL).
			Msg("Failed to connect to this RPC node, trying next...")
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to any RPC node: %w", err)
	}

	// 验证网络连接（增加超时控制）
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	chainID, err := client.ChainID(ctx)
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	if chainID.Int64() != cfg.ChainID {
		client.Close()
		return nil, fmt.Errorf("chain ID mismatch: expected %d, got %d", cfg.ChainID, chainID.Int64())
	}

	logger.Info().
		Int64("chain_id", chainID.Int64()).
		Str("rpc_url", connectedURL).
		Msg("Successfully connected to blockchain")

	// 解析合约地址 - 更新为最新部署地址 (2025-07-04)
	addresses := &ContractAddresses{
		// 核心代币合约
		PXTToken: common.HexToAddress(cfg.Contracts.PXTToken),
		PATToken: common.HexToAddress(cfg.Contracts.PATToken),

		// 质押系统合约
		StakingPool:       common.HexToAddress(cfg.Contracts.StakingPool),
		StakingFactory:    common.HexToAddress(cfg.Contracts.StakingFactory),
		RewardDistributor: common.HexToAddress(cfg.Contracts.RewardDistributor),

		// 治理系统合约
		DAO:             common.HexToAddress(cfg.Contracts.DAO),
		Voting:          common.HexToAddress(cfg.Contracts.Voting),
		ProposalManager: common.HexToAddress(cfg.Contracts.ProposalManager),
		Treasury:        common.HexToAddress(cfg.Contracts.Treasury),
		RoleManager:     common.HexToAddress(cfg.Contracts.RoleManager),

		// 其他系统合约
		TokenRegistry: common.HexToAddress(cfg.Contracts.TokenRegistry),
		PXTFactory:    common.HexToAddress(cfg.Contracts.PXTFactory),
		PATFactory:    common.HexToAddress(cfg.Contracts.PATFactory),

		// 内容系统合约
		ContentRegistry: common.HexToAddress(cfg.Contracts.ContentRegistry),

		// 池子地址配置 - 重要：用于监听转账和奖励分发
		ChinaMainlandPool: common.HexToAddress(cfg.Contracts.ChinaMainlandPool),
		GlobalPool:        common.HexToAddress(cfg.Contracts.GlobalPool),
	}

	// 解析合约ABIs
	abis, err := parseContractABIs()
	if err != nil {
		return nil, fmt.Errorf("failed to parse contract ABIs: %w", err)
	}

	contracts := &ContractManager{
		Addresses: addresses,
		ABIs:      abis,
	}

	return &MultiContractClient{
		client:    client,
		config:    cfg,
		logger:    logger,
		contracts: contracts,
	}, nil
}

// parseContractABIs 解析所有合约的ABI
func parseContractABIs() (*ContractABIs, error) {
	// ERC20 Token ABI
	erc20ABI, err := abi.JSON(strings.NewReader(ERC20ABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	// PXT Token ABI
	pxtTokenABI, err := abi.JSON(strings.NewReader(PXTOKENABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse PXTToken ABI: %w", err)
	}

	// Staking Pool ABI
	stakingPoolABI, err := abi.JSON(strings.NewReader(StakingPoolABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse StakingPool ABI: %w", err)
	}

	// DAO ABI
	daoABI, err := abi.JSON(strings.NewReader(DAOABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse DAO ABI: %w", err)
	}

	// Voting ABI
	votingABI, err := abi.JSON(strings.NewReader(VotingABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Voting ABI: %w", err)
	}

	// Proposal Manager ABI
	proposalManagerABI, err := abi.JSON(strings.NewReader(ProposalManagerABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ProposalManager ABI: %w", err)
	}

	// Treasury ABI
	treasuryABI, err := abi.JSON(strings.NewReader(TreasuryABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Treasury ABI: %w", err)
	}

	// Reward Distributor ABI
	rewardDistributorABI, err := abi.JSON(strings.NewReader(RewardDistributorABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse RewardDistributor ABI: %w", err)
	}

	// Content Registry ABI
	contentRegistryABI, err := abi.JSON(strings.NewReader(ContentRegistryABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ContentRegistry ABI: %w", err)
	}

	return &ContractABIs{
		ERC20Token:        erc20ABI,
		PXTToken:          pxtTokenABI,
		StakingPool:       stakingPoolABI,
		DAO:               daoABI,
		RewardDistributor: rewardDistributorABI,
		Voting:            votingABI,
		ProposalManager:   proposalManagerABI,
		Treasury:          treasuryABI,
		ContentRegistry:   contentRegistryABI,
	}, nil
}

// GetTokenBalance 获取代币余额
func (c *MultiContractClient) GetTokenBalance(ctx context.Context, tokenAddress, userAddress common.Address) (*big.Int, error) {
	callData, err := c.contracts.ABIs.ERC20Token.Pack("balanceOf", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack balanceOf call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call balanceOf: %w", err)
	}

	balance := new(big.Int)
	err = c.contracts.ABIs.ERC20Token.UnpackIntoInterface(&balance, "balanceOf", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack balanceOf result: %w", err)
	}

	return balance, nil
}

// GetPXTBalance 获取PXT代币余额
func (c *MultiContractClient) GetPXTBalance(ctx context.Context, userAddress common.Address) (*big.Int, error) {
	return c.GetTokenBalance(ctx, c.contracts.Addresses.PXTToken, userAddress)
}

// GetPATBalance 获取PAT代币余额
func (c *MultiContractClient) GetPATBalance(ctx context.Context, userAddress common.Address) (*big.Int, error) {
	return c.GetTokenBalance(ctx, c.contracts.Addresses.PATToken, userAddress)
}

// GetTokenTotalSupply 获取代币总供应量
func (c *MultiContractClient) GetTokenTotalSupply(ctx context.Context, tokenAddress common.Address) (*big.Int, error) {
	callData, err := c.contracts.ABIs.ERC20Token.Pack("totalSupply")
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalSupply call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalSupply: %w", err)
	}

	totalSupply := new(big.Int)
	err = c.contracts.ABIs.ERC20Token.UnpackIntoInterface(&totalSupply, "totalSupply", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack totalSupply result: %w", err)
	}

	return totalSupply, nil
}

// GetTokenLockedBalance 获取代币锁定余额 (仅适用于PXT代币)
func (c *MultiContractClient) GetTokenLockedBalance(ctx context.Context, tokenAddress common.Address, userAddress common.Address) (*big.Int, error) {
	callData, err := c.contracts.ABIs.PXTToken.Pack("lockedBalanceOf", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack lockedBalanceOf call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call lockedBalanceOf: %w", err)
	}

	lockedBalance := new(big.Int)
	err = c.contracts.ABIs.PXTToken.UnpackIntoInterface(&lockedBalance, "lockedBalanceOf", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack lockedBalanceOf result: %w", err)
	}

	return lockedBalance, nil
}

// GetTokenTotalBurned 获取代币总销毁量 (仅适用于PXT代币)
func (c *MultiContractClient) GetTokenTotalBurned(ctx context.Context, tokenAddress common.Address) (*big.Int, error) {
	callData, err := c.contracts.ABIs.PXTToken.Pack("totalBurned")
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalBurned call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalBurned: %w", err)
	}

	totalBurned := new(big.Int)
	err = c.contracts.ABIs.PXTToken.UnpackIntoInterface(&totalBurned, "totalBurned", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack totalBurned result: %w", err)
	}

	return totalBurned, nil
}

// FilterTokenTransferLogs 过滤代币转账日志
func (c *MultiContractClient) FilterTokenTransferLogs(ctx context.Context, tokenAddress common.Address, fromBlock, toBlock uint64) ([]types.Log, error) {
	transferEventID := c.contracts.ABIs.ERC20Token.Events["Transfer"].ID

	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: []common.Address{tokenAddress},
		Topics:    [][]common.Hash{{transferEventID}},
	}

	logs, err := c.client.FilterLogs(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to filter transfer logs: %w", err)
	}

	return logs, nil
}

// GetLatestBlockNumber 获取最新区块号
func (c *MultiContractClient) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	header, err := c.client.HeaderByNumber(ctx, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to get latest block header: %w", err)
	}
	return header.Number.Uint64(), nil
}

// GetBlockByNumber 根据区块号获取区块信息
func (c *MultiContractClient) GetBlockByNumber(ctx context.Context, blockNumber uint64) (*types.Block, error) {
	block, err := c.client.BlockByNumber(ctx, big.NewInt(int64(blockNumber)))
	if err != nil {
		return nil, fmt.Errorf("failed to get block %d: %w", blockNumber, err)
	}
	return block, nil
}

// Close 关闭客户端连接
func (c *MultiContractClient) Close() {
	if c.client != nil {
		c.client.Close()
	}
}

// IsHealthy 检查客户端健康状态
func (c *MultiContractClient) IsHealthy(ctx context.Context) error {
	_, err := c.client.ChainID(ctx)
	if err != nil {
		return fmt.Errorf("blockchain client unhealthy: %w", err)
	}
	return nil
}

// GetUserStakeInfo 获取用户质押信息
func (c *MultiContractClient) GetUserStakeInfo(ctx context.Context, stakingPoolAddress common.Address, userAddress common.Address) (*UserStakeInfo, error) {
	// 调用合约的getUserStakeInfo方法
	callData, err := c.contracts.ABIs.StakingPool.Pack("getUserStakeInfo", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack getUserStakeInfo call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &stakingPoolAddress,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getUserStakeInfo: %w", err)
	}

	// 解析返回结果
	var resultData struct {
		Amount              *big.Int
		Level               uint8
		MiningPower         *big.Int
		TotalRewardsClaimed *big.Int
		StartTime           *big.Int
		LastUpdateTime      *big.Int
	}

	err = c.contracts.ABIs.StakingPool.UnpackIntoInterface(&resultData, "getUserStakeInfo", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getUserStakeInfo result: %w", err)
	}

	// 转换时间戳
	startTime := time.Unix(resultData.StartTime.Int64(), 0)
	lastUpdateTime := time.Unix(resultData.LastUpdateTime.Int64(), 0)

	return &UserStakeInfo{
		Amount:              resultData.Amount,
		Level:               resultData.Level,
		MiningPower:         resultData.MiningPower,
		TotalRewardsClaimed: resultData.TotalRewardsClaimed,
		StartTime:           startTime,
		LastUpdateTime:      lastUpdateTime,
	}, nil
}

// GetContracts 获取合约管理器
func (c *MultiContractClient) GetContracts() *ContractManager {
	return c.contracts
}

// GetClient 获取底层ethclient
func (c *MultiContractClient) GetClient() *ethclient.Client {
	return c.client
}

// GetUserStakingInfo 获取用户质押信息（兼容原有接口）
func (c *MultiContractClient) GetUserStakingInfo(ctx context.Context, userAddress common.Address) (*UserStakingInfo, error) {
	// 调用合约的getUserStakingInfo方法
	callData, err := c.contracts.ABIs.StakingPool.Pack("getUserStakingInfo", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack getUserStakingInfo call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &c.contracts.Addresses.StakingPool,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getUserStakingInfo: %w", err)
	}

	// 解析返回结果 - 根据合约实际返回的结构
	var info UserStakingInfo
	err = c.contracts.ABIs.StakingPool.UnpackIntoInterface(&info, "getUserStakingInfo", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getUserStakingInfo result: %w", err)
	}

	return &info, nil
}

// GetUserTotalRewardsClaimed 获取用户累计奖励领取量
func (c *MultiContractClient) GetUserTotalRewardsClaimed(ctx context.Context, userAddress common.Address) (*big.Int, error) {
	// 调用合约的totalRewardsClaimed方法
	callData, err := c.contracts.ABIs.StakingPool.Pack("totalRewardsClaimed", userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalRewardsClaimed call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &c.contracts.Addresses.StakingPool,
		Data: callData,
	}

	result, err := c.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalRewardsClaimed: %w", err)
	}

	// 解析返回结果
	var totalRewardsClaimed *big.Int
	err = c.contracts.ABIs.StakingPool.UnpackIntoInterface(&totalRewardsClaimed, "totalRewardsClaimed", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack totalRewardsClaimed result: %w", err)
	}

	return totalRewardsClaimed, nil
}
