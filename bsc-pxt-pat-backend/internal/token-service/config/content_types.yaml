# 内容类型推荐属性配置文件
# 这个文件定义了各种内容类型的推荐属性，可以根据业务需求灵活调整

content_types:
  video:
    name: "视频"
    description: "视频内容，包括电影、电视剧、短片等"
    default_fee: 1.0
    attributes:
      genre: "视频类型 (如: 动作, 喜剧, 纪录片, 科幻, 爱情)"
      resolution: "分辨率 (如: 1920x1080, 3840x2160, 1280x720)"
      format: "视频格式 (如: mp4, avi, mkv, mov)"
      rating: "评级 (如: G, PG, PG-13, R, NC-17)"
      subtitle_languages: "字幕语言 (如: zh-CN, en-US, ja-JP)"
      quality: "画质 (如: 高清, 超清, 4K, 8K)"
    numerics:
      duration_seconds: "视频时长(秒)"
      file_size_mb: "文件大小(MB)"
      bitrate_kbps: "比特率(kbps)"
      frame_rate: "帧率(fps)"
    creators:
      - "director:导演"
      - "producer:制片人"
      - "actor:演员"
      - "screenwriter:编剧"
    contributors:
      - "editor:剪辑师"
      - "cinematographer:摄影师"
      - "sound_engineer:音响师"
      - "visual_effects:特效师"

  short_video:
    name: "短视频"
    description: "短视频内容，如抖音、快手等平台的短视频"
    default_fee: 0.2
    attributes:
      genre: "短视频类型 (如: 搞笑, 教学, 生活, 美食, 旅行)"
      platform: "目标平台 (如: 抖音, 快手, B站, 小红书)"
      style: "风格 (如: 竖屏, 横屏, 方形)"
      filter: "滤镜效果 (如: 复古, 清新, 暖色调)"
    numerics:
      duration_seconds: "时长(秒)"
      view_count: "播放量"
      like_count: "点赞数"
    creators:
      - "creator:创作者"
      - "performer:表演者"
    contributors:
      - "editor:剪辑师"
      - "music_provider:配乐提供者"

  novel:
    name: "小说"
    description: "小说作品，包括网络小说、传统文学等"
    default_fee: 0.1
    attributes:
      genre: "小说类型 (如: 科幻, 言情, 悬疑, 历史, 玄幻, 都市)"
      status: "状态 (如: 连载中, 已完结, 暂停更新)"
      isbn: "ISBN号"
      series: "系列名称"
      language_style: "语言风格 (如: 现代文, 古文, 白话文)"
      target_audience: "目标读者 (如: 青少年, 成人, 儿童)"
    numerics:
      word_count: "字数"
      chapter_count: "章节数"
      page_count: "页数"
      reading_time_minutes: "预计阅读时间(分钟)"
    creators:
      - "author:作者"
      - "co_author:合著者"
      - "original_author:原作者"
    contributors:
      - "editor:编辑"
      - "translator:翻译"
      - "illustrator:插画师"
      - "proofreader:校对"

  music:
    name: "音乐"
    description: "音乐作品，包括歌曲、纯音乐、专辑等"
    default_fee: 0.5
    attributes:
      genre: "音乐类型 (如: 流行, 摇滚, 古典, 电子, 民谣, 爵士)"
      album: "专辑名称"
      format: "音频格式 (如: mp3, flac, wav, aac)"
      key_signature: "调性 (如: C大调, A小调, G大调)"
      mood: "情绪 (如: 欢快, 忧伤, 激昂, 宁静)"
      language: "演唱语言 (如: 中文, 英文, 日文)"
    numerics:
      duration_seconds: "时长(秒)"
      track_number: "曲目编号"
      bpm: "节拍数"
      sample_rate_hz: "采样率(Hz)"
      bit_depth: "位深度"
    creators:
      - "composer:作曲"
      - "lyricist:作词"
      - "performer:演奏者"
      - "vocalist:歌手"
    contributors:
      - "producer:制作人"
      - "mixer:混音师"
      - "mastering_engineer:母带工程师"
      - "arranger:编曲"

  article:
    name: "文章"
    description: "文章内容，包括博客、新闻、学术论文等"
    default_fee: 0.05
    attributes:
      category: "分类 (如: 技术, 生活, 观点, 新闻, 教育, 健康)"
      difficulty_level: "难度等级 (如: 初级, 中级, 高级, 专业)"
      source: "来源 (如: 原创, 翻译, 转载, 编译)"
      format: "格式 (如: markdown, html, pdf, docx)"
      topic: "主题标签"
    numerics:
      word_count: "字数"
      reading_time_minutes: "阅读时间(分钟)"
      view_count: "阅读量"
      comment_count: "评论数"
    creators:
      - "author:作者"
      - "co_author:合著者"
      - "researcher:研究者"
    contributors:
      - "editor:编辑"
      - "translator:翻译"
      - "reviewer:审稿人"
      - "fact_checker:事实核查员"

  manga:
    name: "漫画"
    description: "漫画作品，包括日漫、国漫、欧美漫画等"
    default_fee: 0.3
    attributes:
      genre: "漫画类型 (如: 少年, 少女, 青年, 儿童, 成人)"
      status: "状态 (如: 连载中, 已完结, 暂停更新)"
      style: "画风 (如: 日式, 美式, 欧式, 国风)"
      color_type: "色彩 (如: 彩色, 黑白, 部分彩色)"
      reading_direction: "阅读方向 (如: 从右到左, 从左到右)"
    numerics:
      page_count: "页数"
      chapter_count: "章节数"
      volume_count: "卷数"
      panel_count: "分格数"
    creators:
      - "mangaka:漫画家"
      - "story_writer:剧本作者"
      - "character_designer:角色设计"
    contributors:
      - "assistant:助手"
      - "colorist:上色师"
      - "translator:翻译"
      - "letterer:文字编辑"

  anime:
    name: "动漫"
    description: "动画作品，包括TV动画、OVA、电影动画等"
    default_fee: 0.8
    attributes:
      genre: "动漫类型 (如: 少年, 少女, 机甲, 魔法, 日常)"
      status: "状态 (如: 播放中, 已完结, 制作中)"
      studio: "制作公司"
      source: "原作来源 (如: 原创, 漫画改编, 小说改编, 游戏改编)"
      target_audience: "目标观众 (如: 儿童, 青少年, 成人)"
    numerics:
      episode_count: "集数"
      duration_minutes: "单集时长(分钟)"
      season_number: "季数"
      production_year: "制作年份"
    creators:
      - "director:导演"
      - "original_creator:原作者"
      - "character_designer:角色设计"
      - "series_composition:系列构成"
    contributors:
      - "animator:动画师"
      - "voice_actor:声优"
      - "music_composer:音乐制作"
      - "art_director:美术监督"

# 通用属性 (适用于所有内容类型)
common_attributes:
  language: "语言 (如: zh-CN, en-US, ja-JP)"
  region: "地区 (如: 中国大陆, 香港, 台湾, 日本, 美国)"
  copyright_status: "版权状态 (如: 原创, 授权, 公有领域)"
  content_warning: "内容警告 (如: 暴力, 血腥, 恐怖, 成人内容)"
  accessibility: "无障碍支持 (如: 字幕, 音频描述, 手语)"

# 通用数值属性
common_numerics:
  creation_timestamp: "创作时间戳"
  publication_timestamp: "发布时间戳"
  last_modified_timestamp: "最后修改时间戳"
  version_number: "版本号"
  file_size_bytes: "文件大小(字节)"
