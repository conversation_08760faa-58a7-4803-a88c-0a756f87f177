package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	Database   DatabaseConfig   `mapstructure:"database"`
	Redis      RedisConfig      `mapstructure:"redis"`
	Blockchain BlockchainConfig `mapstructure:"blockchain"`
	Transfer   TransferConfig   `mapstructure:"transfer"`
	Sync       SyncConfig       `mapstructure:"sync"` // 🆕 同步配置
	Log        LogConfig        `mapstructure:"log"`
	Security   SecurityConfig   `mapstructure:"security"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	HTTPPort int    `mapstructure:"http_port"`
	Mode     string `mapstructure:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host                   string `mapstructure:"host"`
	Port                   int    `mapstructure:"port"`
	User                   string `mapstructure:"user"`
	Password               string `mapstructure:"password"`
	DBName                 string `mapstructure:"dbname"`
	SSLMode                string `mapstructure:"ssl_mode"`
	MaxOpenConns           int    `mapstructure:"max_open_conns"`
	MaxIdleConns           int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetimeMinutes string `mapstructure:"conn_max_lifetime_minutes"`
	AutoMigrate            bool   `mapstructure:"auto_migrate"`
	TimeZone               string `mapstructure:"time_zone"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host                string `mapstructure:"host"`
	Port                int    `mapstructure:"port"`
	Password            string `mapstructure:"password"`
	DB                  int    `mapstructure:"db"`
	PoolSize            int    `mapstructure:"pool_size"`
	MinIdleConns        int    `mapstructure:"min_idle_conns"`
	DialTimeoutSeconds  string `mapstructure:"dial_timeout_seconds"`
	ReadTimeoutSeconds  string `mapstructure:"read_timeout_seconds"`
	WriteTimeoutSeconds string `mapstructure:"write_timeout_seconds"`
	PoolTimeoutSeconds  string `mapstructure:"pool_timeout_seconds"`
}

// BlockchainConfig 区块链配置
type BlockchainConfig struct {
	Network             string          `mapstructure:"network"`
	RPCURL              string          `mapstructure:"rpc_url"`
	BackupRPCURLs       []string        `mapstructure:"backup_rpc_urls"`
	ChainID             int64           `mapstructure:"chain_id"`
	StartBlock          uint64          `mapstructure:"start_block"`
	ConfirmBlocks       uint64          `mapstructure:"confirm_blocks"`
	BatchSize           int             `mapstructure:"batch_size"`
	BSCScanAPIKey       string          `mapstructure:"bscscan_api_key"`
	UseBSCScanForEvents bool            `mapstructure:"use_bscscan_for_events"`
	Contracts           ContractsConfig `mapstructure:"contracts"`
	Sync                SyncConfig      `mapstructure:"sync"`
}

// ContractsConfig 合约配置
type ContractsConfig struct {
	// 核心代币合约
	PXTToken string `mapstructure:"pxt_token"` // PXT 治理代币合约地址
	PATToken string `mapstructure:"pat_token"` // PAT 功能代币合约地址

	// 质押系统合约
	StakingPool       string `mapstructure:"staking_pool"`       // 质押池合约地址
	StakingFactory    string `mapstructure:"staking_factory"`    // 质押工厂合约地址
	RewardDistributor string `mapstructure:"reward_distributor"` // 奖励分配器合约地址

	// 治理系统合约
	DAO             string `mapstructure:"dao"`              // DAO 主合约地址
	Voting          string `mapstructure:"voting"`           // 投票合约地址
	ProposalManager string `mapstructure:"proposal_manager"` // 提案管理器合约地址
	Treasury        string `mapstructure:"treasury"`         // 国库合约地址
	RoleManager     string `mapstructure:"role_manager"`     // 角色管理器合约地址

	// 其他系统合约
	TokenRegistry string `mapstructure:"token_registry"` // 代币注册表合约地址
	PXTFactory    string `mapstructure:"pxt_factory"`    // PXT 工厂合约地址
	PATFactory    string `mapstructure:"pat_factory"`    // PAT 工厂合约地址

	// 池子地址配置 - 重要：用于监听转账和奖励分发
	ChinaMainlandPool string `mapstructure:"china_mainland_pool"` // 中国大陆池子地址
	GlobalPool        string `mapstructure:"global_pool"`         // 全球池子地址

	// 内容注册表
	ContentRegistry string `mapstructure:"content_registry"` // 内容注册表合约地址
}

// TransferConfig 转账配置
type TransferConfig struct {
	SystemPrivateKey string `mapstructure:"system_private_key"`
	SystemAddress    string `mapstructure:"system_address"`
	GasLimit         uint64 `mapstructure:"gas_limit"`
	GasPrice         int64  `mapstructure:"gas_price"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	// 基础同步配置
	Enabled           bool `mapstructure:"enabled"`
	SyncInterval      int  `mapstructure:"sync_interval_seconds"`
	MaxRetries        int  `mapstructure:"max_retries"`
	RetryDelay        int  `mapstructure:"retry_delay_seconds"`
	ConcurrentWorkers int  `mapstructure:"concurrent_workers"`

	// 🆕 每日完整同步配置
	EnableDailyFullSync bool `mapstructure:"enable_daily_full_sync"`
	DailyFullSyncHour   int  `mapstructure:"daily_full_sync_hour"`
	DailyFullSyncMinute int  `mapstructure:"daily_full_sync_minute"`

	// 🆕 同步间隔配置（秒）
	TransferSyncIntervalSeconds int `mapstructure:"transfer_sync_interval_seconds"`
	UserSyncIntervalSeconds     int `mapstructure:"user_sync_interval_seconds"`

	// 🆕 批次大小配置
	TransferBatchSize int `mapstructure:"transfer_batch_size"`
	ContentBatchSize  int `mapstructure:"content_batch_size"`
	BurnBatchSize     int `mapstructure:"burn_batch_size"`

	// 🆕 性能配置
	MaxSyncDurationSeconds int  `mapstructure:"max_sync_duration_seconds"`
	EnableMetrics          bool `mapstructure:"enable_metrics"`

	// 🆕 数据保留配置
	MetricsRetentionDays int `mapstructure:"metrics_retention_days"`
	LogRetentionDays     int `mapstructure:"log_retention_days"`

	// 🆕 日志优化配置
	LogSyncProgressInterval int  `mapstructure:"log_sync_progress_interval"`
	LogTransferDetails      bool `mapstructure:"log_transfer_details"`
	LogBalanceUpdates       bool `mapstructure:"log_balance_updates"`
	EnableColorfulLogs      bool `mapstructure:"enable_colorful_logs"`
	ShowBlockProgress       bool `mapstructure:"show_block_progress"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level         string `mapstructure:"level"`
	Format        string `mapstructure:"format"`
	EnableSQLLogs bool   `mapstructure:"enable_sql_logs"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	CORS CORSConfig `mapstructure:"cors"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	Enabled            bool   `mapstructure:"enabled"`
	AllowedOrigins     string `mapstructure:"allowed_origins"`
	AllowedMethods     string `mapstructure:"allowed_methods"`
	AllowedHeaders     string `mapstructure:"allowed_headers"`
	AllowedCredentials string `mapstructure:"allowed_credentials"`
}

// Load 加载配置
func Load() (*Config, error) {
	// 尝试加载.env文件
	loadEnvFile()

	// 根据环境变量选择配置文件路径
	env := os.Getenv("PXPAT_ENV")
	var envDir string
	switch env {
	case "dev", "development":
		envDir = "dev"
	case "prod", "production":
		envDir = "prod"
	default:
		envDir = "prod"
	}

	// 设置配置文件路径
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(fmt.Sprintf("./configs/%s/token-service/", envDir))
	viper.AddConfigPath(fmt.Sprintf("../../../configs/%s/token-service/", envDir))
	viper.AddConfigPath(fmt.Sprintf("../../../../configs/%s/token-service/", envDir))
	viper.AddConfigPath(fmt.Sprintf("./configs/%s/token-service/", envDir))
	viper.AddConfigPath(fmt.Sprintf("./configs/%s/", envDir))
	viper.AddConfigPath(".")

	// 设置环境变量前缀
	viper.SetEnvPrefix("TOKEN_SERVICE")
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认值和环境变量
	}

	// 从环境变量覆盖配置
	overrideFromEnv()

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 调试：打印数据库配置
	fmt.Printf("DEBUG: Database config - Host: %s, Port: %d, User: %s, DBName: %s\n",
		config.Database.Host, config.Database.Port, config.Database.User, config.Database.DBName)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &config, nil
}

// loadEnvFile 加载.env文件
func loadEnvFile() {
	// 尝试加载多个可能的.env文件位置
	envPaths := []string{
		"./configs/token-service/.env",
		"./.env",
		"./configs/.env",
	}

	for _, envPath := range envPaths {
		if _, err := os.Stat(envPath); err == nil {
			// 手动读取.env文件并设置环境变量
			if file, err := os.Open(envPath); err == nil {
				defer file.Close()

				// 简单的.env文件解析
				buf := make([]byte, 1024)
				content := ""
				for {
					n, err := file.Read(buf)
					if n > 0 {
						content += string(buf[:n])
					}
					if err != nil {
						break
					}
				}

				// 按行分割
				for _, line := range splitLines(content) {
					line = trimSpace(line)
					if line != "" && !startsWith(line, "#") {
						if parts := splitKeyValue(line); len(parts) == 2 {
							os.Setenv(parts[0], parts[1])
						}
					}
				}
				break
			}
		}
	}
}

// 简单的字符串处理函数
func splitLines(s string) []string {
	var lines []string
	var current string
	for _, r := range s {
		if r == '\n' {
			lines = append(lines, current)
			current = ""
		} else if r != '\r' {
			current += string(r)
		}
	}
	if current != "" {
		lines = append(lines, current)
	}
	return lines
}

func trimSpace(s string) string {
	start := 0
	end := len(s)
	for start < end && (s[start] == ' ' || s[start] == '\t') {
		start++
	}
	for end > start && (s[end-1] == ' ' || s[end-1] == '\t') {
		end--
	}
	return s[start:end]
}

func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}

func splitKeyValue(s string) []string {
	for i, r := range s {
		if r == '=' {
			key := trimSpace(s[:i])
			value := trimSpace(s[i+1:])
			return []string{key, value}
		}
	}
	return nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.http_port", 15004)
	viper.SetDefault("server.mode", "debug")

	// 数据库默认配置 - Docker环境优先
	viper.SetDefault("database.host", "postgres")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.dbname", "pxpat")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime_minutes", "60m")
	viper.SetDefault("database.auto_migrate", true)
	viper.SetDefault("database.time_zone", "Asia/Shanghai")

	// Redis默认配置 - Docker环境优先
	viper.SetDefault("redis.host", "redis")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 1)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 5)
	viper.SetDefault("redis.dial_timeout_seconds", "5s")
	viper.SetDefault("redis.read_timeout_seconds", "3s")
	viper.SetDefault("redis.write_timeout_seconds", "3s")
	viper.SetDefault("redis.pool_timeout_seconds", "4s")

	// 区块链默认配置
	viper.SetDefault("blockchain.network", "bsc_testnet")
	viper.SetDefault("blockchain.rpc_url", "https://bsc-testnet.public.blastapi.io")
	viper.SetDefault("blockchain.backup_rpc_urls", []string{
		"https://data-seed-prebsc-1-s1.binance.org:8545",
		"https://data-seed-prebsc-2-s1.binance.org:8545",
		"https://bsc-testnet-rpc.publicnode.com",
		"https://bsc-testnet.blockpi.network/v1/rpc/public",
	})
	viper.SetDefault("blockchain.chain_id", 97)
	viper.SetDefault("blockchain.start_block", 54313108)
	viper.SetDefault("blockchain.confirm_blocks", 12)
	viper.SetDefault("blockchain.batch_size", 1000)
	viper.SetDefault("blockchain.bscscan_api_key", "")
	viper.SetDefault("blockchain.use_bscscan_for_events", false)

	// 合约地址默认配置 - 更新为最新部署地址 (2025-07-04)
	// 核心代币合约
	viper.SetDefault("blockchain.contracts.pxt_token", "0x30e075D324E36CfC0c99F7Dd552477A117F0f8A3") // PXT 治理代币 (新)
	viper.SetDefault("blockchain.contracts.pat_token", "0x5303b9952ee00B0c367c9C002734c9B6ae1f3912") // PAT 功能代币 (新)

	// 质押系统合约
	viper.SetDefault("blockchain.contracts.staking_pool", "0xeb30D55F9af84687618251f9ee4E0c745C244005")       // 质押池合约 (新)
	viper.SetDefault("blockchain.contracts.staking_factory", "0x1e2577517c94B761a0F46b14FcBC78A4e0d75116")    // 质押工厂合约 (新)
	viper.SetDefault("blockchain.contracts.reward_distributor", "0x0813556145e8a89C2a82Fecb25cFf27C1EB5b51B") // 奖励分配器 (新)

	// 治理系统合约
	viper.SetDefault("blockchain.contracts.dao", "0x56c770dEff4bC77Ba90eA481D63C53E65902599A")              // DAO 主合约 (新)
	viper.SetDefault("blockchain.contracts.voting", "0xFA7AE3FDe61117F1182B4DE6CC230f9A28d3b6De")           // 投票合约 (新)
	viper.SetDefault("blockchain.contracts.proposal_manager", "0xBc732b226aBEE7D399b7d7619BC6Be13a871DBcc") // 提案管理器 (新)
	viper.SetDefault("blockchain.contracts.treasury", "0x4653F85970Bb2B2F6456483DD09e8da48b465329")         // 国库合约 (新)
	viper.SetDefault("blockchain.contracts.role_manager", "0xFAE2bd9B4F9b250B3cA74B903071fEfAAE8Fbacb")     // 角色管理器 (新)

	// 其他系统合约
	viper.SetDefault("blockchain.contracts.token_registry", "0xb3834426D53FdaE4127d7bE074ae5De73E86D58E") // 代币注册表 (新)
	viper.SetDefault("blockchain.contracts.pxt_factory", "0xe55EE7b2Fe394801dADDDDCb33Be379f30f450B2")    // PXT 工厂合约 (新)
	viper.SetDefault("blockchain.contracts.pat_factory", "0x2c3cdc3d6c4007a33967F3Fe275B4fB47890F014")    // PAT 工厂合约 (新)

	// 池子地址配置 - 重要：用于监听转账和奖励分发
	viper.SetDefault("blockchain.contracts.china_mainland_pool", "0x1Aa6dA1b4cD18A0fC0B2A513f96209a39A261E6f") // 中国大陆池子
	viper.SetDefault("blockchain.contracts.global_pool", "0xf8CfA57C19C010A40064fcc801419D52Ce658A43")         // 全球池子

	// 内容注册表 (已部署)
	viper.SetDefault("blockchain.contracts.content_registry", "0xE7592356f79813999AA36B8DB7fDAf10818462F7")

	// 转账配置默认值
	viper.SetDefault("transfer.system_private_key", "")
	viper.SetDefault("transfer.system_address", "")
	viper.SetDefault("transfer.gas_limit", 100000)
	viper.SetDefault("transfer.gas_price", 20000000000) // 20 gwei
	viper.SetDefault("blockchain.contracts.treasury", "0x61612Dd77b447244FF059A1618771c46b1976Aec")

	// 同步配置默认值
	viper.SetDefault("sync.enabled", true)
	viper.SetDefault("sync.sync_interval_seconds", 30)
	viper.SetDefault("sync.max_retries", 3)
	viper.SetDefault("sync.retry_delay_seconds", 5)
	viper.SetDefault("sync.concurrent_workers", 2)
	viper.SetDefault("sync.enable_daily_full_sync", true)
	viper.SetDefault("sync.daily_full_sync_hour", 3)
	viper.SetDefault("sync.daily_full_sync_minute", 0)
	viper.SetDefault("sync.transfer_sync_interval_seconds", 300)
	viper.SetDefault("sync.user_sync_interval_seconds", 600)
	viper.SetDefault("sync.transfer_batch_size", 1000)
	viper.SetDefault("sync.content_batch_size", 100)
	viper.SetDefault("sync.burn_batch_size", 500)
	viper.SetDefault("sync.max_sync_duration_seconds", 1800)
	viper.SetDefault("sync.enable_metrics", true)
	viper.SetDefault("sync.metrics_retention_days", 30)
	viper.SetDefault("sync.log_retention_days", 7)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")

	// 安全配置默认值
	viper.SetDefault("security.cors.enabled", true)
	viper.SetDefault("security.cors.allowed_origins", "*")
	viper.SetDefault("security.cors.allowed_methods", "GET, POST, PUT, DELETE, OPTIONS")
	viper.SetDefault("security.cors.allowed_headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
	viper.SetDefault("security.cors.allowed_credentials", "true")
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv() {
	// 服务器配置
	if port := os.Getenv("TOKEN_SERVICE_HTTP_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			viper.Set("server.http_port", p)
		}
	}
	if mode := os.Getenv("TOKEN_SERVICE_MODE"); mode != "" {
		viper.Set("server.mode", mode)
	}

	// 数据库配置
	if host := os.Getenv("TOKEN_SERVICE_DB_HOST"); host != "" {
		viper.Set("database.host", host)
	}
	if port := os.Getenv("TOKEN_SERVICE_DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			viper.Set("database.port", p)
		}
	}
	if user := os.Getenv("TOKEN_SERVICE_DB_USER"); user != "" {
		viper.Set("database.user", user)
	}
	if password := os.Getenv("TOKEN_SERVICE_DB_PASSWORD"); password != "" {
		viper.Set("database.password", password)
	}
	if dbname := os.Getenv("TOKEN_SERVICE_DB_NAME"); dbname != "" {
		viper.Set("database.dbname", dbname)
	}

	// Redis配置
	if host := os.Getenv("TOKEN_SERVICE_REDIS_HOST"); host != "" {
		viper.Set("redis.host", host)
	}
	if port := os.Getenv("TOKEN_SERVICE_REDIS_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			viper.Set("redis.port", p)
		}
	}
	if password := os.Getenv("TOKEN_SERVICE_REDIS_PASSWORD"); password != "" {
		viper.Set("redis.password", password)
	}

	// 区块链配置
	if rpcURL := os.Getenv("TOKEN_SERVICE_RPC_URL"); rpcURL != "" {
		viper.Set("blockchain.rpc_url", rpcURL)
	}
	if chainID := os.Getenv("TOKEN_SERVICE_CHAIN_ID"); chainID != "" {
		if id, err := strconv.ParseInt(chainID, 10, 64); err == nil {
			viper.Set("blockchain.chain_id", id)
		}
	}
	if startBlock := os.Getenv("TOKEN_SERVICE_START_BLOCK"); startBlock != "" {
		if block, err := strconv.ParseUint(startBlock, 10, 64); err == nil {
			viper.Set("blockchain.start_block", block)
		}
	}

	// 合约地址环境变量覆盖
	if pxtToken := os.Getenv("TOKEN_SERVICE_PXT_TOKEN"); pxtToken != "" {
		viper.Set("blockchain.contracts.pxt_token", pxtToken)
	}
	if patToken := os.Getenv("TOKEN_SERVICE_PAT_TOKEN"); patToken != "" {
		viper.Set("blockchain.contracts.pat_token", patToken)
	}
	if stakingPool := os.Getenv("TOKEN_SERVICE_STAKING_POOL"); stakingPool != "" {
		viper.Set("blockchain.contracts.staking_pool", stakingPool)
	}
	if contentRegistry := os.Getenv("TOKEN_SERVICE_CONTENT_REGISTRY"); contentRegistry != "" {
		viper.Set("blockchain.contracts.content_registry", contentRegistry)
	}

	// CORS配置环境变量覆盖
	if corsOrigins := os.Getenv("TOKEN_SERVICE_CORS_ORIGINS"); corsOrigins != "" {
		viper.Set("security.cors.allowed_origins", corsOrigins)
	}
	if corsMethods := os.Getenv("TOKEN_SERVICE_CORS_METHODS"); corsMethods != "" {
		viper.Set("security.cors.allowed_methods", corsMethods)
	}
	if corsHeaders := os.Getenv("TOKEN_SERVICE_CORS_HEADERS"); corsHeaders != "" {
		viper.Set("security.cors.allowed_headers", corsHeaders)
	}
	if corsCredentials := os.Getenv("TOKEN_SERVICE_CORS_CREDENTIALS"); corsCredentials != "" {
		viper.Set("security.cors.allowed_credentials", corsCredentials)
	}

	// 转账配置环境变量覆盖
	if systemPrivateKey := os.Getenv("TOKEN_SERVICE_SYSTEM_PRIVATE_KEY"); systemPrivateKey != "" {
		viper.Set("transfer.system_private_key", systemPrivateKey)
	}
	if systemAddress := os.Getenv("TOKEN_SERVICE_SYSTEM_ADDRESS"); systemAddress != "" {
		viper.Set("transfer.system_address", systemAddress)
	}
	if gasLimit := os.Getenv("TOKEN_SERVICE_GAS_LIMIT"); gasLimit != "" {
		if limit, err := strconv.ParseUint(gasLimit, 10, 64); err == nil {
			viper.Set("transfer.gas_limit", limit)
		}
	}
	if gasPrice := os.Getenv("TOKEN_SERVICE_GAS_PRICE"); gasPrice != "" {
		if price, err := strconv.ParseInt(gasPrice, 10, 64); err == nil {
			viper.Set("transfer.gas_price", price)
		}
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.Server.HTTPPort <= 0 || config.Server.HTTPPort > 65535 {
		return fmt.Errorf("invalid HTTP port: %d", config.Server.HTTPPort)
	}

	if config.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if config.Blockchain.RPCURL == "" {
		return fmt.Errorf("blockchain RPC URL is required")
	}

	if config.Blockchain.Contracts.StakingPool == "" {
		return fmt.Errorf("staking pool address is required")
	}

	if config.Blockchain.Contracts.PXTToken == "" {
		return fmt.Errorf("PXT token address is required")
	}

	if config.Blockchain.Contracts.PATToken == "" {
		return fmt.Errorf("PAT token address is required")
	}

	return nil
}

// GetTransferSyncInterval 获取转账同步间隔
func (c *SyncConfig) GetTransferSyncInterval() time.Duration {
	if c.TransferSyncIntervalSeconds <= 0 {
		return 5 * time.Minute // 默认5分钟
	}
	return time.Duration(c.TransferSyncIntervalSeconds) * time.Second
}

// GetUserSyncInterval 获取用户同步间隔
func (c *SyncConfig) GetUserSyncInterval() time.Duration {
	if c.UserSyncIntervalSeconds <= 0 {
		return 10 * time.Minute // 默认10分钟
	}
	return time.Duration(c.UserSyncIntervalSeconds) * time.Second
}

// GetDailyFullSyncTime 获取每日完整同步时间
func (c *SyncConfig) GetDailyFullSyncTime() (hour, minute int) {
	hour = c.DailyFullSyncHour
	minute = c.DailyFullSyncMinute

	// 默认值：凌晨3点
	if hour < 0 || hour > 23 {
		hour = 3
	}
	if minute < 0 || minute > 59 {
		minute = 0
	}

	return hour, minute
}

// IsValidSyncTime 检查同步时间配置是否有效
func (c *SyncConfig) IsValidSyncTime() bool {
	return c.DailyFullSyncHour >= 0 && c.DailyFullSyncHour <= 23 &&
		c.DailyFullSyncMinute >= 0 && c.DailyFullSyncMinute <= 59
}

// ShouldEnableDailySync 是否应该启用每日同步
func (c *SyncConfig) ShouldEnableDailySync() bool {
	return c.EnableDailyFullSync
}

// GetMaxSyncDuration 获取最大同步时长
func (c *SyncConfig) GetMaxSyncDuration() time.Duration {
	if c.MaxSyncDurationSeconds <= 0 {
		return 30 * time.Minute // 默认30分钟
	}
	return time.Duration(c.MaxSyncDurationSeconds) * time.Second
}

// GetRetryInterval 获取重试间隔
func (c *SyncConfig) GetRetryInterval() time.Duration {
	if c.RetryDelay <= 0 {
		return 30 * time.Second // 默认30秒
	}
	return time.Duration(c.RetryDelay) * time.Second
}
