# Token Service

Token服务是金融集群的核心组件，负责监听BSC测试网上的质押合约事件，提供质押排行榜、用户统计等功能。

## 🎯 功能特性

- **事件监听**: 实时监听BSC测试网质押合约事件
- **数据同步**: 将链上数据同步到数据库
- **排行榜**: 提供多维度质押排行榜
- **用户统计**: 个人质押数据统计和分析
- **缓存优化**: Redis缓存提升查询性能
- **实时更新**: 支持实时数据推送

## 📋 合约信息

- **网络**: BSC测试网
- **质押池地址**: `0x6fD63476E4F2cb0b4841500e13eE851679A869f1`
- **RPC地址**: `https://data-seed-prebsc-1-s1.binance.org:8545/`
- **起始区块**: `54313108`

## 🏗️ 项目结构

```
token-service/
├── cmd/                    # 应用入口
│   └── main.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── handler/           # HTTP处理器
│   ├── service/           # 业务逻辑
│   ├── repository/        # 数据访问
│   ├── blockchain/        # 区块链交互
│   └── listener/          # 事件监听
├── models/                # 数据模型
├── migrations/            # 数据库迁移
├── configs/               # 配置文件
├── Dockerfile            # Docker配置
├── Makefile              # 构建脚本
└── README.md             # 项目文档
```

## 🚀 快速开始

### 环境要求

- Go 1.23+
- PostgreSQL 13+
- Redis 6+
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
cd backend/internal/token-service
```

2. **安装依赖**
```bash
make deps
```

3. **配置数据库**
```bash
# 创建数据库
createdb pxpat_finance

# 运行GORM自动迁移
make db-migrate

# 或者手动运行迁移
go run ./cmd/migrate/main.go -action=up
```

4. **配置文件**
```bash
# 复制配置文件
cp configs/token-service.yaml.example configs/token-service.yaml

# 编辑配置
vim configs/token-service.yaml
```

5. **启动服务**
```bash
make dev
```

### Docker部署

```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run
```

## 📊 API接口

### 排行榜接口

```http
GET /api/v1/staking/leaderboard?sort=amount&page=1&limit=50
```

**参数说明:**
- `sort`: 排序方式 (amount, mining_power, level)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认50, 最大200)

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "user_address": "0x...",
        "amount": "100000000000000000000000",
        "staking_level": 6,
        "mining_power": "150000000000000000000000",
        "total_rewards_claimed": "5000000000000000000000",
        "start_time": "2025-01-10T10:00:00Z",
        "rank": 1
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1000,
      "total_pages": 20
    }
  }
}
```

### 用户统计接口

```http
GET /api/v1/staking/user/{address}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user_address": "0x...",
    "amount": "100000000000000000000000",
    "staking_level": 6,
    "mining_power": "150000000000000000000000",
    "total_rewards_claimed": "5000000000000000000000",
    "pending_rewards": "100000000000000000000",
    "start_time": "2025-01-10T10:00:00Z",
    "last_update_time": "2025-01-10T15:30:00Z",
    "rank_by_amount": 1,
    "rank_by_mining_power": 1,
    "staking_days": 30,
    "is_active": true
  }
}
```

### 统计概览接口

```http
GET /api/v1/staking/overview
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_staked": "10000000000000000000000000",
    "total_users": 1000,
    "active_users": 950,
    "total_rewards_claimed": "500000000000000000000000",
    "total_mining_power": "15000000000000000000000000",
    "average_stake_amount": "10526315789473684210526",
    "last_update_time": "2025-01-10T15:30:00Z"
  }
}
```

## 🔧 配置说明

### 主要配置项

```yaml
# 服务器配置
server:
  http_port: 14001        # HTTP端口
  grpc_port: 24001        # gRPC端口
  mode: debug             # 运行模式

# 数据库配置
database:
  host: localhost
  port: 5432
  user: postgres
  password: ""
  dbname: pxpat_finance

# 区块链配置
blockchain:
  rpc_url: "https://data-seed-prebsc-1-s1.binance.org:8545/"
  staking_pool_address: "0x6fD63476E4F2cb0b4841500e13eE851679A869f1"
  start_block: 54313108
```

### 环境变量

```bash
# 服务器配置
TOKEN_SERVICE_HTTP_PORT=14001
TOKEN_SERVICE_MODE=production

# 数据库配置
TOKEN_SERVICE_DB_HOST=localhost
TOKEN_SERVICE_DB_PASSWORD=your_password

# 区块链配置
TOKEN_SERVICE_RPC_URL=https://your-rpc-url
```

## 🧪 测试

```bash
# 运行所有测试
make test

# 运行测试覆盖率
make test-coverage

# 运行基准测试
make benchmark
```

## 📈 监控

### 健康检查

```http
GET /health
```

### 指标监控

```http
GET /metrics
```

## 🔍 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行状态
   - 检查网络连接

2. **区块链连接失败**
   - 检查RPC URL配置
   - 确认网络连接
   - 检查API限制

3. **事件监听异常**
   - 检查合约地址
   - 确认起始区块号
   - 查看错误日志

### 日志查看

```bash
# 查看应用日志
make logs

# 查看服务状态
make status
```

## 📚 开发指南

### 添加新的事件监听

1. 在 `models/staking.go` 中定义事件结构
2. 在 `listener/event_listener.go` 中添加监听逻辑
3. 在 `service/staking_service.go` 中添加处理逻辑
4. 更新数据库表结构

### 添加新的API接口

1. 在 `handler/http_handler.go` 中添加路由
2. 在 `service/` 中实现业务逻辑
3. 在 `repository/` 中添加数据访问方法
4. 编写单元测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
