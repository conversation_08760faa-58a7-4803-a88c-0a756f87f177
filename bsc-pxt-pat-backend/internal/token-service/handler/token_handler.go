package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// TokenHandler 代币相关HTTP处理器
type TokenHandler struct {
	tokenService *service.TokenService
	logger       zerolog.Logger
}

// NewTokenHandler 创建新的代币处理器
func NewTokenHandler(tokenService *service.TokenService, logger zerolog.Logger) *TokenHandler {
	return &TokenHandler{
		tokenService: tokenService,
		logger:       logger.With().Str("component", "token_handler").Logger(),
	}
}

// GetOverview 获取代币概览
func (h *TokenHandler) GetOverview(c *gin.Context) {
	// 获取PXT信息
	pxtInfo, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PXT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PXT info")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"TOKEN_OVERVIEW_ERROR",
			"获取PXT信息失败: "+err.Error(),
		))
		return
	}

	// 获取PAT信息
	patInfo, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PAT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PAT info")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"TOKEN_OVERVIEW_ERROR",
			"获取PAT信息失败: "+err.Error(),
		))
		return
	}

	// 构建概览数据
	overview := map[string]interface{}{
		"pxt": pxtInfo,
		"pat": patInfo,
		"summary": map[string]interface{}{
			"total_tokens": 2,
			"total_supply_pxt": pxtInfo.TotalSupply,
			"total_supply_pat": patInfo.TotalSupply,
		},
	}

	c.JSON(http.StatusOK, NewSuccessResponse(overview))
}

// GetPXTInfo 获取PXT代币信息
func (h *TokenHandler) GetPXTInfo(c *gin.Context) {
	info, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PXT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PXT info")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get PXT info",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: info,
	})
}

// GetPATInfo 获取PAT代币信息
func (h *TokenHandler) GetPATInfo(c *gin.Context) {
	info, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PAT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PAT info")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get PAT info",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: info,
	})
}

// GetTokenInfo 获取指定代币信息
func (h *TokenHandler) GetTokenInfo(c *gin.Context) {
	token := strings.ToUpper(c.Param("token"))

	info, err := h.tokenService.GetTokenInfo(c.Request.Context(), token)
	if err != nil {
		h.logger.Error().Err(err).Str("token", token).Msg("Failed to get token info")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get token info",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: info,
	})
}

// GetTokenSupply 获取所有代币供应量
func (h *TokenHandler) GetTokenSupply(c *gin.Context) {
	// 获取PXT信息
	pxtInfo, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PXT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PXT supply")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get token supply",
			Message: err.Error(),
		})
		return
	}

	// 获取PAT信息
	patInfo, err := h.tokenService.GetTokenInfo(c.Request.Context(), "PAT")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PAT supply")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get token supply",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]interface{}{
			"PXT": pxtInfo,
			"PAT": patInfo,
		},
	})
}

// GetUserBalances 获取用户代币余额
func (h *TokenHandler) GetUserBalances(c *gin.Context) {
	address := c.Param("address")

	balances, err := h.tokenService.GetUserBalances(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user balances")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get user balances",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: balances,
	})
}

// RefreshUserBalance 刷新用户余额
func (h *TokenHandler) RefreshUserBalance(c *gin.Context) {
	address := c.Param("address")

	balances, err := h.tokenService.RefreshUserBalance(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to refresh user balance")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to refresh user balance",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: balances,
	})
}

// GetTransferHistory 获取转账历史
func (h *TokenHandler) GetTransferHistory(c *gin.Context) {
	address := c.Param("address")
	token := c.Query("token")

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	history, err := h.tokenService.GetTransferHistory(c.Request.Context(), address, token, page, limit)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get transfer history")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get transfer history",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: history,
	})
}

// GetRecentTransfers 获取最近的转账记录
func (h *TokenHandler) GetRecentTransfers(c *gin.Context) {
	token := c.Query("token")

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	history, err := h.tokenService.GetRecentTransfers(c.Request.Context(), token, page, limit)
	if err != nil {
		h.logger.Error().Err(err).Str("token", token).Msg("Failed to get recent transfers")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get recent transfers",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: history,
	})
}

// GetTopHolders 获取所有代币的持币者排行榜
func (h *TokenHandler) GetTopHolders(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	// 获取PXT持币者
	pxtHolders, err := h.tokenService.GetTopHolders(c.Request.Context(), "PXT", limit)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PXT holders")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get token holders",
			Message: err.Error(),
		})
		return
	}

	// 获取PAT持币者
	patHolders, err := h.tokenService.GetTopHolders(c.Request.Context(), "PAT", limit)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get PAT holders")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get token holders",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]interface{}{
			"PXT": pxtHolders,
			"PAT": patHolders,
		},
	})
}

// GetTokenHolders 获取指定代币的持币者排行榜
func (h *TokenHandler) GetTokenHolders(c *gin.Context) {
	token := strings.ToUpper(c.Param("token"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	holders, err := h.tokenService.GetTopHolders(c.Request.Context(), token, limit)
	if err != nil {
		h.logger.Error().Err(err).Str("token", token).Msg("Failed to get token holders")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get token holders",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: holders,
	})
}
