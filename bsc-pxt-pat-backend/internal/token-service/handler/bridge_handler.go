package handler

import (
	"net/http"
	"pxpat-backend/internal/token-service/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// BridgeHandler 跨链桥处理器
type BridgeHandler struct {
	bridgeService *service.BridgeService
	logger        zerolog.Logger
}

// NewBridgeHandler 创建跨链桥处理器
func NewBridgeHandler(bridgeService *service.BridgeService, logger zerolog.Logger) *BridgeHandler {
	return &BridgeHandler{
		bridgeService: bridgeService,
		logger:        logger,
	}
}

// GetBridgeOverview 获取跨链概览
// @Summary 获取跨链概览
// @Description 获取跨链桥的总体统计信息
// @Tags Bridge
// @Accept json
// @Produce json
// @Success 200 {object} models.BridgeOverviewDTO
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/bridge/overview [get]
func (h *BridgeHandler) GetBridgeOverview(c *gin.Context) {
	overview, err := h.bridgeService.GetBridgeOverview()
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get bridge overview")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"BRIDGE_OVERVIEW_ERROR",
			"获取跨链概览失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(overview))
}

// GetBridgeRequests 获取跨链请求列表
// @Summary 获取跨链请求列表
// @Description 分页获取跨链请求列表，支持按状态筛选
// @Tags Bridge
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "状态筛选" Enums(pending,confirmed,failed)
// @Success 200 {object} PaginatedResponse{data=[]models.BridgeRequestDTO}
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/bridge/requests [get]
func (h *BridgeHandler) GetBridgeRequests(c *gin.Context) {
	// 解析分页参数
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	status := c.Query("status")

	// 获取数据
	requests, total, err := h.bridgeService.GetBridgeRequests(page, pageSize, status)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get bridge requests")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"BRIDGE_REQUESTS_ERROR",
			"获取跨链请求列表失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewPaginatedResponse(requests, page, pageSize, total))
}

// GetBridgeRequestByID 获取跨链请求详情
// @Summary 获取跨链请求详情
// @Description 根据请求ID获取跨链请求的详细信息
// @Tags Bridge
// @Accept json
// @Produce json
// @Param request_id path string true "跨链请求ID"
// @Success 200 {object} models.BridgeRequestDTO
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/bridge/requests/{request_id} [get]
func (h *BridgeHandler) GetBridgeRequestByID(c *gin.Context) {
	requestID := c.Param("request_id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"INVALID_REQUEST_ID",
			"请求ID不能为空",
		))
		return
	}

	request, err := h.bridgeService.GetBridgeRequestByID(requestID)
	if err != nil {
		h.logger.Error().Err(err).Str("request_id", requestID).Msg("Failed to get bridge request")
		c.JSON(http.StatusNotFound, NewErrorResponse(
			http.StatusNotFound,
			"BRIDGE_REQUEST_NOT_FOUND",
			"跨链请求不存在: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(request))
}

// GetBridgeStats 获取跨链统计
// @Summary 获取跨链统计
// @Description 获取指定天数内的跨链统计数据
// @Tags Bridge
// @Accept json
// @Produce json
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} []models.BridgeStatsDTO
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/bridge/stats [get]
func (h *BridgeHandler) GetBridgeStats(c *gin.Context) {
	days, err := strconv.Atoi(c.DefaultQuery("days", "30"))
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	stats, err := h.bridgeService.GetBridgeStats(days)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get bridge stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"BRIDGE_STATS_ERROR",
			"获取跨链统计失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetBridgeValidators 获取验证者列表
// @Summary 获取验证者列表
// @Description 获取跨链桥验证者列表及其统计信息
// @Tags Bridge
// @Accept json
// @Produce json
// @Success 200 {object} []models.BridgeValidatorDTO
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/bridge/validators [get]
func (h *BridgeHandler) GetBridgeValidators(c *gin.Context) {
	validators, err := h.bridgeService.GetBridgeValidators()
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get bridge validators")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"BRIDGE_VALIDATORS_ERROR",
			"获取验证者列表失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(validators))
}

// GetBridgeHealth 获取跨链桥健康状态
// @Summary 获取跨链桥健康状态
// @Description 检查跨链桥系统的健康状态
// @Tags Bridge
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/bridge/health [get]
func (h *BridgeHandler) GetBridgeHealth(c *gin.Context) {
	// 获取基本统计信息
	overview, err := h.bridgeService.GetBridgeOverview()
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"BRIDGE_HEALTH_ERROR",
			"获取跨链桥健康状态失败: "+err.Error(),
		))
		return
	}

	// 计算健康指标
	healthStatus := "healthy"
	if overview.PendingRequests > 100 {
		healthStatus = "warning"
	}
	if overview.ActiveValidators < 2 {
		healthStatus = "critical"
	}

	health := map[string]interface{}{
		"status":            healthStatus,
		"active_validators": overview.ActiveValidators,
		"pending_requests":  overview.PendingRequests,
		"success_rate":      overview.SuccessRate,
		"total_volume":      overview.TotalVolume,
		"timestamp":         "now",
	}

	c.JSON(http.StatusOK, NewSuccessResponse(health))
}
