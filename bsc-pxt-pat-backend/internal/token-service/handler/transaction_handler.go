package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// TransactionHandler 交易相关HTTP处理器
type TransactionHandler struct {
	transactionService *service.TransactionService
	logger             zerolog.Logger
}

// NewTransactionHandler 创建新的交易处理器
func NewTransactionHandler(transactionService *service.TransactionService, logger zerolog.Logger) *TransactionHandler {
	return &TransactionHandler{
		transactionService: transactionService,
		logger:             logger.With().Str("component", "transaction_handler").Logger(),
	}
}

// PrepareTransaction 准备交易
func (h *TransactionHandler) PrepareTransaction(c *gin.Context) {
	var req service.TransactionPrepareRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	response, err := h.transactionService.PrepareTransaction(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to prepare transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to prepare transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}

// BroadcastTransaction 广播交易
func (h *TransactionHandler) BroadcastTransaction(c *gin.Context) {
	var req service.TransactionBroadcastRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	response, err := h.transactionService.BroadcastTransaction(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to broadcast transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to broadcast transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}

// GetTransactionStatus 获取交易状态
func (h *TransactionHandler) GetTransactionStatus(c *gin.Context) {
	txHash := c.Param("hash")

	status, err := h.transactionService.GetTransactionStatus(c.Request.Context(), txHash)
	if err != nil {
		h.logger.Error().Err(err).Str("tx_hash", txHash).Msg("Failed to get transaction status")

		if err.Error() == "transaction not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Transaction not found",
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Failed to get transaction status",
				Message: err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: status,
	})
}

// GetNetworkInfo 获取网络信息
func (h *TransactionHandler) GetNetworkInfo(c *gin.Context) {
	info, err := h.transactionService.GetNetworkInfo(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get network info")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get network info",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: info,
	})
}

// GetGasPrice 获取当前Gas价格
func (h *TransactionHandler) GetGasPrice(c *gin.Context) {
	info, err := h.transactionService.GetNetworkInfo(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get gas price")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get gas price",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]string{
			"gas_price": info.GasPrice,
		},
	})
}

// GetUserNonce 获取用户nonce
func (h *TransactionHandler) GetUserNonce(c *gin.Context) {
	address := c.Param("address")

	nonce, err := h.transactionService.GetUserNonce(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user nonce")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get user nonce",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]uint64{
			"nonce": nonce,
		},
	})
}

// PrepareStakeTransaction 准备质押交易
func (h *TransactionHandler) PrepareStakeTransaction(c *gin.Context) {
	var reqData map[string]interface{}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	userAddress, ok := reqData["user_address"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing user_address",
			Message: "user_address is required",
		})
		return
	}

	req := &service.TransactionPrepareRequest{
		UserAddress: userAddress,
		Type:        "stake",
		Data:        reqData,
	}

	response, err := h.transactionService.PrepareTransaction(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to prepare stake transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to prepare stake transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}

// PrepareUnstakeTransaction 准备解质押交易
func (h *TransactionHandler) PrepareUnstakeTransaction(c *gin.Context) {
	var reqData map[string]interface{}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	userAddress, ok := reqData["user_address"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing user_address",
			Message: "user_address is required",
		})
		return
	}

	req := &service.TransactionPrepareRequest{
		UserAddress: userAddress,
		Type:        "unstake",
		Data:        reqData,
	}

	response, err := h.transactionService.PrepareTransaction(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to prepare unstake transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to prepare unstake transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}

// EstimateStakingRewards 估算质押收益
func (h *TransactionHandler) EstimateStakingRewards(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	// TODO: 实现质押收益估算逻辑
	// 这里需要根据具体的质押规则来计算收益

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]interface{}{
			"estimated_apy":      "12.5",
			"estimated_rewards":  "125.0",
			"level_bonus":        "1.6x",
			"random_bonus_range": "1.5x - 1.6x",
		},
	})
}

// PrepareVoteTransaction 准备投票交易
func (h *TransactionHandler) PrepareVoteTransaction(c *gin.Context) {
	var reqData map[string]interface{}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	userAddress, ok := reqData["user_address"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing user_address",
			Message: "user_address is required",
		})
		return
	}

	req := &service.TransactionPrepareRequest{
		UserAddress: userAddress,
		Type:        "vote",
		Data:        reqData,
	}

	response, err := h.transactionService.PrepareTransaction(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to prepare vote transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to prepare vote transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}

// PrepareDelegateTransaction 准备委托交易
func (h *TransactionHandler) PrepareDelegateTransaction(c *gin.Context) {
	var reqData map[string]interface{}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	userAddress, ok := reqData["user_address"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing user_address",
			Message: "user_address is required",
		})
		return
	}

	req := &service.TransactionPrepareRequest{
		UserAddress: userAddress,
		Type:        "delegate",
		Data:        reqData,
	}

	response, err := h.transactionService.PrepareTransaction(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to prepare delegate transaction")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to prepare delegate transaction",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: response,
	})
}
