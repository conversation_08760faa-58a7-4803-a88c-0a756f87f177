package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// ContentHandler 内容处理器
type ContentHandler struct {
	contentService *service.ContentService
	logger         zerolog.Logger
}

// NewContentHandler 创建内容处理器
func NewContentHandler(contentService *service.ContentService, logger zerolog.Logger) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
		logger:         logger.With().Str("component", "content_handler").Logger(),
	}
}

// PublishContent 发布内容到区块链
func (h *ContentHandler) PublishContent(c *gin.Context) {
	var req service.ContentPublishRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind request")
		c.<PERSON>(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Title == "" || req.ContentType == "" || req.PublisherKSUID == "" || req.OnChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing required fields", "id, title, content_type, publisher_ksuid, on_chain_id are required"))
		return
	}

	// 验证审核员数量
	if len(req.Reviewers) == 0 {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid reviewers", "At least one reviewer is required"))
		return
	}

	// 发布内容
	record, err := h.contentService.PublishContent(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Str("content_id", req.ID).Msg("Failed to publish content")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to publish content", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_id", req.ID).
		Str("on_chain_id", req.OnChainID).
		Uint("record_id", record.ID).
		Msg("Content publish initiated")

	c.JSON(http.StatusOK, NewSuccessResponse(record))
}

// GetContentRecord 获取内容记录
func (h *ContentHandler) GetContentRecord(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "on_chain_id is required"))
		return
	}

	record, err := h.contentService.GetContentRecord(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content record")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(record))
}

// GetContentDetailByOnChainID 根据上链ID获取完整内容详情
func (h *ContentHandler) GetContentDetailByOnChainID(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "on_chain_id is required"))
		return
	}

	detail, err := h.contentService.GetContentDetailByOnChainID(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content detail")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content detail not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(detail))
}

// GetContentRecords 分页获取内容记录
func (h *ContentHandler) GetContentRecords(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	contentType := c.Query("content_type")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	records, total, err := h.contentService.GetContentRecords(c.Request.Context(), page, limit, contentType, status)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content records")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content records", err.Error()))
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)

	response := map[string]interface{}{
		"records": records,
		"pagination": map[string]interface{}{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	}

	c.JSON(http.StatusOK, NewSuccessResponse(response))
}

// GetContentStats 获取内容统计
func (h *ContentHandler) GetContentStats(c *gin.Context) {
	stats, err := h.contentService.GetContentStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetContentTypeStats 获取内容类型统计
func (h *ContentHandler) GetContentTypeStats(c *gin.Context) {
	stats, err := h.contentService.GetContentTypeStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content type stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content type stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetContentFees 获取内容类型费用
func (h *ContentHandler) GetContentFees(c *gin.Context) {
	fees := h.contentService.GetSupportedContentTypes()
	c.JSON(http.StatusOK, NewSuccessResponse(fees))
}

// GetContentFee 获取特定内容类型费用
func (h *ContentHandler) GetContentFee(c *gin.Context) {
	contentType := c.Param("content_type")
	if contentType == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "content_type is required"))
		return
	}

	fee, err := h.contentService.GetContentFee(contentType)
	if err != nil {
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content type not supported", err.Error()))
		return
	}

	response := map[string]interface{}{
		"content_type": contentType,
		"fee":          fee,
		"unit":         "PAT",
	}

	c.JSON(http.StatusOK, NewSuccessResponse(response))
}

// UpdateContentStatus 更新内容状态（内部接口）
func (h *ContentHandler) UpdateContentStatus(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing parameter", "on_chain_id is required"))
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind update status request")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证状态值
	if req.Status != "pending" && req.Status != "confirmed" && req.Status != "failed" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid status", "Status must be pending, confirmed, or failed"))
		return
	}

	// 获取内容记录
	record, err := h.contentService.GetContentRecord(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content record")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content not found", err.Error()))
		return
	}

	// 更新状态
	err = h.contentService.UpdateContentStatus(c.Request.Context(), record.ID, req.Status)
	if err != nil {
		h.logger.Error().Err(err).Uint("record_id", record.ID).Str("status", req.Status).Msg("Failed to update content status")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to update status", err.Error()))
		return
	}

	h.logger.Info().
		Str("on_chain_id", onChainID).
		Uint("record_id", record.ID).
		Str("old_status", record.Status).
		Str("new_status", req.Status).
		Msg("Content status updated successfully")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message":     "Content status updated successfully",
		"on_chain_id": onChainID,
		"old_status":  record.Status,
		"new_status":  req.Status,
	}))
}

// RegisterContentRoutes 注册内容相关路由
func RegisterContentRoutes(r *gin.RouterGroup, handler *ContentHandler) {
	content := r.Group("/content")
	{
		content.POST("/publish", handler.PublishContent)
		content.GET("/records", handler.GetContentRecords)
		content.GET("/stats", handler.GetContentStats)
		content.GET("/stats/types", handler.GetContentTypeStats)
		content.GET("/fees", handler.GetContentFees)
		content.GET("/fees/:content_type", handler.GetContentFee)
		content.GET("/:on_chain_id/detail", handler.GetContentDetailByOnChainID)
		content.GET("/:on_chain_id", handler.GetContentRecord)

		// 内部管理接口
		content.PUT("/internal/status/:on_chain_id", handler.UpdateContentStatus)
	}
}

// ContentDetailResponse 内容详情响应结构
type ContentDetailResponse struct {
	// Token记录信息
	TokenRecord interface{} `json:"token_record"`
	// 内容详情信息
	ContentDetail interface{} `json:"content_detail"`
}

// ============ 新的IPFS支持的API处理器 ============

// UploadToIPFS 上传文件到IPFS
func (h *ContentHandler) UploadToIPFS(c *gin.Context) {
	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get uploaded file")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "No file uploaded", err.Error()))
		return
	}
	defer file.Close()

	// 读取文件内容
	fileContent := make([]byte, header.Size)
	_, err = file.Read(fileContent)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to read file content")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to read file", err.Error()))
		return
	}

	// 获取元数据（可选）
	metadataStr := c.PostForm("metadata")

	h.logger.Info().
		Str("filename", header.Filename).
		Int64("size", header.Size).
		Str("content_type", header.Header.Get("Content-Type")).
		Msg("Uploading file to IPFS")

	// 上传到IPFS
	result, err := h.contentService.UploadToIPFS(c.Request.Context(), fileContent, header.Filename, metadataStr)
	if err != nil {
		h.logger.Error().Err(err).Str("filename", header.Filename).Msg("Failed to upload file to IPFS")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to upload to IPFS", err.Error()))
		return
	}

	h.logger.Info().
		Str("filename", header.Filename).
		Str("ipfs_hash", result.Hash).
		Str("size", result.Size).
		Msg("File uploaded to IPFS successfully")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message": "File uploaded to IPFS successfully",
		"data": map[string]interface{}{
			"ipfs_hash": result.Hash,
			"filename":  header.Filename,
			"size":      result.Size,
			"timestamp": result.Timestamp,
			"ipfs_url":  fmt.Sprintf("https://gateway.pinata.cloud/ipfs/%s", result.Hash),
		},
	}))
}

// PublishContentWithIPFS 发布内容到IPFS和区块链 - 学习xLog的完整上链方案
func (h *ContentHandler) PublishContentWithIPFS(c *gin.Context) {
	var req service.ContentPublishWithIPFSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind IPFS content request")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Title == "" || req.ContentType == "" || req.PublisherKSUID == "" || req.OnChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing required fields", "id, title, content_type, publisher_ksuid, on_chain_id are required"))
		return
	}

	// 验证审核员数量
	if len(req.Reviewers) == 0 {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid reviewers", "At least one reviewer is required"))
		return
	}

	// 验证IPFS相关参数
	if len(req.ContentData) == 0 && req.IPFSHash == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing content data", "Either content_data or ipfs_hash is required"))
		return
	}

	h.logger.Info().
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("publisher_ksuid", req.PublisherKSUID).
		Bool("has_content_data", len(req.ContentData) > 0).
		Str("ipfs_hash", req.IPFSHash).
		Msg("Publishing content with IPFS support")

	// 发布内容到IPFS和区块链
	record, err := h.contentService.PublishContentWithIPFS(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to publish content with IPFS")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to publish content", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_id", record.ContentID).
		Str("ipfs_hash", record.IPFSHash).
		Str("metadata_ipfs", record.MetadataIPFS).
		Str("tx_hash", record.TransactionHash).
		Msg("Content published with IPFS successfully")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message": "Content published with IPFS successfully",
		"record":  record,
	}))
}

// GetContentIPFSInfo 获取内容的IPFS信息
func (h *ContentHandler) GetContentIPFSInfo(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing on_chain_id", "on_chain_id parameter is required"))
		return
	}

	// 获取内容记录
	record, err := h.contentService.GetContentRecord(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content record")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content not found", err.Error()))
		return
	}

	// 构造IPFS信息响应
	ipfsInfo := map[string]interface{}{
		"content_id":    record.ContentID,
		"on_chain_id":   record.OnChainID,
		"title":         record.Title,
		"content_type":  record.ContentType,
		"ipfs_hash":     record.IPFSHash,
		"metadata_ipfs": record.MetadataIPFS,
		"ipfs_url":      "", // 这里可以构造完整的IPFS URL
		"metadata_url":  "", // 这里可以构造完整的元数据URL
		"created_at":    record.CreatedAt,
		"updated_at":    record.UpdatedAt,
	}

	// 如果有IPFS服务，可以构造完整URL
	if record.IPFSHash != "" {
		ipfsInfo["ipfs_url"] = "https://gateway.pinata.cloud/ipfs/" + record.IPFSHash
	}
	if record.MetadataIPFS != "" {
		ipfsInfo["metadata_url"] = "https://gateway.pinata.cloud/ipfs/" + record.MetadataIPFS
	}

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message": "IPFS info retrieved successfully",
		"data":    ipfsInfo,
	}))
}

// GetActiveContentTypes 获取激活的内容类型
func (h *ContentHandler) GetActiveContentTypes(c *gin.Context) {
	types, err := h.contentService.GetActiveContentTypes(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get active content types")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content types", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message":       "Active content types retrieved successfully",
		"content_types": types,
	}))
}

// AddContentType 添加新的内容类型 (管理员功能)
func (h *ContentHandler) AddContentType(c *gin.Context) {
	var req struct {
		ContentType string  `json:"content_type" binding:"required"`
		DefaultFee  float64 `json:"default_fee" binding:"required"`
		Description string  `json:"description" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind add content type request")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	err := h.contentService.AddContentType(c.Request.Context(), req.ContentType, req.DefaultFee, req.Description)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to add content type")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to add content type", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_type", req.ContentType).
		Float64("default_fee", req.DefaultFee).
		Msg("Content type added successfully")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message":      "Content type added successfully",
		"content_type": req.ContentType,
	}))
}

// UpdateContentType 更新内容类型 (管理员功能)
func (h *ContentHandler) UpdateContentType(c *gin.Context) {
	contentType := c.Param("content_type")
	if contentType == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing content_type", "content_type parameter is required"))
		return
	}

	var req struct {
		DefaultFee  float64 `json:"default_fee" binding:"required"`
		Description string  `json:"description" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind update content type request")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	err := h.contentService.UpdateContentType(c.Request.Context(), contentType, req.DefaultFee, req.Description)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to update content type")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to update content type", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_type", contentType).
		Float64("new_fee", req.DefaultFee).
		Msg("Content type updated successfully")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message":      "Content type updated successfully",
		"content_type": contentType,
	}))
}

// GetContentTypeRecommendedAttributes 获取内容类型的推荐属性
func (h *ContentHandler) GetContentTypeRecommendedAttributes(c *gin.Context) {
	contentType := c.Param("content_type")
	if contentType == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing content_type", "content_type parameter is required"))
		return
	}

	attributes := h.contentService.GetContentTypeRecommendedAttributes(contentType)

	h.logger.Info().
		Str("content_type", contentType).
		Msg("Retrieved content type recommended attributes")

	c.JSON(http.StatusOK, NewSuccessResponse(map[string]interface{}{
		"message": "Content type recommended attributes retrieved successfully",
		"data":    attributes,
	}))
}

// RegisterContentIPFSRoutes 注册IPFS内容相关路由
func RegisterContentIPFSRoutes(r *gin.RouterGroup, handler *ContentHandler) {
	content := r.Group("/content")
	{
		// IPFS文件上传
		content.POST("/ipfs/upload", handler.UploadToIPFS)

		// 新的IPFS支持的API
		content.POST("/publish-ipfs", handler.PublishContentWithIPFS)
		content.GET("/:on_chain_id/ipfs", handler.GetContentIPFSInfo)

		// 动态内容类型管理API
		content.GET("/types/active", handler.GetActiveContentTypes)
		content.POST("/types", handler.AddContentType)                                              // 管理员功能
		content.PUT("/types/:content_type", handler.UpdateContentType)                              // 管理员功能
		content.GET("/types/:content_type/attributes", handler.GetContentTypeRecommendedAttributes) // 获取推荐属性
	}
}
