package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// RankingHandler 排行榜HTTP处理器
type RankingHandler struct {
	rankingService *service.RankingService
	logger         zerolog.Logger
}

// NewRankingHandler 创建新的排行榜处理器
func NewRankingHandler(
	rankingService *service.RankingService,
	logger zerolog.Logger,
) *RankingHandler {
	return &RankingHandler{
		rankingService: rankingService,
		logger:         logger.With().Str("component", "ranking_handler").Logger(),
	}
}

// GetStakingRanking 获取质押排行榜
func (h *RankingHandler) GetStakingRanking(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	ranking, err := h.rankingService.GetStakingRanking(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get staking ranking")
		c.J<PERSON>(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get staking ranking",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: ranking,
	})
}

// GetTokenHolderRanking 获取代币持有者排行榜
func (h *RankingHandler) GetTokenHolderRanking(c *gin.Context) {
	token := strings.ToUpper(c.Param("token"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	ranking, err := h.rankingService.GetTokenHolderRanking(c.Request.Context(), token, limit)
	if err != nil {
		h.logger.Error().Err(err).Str("token", token).Msg("Failed to get token holder ranking")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get token holder ranking",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: ranking,
	})
}

// GetVotingPowerRanking 获取投票权重排行榜
func (h *RankingHandler) GetVotingPowerRanking(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	ranking, err := h.rankingService.GetVotingPowerRanking(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get voting power ranking")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get voting power ranking",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: ranking,
	})
}

// GetUserRanking 获取用户排名
func (h *RankingHandler) GetUserRanking(c *gin.Context) {
	address := c.Param("address")

	ranking, err := h.rankingService.GetUserRanking(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user ranking")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get user ranking",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: ranking,
	})
}

// RefreshRankings 刷新排行榜
func (h *RankingHandler) RefreshRankings(c *gin.Context) {
	err := h.rankingService.RefreshRankings(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to refresh rankings")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to refresh rankings",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: map[string]string{
			"message": "Rankings refreshed successfully",
		},
	})
}
