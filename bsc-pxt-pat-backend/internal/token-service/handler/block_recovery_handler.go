package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// BlockRecoveryHandler 区块恢复处理器
type BlockRecoveryHandler struct {
	recoveryService *service.BlockRecoveryService
	logger          zerolog.Logger
}

// NewBlockRecoveryHandler 创建区块恢复处理器
func NewBlockRecoveryHandler(
	recoveryService *service.BlockRecoveryService,
	logger zerolog.Logger,
) *BlockRecoveryHandler {
	return &BlockRecoveryHandler{
		recoveryService: recoveryService,
		logger:          logger.With().Str("handler", "block_recovery").Logger(),
	}
}

// RegisterRoutes 注册路由
func (h *BlockRecoveryHandler) RegisterRoutes(router *gin.RouterGroup) {
	recovery := router.Group("/recovery")
	{
		recovery.GET("/failed-blocks", h.GetFailedBlocks)
		recovery.POST("/retry-block/:blockNumber", h.RetryBlock)
		recovery.POST("/retry-all", h.RetryAllFailedBlocks)
		recovery.GET("/block-status/:blockNumber", h.GetBlockStatus)
		recovery.POST("/mark-retry/:blockNumber", h.MarkBlockForRetry)
		recovery.DELETE("/clear-block/:blockNumber", h.ClearFailedBlock)
		recovery.GET("/statistics", h.GetSyncStatistics)
	}
}

// GetFailedBlocks 获取所有失败的区块
func (h *BlockRecoveryHandler) GetFailedBlocks(c *gin.Context) {
	failedBlocks, err := h.recoveryService.GetFailedBlocks(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("获取失败区块列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取失败区块列表失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"failed_blocks": failedBlocks,
		"count":         len(failedBlocks),
	})
}

// RetryBlock 重试单个区块
func (h *BlockRecoveryHandler) RetryBlock(c *gin.Context) {
	blockNumberStr := c.Param("blockNumber")
	blockNumber, err := strconv.ParseUint(blockNumberStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的区块号",
		})
		return
	}

	h.logger.Info().
		Uint64("block", blockNumber).
		Msg("收到手动重试区块请求")

	err = h.recoveryService.RetryFailedBlock(c.Request.Context(), blockNumber)
	if err != nil {
		h.logger.Error().
			Err(err).
			Uint64("block", blockNumber).
			Msg("重试区块失败")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":        "重试区块失败",
			"block_number": blockNumber,
			"details":      err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "区块重试成功",
		"block_number": blockNumber,
	})
}

// RetryAllFailedBlocks 重试所有失败的区块
func (h *BlockRecoveryHandler) RetryAllFailedBlocks(c *gin.Context) {
	h.logger.Info().Msg("收到重试所有失败区块请求")

	err := h.recoveryService.RetryAllFailedBlocks(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("批量重试失败区块失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "批量重试失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量重试完成",
	})
}

// GetBlockStatus 获取特定区块的同步状态
func (h *BlockRecoveryHandler) GetBlockStatus(c *gin.Context) {
	blockNumberStr := c.Param("blockNumber")
	blockNumber, err := strconv.ParseUint(blockNumberStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的区块号",
		})
		return
	}

	status, err := h.recoveryService.GetBlockSyncStatus(c.Request.Context(), blockNumber)
	if err != nil {
		h.logger.Error().
			Err(err).
			Uint64("block", blockNumber).
			Msg("查询区块状态失败")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查询区块状态失败",
			"details": err.Error(),
		})
		return
	}

	if status == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":        "区块状态未找到",
			"block_number": blockNumber,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"block_number": blockNumber,
		"status":       status,
	})
}

// MarkBlockForRetry 标记区块需要重试
func (h *BlockRecoveryHandler) MarkBlockForRetry(c *gin.Context) {
	blockNumberStr := c.Param("blockNumber")
	blockNumber, err := strconv.ParseUint(blockNumberStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的区块号",
		})
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
		})
		return
	}

	if req.Reason == "" {
		req.Reason = "手动标记重试"
	}

	err = h.recoveryService.MarkBlockForRetry(c.Request.Context(), blockNumber, req.Reason)
	if err != nil {
		h.logger.Error().
			Err(err).
			Uint64("block", blockNumber).
			Msg("标记区块重试失败")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "标记区块重试失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "区块已标记为需要重试",
		"block_number": blockNumber,
		"reason":       req.Reason,
	})
}

// ClearFailedBlock 清除失败区块记录
func (h *BlockRecoveryHandler) ClearFailedBlock(c *gin.Context) {
	blockNumberStr := c.Param("blockNumber")
	blockNumber, err := strconv.ParseUint(blockNumberStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的区块号",
		})
		return
	}

	err = h.recoveryService.ClearFailedBlock(c.Request.Context(), blockNumber)
	if err != nil {
		h.logger.Error().
			Err(err).
			Uint64("block", blockNumber).
			Msg("清除失败区块记录失败")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "清除失败区块记录失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "失败区块记录已清除",
		"block_number": blockNumber,
	})
}

// GetSyncStatistics 获取同步统计信息
func (h *BlockRecoveryHandler) GetSyncStatistics(c *gin.Context) {
	stats, err := h.recoveryService.GetSyncStatistics(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("获取同步统计失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取同步统计失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"statistics": stats,
	})
}
