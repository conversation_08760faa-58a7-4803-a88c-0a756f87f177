package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// AnalyticsHandler 统计分析HTTP处理器
type AnalyticsHandler struct {
	analyticsService *service.AnalyticsService
	websocketService *service.WebSocketService
	logger           zerolog.Logger
}

// NewAnalyticsHandler 创建新的统计分析处理器
func NewAnalyticsHandler(
	analyticsService *service.AnalyticsService,
	websocketService *service.WebSocketService,
	logger zerolog.Logger,
) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		websocketService: websocketService,
		logger:           logger.With().Str("component", "analytics_handler").Logger(),
	}
}

// GetDashboardStats 获取仪表板统计数据
func (h *AnalyticsHandler) GetDashboardStats(c *gin.Context) {
	stats, err := h.analyticsService.GetDashboardStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get dashboard stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"DASHBOARD_STATS_ERROR",
			"获取仪表板统计失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetTokenTrends 获取代币趋势数据
func (h *AnalyticsHandler) GetTokenTrends(c *gin.Context) {
	token := strings.ToUpper(c.Param("token"))
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))

	if token != "PXT" && token != "PAT" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"INVALID_TOKEN_TYPE",
			"代币类型必须是PXT或PAT",
		))
		return
	}

	trends, err := h.analyticsService.GetTokenTrends(c.Request.Context(), token, days)
	if err != nil {
		h.logger.Error().Err(err).Str("token", token).Msg("Failed to get token trends")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"TOKEN_TRENDS_ERROR",
			"获取代币趋势失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(trends))
}

// GetStakingTrends 获取质押趋势数据
func (h *AnalyticsHandler) GetStakingTrends(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))

	trends, err := h.analyticsService.GetStakingTrends(c.Request.Context(), days)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get staking trends")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"STAKING_TRENDS_ERROR",
			"获取质押趋势失败: "+err.Error(),
		))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(trends))
}

// GetOverview 获取系统概览
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	// 获取仪表板统计作为概览
	stats, err := h.analyticsService.GetDashboardStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get overview")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"OVERVIEW_ERROR",
			"获取系统概览失败: "+err.Error(),
		))
		return
	}

	// 简化的概览数据
	overview := map[string]interface{}{
		"tokens": map[string]interface{}{
			"pxt_supply":    stats.TokenStats.PXTTotalSupply,
			"pat_supply":    stats.TokenStats.PATTotalSupply,
			"pxt_holders":   stats.TokenStats.PXTHolders,
			"pat_holders":   stats.TokenStats.PATHolders,
			"transfers_24h": stats.TokenStats.TotalTransfers24h,
		},
		"staking": map[string]interface{}{
			"total_staked":  stats.StakingStats.TotalStaked,
			"total_stakers": stats.StakingStats.TotalStakers,
			"staking_ratio": stats.StakingStats.StakingRatio,
			"apy":           stats.StakingStats.APY,
		},
		"governance": map[string]interface{}{
			"total_proposals":    stats.GovernanceStats.TotalProposals,
			"active_proposals":   stats.GovernanceStats.ActiveProposals,
			"participation_rate": stats.GovernanceStats.ParticipationRate,
		},
		"network":      stats.NetworkStats,
		"last_updated": stats.LastUpdated,
	}

	c.JSON(http.StatusOK, NewSuccessResponse(overview))
}

// GetWebSocketStats 获取WebSocket统计信息
func (h *AnalyticsHandler) GetWebSocketStats(c *gin.Context) {
	if h.websocketService == nil {
		c.JSON(http.StatusServiceUnavailable, NewErrorResponse(
			http.StatusServiceUnavailable,
			"WEBSOCKET_SERVICE_UNAVAILABLE",
			"WebSocket服务不可用",
		))
		return
	}

	stats := h.websocketService.GetStats()
	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}
