package handler

import (
	"math/big"
	"net/http"
	"strconv"
	"time"

	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/service"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// HTTPHandler HTTP处理器
type HTTPHandler struct {
	stakingService     *service.StakingService
	leaderboardService *service.LeaderboardService
	tokenService       *service.TokenService
	governanceService  *service.GovernanceService
	transactionService *service.TransactionService
	analyticsHandler   *AnalyticsHandler
	websocketService   *service.WebSocketService
	syncService        *service.SyncService
	logger             zerolog.Logger
}

// NewHTTPHandler 创建新的HTTP处理器
func NewHTTPHandler(
	stakingService *service.StakingService,
	leaderboardService *service.LeaderboardService,
	tokenService *service.TokenService,
	governanceService *service.GovernanceService,
	transactionService *service.TransactionService,
	analyticsHandler *AnalyticsHandler,
	websocketService *service.WebSocketService,
	syncService *service.SyncService,
	logger zerolog.Logger,
) *HTTPHandler {
	return &HTTPHandler{
		stakingService:     stakingService,
		leaderboardService: leaderboardService,
		tokenService:       tokenService,
		governanceService:  governanceService,
		transactionService: transactionService,
		analyticsHandler:   analyticsHandler,
		websocketService:   websocketService,
		syncService:        syncService,
		logger: logger.With().
			Str("component", "http_handler").
			Logger(),
	}
}

// APIResponse 统一API响应格式
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResponse 成功响应
func (h *HTTPHandler) SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// ErrorResponse 错误响应
func (h *HTTPHandler) ErrorResponse(c *gin.Context, code int, message string) {
	c.JSON(code, APIResponse{
		Code:    code,
		Message: message,
	})
}

// HealthCheck 健康检查
func (h *HTTPHandler) HealthCheck(c *gin.Context) {
	h.SuccessResponse(c, gin.H{
		"status":    "healthy",
		"service":   "token-service",
		"timestamp": gin.H{},
	})
}

// Metrics 指标接口
func (h *HTTPHandler) Metrics(c *gin.Context) {
	// TODO: 实现指标收集
	h.SuccessResponse(c, gin.H{
		"metrics": "not implemented yet",
	})
}

// GetLeaderboard 获取排行榜
func (h *HTTPHandler) GetLeaderboard(c *gin.Context) {
	// 解析查询参数
	sortBy := c.DefaultQuery("sort", "amount")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	req := &service.LeaderboardRequest{
		SortBy: sortBy,
		Page:   page,
		Limit:  limit,
	}

	// 获取排行榜数据
	response, err := h.leaderboardService.GetLeaderboard(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get leaderboard")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get leaderboard")
		return
	}

	h.SuccessResponse(c, response)
}

// GetStakingOverview 获取质押总览
func (h *HTTPHandler) GetStakingOverview(c *gin.Context) {
	overview, err := h.leaderboardService.GetStakingOverview(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get staking overview")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get staking overview")
		return
	}

	h.SuccessResponse(c, overview)
}

// GetLevelDistribution 获取等级分布
func (h *HTTPHandler) GetLevelDistribution(c *gin.Context) {
	distribution, err := h.leaderboardService.GetLevelDistribution(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get level distribution")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get level distribution")
		return
	}

	h.SuccessResponse(c, distribution)
}

// GetUserStakingStats 获取用户质押统计
func (h *HTTPHandler) GetUserStakingStats(c *gin.Context) {
	userAddress := c.Param("address")
	if userAddress == "" {
		h.ErrorResponse(c, http.StatusBadRequest, "User address is required")
		return
	}

	stats, err := h.stakingService.GetUserStakingStats(c.Request.Context(), userAddress)
	if err != nil {
		h.logger.Error().Err(err).Str("address", userAddress).Msg("Failed to get user staking stats")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user staking stats")
		return
	}

	// 如果用户没有质押记录，返回空的默认数据而不是404
	if stats == nil {
		// 创建默认的空用户质押统计
		emptyStats := &models.UserStakingStats{
			UserAddress:         userAddress,
			Amount:              models.NewBigInt(big.NewInt(0)),
			StakingLevel:        0,
			MiningPower:         models.NewBigInt(big.NewInt(0)),
			TotalRewardsClaimed: models.NewBigInt(big.NewInt(0)),
			PendingRewards:      models.NewBigInt(big.NewInt(0)),
			StartTime:           time.Time{},
			LastUpdateTime:      time.Time{},
			RankByAmount:        0,
			RankByMiningPower:   0,
			StakingDays:         0,
			IsActive:            false,
		}
		h.SuccessResponse(c, emptyStats)
		return
	}

	h.SuccessResponse(c, stats)
}

// GetUserRanking 获取用户排名
func (h *HTTPHandler) GetUserRanking(c *gin.Context) {
	userAddress := c.Param("address")
	if userAddress == "" {
		h.ErrorResponse(c, http.StatusBadRequest, "User address is required")
		return
	}

	ranking, err := h.leaderboardService.GetUserRanking(c.Request.Context(), userAddress)
	if err != nil {
		h.logger.Error().Err(err).Str("address", userAddress).Msg("Failed to get user ranking")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user ranking")
		return
	}

	h.SuccessResponse(c, ranking)
}

// SyncUserStakingData 同步用户质押数据
func (h *HTTPHandler) SyncUserStakingData(c *gin.Context) {
	userAddress := c.Param("address")
	if userAddress == "" {
		h.ErrorResponse(c, http.StatusBadRequest, "User address is required")
		return
	}

	// 调用同步服务
	err := h.syncService.SyncUserStakingInfo(c.Request.Context(), userAddress)
	if err != nil {
		h.logger.Error().Err(err).Str("address", userAddress).Msg("Failed to sync user staking data")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to sync user staking data")
		return
	}

	h.SuccessResponse(c, gin.H{
		"message": "User staking data synced successfully",
		"address": userAddress,
	})
}

// CheckDataFreshness 检查数据新鲜度
func (h *HTTPHandler) CheckDataFreshness(c *gin.Context) {
	userAddress := c.Param("address")
	if userAddress == "" {
		h.ErrorResponse(c, http.StatusBadRequest, "User address is required")
		return
	}

	// 获取用户数据
	userStats, err := h.stakingService.GetUserStakingStats(c.Request.Context(), userAddress)
	if err != nil {
		h.logger.Error().Err(err).Str("address", userAddress).Msg("Failed to get user stake")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user data")
		return
	}

	// 🆕 如果用户没有质押记录，返回默认的新鲜度信息
	if userStats == nil {
		h.SuccessResponse(c, gin.H{
			"address":          userAddress,
			"last_update_time": nil,
			"needs_sync":       true, // 新用户总是需要同步
			"data_age_minutes": 0,
			"is_new_user":      true,
		})
		return
	}

	// 检查数据新鲜度（如果超过5分钟没更新，认为需要同步）
	needsSync := time.Since(userStats.LastUpdateTime) > 5*time.Minute

	h.SuccessResponse(c, gin.H{
		"address":          userAddress,
		"last_update_time": userStats.LastUpdateTime,
		"needs_sync":       needsSync,
		"data_age_minutes": int(time.Since(userStats.LastUpdateTime).Minutes()),
		"is_new_user":      false,
	})
}

// RefreshCache 刷新缓存
func (h *HTTPHandler) RefreshCache(c *gin.Context) {
	err := h.leaderboardService.RefreshLeaderboardCache(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to refresh cache")
		h.ErrorResponse(c, http.StatusInternalServerError, "Failed to refresh cache")
		return
	}

	h.SuccessResponse(c, gin.H{
		"message": "Cache refreshed successfully",
	})
}
