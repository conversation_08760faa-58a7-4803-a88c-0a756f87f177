package handler

import "time"

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code      int       `json:"code"`
	Error     string    `json:"error"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}) SuccessResponse {
	return SuccessResponse{
		Code:      200,
		Message:   "success",
		Data:      data,
		Timestamp: time.Now(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, error, message string) ErrorResponse {
	return ErrorResponse{
		Code:      code,
		Error:     error,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// PaginationMeta 分页元数据
type PaginationMeta struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasMore    bool  `json:"has_more"`
}

// PaginatedResponse 分页响应结构
type PaginatedResponse struct {
	Code       int            `json:"code"`
	Message    string         `json:"message"`
	Data       interface{}    `json:"data"`
	Pagination PaginationMeta `json:"pagination"`
	Timestamp  time.Time      `json:"timestamp"`
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(data interface{}, page, limit int, total int64) PaginatedResponse {
	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasMore := page < totalPages

	return PaginatedResponse{
		Code:    200,
		Message: "success",
		Data:    data,
		Pagination: PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
			HasMore:    hasMore,
		},
		Timestamp: time.Now(),
	}
}
