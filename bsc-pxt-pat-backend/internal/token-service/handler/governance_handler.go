package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// GovernanceHandler 治理相关HTTP处理器
type GovernanceHandler struct {
	governanceService *service.GovernanceService
	logger            zerolog.Logger
}

// NewGovernanceHandler 创建新的治理处理器
func NewGovernanceHandler(governanceService *service.GovernanceService, logger zerolog.Logger) *GovernanceHandler {
	return &GovernanceHandler{
		governanceService: governanceService,
		logger:            logger.With().Str("component", "governance_handler").Logger(),
	}
}

// GetProposals 获取提案列表
func (h *GovernanceHandler) GetProposals(c *gin.Context) {
	status := c.Query("status")
	category := c.Query("category")
	page, _ := strconv.Atoi(c.<PERSON>ult<PERSON>uer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "20"))

	proposals, err := h.governanceService.GetProposals(c.Request.Context(), status, category, page, limit)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get proposals")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get proposals",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: proposals,
	})
}

// GetProposalDetail 获取提案详情
func (h *GovernanceHandler) GetProposalDetail(c *gin.Context) {
	idStr := c.Param("id")
	proposalID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid proposal ID",
			Message: "Proposal ID must be a valid number",
		})
		return
	}

	detail, err := h.governanceService.GetProposalDetail(c.Request.Context(), proposalID)
	if err != nil {
		h.logger.Error().Err(err).Uint64("proposal_id", proposalID).Msg("Failed to get proposal detail")

		if err.Error() == "proposal not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Proposal not found",
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Failed to get proposal detail",
				Message: err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: detail,
	})
}

// GetProposalVotes 获取提案投票详情
func (h *GovernanceHandler) GetProposalVotes(c *gin.Context) {
	idStr := c.Param("id")
	proposalID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid proposal ID",
			Message: "Proposal ID must be a valid number",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	votes, err := h.governanceService.GetProposalVotes(c.Request.Context(), proposalID, page, limit)
	if err != nil {
		h.logger.Error().Err(err).Uint64("proposal_id", proposalID).Msg("Failed to get proposal votes")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get proposal votes",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: votes,
	})
}

// GetUserVotingPower 获取用户投票权重
func (h *GovernanceHandler) GetUserVotingPower(c *gin.Context) {
	address := c.Param("address")

	power, err := h.governanceService.GetUserVotingPower(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user voting power")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get user voting power",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: power,
	})
}

// GetUserVoteHistory 获取用户投票历史
func (h *GovernanceHandler) GetUserVoteHistory(c *gin.Context) {
	address := c.Param("address")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	history, err := h.governanceService.GetUserVoteHistory(c.Request.Context(), address, page, limit)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user vote history")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to get user vote history",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: history,
	})
}

// GetUserDelegations 获取用户委托信息
func (h *GovernanceHandler) GetUserDelegations(c *gin.Context) {
	_ = c.Param("address") // 暂时不使用

	// TODO: 实现获取用户委托信息的逻辑
	// delegations, err := h.governanceService.GetUserDelegations(c.Request.Context(), address)
	// if err != nil {
	//     h.logger.Error().Err(err).Str("address", address).Msg("Failed to get user delegations")
	//     c.JSON(http.StatusBadRequest, ErrorResponse{
	//         Error:   "Failed to get user delegations",
	//         Message: err.Error(),
	//     })
	//     return
	// }

	// 暂时返回空数组
	c.JSON(http.StatusOK, SuccessResponse{
		Data: []interface{}{},
	})
}

// GetGovernanceStats 获取治理统计
func (h *GovernanceHandler) GetGovernanceStats(c *gin.Context) {
	stats, err := h.governanceService.GetGovernanceStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get governance stats")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get governance stats",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Data: stats,
	})
}
