package database

import (
	"fmt"
	"strings"

	"pxpat-backend/internal/token-service/models"

	"gorm.io/gorm"
)

// AutoMigrate 执行GORM自动迁移
func AutoMigrate(db *gorm.DB) error {
	// 定义需要迁移的模型
	models := []any{
		// 质押相关表
		&models.UserStake{},
		&models.StakingEvent{},
		&models.StakingStats{},
		&models.BlockSyncStatus{},
		&models.LeaderboardCache{},

		// 代币相关表
		&models.TokenBalance{},
		&models.TokenTransfer{},
		&models.TokenSupply{},
		&models.TokenHolder{},
		&models.TokenMetrics{},
		&models.TokenLock{},
		&models.TokenBurn{},
		&models.TokenContentRecord{},

		// 治理相关表
		&models.Proposal{},
		&models.Vote{},
		&models.VotingPower{},
		&models.Delegation{},
		&models.GovernanceStats{},
		&models.ProposalParameter{},
		&models.TreasuryOperation{},

		// 池子相关表 - 新增 (2025-07-04)
		&models.PoolBalance{},
		&models.PoolActivity{},
		&models.PoolStats{},

		// 跨链桥相关表 - 新增 (2025-07-23)
		&models.BridgeRequest{},
		&models.BridgeConfirmation{},
		&models.BridgeStats{},
		&models.BridgeValidator{},
	}

	// 执行自动迁移
	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	// 创建索引
	if err := createIndexes(db); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// 添加唯一约束防止重复地址
	if err := addUniqueConstraints(db); err != nil {
		return fmt.Errorf("failed to add unique constraints: %w", err)
	}

	// 初始化数据
	if err := initializeData(db); err != nil {
		return fmt.Errorf("failed to initialize data: %w", err)
	}

	return nil
}

// createIndexes 创建额外的索引
func createIndexes(db *gorm.DB) error {
	// 用户质押信息表索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_token_user_stakes_amount_desc ON token_user_stakes(amount DESC)",
		"CREATE INDEX IF NOT EXISTS idx_token_user_stakes_mining_power_desc ON token_user_stakes(mining_power DESC)",
		"CREATE INDEX IF NOT EXISTS idx_token_user_stakes_level_amount ON token_user_stakes(staking_level, amount DESC)",

		// 质押事件表索引
		"CREATE INDEX IF NOT EXISTS idx_token_staking_events_user_type ON token_staking_events(user_address, event_type)",
		"CREATE INDEX IF NOT EXISTS idx_token_staking_events_timestamp_desc ON token_staking_events(timestamp DESC)",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_token_staking_events_tx_log ON token_staking_events(transaction_hash, log_index)",

		// 排行榜缓存表索引
		"CREATE INDEX IF NOT EXISTS idx_token_leaderboard_cache_expires ON token_leaderboard_cache(expires_at)",

		// 代币余额表索引
		"CREATE INDEX IF NOT EXISTS idx_token_balances_user_token ON token_balances(user_address, token_type)",
		"CREATE INDEX IF NOT EXISTS idx_token_balances_updated ON token_balances(last_updated)",

		// 代币转账表索引
		"CREATE INDEX IF NOT EXISTS idx_token_transfers_from ON token_transfers(from_address)",
		"CREATE INDEX IF NOT EXISTS idx_token_transfers_to ON token_transfers(to_address)",
		"CREATE INDEX IF NOT EXISTS idx_token_transfers_token_time ON token_transfers(token_type, timestamp DESC)",

		// 代币持有者表索引
		"CREATE INDEX IF NOT EXISTS idx_token_holders_token_rank ON token_holders(token_type, rank)",
		"CREATE INDEX IF NOT EXISTS idx_token_holders_balance ON token_holders(balance DESC)",

		// 治理提案表索引
		"CREATE INDEX IF NOT EXISTS idx_token_proposals_status ON token_proposals(status)",
		"CREATE INDEX IF NOT EXISTS idx_token_proposals_category ON token_proposals(category)",
		"CREATE INDEX IF NOT EXISTS idx_token_proposals_proposer ON token_proposals(proposer)",
		"CREATE INDEX IF NOT EXISTS idx_token_proposals_blocks ON token_proposals(start_block, end_block)",

		// 投票记录表索引
		"CREATE INDEX IF NOT EXISTS idx_token_votes_proposal ON token_votes(proposal_id)",
		"CREATE INDEX IF NOT EXISTS idx_token_votes_voter ON token_votes(voter)",
		"CREATE INDEX IF NOT EXISTS idx_token_votes_timestamp ON token_votes(timestamp DESC)",

		// 投票权重表索引
		"CREATE INDEX IF NOT EXISTS idx_token_voting_power_user ON token_voting_power(user_address)",
		"CREATE INDEX IF NOT EXISTS idx_token_voting_power_total ON token_voting_power(total_power DESC)",

		// 委托记录表索引
		"CREATE INDEX IF NOT EXISTS idx_token_delegations_delegator ON token_delegations(delegator)",
		"CREATE INDEX IF NOT EXISTS idx_token_delegations_delegate ON token_delegations(delegate)",
		"CREATE INDEX IF NOT EXISTS idx_token_delegations_active ON token_delegations(is_active)",

		// 跨链桥表索引 - 新增 (2025-07-23)
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_status ON bridge_requests(status)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_from_chain ON bridge_requests(from_chain)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_to_chain ON bridge_requests(to_chain)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_sender ON bridge_requests(sender)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_receiver ON bridge_requests(receiver)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_requests_created_at ON bridge_requests(created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_confirmations_request_id ON bridge_confirmations(request_id)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_confirmations_validator ON bridge_confirmations(validator)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_stats_date ON bridge_stats(date DESC)",
		"CREATE INDEX IF NOT EXISTS idx_bridge_validators_active ON bridge_validators(is_active)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// 如果索引已存在，忽略错误
			continue
		}
	}

	return nil
}

// initializeData 初始化基础数据
func initializeData(db *gorm.DB) error {
	// 检查是否已有区块同步状态记录
	var count int64
	if err := db.Model(&models.BlockSyncStatus{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count block sync status: %w", err)
	}

	// 如果没有记录，创建初始记录
	if count == 0 {
		initialStatus := &models.BlockSyncStatus{
			LastSyncedBlock: 57050000, // 基于部署时间 2025-07-04T00:53:50.751Z 设置起始区块
			SyncStatus:      "syncing",
			ErrorMessage:    "",
		}

		if err := db.Create(initialStatus).Error; err != nil {
			return fmt.Errorf("failed to create initial block sync status: %w", err)
		}
	}

	// 注释：不再初始化测试数据，使用真实链上数据 (2025-07-04)
	// 所有数据将通过区块链事件监听器从链上同步获取

	return nil
}

// 注释：已删除测试数据生成函数 (2025-07-04)
// 所有质押数据现在通过区块链事件监听器从链上实时同步获取

// CreateViews 创建数据库视图
func CreateViews(db *gorm.DB) error {
	views := []string{
		// 活跃用户质押排行榜视图
		`CREATE OR REPLACE VIEW token_active_staking_leaderboard AS
		SELECT
			user_address,
			amount,
			staking_level,
			mining_power,
			total_rewards_claimed,
			start_time,
			ROW_NUMBER() OVER (ORDER BY amount DESC) as rank_by_amount,
			ROW_NUMBER() OVER (ORDER BY mining_power DESC) as rank_by_mining_power
		FROM token_user_stakes
		WHERE is_active = true
		ORDER BY amount DESC`,

		// 质押统计概览视图
		`CREATE OR REPLACE VIEW token_staking_overview AS
		SELECT
			COUNT(*) as total_users,
			COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
			COALESCE(SUM(CASE WHEN is_active = true THEN amount ELSE 0 END), 0) as total_staked,
			COALESCE(SUM(total_rewards_claimed), 0) as total_rewards_claimed,
			COALESCE(SUM(CASE WHEN is_active = true THEN mining_power ELSE 0 END), 0) as total_mining_power,
			COALESCE(AVG(CASE WHEN is_active = true THEN amount END), 0) as average_stake_amount,
			MAX(updated_at) as last_update_time
		FROM token_user_stakes`,

		// 等级分布统计视图
		`CREATE OR REPLACE VIEW token_level_distribution AS
		SELECT
			staking_level as level,
			COUNT(*) as count,
			COALESCE(SUM(amount), 0) as total_amount,
			ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
		FROM token_user_stakes
		WHERE is_active = true
		GROUP BY staking_level
		ORDER BY staking_level`,
	}

	for _, viewSQL := range views {
		if err := db.Exec(viewSQL).Error; err != nil {
			return fmt.Errorf("failed to create view: %w", err)
		}
	}

	return nil
}

// CreateFunctions 创建数据库函数
func CreateFunctions(db *gorm.DB) error {
	functions := []string{
		// 获取用户排名函数
		`CREATE OR REPLACE FUNCTION get_user_rank(user_addr VARCHAR(42), rank_type VARCHAR(20) DEFAULT 'amount')
		RETURNS INTEGER AS $$
		DECLARE
			user_rank INTEGER;
		BEGIN
			IF rank_type = 'amount' THEN
				SELECT rank_by_amount INTO user_rank
				FROM token_active_staking_leaderboard
				WHERE user_address = user_addr;
			ELSIF rank_type = 'mining_power' THEN
				SELECT rank_by_mining_power INTO user_rank
				FROM token_active_staking_leaderboard
				WHERE user_address = user_addr;
			END IF;

			RETURN COALESCE(user_rank, 0);
		END;
		$$ LANGUAGE plpgsql;`,

		// 清理过期缓存函数
		`CREATE OR REPLACE FUNCTION cleanup_expired_cache()
		RETURNS INTEGER AS $$
		DECLARE
			deleted_count INTEGER;
		BEGIN
			DELETE FROM token_leaderboard_cache WHERE expires_at < CURRENT_TIMESTAMP;
			GET DIAGNOSTICS deleted_count = ROW_COUNT;
			RETURN deleted_count;
		END;
		$$ LANGUAGE plpgsql;`,
	}

	for _, functionSQL := range functions {
		if err := db.Exec(functionSQL).Error; err != nil {
			// PostgreSQL函数创建可能因为权限问题失败，记录但不中断
			continue
		}
	}

	return nil
}

// DropTables 删除所有表（用于重置）
func DropTables(db *gorm.DB) error {
	// 删除视图
	views := []string{
		"DROP VIEW IF EXISTS token_active_staking_leaderboard",
		"DROP VIEW IF EXISTS token_staking_overview",
		"DROP VIEW IF EXISTS token_level_distribution",
		// 兼容旧视图名称
		"DROP VIEW IF EXISTS active_staking_leaderboard",
		"DROP VIEW IF EXISTS staking_overview",
		"DROP VIEW IF EXISTS level_distribution",
	}

	for _, viewSQL := range views {
		db.Exec(viewSQL)
	}

	// 删除函数
	functions := []string{
		"DROP FUNCTION IF EXISTS get_user_rank(VARCHAR, VARCHAR)",
		"DROP FUNCTION IF EXISTS cleanup_expired_cache()",
	}

	for _, functionSQL := range functions {
		db.Exec(functionSQL)
	}

	// 删除表（按依赖关系倒序删除）
	tables := []any{
		// 跨链桥相关表 - 新增 (2025-07-23)
		&models.BridgeStats{},
		&models.BridgeConfirmation{},
		&models.BridgeValidator{},
		&models.BridgeRequest{},

		// 池子相关表 - 新增 (2025-07-04)
		&models.PoolStats{},
		&models.PoolActivity{},
		&models.PoolBalance{},

		// 治理相关表
		&models.TreasuryOperation{},
		&models.ProposalParameter{},
		&models.GovernanceStats{},
		&models.Delegation{},
		&models.VotingPower{},
		&models.Vote{},
		&models.Proposal{},

		// 代币相关表
		&models.TokenBurn{},
		&models.TokenLock{},
		&models.TokenMetrics{},
		&models.TokenHolder{},
		&models.TokenSupply{},
		&models.TokenTransfer{},
		&models.TokenBalance{},

		// 质押相关表
		&models.LeaderboardCache{},
		&models.StakingEvent{},
		&models.BlockSyncStatus{},
		&models.UserStake{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			return fmt.Errorf("failed to drop table %T: %w", table, err)
		}
	}

	return nil
}

// addUniqueConstraints 添加唯一约束防止重复记录
func addUniqueConstraints(db *gorm.DB) error {
	// 首先检查并删除可能存在的重复记录
	cleanupSQL := `
		-- 删除重复记录，保留最新的记录
		WITH duplicates AS (
			SELECT
				id,
				user_address,
				LOWER(user_address) as normalized_address,
				ROW_NUMBER() OVER (
					PARTITION BY LOWER(user_address)
					ORDER BY created_at DESC, last_update_time DESC, id DESC
				) as rn
			FROM token_user_stakes
		)
		DELETE FROM token_user_stakes
		WHERE id IN (
			SELECT id FROM duplicates WHERE rn > 1
		)
	`

	if err := db.Exec(cleanupSQL).Error; err != nil {
		return fmt.Errorf("failed to cleanup duplicate records: %w", err)
	}

	// 创建唯一索引（不使用 CONCURRENTLY，因为在迁移过程中）
	constraints := []string{
		// 为用户质押表添加基于小写地址的唯一索引
		`CREATE UNIQUE INDEX IF NOT EXISTS idx_user_stakes_unique_address
		 ON token_user_stakes (LOWER(user_address))`,

		// 为代币余额表添加基于小写地址和代币类型的唯一索引
		`CREATE UNIQUE INDEX IF NOT EXISTS idx_token_balances_unique_address_type
		 ON token_balances (LOWER(user_address), token_type)`,
	}

	for _, constraintSQL := range constraints {
		if err := db.Exec(constraintSQL).Error; err != nil {
			// 如果索引已存在，忽略错误
			if strings.Contains(err.Error(), "already exists") ||
				strings.Contains(err.Error(), "relation") && strings.Contains(err.Error(), "already exists") {
				continue
			}
			return fmt.Errorf("failed to add unique constraint: %w", err)
		}
	}

	return nil
}
