package service

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/config"
)

// TransferService 区块链转账服务
type TransferService struct {
	client     *ethclient.Client
	privateKey *ecdsa.PrivateKey
	fromAddr   common.Address
	config     config.TransferConfig
	logger     zerolog.Logger
}

// ERC20 ABI (简化版，只包含transfer方法)
const ERC20ABI = `[
	{
		"constant": false,
		"inputs": [
			{"name": "_to", "type": "address"},
			{"name": "_value", "type": "uint256"}
		],
		"name": "transfer",
		"outputs": [{"name": "", "type": "bool"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [{"name": "_owner", "type": "address"}],
		"name": "balanceOf",
		"outputs": [{"name": "balance", "type": "uint256"}],
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "decimals",
		"outputs": [{"name": "", "type": "uint8"}],
		"type": "function"
	}
]`

// TransferRequest 转账请求
type TransferRequest struct {
	TokenAddress common.Address
	ToAddress    common.Address
	Amount       *big.Int
	Memo         string
}

// TransferResult 转账结果
type TransferResult struct {
	TxHash      string
	BlockNumber uint64
	GasUsed     uint64
	Status      uint64
}

// NewTransferService 创建转账服务
func NewTransferService(
	rpcURL string,
	transferConfig config.TransferConfig,
	logger zerolog.Logger,
) (*TransferService, error) {
	// 连接到区块链节点
	client, err := ethclient.Dial(rpcURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ethereum client: %w", err)
	}

	// 解析私钥 - 处理有无0x前缀的情况
	privateKeyHex := strings.TrimSpace(transferConfig.SystemPrivateKey)
	privateKeyHex = strings.TrimPrefix(privateKeyHex, "0x")

	// 验证私钥长度
	if len(privateKeyHex) != 64 {
		return nil, fmt.Errorf("invalid private key length: expected 64 characters, got %d", len(privateKeyHex))
	}

	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	// 获取地址
	fromAddr := crypto.PubkeyToAddress(privateKey.PublicKey)

	// 验证地址匹配
	if transferConfig.SystemAddress != "" {
		expectedAddr := common.HexToAddress(transferConfig.SystemAddress)
		if fromAddr != expectedAddr {
			return nil, fmt.Errorf("private key does not match system address")
		}
	}

	return &TransferService{
		client:     client,
		privateKey: privateKey,
		fromAddr:   fromAddr,
		config:     transferConfig,
		logger:     logger.With().Str("component", "transfer_service").Logger(),
	}, nil
}

// TransferToken 执行代币转账
func (s *TransferService) TransferToken(ctx context.Context, req *TransferRequest) (*TransferResult, error) {
	s.logger.Info().
		Str("token", req.TokenAddress.Hex()).
		Str("to", req.ToAddress.Hex()).
		Str("amount", req.Amount.String()).
		Str("memo", req.Memo).
		Msg("Starting token transfer")

	// 解析ERC20 ABI
	parsedABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	// 获取nonce
	nonce, err := s.client.PendingNonceAt(ctx, s.fromAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// 获取gas价格
	gasPrice := big.NewInt(s.config.GasPrice)
	if s.config.GasPrice == 0 {
		gasPrice, err = s.client.SuggestGasPrice(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to suggest gas price: %w", err)
		}
	}

	// 构造transfer调用数据
	data, err := parsedABI.Pack("transfer", req.ToAddress, req.Amount)
	if err != nil {
		return nil, fmt.Errorf("failed to pack transfer data: %w", err)
	}

	// 创建交易
	tx := types.NewTransaction(
		nonce,
		req.TokenAddress,
		big.NewInt(0), // ERC20转账value为0
		s.config.GasLimit,
		gasPrice,
		data,
	)

	// 获取链ID
	chainID, err := s.client.NetworkID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 签名交易
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), s.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 发送交易
	err = s.client.SendTransaction(ctx, signedTx)
	if err != nil {
		return nil, fmt.Errorf("failed to send transaction: %w", err)
	}

	txHash := signedTx.Hash().Hex()
	s.logger.Info().
		Str("tx_hash", txHash).
		Msg("Transaction sent, waiting for confirmation")

	// 等待交易确认
	receipt, err := bind.WaitMined(ctx, s.client, signedTx)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for transaction confirmation: %w", err)
	}

	s.logger.Info().
		Str("tx_hash", txHash).
		Uint64("block_number", receipt.BlockNumber.Uint64()).
		Uint64("gas_used", receipt.GasUsed).
		Uint64("status", receipt.Status).
		Msg("Transaction confirmed")

	return &TransferResult{
		TxHash:      txHash,
		BlockNumber: receipt.BlockNumber.Uint64(),
		GasUsed:     receipt.GasUsed,
		Status:      receipt.Status,
	}, nil
}

// TransferPAT 转账PAT代币
func (s *TransferService) TransferPAT(ctx context.Context, patTokenAddr, toAddr common.Address, amount *big.Int, memo string) (*TransferResult, error) {
	req := &TransferRequest{
		TokenAddress: patTokenAddr,
		ToAddress:    toAddr,
		Amount:       amount,
		Memo:         memo,
	}
	return s.TransferToken(ctx, req)
}

// TransferPXT 转账PXT代币
func (s *TransferService) TransferPXT(ctx context.Context, pxtTokenAddr, toAddr common.Address, amount *big.Int, memo string) (*TransferResult, error) {
	req := &TransferRequest{
		TokenAddress: pxtTokenAddr,
		ToAddress:    toAddr,
		Amount:       amount,
		Memo:         memo,
	}
	return s.TransferToken(ctx, req)
}

// GetBalance 获取代币余额
func (s *TransferService) GetBalance(ctx context.Context, tokenAddr common.Address) (*big.Int, error) {
	// 解析ERC20 ABI
	parsedABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	// 构造balanceOf调用数据
	data, err := parsedABI.Pack("balanceOf", s.fromAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to pack balanceOf data: %w", err)
	}

	// 调用合约
	msg := ethereum.CallMsg{
		To:   &tokenAddr,
		Data: data,
	}
	result, err := s.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call balanceOf: %w", err)
	}

	// 解析结果
	balance := new(big.Int)
	err = parsedABI.UnpackIntoInterface(&balance, "balanceOf", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack balanceOf result: %w", err)
	}

	return balance, nil
}

// GetSystemAddress 获取系统地址
func (s *TransferService) GetSystemAddress() common.Address {
	return s.fromAddr
}

// Close 关闭服务
func (s *TransferService) Close() {
	if s.client != nil {
		s.client.Close()
	}
}
