package service

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/pkg/httpclient"
)

// ContentService 内容上链服务 - 支持IPFS和新的内容合约
type ContentService struct {
	contentRepo            *repository.ContentRepository
	blockchainMgr          *blockchain.MultiContractClient
	transferService        *TransferService
	ipfsService            *IPFSService              // IPFS服务
	contentRegistryService *ContentRegistryService   // ContentRegistry合约服务
	contentTypeConfig      *ContentTypeConfigService // 内容类型配置服务
	logger                 zerolog.Logger
	contentRegistry        common.Address         // 内容注册合约地址
	contentMint            common.Address         // 内容铸造合约地址
	walletServiceURL       string                 // 钱包服务URL
	pointsServiceURL       string                 // 点数服务URL
	patTokenAddr           common.Address         // PAT代币地址
	pxtTokenAddr           common.Address         // PXT代币地址
	walletClient           *httpclient.HTTPClient // 钱包服务客户端
	pointsClient           *httpclient.HTTPClient // 点数服务客户端
	contentClient          *httpclient.HTTPClient // 内容服务客户端
}

// NewContentService 创建内容服务实例
func NewContentService(
	contentRepo *repository.ContentRepository,
	blockchainMgr *blockchain.MultiContractClient,
	transferService *TransferService,
	ipfsService *IPFSService,
	contentRegistryService *ContentRegistryService,
	patTokenAddr, pxtTokenAddr, contentRegistry, contentMint common.Address,
	logger zerolog.Logger,
) *ContentService {
	// 创建内容类型配置服务
	contentTypeConfig, err := NewContentTypeConfigService("")
	if err != nil {
		logger.Warn().Err(err).Msg("Failed to load content type config, using defaults")
		contentTypeConfig, _ = NewContentTypeConfigService("") // 使用默认配置
	}
	// 创建钱包服务客户端
	walletClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          "http://localhost:15002",
		Timeout:          30 * time.Second,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})

	// 创建点数服务客户端
	pointsClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          "http://localhost:15001",
		Timeout:          30 * time.Second,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})

	// 创建内容服务客户端
	contentClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          "http://content-service:12001",
		Timeout:          10 * time.Second,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})

	return &ContentService{
		contentRepo:            contentRepo,
		blockchainMgr:          blockchainMgr,
		transferService:        transferService,
		ipfsService:            ipfsService,
		contentRegistryService: contentRegistryService,
		contentTypeConfig:      contentTypeConfig,
		logger:                 logger.With().Str("component", "content_service").Logger(),
		contentRegistry:        contentRegistry,
		contentMint:            contentMint,
		walletServiceURL:       "http://localhost:15002", // 钱包服务地址
		pointsServiceURL:       "http://localhost:15001", // 点数服务地址
		patTokenAddr:           patTokenAddr,
		pxtTokenAddr:           pxtTokenAddr,
		walletClient:           walletClient,
		pointsClient:           pointsClient,
		contentClient:          contentClient,
	}
}

// WalletTransferRequest 钱包转账请求
type WalletTransferRequest struct {
	FromKSUID   string  `json:"from_ks_uid"`
	ToAddress   string  `json:"to_address"`
	TokenSymbol string  `json:"token_symbol"`
	Amount      float64 `json:"amount"`
	Memo        string  `json:"memo"`
	Purpose     string  `json:"purpose"`
}

// WalletTransferResponse 钱包转账响应
type WalletTransferResponse struct {
	Code int `json:"code"`
	Data struct {
		TransactionHash string `json:"transaction_hash"`
		BlockNumber     uint64 `json:"block_number"`
		Status          string `json:"status"`
	} `json:"data"`
	Message string `json:"message"`
}

// PointsDeductRequest 点数扣除请求（对应SpendPATRequest）
type PointsDeductRequest struct {
	KsUID       string  `json:"ks_uid"`
	Amount      float64 `json:"amount"`
	Action      string  `json:"action"`
	Description string  `json:"description"`
}

// PointsDeductResponse 点数扣除响应
type PointsDeductResponse struct {
	Code int `json:"code"`
	Data struct {
		RemainingPAT float64 `json:"remaining_pat"`
		DeductedPAT  float64 `json:"deducted_pat"`
	} `json:"data"`
	Message string `json:"message"`
}

// ContentPublishRequest 内容发布请求
type ContentPublishRequest struct {
	ID             string    `json:"id"`
	Title          string    `json:"title"`
	OriginalTitle  string    `json:"original_title"`
	Description    string    `json:"description"`
	ContentType    string    `json:"content_type"` // video, novel, short_drama, anime, manga, music, article, short_video
	LicenseNumber  string    `json:"license_number"`
	Cast           []string  `json:"cast"`
	Director       string    `json:"director"`
	Publisher      string    `json:"publisher"`
	PublisherKSUID string    `json:"publisher_ksuid"`
	ReleaseDate    time.Time `json:"release_date"`
	OnChainDate    time.Time `json:"on_chain_date"`
	OnChainID      string    `json:"on_chain_id"`
	Reviewers      []string  `json:"reviewers"`
	FileKSUID      string    `json:"file_ksuid"` // 文件KSUID
	FileMD5        string    `json:"file_md5"`   // 文件MD5哈希
}

// 默认内容类型费用配置 (仅用于初始化和后备)
var DefaultContentTypeFees = map[string]float64{
	"video":       1.0,  // 视频 - 1 PAT
	"novel":       0.1,  // 小说 - 0.1 PAT
	"short_drama": 0.25, // 短剧 - 0.25 PAT
	"anime":       0.8,  // 动漫 - 0.8 PAT
	"manga":       0.3,  // 漫画 - 0.3 PAT
	"music":       0.5,  // 音乐 - 0.5 PAT
	"article":     0.05, // 文章 - 0.05 PAT
	"short_video": 0.2,  // 短视频 - 0.2 PAT
	"image":       0.3,  // 图片 - 0.3 PAT
}

// ContentTypeInfo 内容类型信息
type ContentTypeInfo struct {
	TypeName    string  `json:"type_name"`
	DefaultFee  float64 `json:"default_fee"`
	IsActive    bool    `json:"is_active"`
	Description string  `json:"description"`
	CreatedAt   string  `json:"created_at"`
}

// PublishContent 发布内容到区块链
func (s *ContentService) PublishContent(ctx context.Context, req *ContentPublishRequest) (*models.TokenContentRecord, error) {
	s.logger.Info().
		Str("content_id", req.ID).
		Str("content_type", req.ContentType).
		Str("publisher", req.PublisherKSUID).
		Msg("Publishing content to blockchain")

	// 1. 验证内容类型和费用
	fee, err := s.GetContentFeeFromContract(ctx, req.ContentType)
	if err != nil {
		return nil, fmt.Errorf("failed to get content type fee: %w", err)
	}

	// 2. 生成内容哈希
	contentHash, err := s.generateContentHash(req)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content hash: %w", err)
	}

	// 3. 创建数据库记录
	castJSON, _ := json.Marshal(req.Cast)
	reviewersJSON, _ := json.Marshal(req.Reviewers)

	record := &models.TokenContentRecord{
		ContentID:      req.ID,
		OnChainID:      req.OnChainID,
		Title:          req.Title,
		OriginalTitle:  req.OriginalTitle,
		Description:    req.Description,
		ContentType:    req.ContentType,
		LicenseNumber:  req.LicenseNumber,
		Cast:           string(castJSON),
		Director:       req.Director,
		Publisher:      req.Publisher,
		PublisherKSUID: req.PublisherKSUID,
		ReleaseDate:    req.ReleaseDate,
		OnChainDate:    req.OnChainDate,
		Reviewers:      string(reviewersJSON),
		ContentHash:    contentHash,
		FileKSUID:      req.FileKSUID,
		FileMD5:        req.FileMD5,
		PATFee:         fmt.Sprintf("%.8f", fee),
		Status:         "pending",
	}

	// 4. 保存到数据库
	if err := s.contentRepo.CreateContentRecord(ctx, record); err != nil {
		return nil, fmt.Errorf("failed to create content record: %w", err)
	}

	// 5. 执行上链操作
	go s.executeOnChainPublish(context.Background(), record)

	return record, nil
}

// executeOnChainPublish 执行上链发布（异步）
func (s *ContentService) executeOnChainPublish(ctx context.Context, record *models.TokenContentRecord) {
	s.logger.Info().
		Uint("record_id", record.ID).
		Str("on_chain_id", record.OnChainID).
		Msg("Executing on-chain publish with real PAT transfer")

	// 记录内容上链信息到日志
	s.logger.Info().
		Str("content_id", record.ContentID).
		Str("on_chain_id", record.OnChainID).
		Str("content_hash", record.ContentHash).
		Str("content_type", record.ContentType).
		Str("publisher", record.PublisherKSUID).
		Msg("Processing content publish transaction")

	// 解析PAT费用
	patFee := 0.0
	fmt.Sscanf(record.PATFee, "%f", &patFee)

	// 跳过点数服务调用，直接执行真实PAT转账
	s.logger.Info().
		Uint("record_id", record.ID).
		Str("publisher", record.PublisherKSUID).
		Float64("pat_fee", patFee).
		Msg("Skipping points service, executing direct PAT transfer")

	// 调用ContentRegistry合约注册内容
	s.logger.Info().Msg("调用ContentRegistry合约注册普通内容")

	registryReq := &RegisterContentRequest{
		ContentType: record.ContentType,
		Title:       record.Title,
		IPFSHash:    record.ContentHash,            // 使用内容哈希作为IPFS哈希
		MetadataURI: record.ContentHash,            // 使用内容哈希作为元数据URI
		Reviewers:   []string{"reviewer_auto_001"}, // 默认审核员
	}

	registryResult, err := s.contentRegistryService.RegisterContent(ctx, registryReq)
	if err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", record.ID).
			Str("publisher", record.PublisherKSUID).
			Float64("pat_fee", patFee).
			Msg("Failed to register content to ContentRegistry")
		s.updateRecordStatus(ctx, record.ID, "failed", "", 0)
		return
	}

	txHash := registryResult.TxHash
	blockNumber := registryResult.BlockNumber

	s.logger.Info().
		Str("tx_hash", txHash).
		Uint64("block_number", blockNumber).
		Interface("content_id", registryResult.ContentID).
		Msg("普通内容注册到ContentRegistry成功")

	// 更新记录状态
	s.updateRecordStatus(ctx, record.ID, "confirmed", txHash, blockNumber)

	s.logger.Info().
		Str("tx_hash", txHash).
		Uint64("block_number", blockNumber).
		Float64("pat_fee", patFee).
		Msg("Content publish confirmed on blockchain")
}

// executeWalletTransfer 执行钱包转账
func (s *ContentService) executeWalletTransfer(ctx context.Context, req *WalletTransferRequest) (string, uint64, error) {
	// 发送HTTP请求到钱包服务
	var transferResp WalletTransferResponse
	err := s.walletClient.Post("/api/v1/wallet/transfer", req, &transferResp)
	if err != nil {
		return "", 0, fmt.Errorf("failed to send request: %w", err)
	}

	// 检查响应状态
	if transferResp.Code != 0 {
		return "", 0, fmt.Errorf("wallet transfer failed: %s", transferResp.Message)
	}

	s.logger.Info().
		Str("tx_hash", transferResp.Data.TransactionHash).
		Uint64("block_number", transferResp.Data.BlockNumber).
		Str("from_ksuid", req.FromKSUID).
		Str("to_address", req.ToAddress).
		Float64("amount", req.Amount).
		Str("token", req.TokenSymbol).
		Msg("Wallet transfer executed successfully")

	return transferResp.Data.TransactionHash, transferResp.Data.BlockNumber, nil
}

// deductPATPoints 扣除PAT积分
func (s *ContentService) deductPATPoints(ctx context.Context, req *PointsDeductRequest) error {
	// 发送HTTP请求到点数服务
	var deductResp PointsDeductResponse
	err := s.pointsClient.Post("/api/v1/pat/spend", req, &deductResp)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	// 检查响应状态
	if deductResp.Code != 0 {
		return fmt.Errorf("points deduction failed: %s", deductResp.Message)
	}

	s.logger.Info().
		Str("ks_uid", req.KsUID).
		Float64("deducted_amount", req.Amount).
		Float64("remaining_pat", deductResp.Data.RemainingPAT).
		Str("action", req.Action).
		Msg("PAT points deducted successfully")

	return nil
}

// executePATTransfer 执行真实的PAT代币转账
func (s *ContentService) executePATTransfer(ctx context.Context, record *models.TokenContentRecord, patFee float64) (string, uint64, error) {
	// 构造转账备注（包含内容信息）
	memo := map[string]interface{}{
		"action":       "content_publish",
		"content_id":   record.ContentID,
		"on_chain_id":  record.OnChainID,
		"content_hash": record.ContentHash,
		"content_type": record.ContentType,
		"title":        record.Title,
		"publisher":    record.PublisherKSUID,
		"reviewers":    record.Reviewers,
		"timestamp":    record.OnChainDate.Unix(),
	}

	memoJSON, _ := json.Marshal(memo)

	// 将PAT费用转换为wei (18位精度)
	patFeeWei := new(big.Int)
	patFeeWei.SetString(fmt.Sprintf("%.0f", patFee*1e18), 10)

	// 使用TransferService执行真实的PAT代币转账
	result, err := s.transferService.TransferPAT(
		ctx,
		s.patTokenAddr,
		s.contentRegistry,
		patFeeWei,
		string(memoJSON),
	)
	if err != nil {
		return "", 0, fmt.Errorf("failed to execute PAT transfer: %w", err)
	}

	// 检查交易状态
	if result.Status != 1 {
		return "", 0, fmt.Errorf("PAT transfer transaction failed, status: %d", result.Status)
	}

	s.logger.Info().
		Str("tx_hash", result.TxHash).
		Uint64("block_number", result.BlockNumber).
		Uint64("gas_used", result.GasUsed).
		Float64("pat_fee", patFee).
		Str("to_address", s.contentRegistry.Hex()).
		Str("memo", string(memoJSON)).
		Msg("PAT transfer executed successfully for content publish")

	return result.TxHash, result.BlockNumber, nil
}

// generateContentHash 生成内容哈希
func (s *ContentService) generateContentHash(req *ContentPublishRequest) (string, error) {
	// 构造哈希数据
	hashData := map[string]interface{}{
		"content_id":     req.ID,
		"title":          req.Title,
		"original_title": req.OriginalTitle,
		"description":    req.Description,
		"content_type":   req.ContentType,
		"license_number": req.LicenseNumber,
		"cast":           req.Cast,
		"director":       req.Director,
		"publisher":      req.Publisher,
		"release_date":   req.ReleaseDate.Unix(),
		"reviewers":      req.Reviewers,
		"file_ksuid":     req.FileKSUID,
		"file_md5":       req.FileMD5,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(hashData)
	if err != nil {
		return "", err
	}

	// 计算SHA256哈希
	hash := sha256.Sum256(jsonData)
	return "0x" + hex.EncodeToString(hash[:]), nil
}

// updateRecordStatus 更新记录状态
func (s *ContentService) updateRecordStatus(ctx context.Context, id uint, status, txHash string, blockNumber uint64) {
	if err := s.contentRepo.UpdateTransactionInfo(ctx, id, txHash, blockNumber, status); err != nil {
		s.logger.Error().Err(err).Uint("record_id", id).Msg("Failed to update record status")
	}
}

// UpdateContentStatus 公开的更新内容状态方法
func (s *ContentService) UpdateContentStatus(ctx context.Context, id uint, status string) error {
	err := s.contentRepo.UpdateTransactionInfo(ctx, id, "", 0, status)
	if err != nil {
		s.logger.Error().Err(err).Uint("record_id", id).Str("status", status).Msg("Failed to update content status")
		return fmt.Errorf("failed to update content status: %w", err)
	}

	s.logger.Info().Uint("record_id", id).Str("status", status).Msg("Content status updated successfully")
	return nil
}

// GetContentRecord 获取内容记录
func (s *ContentService) GetContentRecord(ctx context.Context, onChainID string) (*models.TokenContentRecord, error) {
	return s.contentRepo.GetContentRecordByOnChainID(ctx, onChainID)
}

// GetContentRecords 分页获取内容记录
func (s *ContentService) GetContentRecords(ctx context.Context, page, limit int, contentType, status string) ([]*models.TokenContentRecord, int64, error) {
	return s.contentRepo.GetContentRecords(ctx, page, limit, contentType, status)
}

// GetContentStats 获取内容统计
func (s *ContentService) GetContentStats(ctx context.Context) (map[string]interface{}, error) {
	return s.contentRepo.GetContentStats(ctx)
}

// GetContentTypeStats 获取内容类型统计
func (s *ContentService) GetContentTypeStats(ctx context.Context) ([]map[string]interface{}, error) {
	return s.contentRepo.GetContentTypeStats(ctx)
}

// GetContentDetailByOnChainID 根据上链ID获取完整内容详情
func (s *ContentService) GetContentDetailByOnChainID(ctx context.Context, onChainID string) (map[string]interface{}, error) {
	s.logger.Info().
		Str("on_chain_id", onChainID).
		Msg("Getting content detail by on-chain ID")

	// 1. 获取Token记录
	tokenRecord, err := s.contentRepo.GetContentRecordByOnChainID(ctx, onChainID)
	if err != nil {
		s.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get token record")
		return nil, fmt.Errorf("failed to get token record: %w", err)
	}

	// 2. 调用内容服务获取内容详情
	contentDetail, err := s.getContentFromContentService(ctx, tokenRecord.ContentID)
	if err != nil {
		s.logger.Error().Err(err).
			Str("on_chain_id", onChainID).
			Str("content_id", tokenRecord.ContentID).
			Msg("Failed to get content detail from content service")
		// 如果获取内容详情失败，仍然返回Token记录
		contentDetail = map[string]interface{}{
			"error":      "Failed to get content detail",
			"content_id": tokenRecord.ContentID,
		}
	}

	// 3. 组合返回结果
	result := map[string]interface{}{
		"token_record":   tokenRecord,
		"content_detail": contentDetail,
	}

	s.logger.Info().
		Str("on_chain_id", onChainID).
		Str("content_id", tokenRecord.ContentID).
		Msg("Content detail retrieved successfully")

	return result, nil
}

// getContentFromContentService 从内容服务获取内容详情
func (s *ContentService) getContentFromContentService(ctx context.Context, contentID string) (interface{}, error) {
	s.logger.Info().
		Str("content_id", contentID).
		Msg("Calling content service to get content detail")

	// 调用内容服务API: GET /api/v1/content/{content_id}
	url := fmt.Sprintf("/api/v1/content/%s", contentID)

	// 发送请求
	var response map[string]interface{}
	err := s.contentClient.Get(url, &response)
	if err != nil {
		s.logger.Error().Err(err).Str("url", url).Msg("Failed to call content service")
		return nil, fmt.Errorf("failed to call content service: %w", err)
	}

	// 检查响应状态
	if code, ok := response["code"].(float64); !ok || code != 1 {
		s.logger.Error().
			Str("url", url).
			Interface("response", response).
			Msg("Content service returned error")
		return nil, fmt.Errorf("content service returned error: %v", response["message"])
	}

	// 返回内容数据
	if data, ok := response["data"]; ok {
		s.logger.Info().
			Str("content_id", contentID).
			Msg("Successfully fetched content detail from content service")
		return data, nil
	}

	return nil, fmt.Errorf("no data in content service response")
}

// GetContentFee 获取内容类型费用
func (s *ContentService) GetContentFee(contentType string) (float64, error) {
	// 首先尝试从配置服务获取
	if s.contentTypeConfig.IsValidContentType(contentType) {
		fee := s.contentTypeConfig.GetContentTypeFee(contentType)
		return fee, nil
	}

	// 如果配置服务中没有，尝试从默认配置获取
	if fee, exists := DefaultContentTypeFees[contentType]; exists {
		return fee, nil
	}

	return 0, fmt.Errorf("unsupported content type: %s", contentType)
}

// GetContentFeeFromContract 从合约获取内容类型费用
func (s *ContentService) GetContentFeeFromContract(ctx context.Context, contentType string) (float64, error) {
	// 调用ContentRegistry合约获取费用
	feeBigInt, err := s.contentRegistryService.GetContentFee(ctx, contentType)
	if err != nil {
		s.logger.Warn().Err(err).Str("content_type", contentType).Msg("Failed to get fee from contract, using default")
		// 使用默认费用作为后备
		if fee, exists := DefaultContentTypeFees[contentType]; exists {
			return fee, nil
		}
		return 0.1, nil // 默认费用
	}

	// 将wei转换为PAT (18位小数)
	feeFloat, _ := new(big.Float).SetInt(feeBigInt).Float64()
	feeInPAT := feeFloat / 1e18

	s.logger.Debug().
		Str("content_type", contentType).
		Float64("fee_pat", feeInPAT).
		Msg("Got content fee from contract")

	return feeInPAT, nil
}

// GetSupportedContentTypes 获取支持的内容类型
func (s *ContentService) GetSupportedContentTypes() map[string]float64 {
	result := make(map[string]float64)

	// 首先从默认配置中获取所有类型
	for typeName, fee := range DefaultContentTypeFees {
		result[typeName] = fee
	}
	s.logger.Debug().Int("default_types_count", len(result)).Msg("Added default content types")

	// 然后从配置服务中获取类型，可能会覆盖默认配置
	configTypes := s.contentTypeConfig.GetAllContentTypes()
	for typeName, def := range configTypes {
		result[typeName] = def.DefaultFee
	}
	s.logger.Debug().Int("config_types_count", len(configTypes)).Int("total_types_count", len(result)).Msg("Added config content types")

	s.logger.Info().Interface("supported_types", result).Msg("Final supported content types")
	return result
}

// GetActiveContentTypes 获取激活的内容类型
func (s *ContentService) GetActiveContentTypes(ctx context.Context) ([]ContentTypeInfo, error) {
	// 从配置服务获取内容类型列表
	types := s.contentTypeConfig.GetContentTypesList()

	// 添加时间戳
	for i := range types {
		types[i].CreatedAt = time.Now().Format(time.RFC3339)
	}

	return types, nil
}

// AddContentType 添加新的内容类型 (管理员功能)
func (s *ContentService) AddContentType(ctx context.Context, contentType string, defaultFee float64, description string) error {
	// TODO: 这里应该调用ContentRegistry合约的addContentType方法
	s.logger.Info().
		Str("content_type", contentType).
		Float64("default_fee", defaultFee).
		Str("description", description).
		Msg("Adding new content type")

	return fmt.Errorf("not implemented: contract integration required")
}

// UpdateContentType 更新内容类型 (管理员功能)
func (s *ContentService) UpdateContentType(ctx context.Context, contentType string, newFee float64, newDescription string) error {
	// TODO: 这里应该调用ContentRegistry合约的updateContentType方法
	s.logger.Info().
		Str("content_type", contentType).
		Float64("new_fee", newFee).
		Str("new_description", newDescription).
		Msg("Updating content type")

	return fmt.Errorf("not implemented: contract integration required")
}

// getContentTypeDescription 获取内容类型描述
func (s *ContentService) getContentTypeDescription(contentType string) string {
	descriptions := map[string]string{
		"video":       "视频内容",
		"novel":       "小说内容",
		"short_drama": "短剧内容",
		"anime":       "动漫内容",
		"manga":       "漫画内容",
		"music":       "音乐内容",
		"article":     "文章内容",
		"short_video": "短视频内容",
		"image":       "图片内容",
	}

	if desc, exists := descriptions[contentType]; exists {
		return desc
	}
	return "自定义内容类型"
}

// GetContentTypeRecommendedAttributes 获取内容类型的推荐属性 (从配置文件)
func (s *ContentService) GetContentTypeRecommendedAttributes(contentType string) map[string]interface{} {
	return s.contentTypeConfig.GetRecommendedAttributes(contentType)
}

// ============ 元数据构建辅助方法 ============

// buildCreatorsFromRequest 从请求构建创作者信息
func (s *ContentService) buildCreatorsFromRequest(req *ContentPublishWithIPFSRequest) []string {
	var creators []string

	// 优先使用扩展字段中的创作者信息
	if len(req.Creators) > 0 {
		creators = append(creators, req.Creators...)
	} else {
		// 从原有字段构建创作者信息 (向后兼容)
		if req.Director != "" {
			creators = append(creators, "director:"+req.Director)
		}

		if req.Publisher != "" {
			creators = append(creators, "publisher:"+req.Publisher)
		}

		// 处理演员列表
		for _, actor := range req.Cast {
			if actor != "" {
				creators = append(creators, "actor:"+actor)
			}
		}
	}

	return creators
}

// buildContributorsFromRequest 从请求构建贡献者信息
func (s *ContentService) buildContributorsFromRequest(req *ContentPublishWithIPFSRequest) []string {
	var contributors []string

	// 优先使用扩展字段中的贡献者信息
	if len(req.Contributors) > 0 {
		contributors = append(contributors, req.Contributors...)
	}

	// 处理审核员列表 (总是添加)
	for _, reviewer := range req.Reviewers {
		if reviewer != "" {
			contributors = append(contributors, "reviewer:"+reviewer)
		}
	}

	return contributors
}

// buildAttributesFromRequest 从请求构建扩展属性
func (s *ContentService) buildAttributesFromRequest(req *ContentPublishWithIPFSRequest) (map[string]string, map[string]int64) {
	attributes := make(map[string]string)
	numerics := make(map[string]int64)

	// 优先使用请求中的扩展属性
	if req.Attributes != nil {
		for key, value := range req.Attributes {
			attributes[key] = value
		}
	}

	if req.Numerics != nil {
		for key, value := range req.Numerics {
			numerics[key] = value
		}
	}

	// 如果没有提供扩展属性，则根据内容类型添加推荐属性
	if len(attributes) == 0 {
		switch req.ContentType {
		case "video", "short_video":
			// 视频类型属性
			if req.Description != "" {
				attributes["genre"] = s.extractGenreFromDescription(req.Description)
			}
			attributes["format"] = "mp4" // 默认格式

		case "novel":
			// 小说类型属性
			attributes["status"] = "published"
			if req.Description != "" {
				attributes["genre"] = s.extractGenreFromDescription(req.Description)
			}

		case "music":
			// 音乐类型属性
			attributes["format"] = "mp3" // 默认格式
			if req.Description != "" {
				attributes["genre"] = s.extractGenreFromDescription(req.Description)
			}

		case "article":
			// 文章类型属性
			attributes["category"] = "general"
			if req.Description != "" {
				attributes["category"] = s.extractCategoryFromDescription(req.Description)
			}

		case "image":
			// 图片类型属性
			attributes["format"] = "jpg" // 默认格式
			attributes["category"] = "general"
			if req.Description != "" {
				attributes["category"] = s.extractCategoryFromDescription(req.Description)
			}

		default:
			// 通用属性
			attributes["category"] = "general"
		}
	}

	// 添加发布日期相关的数值属性 (如果没有提供)
	if _, exists := numerics["release_timestamp"]; !exists && !req.ReleaseDate.IsZero() {
		numerics["release_timestamp"] = req.ReleaseDate.Unix()
	}

	if _, exists := numerics["onchain_timestamp"]; !exists && !req.OnChainDate.IsZero() {
		numerics["onchain_timestamp"] = req.OnChainDate.Unix()
	}

	return attributes, numerics
}

// extractGenreFromDescription 从描述中提取类型信息 (简单实现)
func (s *ContentService) extractGenreFromDescription(description string) string {
	// 这里可以实现更复杂的NLP分析
	// 暂时返回默认值
	return "general"
}

// extractCategoryFromDescription 从描述中提取分类信息 (简单实现)
func (s *ContentService) extractCategoryFromDescription(description string) string {
	// 这里可以实现更复杂的分类逻辑
	// 暂时返回默认值
	return "general"
}

// ============ 新的IPFS支持的内容上链方法 ============

// ContentPublishWithIPFSRequest 支持IPFS的内容发布请求 - 扩展元数据支持
type ContentPublishWithIPFSRequest struct {
	ContentPublishRequest
	ContentData []byte `json:"content_data,omitempty"` // 内容数据 (可选，用于上传到IPFS)
	IPFSHash    string `json:"ipfs_hash,omitempty"`    // 已有的IPFS哈希 (可选)

	// 扩展元数据字段 (灵活支持各种内容类型)
	Creators     []string          `json:"creators,omitempty"`     // 创作者列表 (格式: "role:name")
	Contributors []string          `json:"contributors,omitempty"` // 贡献者列表 (格式: "role:name")
	Attributes   map[string]string `json:"attributes,omitempty"`   // 字符串属性
	Numerics     map[string]int64  `json:"numerics,omitempty"`     // 数值属性
}

// PublishContentWithIPFS 发布内容到IPFS和区块链 - 学习xLog的完整上链方案
func (s *ContentService) PublishContentWithIPFS(ctx context.Context, req *ContentPublishWithIPFSRequest) (*models.ContentRecord, error) {
	s.logger.Info().
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("publisher_ksuid", req.PublisherKSUID).
		Msg("开始发布内容到IPFS和区块链")

	// 1. 验证请求参数
	if err := s.validateContentRequest(&req.ContentPublishRequest); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 2. 处理IPFS上传
	var contentIPFSHash string
	var metadataIPFSHash string
	var err error

	// 如果提供了内容数据，上传到IPFS
	if len(req.ContentData) > 0 {
		s.logger.Info().Msg("上传内容数据到IPFS")

		filename := fmt.Sprintf("%s_%s.%s", req.ContentType, req.OnChainID, s.getFileExtension(req.ContentType))
		uploadResp, err := s.ipfsService.UploadContent(ctx, req.ContentData, filename)
		if err != nil {
			return nil, fmt.Errorf("上传内容到IPFS失败: %w", err)
		}
		contentIPFSHash = uploadResp.Hash

		s.logger.Info().
			Str("ipfs_hash", contentIPFSHash).
			Str("size", uploadResp.Size).
			Msg("内容上传到IPFS成功")
	} else if req.IPFSHash != "" {
		// 使用提供的IPFS哈希
		if !s.ipfsService.ValidateIPFSHash(req.IPFSHash) {
			return nil, fmt.Errorf("无效的IPFS哈希: %s", req.IPFSHash)
		}
		contentIPFSHash = req.IPFSHash
	} else {
		return nil, fmt.Errorf("必须提供内容数据或IPFS哈希")
	}

	// 3. 创建并上传元数据到IPFS
	s.logger.Info().Msg("创建并上传元数据到IPFS")

	// 构建创作者和贡献者信息 (合并请求中的扩展字段和原有字段)
	creators := s.buildCreatorsFromRequest(req)
	contributors := s.buildContributorsFromRequest(req)

	// 构建扩展属性 (合并请求中的扩展字段和自动生成的属性)
	attributes, numerics := s.buildAttributesFromRequest(req)

	metadata := &ContentMetadata{
		Title:         req.Title,
		Description:   req.Description,
		OriginalTitle: req.OriginalTitle,
		ContentType:   req.ContentType,
		LicenseNumber: req.LicenseNumber,
		Reviewers:     req.Reviewers,
		Version:       1,
		Creators:      creators,
		Contributors:  contributors,
		Attributes:    attributes,
		Numerics:      numerics,
	}

	metadataResp, err := s.ipfsService.UploadMetadata(ctx, metadata)
	if err != nil {
		return nil, fmt.Errorf("上传元数据到IPFS失败: %w", err)
	}
	metadataIPFSHash = metadataResp.Hash

	s.logger.Info().
		Str("metadata_hash", metadataIPFSHash).
		Msg("元数据上传到IPFS成功")

	// 4. 获取内容类型费用
	fee, err := s.GetContentFeeFromContract(ctx, req.ContentType)
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get content fee from contract, using default")
		fee = 0.1 // 默认费用
	}

	// 5. 调用区块链合约注册内容
	s.logger.Info().
		Str("content_hash", contentIPFSHash).
		Str("metadata_hash", metadataIPFSHash).
		Float64("pat_fee", fee).
		Msg("调用区块链合约注册内容")

	// 调用ContentRegistry合约注册内容
	s.logger.Info().Msg("调用ContentRegistry合约注册内容")

	registryReq := &RegisterContentRequest{
		ContentType: req.ContentType,
		Title:       req.Title,
		IPFSHash:    contentIPFSHash,
		MetadataURI: metadataIPFSHash,
		Reviewers:   req.Reviewers,
	}

	registryResult, err := s.contentRegistryService.RegisterContent(ctx, registryReq)
	if err != nil {
		return nil, fmt.Errorf("ContentRegistry合约调用失败: %w", err)
	}

	txHash := registryResult.TxHash
	blockNumber := registryResult.BlockNumber

	s.logger.Info().
		Str("tx_hash", txHash).
		Uint64("block_number", blockNumber).
		Interface("content_id", registryResult.ContentID).
		Float64("amount", fee).
		Msg("ContentRegistry合约调用成功")

	// 构造转账响应结构
	transferResp := &WalletTransferResponse{
		Data: struct {
			TransactionHash string `json:"transaction_hash"`
			BlockNumber     uint64 `json:"block_number"`
			Status          string `json:"status"`
		}{
			TransactionHash: txHash,
			BlockNumber:     blockNumber,
			Status:          "confirmed",
		},
	}

	// 6. 创建内容记录
	s.logger.Info().
		Str("tx_hash", transferResp.Data.TransactionHash).
		Msg("创建内容记录")

	// 序列化数组字段
	castJSON, _ := json.Marshal(req.Cast)
	reviewersJSON, _ := json.Marshal(req.Reviewers)

	contentRecord := &models.ContentRecord{
		ContentID:      req.ID,
		Title:          req.Title,
		OriginalTitle:  req.OriginalTitle,
		Description:    req.Description,
		ContentType:    req.ContentType,
		LicenseNumber:  req.LicenseNumber,
		Cast:           string(castJSON),
		Director:       req.Director,
		Publisher:      req.Publisher,
		PublisherKSUID: req.PublisherKSUID,
		ReleaseDate:    req.ReleaseDate,
		OnChainDate:    req.OnChainDate,
		OnChainID:      req.OnChainID,
		Reviewers:      string(reviewersJSON),
		FileKSUID:      req.FileKSUID,
		FileMD5:        req.FileMD5,

		// 新增IPFS相关字段
		IPFSHash:     contentIPFSHash,
		MetadataIPFS: metadataIPFSHash,

		// 区块链相关
		TransactionHash: transferResp.Data.TransactionHash,
		BlockNumber:     transferResp.Data.BlockNumber,
		PATFee:          fmt.Sprintf("%.8f", fee),
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 生成内容哈希
	contentHash, err := s.generateContentHash(&req.ContentPublishRequest)
	if err != nil {
		return nil, fmt.Errorf("生成内容哈希失败: %w", err)
	}
	contentRecord.ContentHash = contentHash

	// 保存到数据库
	err = s.contentRepo.CreateContentRecord(ctx, contentRecord)
	if err != nil {
		return nil, fmt.Errorf("保存内容记录失败: %w", err)
	}

	s.logger.Info().
		Str("content_id", contentRecord.ContentID).
		Str("ipfs_hash", contentIPFSHash).
		Str("metadata_hash", metadataIPFSHash).
		Str("tx_hash", transferResp.Data.TransactionHash).
		Msg("内容发布到IPFS和区块链成功")

	return contentRecord, nil
}

// getFileExtension 根据内容类型获取文件扩展名
func (s *ContentService) getFileExtension(contentType string) string {
	switch contentType {
	case "video", "short_video":
		return "mp4"
	case "music":
		return "mp3"
	case "article", "novel":
		return "md"
	case "manga":
		return "pdf"
	case "image":
		return "jpg"
	default:
		return "json"
	}
}

// validateContentRequest 验证内容请求参数
func (s *ContentService) validateContentRequest(req *ContentPublishRequest) error {
	if req.Title == "" {
		return fmt.Errorf("标题不能为空")
	}

	if req.ContentType == "" {
		return fmt.Errorf("内容类型不能为空")
	}

	if req.PublisherKSUID == "" {
		return fmt.Errorf("发布者KSUID不能为空")
	}

	if req.OnChainID == "" {
		return fmt.Errorf("上链ID不能为空")
	}

	if len(req.Reviewers) == 0 {
		return fmt.Errorf("审核员列表不能为空")
	}

	// 验证内容类型是否支持
	if !s.contentTypeConfig.IsValidContentType(req.ContentType) {
		// 如果配置服务中没有，检查默认配置
		if _, exists := DefaultContentTypeFees[req.ContentType]; !exists {
			return fmt.Errorf("不支持的内容类型: %s", req.ContentType)
		}
	}

	return nil
}

// UploadToIPFS 上传文件到IPFS
func (s *ContentService) UploadToIPFS(ctx context.Context, fileContent []byte, filename, metadataStr string) (*IPFSUploadResponse, error) {
	s.logger.Info().
		Str("filename", filename).
		Int("size", len(fileContent)).
		Msg("Uploading file to IPFS")

	// 检查IPFS服务是否可用
	if s.ipfsService == nil {
		return nil, fmt.Errorf("IPFS service not available")
	}

	// 直接使用IPFS服务上传文件
	result, err := s.ipfsService.UploadContent(ctx, fileContent, filename)
	if err != nil {
		return nil, fmt.Errorf("failed to upload to IPFS: %w", err)
	}

	s.logger.Info().
		Str("filename", filename).
		Str("ipfs_hash", result.Hash).
		Str("size", result.Size).
		Msg("File uploaded to IPFS successfully")

	return result, nil
}
