package service

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"

	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

type GovernanceSyncService struct {
	client     *ethclient.Client
	repo       *repository.GovernanceRepository
	contracts  map[string]common.Address
	abis       map[string]*abi.ABI
	lastBlocks map[string]uint64
}

func NewGovernanceSyncService(
	client *ethclient.Client,
	repo *repository.GovernanceRepository,
) *GovernanceSyncService {
	return &GovernanceSyncService{
		client:     client,
		repo:       repo,
		contracts:  make(map[string]common.Address),
		abis:       make(map[string]*abi.ABI),
		lastBlocks: make(map[string]uint64),
	}
}

// SetContract 设置合约地址和ABI
func (s *GovernanceSyncService) SetContract(name, address, abiJSON string) error {
	contractABI, err := abi.JSON(strings.NewReader(abiJSON))
	if err != nil {
		return fmt.Errorf("解析ABI失败: %v", err)
	}

	s.contracts[name] = common.HexToAddress(address)
	s.abis[name] = &contractABI
	return nil
}

// SyncProposals 同步提案数据
func (s *GovernanceSyncService) SyncProposals(ctx context.Context) error {
	proposalManagerAddr, exists := s.contracts["ProposalManager"]
	if !exists {
		return fmt.Errorf("ProposalManager合约地址未设置")
	}

	proposalManagerABI, exists := s.abis["ProposalManager"]
	if !exists {
		return fmt.Errorf("ProposalManager ABI未设置")
	}

	// 获取最新区块号
	latestBlock, err := s.client.BlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("获取最新区块号失败: %v", err)
	}

	// 获取上次同步的区块号
	fromBlock := s.lastBlocks["ProposalManager"]
	if fromBlock == 0 {
		// 如果是第一次同步，从最近1000个区块开始
		if latestBlock > 1000 {
			fromBlock = latestBlock - 1000
		}
	}

	// 查询ProposalCreated事件
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(latestBlock)),
		Addresses: []common.Address{proposalManagerAddr},
		Topics: [][]common.Hash{
			{proposalManagerABI.Events["ProposalCreated"].ID},
		},
	}

	logs, err := s.client.FilterLogs(ctx, query)
	if err != nil {
		return fmt.Errorf("查询事件日志失败: %v", err)
	}

	// 处理每个事件
	for _, vLog := range logs {
		if err := s.processProposalCreatedEvent(ctx, vLog, proposalManagerABI); err != nil {
			log.Printf("处理ProposalCreated事件失败: %v", err)
			continue
		}
	}

	// 更新最后同步的区块号
	s.lastBlocks["ProposalManager"] = latestBlock

	return nil
}

// processProposalCreatedEvent 处理提案创建事件
func (s *GovernanceSyncService) processProposalCreatedEvent(
	ctx context.Context,
	vLog types.Log,
	contractABI *abi.ABI,
) error {
	// 解析事件数据
	event := struct {
		ProposalId   *big.Int
		Proposer     common.Address
		ProposalType uint8
		StartTime    *big.Int
		EndTime      *big.Int
		Title        string
		Description  string
	}{}

	err := contractABI.UnpackIntoInterface(&event, "ProposalCreated", vLog.Data)
	if err != nil {
		return fmt.Errorf("解析事件数据失败: %v", err)
	}

	// 检查提案是否已存在
	existingProposal, err := s.repo.GetProposal(ctx, event.ProposalId.Uint64())
	if err != nil {
		return fmt.Errorf("查询现有提案失败: %v", err)
	}

	if existingProposal != nil {
		// 提案已存在，跳过
		return nil
	}

	// 创建新提案记录
	proposal := &models.Proposal{
		ProposalID:       event.ProposalId.Uint64(),
		Proposer:         event.Proposer.Hex(),
		Title:            event.Title,
		Description:      event.Description,
		Category:         getProposalCategory(event.ProposalType),
		Status:           "active",
		ForVotes:         "0",
		AgainstVotes:     "0",
		AbstainVotes:     "0",
		TotalVotes:       "0",
		QuorumRequired:   "1000000", // 默认值，应该从合约读取
		MajorityRequired: 0.5,       // 默认值，应该从合约读取
		StartBlock:       uint64(vLog.BlockNumber),
		EndBlock:         uint64(vLog.BlockNumber) + 40320, // 假设7天 = 40320个区块
		CreatedAt:        time.Now(),
	}

	// 保存提案
	if err := s.repo.CreateProposal(ctx, proposal); err != nil {
		return fmt.Errorf("保存提案失败: %v", err)
	}

	log.Printf("同步新提案: ID=%d, 标题=%s", proposal.ProposalID, proposal.Title)
	return nil
}

// SyncVotes 同步投票数据
func (s *GovernanceSyncService) SyncVotes(ctx context.Context) error {
	votingAddr, exists := s.contracts["Voting"]
	if !exists {
		return fmt.Errorf("Voting合约地址未设置")
	}

	votingABI, exists := s.abis["Voting"]
	if !exists {
		return fmt.Errorf("Voting ABI未设置")
	}

	// 获取最新区块号
	latestBlock, err := s.client.BlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("获取最新区块号失败: %v", err)
	}

	// 获取上次同步的区块号
	fromBlock := s.lastBlocks["Voting"]
	if fromBlock == 0 {
		if latestBlock > 1000 {
			fromBlock = latestBlock - 1000
		}
	}

	// 查询VoteCast事件
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(latestBlock)),
		Addresses: []common.Address{votingAddr},
		Topics: [][]common.Hash{
			{votingABI.Events["VoteCast"].ID},
		},
	}

	logs, err := s.client.FilterLogs(ctx, query)
	if err != nil {
		return fmt.Errorf("查询投票事件失败: %v", err)
	}

	// 处理每个投票事件
	for _, vLog := range logs {
		if err := s.processVoteCastEvent(ctx, vLog, votingABI); err != nil {
			log.Printf("处理VoteCast事件失败: %v", err)
			continue
		}
	}

	// 更新最后同步的区块号
	s.lastBlocks["Voting"] = latestBlock

	return nil
}

// processVoteCastEvent 处理投票事件
func (s *GovernanceSyncService) processVoteCastEvent(
	ctx context.Context,
	vLog types.Log,
	contractABI *abi.ABI,
) error {
	// 解析事件数据
	event := struct {
		ProposalId  *big.Int
		Voter       common.Address
		Support     uint8
		VotingPower *big.Int
		Reason      string
	}{}

	err := contractABI.UnpackIntoInterface(&event, "VoteCast", vLog.Data)
	if err != nil {
		return fmt.Errorf("解析投票事件数据失败: %v", err)
	}

	// 检查投票是否已存在
	existingVote, err := s.repo.GetVote(ctx, event.ProposalId.Uint64(), event.Voter.Hex())
	if err != nil {
		return fmt.Errorf("查询现有投票失败: %v", err)
	}

	if existingVote != nil {
		// 投票已存在，跳过
		return nil
	}

	// 创建新投票记录
	vote := &models.Vote{
		ProposalID:      event.ProposalId.Uint64(),
		Voter:           event.Voter.Hex(),
		Support:         int(event.Support),
		VotingPower:     event.VotingPower.String(),
		Reason:          event.Reason,
		BlockNumber:     uint64(vLog.BlockNumber),
		TransactionHash: vLog.TxHash.Hex(),
		Timestamp:       time.Now(),
	}

	// 保存投票
	if err := s.repo.CreateVote(ctx, vote); err != nil {
		return fmt.Errorf("保存投票失败: %v", err)
	}

	// 更新提案的投票统计
	if err := s.updateProposalVoteStats(ctx, event.ProposalId.Uint64()); err != nil {
		log.Printf("更新提案投票统计失败: %v", err)
	}

	log.Printf("同步新投票: 提案ID=%d, 投票者=%s, 支持=%d",
		vote.ProposalID, vote.Voter, vote.Support)
	return nil
}

// updateProposalVoteStats 更新提案投票统计
func (s *GovernanceSyncService) updateProposalVoteStats(ctx context.Context, proposalID uint64) error {
	// 获取提案的所有投票
	votes, _, err := s.repo.GetProposalVotes(ctx, proposalID, 1, 10000)
	if err != nil {
		return fmt.Errorf("获取提案投票失败: %v", err)
	}

	// 计算投票统计
	var forVotes, againstVotes, abstainVotes big.Int
	for _, vote := range votes {
		votingPower, ok := new(big.Int).SetString(vote.VotingPower, 10)
		if !ok {
			continue
		}

		switch vote.Support {
		case 1: // 支持
			forVotes.Add(&forVotes, votingPower)
		case 0: // 反对
			againstVotes.Add(&againstVotes, votingPower)
		case 2: // 弃权
			abstainVotes.Add(&abstainVotes, votingPower)
		}
	}

	// 计算总投票数
	totalVotes := new(big.Int)
	totalVotes.Add(&forVotes, &againstVotes)
	totalVotes.Add(totalVotes, &abstainVotes)

	// 更新提案
	proposal, err := s.repo.GetProposal(ctx, proposalID)
	if err != nil {
		return fmt.Errorf("获取提案失败: %v", err)
	}

	if proposal != nil {
		proposal.ForVotes = forVotes.String()
		proposal.AgainstVotes = againstVotes.String()
		proposal.AbstainVotes = abstainVotes.String()
		proposal.TotalVotes = totalVotes.String()

		if err := s.repo.UpdateProposal(ctx, proposal); err != nil {
			return fmt.Errorf("更新提案失败: %v", err)
		}
	}

	return nil
}

// StartSyncWorker 启动同步工作器
func (s *GovernanceSyncService) StartSyncWorker(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := s.SyncProposals(ctx); err != nil {
				log.Printf("同步提案失败: %v", err)
			}

			if err := s.SyncVotes(ctx); err != nil {
				log.Printf("同步投票失败: %v", err)
			}
		}
	}
}

// getProposalCategory 根据提案类型获取分类
func getProposalCategory(proposalType uint8) string {
	switch proposalType {
	case 0:
		return "parameter"
	case 1:
		return "funding"
	case 2:
		return "upgrade"
	case 3:
		return "membership"
	case 4:
		return "emergency"
	default:
		return "other"
	}
}
