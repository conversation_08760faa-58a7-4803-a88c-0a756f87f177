package service

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// RankingService 排行榜服务
type RankingService struct {
	stakingRepo    *repository.StakingRepository
	tokenRepo      *repository.TokenRepository
	governanceRepo *repository.GovernanceRepository
	cacheManager   *cache.CacheManager
	logger         zerolog.Logger
}

// NewRankingService 创建新的排行榜服务
func NewRankingService(
	stakingRepo *repository.StakingRepository,
	tokenRepo *repository.TokenRepository,
	governanceRepo *repository.GovernanceRepository,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *RankingService {
	return &RankingService{
		stakingRepo:    stakingRepo,
		tokenRepo:      tokenRepo,
		governanceRepo: governanceRepo,
		cacheManager:   cacheManager,
		logger:         logger.With().Str("component", "ranking_service").Logger(),
	}
}

// StakingRankingItem 质押排行榜项目
type StakingRankingItem struct {
	Rank        int                 `json:"rank"`
	UserAddress string              `json:"user_address"`
	Amount      string              `json:"amount"`
	MiningPower string              `json:"mining_power"`
	Level       models.StakingLevel `json:"level"`
	LevelName   string              `json:"level_name"`
	Rewards     string              `json:"rewards"`
	StakeTime   time.Time           `json:"stake_time"`
	LastUpdate  time.Time           `json:"last_update"`
}

// TokenHolderRankingItem 代币持有者排行榜项目
type TokenHolderRankingItem struct {
	Rank        int       `json:"rank"`
	UserAddress string    `json:"user_address"`
	Balance     string    `json:"balance"`
	Percentage  float64   `json:"percentage"`
	LastUpdate  time.Time `json:"last_update"`
}

// VotingPowerRankingItem 投票权重排行榜项目
type VotingPowerRankingItem struct {
	Rank         int       `json:"rank"`
	UserAddress  string    `json:"user_address"`
	VotingPower  string    `json:"voting_power"`
	StakedAmount string    `json:"staked_amount"`
	Level        int       `json:"level"`
	Multiplier   float64   `json:"multiplier"`
	LastUpdate   time.Time `json:"last_update"`
}

// RankingResponse 排行榜响应
type RankingResponse struct {
	Rankings   interface{} `json:"rankings"`
	Total      int         `json:"total"`
	UpdateTime time.Time   `json:"update_time"`
	NextUpdate time.Time   `json:"next_update"`
}

// GetStakingRanking 获取质押排行榜
func (s *RankingService) GetStakingRanking(ctx context.Context, limit int) (*RankingResponse, error) {
	if limit < 1 || limit > 1000 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("staking_ranking:%d", limit)
	var rankings []StakingRankingItem
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &rankings); err == nil {
			return &RankingResponse{
				Rankings:   rankings,
				Total:      len(rankings),
				UpdateTime: time.Now(),
				NextUpdate: time.Now().Add(10 * time.Minute),
			}, nil
		}
	}

	// 从数据库获取质押排行榜
	leaderboard, err := s.stakingRepo.GetLeaderboard(ctx, "amount", 0, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get staking leaderboard: %w", err)
	}

	rankings = make([]StakingRankingItem, 0, len(leaderboard))
	for i, user := range leaderboard {
		rankings = append(rankings, StakingRankingItem{
			Rank:        i + 1,
			UserAddress: user.UserAddress,
			Amount:      user.Amount.String(),
			MiningPower: user.MiningPower.String(),
			Level:       user.StakingLevel,
			LevelName:   s.getLevelName(user.StakingLevel),
			Rewards:     user.TotalRewardsClaimed.String(),
			StakeTime:   user.StartTime,
			LastUpdate:  time.Now(), // 使用当前时间作为默认值
		})
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, rankings, 10*time.Minute)
	}

	return &RankingResponse{
		Rankings:   rankings,
		Total:      len(rankings),
		UpdateTime: time.Now(),
		NextUpdate: time.Now().Add(10 * time.Minute),
	}, nil
}

// GetTokenHolderRanking 获取代币持有者排行榜
func (s *RankingService) GetTokenHolderRanking(ctx context.Context, tokenType string, limit int) (*RankingResponse, error) {
	tokenType = strings.ToUpper(tokenType)
	if tokenType != "PXT" && tokenType != "PAT" {
		return nil, fmt.Errorf("unsupported token type: %s", tokenType)
	}

	if limit < 1 || limit > 1000 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("token_holder_ranking:%s:%d", tokenType, limit)
	var rankings []TokenHolderRankingItem
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &rankings); err == nil {
			return &RankingResponse{
				Rankings:   rankings,
				Total:      len(rankings),
				UpdateTime: time.Now(),
				NextUpdate: time.Now().Add(15 * time.Minute),
			}, nil
		}
	}

	// 从数据库获取持币者排行榜
	holders, err := s.tokenRepo.GetTopHolders(ctx, tokenType, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get token holders: %w", err)
	}

	// 获取总供应量用于计算百分比
	supply, err := s.tokenRepo.GetTokenSupply(ctx, tokenType)
	if err != nil {
		s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to get token supply")
	}

	var totalSupply *big.Int
	if supply != nil {
		totalSupply, _ = new(big.Int).SetString(supply.TotalSupply, 10)
	}

	rankings = make([]TokenHolderRankingItem, 0, len(holders))
	for _, holder := range holders {
		percentage := 0.0
		if totalSupply != nil && totalSupply.Cmp(big.NewInt(0)) > 0 {
			balance, _ := new(big.Int).SetString(holder.Balance, 10)
			if balance != nil {
				// 计算百分比 (balance / totalSupply * 100)
				ratio := new(big.Float).Quo(new(big.Float).SetInt(balance), new(big.Float).SetInt(totalSupply))
				percentage, _ = ratio.Float64()
				percentage *= 100
			}
		}

		rankings = append(rankings, TokenHolderRankingItem{
			Rank:        holder.Rank,
			UserAddress: holder.UserAddress,
			Balance:     holder.Balance,
			Percentage:  percentage,
			LastUpdate:  holder.LastUpdated,
		})
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, rankings, 15*time.Minute)
	}

	return &RankingResponse{
		Rankings:   rankings,
		Total:      len(rankings),
		UpdateTime: time.Now(),
		NextUpdate: time.Now().Add(15 * time.Minute),
	}, nil
}

// GetVotingPowerRanking 获取投票权重排行榜
func (s *RankingService) GetVotingPowerRanking(ctx context.Context, limit int) (*RankingResponse, error) {
	if limit < 1 || limit > 1000 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("voting_power_ranking:%d", limit)
	var rankings []VotingPowerRankingItem
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &rankings); err == nil {
			return &RankingResponse{
				Rankings:   rankings,
				Total:      len(rankings),
				UpdateTime: time.Now(),
				NextUpdate: time.Now().Add(20 * time.Minute),
			}, nil
		}
	}

	// 从数据库获取投票权重排行榜
	votingPowers, err := s.governanceRepo.GetTopVotingPowers(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get voting powers: %w", err)
	}

	rankings = make([]VotingPowerRankingItem, 0, len(votingPowers))
	for i, power := range votingPowers {
		rankings = append(rankings, VotingPowerRankingItem{
			Rank:         i + 1,
			UserAddress:  power.UserAddress,
			VotingPower:  power.TotalPower,
			StakedAmount: power.StakedAmount,
			Level:        power.StakingLevel,
			Multiplier:   power.LevelMultiplier,
			LastUpdate:   power.LastUpdated,
		})
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, rankings, 20*time.Minute)
	}

	return &RankingResponse{
		Rankings:   rankings,
		Total:      len(rankings),
		UpdateTime: time.Now(),
		NextUpdate: time.Now().Add(20 * time.Minute),
	}, nil
}

// GetUserRanking 获取用户在各个排行榜中的排名
func (s *RankingService) GetUserRanking(ctx context.Context, userAddress string) (*UserRankingInfo, error) {
	userAddress = strings.ToLower(userAddress)

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("user_ranking:%s", userAddress)
	var ranking UserRankingInfo
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &ranking); err == nil {
			return &ranking, nil
		}
	}

	ranking = UserRankingInfo{
		UserAddress: userAddress,
	}

	// 并发获取各种排名
	stakingRankChan := make(chan int, 1)
	pxtRankChan := make(chan int, 1)
	patRankChan := make(chan int, 1)
	votingRankChan := make(chan int, 1)

	// 获取质押排名
	go func() {
		if rank, err := s.stakingRepo.GetUserRanking(ctx, userAddress); err == nil {
			stakingRankChan <- int(rank.Rank)
		} else {
			stakingRankChan <- 0
		}
	}()

	// 获取PXT持币排名
	go func() {
		// TODO: 实现获取用户在持币排行榜中的排名
		pxtRankChan <- 0
	}()

	// 获取PAT持币排名
	go func() {
		// TODO: 实现获取用户在持币排行榜中的排名
		patRankChan <- 0
	}()

	// 获取投票权重排名
	go func() {
		// TODO: 实现获取用户在投票权重排行榜中的排名
		votingRankChan <- 0
	}()

	// 等待所有结果
	for i := 0; i < 4; i++ {
		select {
		case stakingRank := <-stakingRankChan:
			ranking.StakingRank = stakingRank
		case pxtRank := <-pxtRankChan:
			ranking.PXTHolderRank = pxtRank
		case patRank := <-patRankChan:
			ranking.PATHolderRank = patRank
		case votingRank := <-votingRankChan:
			ranking.VotingPowerRank = votingRank
		case <-time.After(5 * time.Second):
			s.logger.Warn().Str("user", userAddress).Msg("Timeout getting user ranking")
		}
	}

	ranking.LastUpdated = time.Now()

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, ranking, 5*time.Minute)
	}

	return &ranking, nil
}

// UserRankingInfo 用户排名信息
type UserRankingInfo struct {
	UserAddress     string    `json:"user_address"`
	StakingRank     int       `json:"staking_rank"`
	PXTHolderRank   int       `json:"pxt_holder_rank"`
	PATHolderRank   int       `json:"pat_holder_rank"`
	VotingPowerRank int       `json:"voting_power_rank"`
	LastUpdated     time.Time `json:"last_updated"`
}

// RefreshRankings 刷新所有排行榜缓存
func (s *RankingService) RefreshRankings(ctx context.Context) error {
	s.logger.Info().Msg("Refreshing all rankings cache...")

	// 清除相关缓存
	if s.cacheManager != nil {
		// 清除质押排行榜缓存
		for limit := 10; limit <= 1000; limit += 10 {
			cacheKey := fmt.Sprintf("staking_ranking:%d", limit)
			s.cacheManager.Delete(ctx, cacheKey)
		}

		// 清除代币持有者排行榜缓存
		for _, tokenType := range []string{"PXT", "PAT"} {
			for limit := 10; limit <= 1000; limit += 10 {
				cacheKey := fmt.Sprintf("token_holder_ranking:%s:%d", tokenType, limit)
				s.cacheManager.Delete(ctx, cacheKey)
			}
		}

		// 清除投票权重排行榜缓存
		for limit := 10; limit <= 1000; limit += 10 {
			cacheKey := fmt.Sprintf("voting_power_ranking:%d", limit)
			s.cacheManager.Delete(ctx, cacheKey)
		}
	}

	// 预热缓存 - 获取常用的排行榜
	go func() {
		s.GetStakingRanking(context.Background(), 100)
		s.GetTokenHolderRanking(context.Background(), "PXT", 100)
		s.GetTokenHolderRanking(context.Background(), "PAT", 100)
		s.GetVotingPowerRanking(context.Background(), 100)
	}()

	s.logger.Info().Msg("Rankings cache refresh completed")
	return nil
}

// getLevelName 获取等级名称
func (s *RankingService) getLevelName(level models.StakingLevel) string {
	switch level {
	case models.DingJi:
		return "丁级"
	case models.ChengJi:
		return "丙级"
	case models.YiJi:
		return "乙级"
	case models.JiaJi:
		return "甲级"
	case models.ShiJue:
		return "十爵"
	case models.ShuangShiJue:
		return "双十爵"
	case models.ZhiZun:
		return "至尊"
	default:
		return "未知"
	}
}
