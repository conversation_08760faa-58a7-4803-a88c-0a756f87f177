package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// TokenService 代币服务
type TokenService struct {
	tokenRepo    *repository.TokenRepository
	client       *blockchain.MultiContractClient
	cacheManager *cache.CacheManager
	logger       zerolog.Logger
}

// NewTokenService 创建新的代币服务
func NewTokenService(
	tokenRepo *repository.TokenRepository,
	client *blockchain.MultiContractClient,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *TokenService {
	return &TokenService{
		tokenRepo:    tokenRepo,
		client:       client,
		cacheManager: cacheManager,
		logger:       logger.With().Str("component", "token_service").Logger(),
	}
}

// TokenInfo 代币基础信息
type TokenInfo struct {
	Name              string    `json:"name"`
	Symbol            string    `json:"symbol"`
	Decimals          uint8     `json:"decimals"`
	TotalSupply       string    `json:"total_supply"`
	CirculatingSupply string    `json:"circulating_supply"`
	LockedSupply      string    `json:"locked_supply"`
	BurnedSupply      string    `json:"burned_supply"`
	ContractAddress   string    `json:"contract_address"`
	LastUpdated       time.Time `json:"last_updated"`
}

// UserBalanceInfo 用户余额信息
type UserBalanceInfo struct {
	UserAddress string    `json:"user_address"`
	PXTBalance  string    `json:"pxt_balance"`
	PATBalance  string    `json:"pat_balance"`
	PXTLocked   string    `json:"pxt_locked"`
	PATLocked   string    `json:"pat_locked"`
	LastUpdated time.Time `json:"last_updated"`
}

// TransferHistoryResponse 转账历史响应
type TransferHistoryResponse struct {
	Transfers []models.TokenTransfer `json:"transfers"`
	Total     int64                  `json:"total"`
	Page      int                    `json:"page"`
	Limit     int                    `json:"limit"`
	HasMore   bool                   `json:"has_more"`
}

// GetTokenInfo 获取代币基础信息
func (s *TokenService) GetTokenInfo(ctx context.Context, tokenType string) (*TokenInfo, error) {
	tokenType = strings.ToUpper(tokenType)
	if tokenType != "PXT" && tokenType != "PAT" {
		return nil, fmt.Errorf("unsupported token type: %s", tokenType)
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("token_info:%s", tokenType)
	if s.cacheManager != nil {
		var info TokenInfo
		if err := s.cacheManager.Get(ctx, cacheKey, &info); err == nil {
			return &info, nil
		}
	}

	// 从数据库获取供应量信息
	supply, err := s.tokenRepo.GetTokenSupply(ctx, tokenType)
	if err != nil {
		return nil, fmt.Errorf("failed to get token supply: %w", err)
	}

	// 获取合约地址
	contracts := s.client.GetContracts()
	var contractAddress string
	if tokenType == "PXT" {
		contractAddress = contracts.Addresses.PXTToken.Hex()
	} else {
		contractAddress = contracts.Addresses.PATToken.Hex()
	}

	// 构建响应
	info := &TokenInfo{
		Name:            fmt.Sprintf("%s Token", tokenType),
		Symbol:          tokenType,
		Decimals:        18, // 假设都是18位小数
		ContractAddress: contractAddress,
	}

	if supply != nil {
		info.TotalSupply = supply.TotalSupply
		info.CirculatingSupply = supply.CirculatingSupply
		info.LockedSupply = supply.LockedSupply
		info.BurnedSupply = supply.BurnedSupply
		info.LastUpdated = supply.LastUpdated
	} else {
		// 如果数据库没有数据，从链上获取
		var tokenAddress common.Address
		if tokenType == "PXT" {
			tokenAddress = contracts.Addresses.PXTToken
		} else {
			tokenAddress = contracts.Addresses.PATToken
		}

		totalSupply, err := s.client.GetTokenTotalSupply(ctx, tokenAddress)
		if err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to get total supply from chain")
			info.TotalSupply = "0"
		} else {
			info.TotalSupply = totalSupply.String()
		}
		info.CirculatingSupply = info.TotalSupply
		info.LockedSupply = "0"
		info.BurnedSupply = "0"
		info.LastUpdated = time.Now()
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, info, 5*time.Minute)
	}

	return info, nil
}

// GetUserBalances 获取用户代币余额
func (s *TokenService) GetUserBalances(ctx context.Context, userAddress string) (*UserBalanceInfo, error) {
	if !common.IsHexAddress(userAddress) {
		return nil, fmt.Errorf("invalid address format: %s", userAddress)
	}

	userAddress = strings.ToLower(userAddress)

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("user_balances:%s", userAddress)
	if s.cacheManager != nil {
		var balances UserBalanceInfo
		if err := s.cacheManager.Get(ctx, cacheKey, &balances); err == nil {
			return &balances, nil
		}
	}

	// 从数据库获取余额
	balances, err := s.tokenRepo.GetUserBalances(ctx, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get user balances: %w", err)
	}

	// 构建响应
	info := &UserBalanceInfo{
		UserAddress: userAddress,
		PXTBalance:  "0",
		PATBalance:  "0",
		PXTLocked:   "0",
		PATLocked:   "0",
		LastUpdated: time.Now(),
	}

	// 填充余额数据
	for _, balance := range balances {
		if balance.TokenType == "PXT" {
			info.PXTBalance = balance.Balance
			info.PXTLocked = balance.LockedAmount
			info.LastUpdated = balance.LastUpdated
		} else if balance.TokenType == "PAT" {
			info.PATBalance = balance.Balance
			info.PATLocked = balance.LockedAmount
			if balance.LastUpdated.After(info.LastUpdated) {
				info.LastUpdated = balance.LastUpdated
			}
		}
	}

	// 如果数据库没有数据，从链上获取
	if len(balances) == 0 {
		if err := s.syncUserBalancesFromChain(ctx, userAddress, info); err != nil {
			s.logger.Warn().Err(err).Str("user", userAddress).Msg("Failed to sync balances from chain")
		}
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, info, 1*time.Minute)
	}

	return info, nil
}

// syncUserBalancesFromChain 从链上同步用户余额
func (s *TokenService) syncUserBalancesFromChain(ctx context.Context, userAddress string, info *UserBalanceInfo) error {
	addr := common.HexToAddress(userAddress)

	// 获取PXT余额
	if pxtBalance, err := s.client.GetPXTBalance(ctx, addr); err == nil {
		info.PXTBalance = pxtBalance.String()
		// 保存到数据库
		tokenBalance := &models.TokenBalance{
			UserAddress:  userAddress,
			TokenType:    "PXT",
			Balance:      pxtBalance.String(),
			LockedAmount: "0",
		}
		s.tokenRepo.CreateOrUpdateBalance(ctx, tokenBalance)
	}

	// 获取PAT余额
	if patBalance, err := s.client.GetPATBalance(ctx, addr); err == nil {
		info.PATBalance = patBalance.String()
		// 保存到数据库
		tokenBalance := &models.TokenBalance{
			UserAddress:  userAddress,
			TokenType:    "PAT",
			Balance:      patBalance.String(),
			LockedAmount: "0",
		}
		s.tokenRepo.CreateOrUpdateBalance(ctx, tokenBalance)
	}

	return nil
}

// GetTransferHistory 获取用户转账历史
func (s *TokenService) GetTransferHistory(ctx context.Context, userAddress, tokenType string, page, limit int) (*TransferHistoryResponse, error) {
	if !common.IsHexAddress(userAddress) {
		return nil, fmt.Errorf("invalid address format: %s", userAddress)
	}

	userAddress = strings.ToLower(userAddress)

	if tokenType != "" {
		tokenType = strings.ToUpper(tokenType)
		if tokenType != "PXT" && tokenType != "PAT" {
			return nil, fmt.Errorf("unsupported token type: %s", tokenType)
		}
	}

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 获取转账记录
	transfers, total, err := s.tokenRepo.GetUserTransfers(ctx, userAddress, tokenType, page, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get transfer history: %w", err)
	}

	return &TransferHistoryResponse{
		Transfers: transfers,
		Total:     total,
		Page:      page,
		Limit:     limit,
		HasMore:   int64(page*limit) < total,
	}, nil
}

// GetRecentTransfers 获取最近的转账记录
func (s *TokenService) GetRecentTransfers(ctx context.Context, tokenType string, page, limit int) (*TransferHistoryResponse, error) {
	if tokenType != "" {
		tokenType = strings.ToUpper(tokenType)
		if tokenType != "PXT" && tokenType != "PAT" {
			return nil, fmt.Errorf("unsupported token type: %s", tokenType)
		}
	}

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 获取转账记录
	transfers, total, err := s.tokenRepo.GetRecentTransfers(ctx, tokenType, page, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent transfers: %w", err)
	}

	return &TransferHistoryResponse{
		Transfers: transfers,
		Total:     total,
		Page:      page,
		Limit:     limit,
		HasMore:   int64(page*limit) < total,
	}, nil
}

// GetTopHolders 获取代币持有者排行榜
func (s *TokenService) GetTopHolders(ctx context.Context, tokenType string, limit int) ([]models.TokenHolder, error) {
	tokenType = strings.ToUpper(tokenType)
	if tokenType != "PXT" && tokenType != "PAT" {
		return nil, fmt.Errorf("unsupported token type: %s", tokenType)
	}

	if limit < 1 || limit > 100 {
		limit = 50
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("top_holders:%s:%d", tokenType, limit)
	if s.cacheManager != nil {
		var holders []models.TokenHolder
		if err := s.cacheManager.Get(ctx, cacheKey, &holders); err == nil {
			return holders, nil
		}
	}

	// 从数据库获取
	holders, err := s.tokenRepo.GetTopHolders(ctx, tokenType, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get top holders: %w", err)
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, holders, 10*time.Minute)
	}

	return holders, nil
}

// RefreshUserBalance 刷新用户余额（从链上同步）
func (s *TokenService) RefreshUserBalance(ctx context.Context, userAddress string) (*UserBalanceInfo, error) {
	if !common.IsHexAddress(userAddress) {
		return nil, fmt.Errorf("invalid address format: %s", userAddress)
	}

	userAddress = strings.ToLower(userAddress)

	// 清除缓存
	cacheKey := fmt.Sprintf("user_balances:%s", userAddress)
	if s.cacheManager != nil {
		s.cacheManager.Delete(ctx, cacheKey)
	}

	// 从链上同步余额
	info := &UserBalanceInfo{
		UserAddress: userAddress,
		PXTBalance:  "0",
		PATBalance:  "0",
		PXTLocked:   "0",
		PATLocked:   "0",
		LastUpdated: time.Now(),
	}

	if err := s.syncUserBalancesFromChain(ctx, userAddress, info); err != nil {
		return nil, fmt.Errorf("failed to refresh user balance: %w", err)
	}

	return info, nil
}
