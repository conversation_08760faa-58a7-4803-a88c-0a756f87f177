package service

import (
	"context"
	"fmt"

	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"

	"github.com/rs/zerolog"
)

// LeaderboardService 排行榜服务
type LeaderboardService struct {
	stakingRepo  *repository.StakingRepository
	cacheManager *cache.CacheManager
	logger       zerolog.Logger
}

// NewLeaderboardService 创建新的排行榜服务
func NewLeaderboardService(
	stakingRepo *repository.StakingRepository,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *LeaderboardService {
	return &LeaderboardService{
		stakingRepo:  stakingRepo,
		cacheManager: cacheManager,
		logger: logger.With().
			Str("component", "leaderboard_service").
			Logger(),
	}
}

// LeaderboardRequest 排行榜请求参数
type LeaderboardRequest struct {
	SortBy string `json:"sort_by"` // amount, mining_power, level, rewards
	Page   int    `json:"page"`
	Limit  int    `json:"limit"`
}

// LeaderboardResponse 排行榜响应
type LeaderboardResponse struct {
	List       []*models.LeaderboardEntry `json:"list"`
	Pagination *PaginationInfo            `json:"pagination"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// GetLeaderboard 获取排行榜
func (s *LeaderboardService) GetLeaderboard(ctx context.Context, req *LeaderboardRequest) (*LeaderboardResponse, error) {
	// 验证参数
	if err := s.validateLeaderboardRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 尝试从缓存获取
	var cached LeaderboardResponse
	if s.cacheManager != nil {
		err := s.cacheManager.GetLeaderboard(ctx, req.SortBy, req.Page, req.Limit, &cached)
		if err == nil {
			s.logger.Debug().Str("sort_by", req.SortBy).Int("page", req.Page).Msg("Leaderboard cache hit")
			return &cached, nil
		}
		if err != cache.ErrCacheNotFound {
			s.logger.Error().Err(err).Msg("Failed to get leaderboard from cache")
		}
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.Limit

	// 从数据库获取排行榜数据
	entries, err := s.stakingRepo.GetLeaderboard(ctx, req.SortBy, offset, req.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get leaderboard: %w", err)
	}

	// 获取总数
	total, err := s.stakingRepo.GetActiveUsersCount(ctx)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get active users count")
		total = 0
	}

	// 计算总页数
	totalPages := int(total) / req.Limit
	if int(total)%req.Limit > 0 {
		totalPages++
	}

	response := &LeaderboardResponse{
		List: entries,
		Pagination: &PaginationInfo{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	// 缓存结果
	if s.cacheManager != nil {
		if err := s.cacheManager.SetLeaderboard(ctx, req.SortBy, req.Page, req.Limit, response); err != nil {
			s.logger.Error().Err(err).Msg("Failed to set leaderboard cache")
		}
	}

	return response, nil
}

// GetStakingOverview 获取质押总览
func (s *LeaderboardService) GetStakingOverview(ctx context.Context) (*models.StakingOverview, error) {
	// 尝试从缓存获取
	var cached models.StakingOverview
	if s.cacheManager != nil {
		err := s.cacheManager.GetOverview(ctx, &cached)
		if err == nil {
			s.logger.Debug().Msg("Overview cache hit")
			return &cached, nil
		}
		if err != cache.ErrCacheNotFound {
			s.logger.Error().Err(err).Msg("Failed to get overview from cache")
		}
	}

	// 从数据库获取
	overview, err := s.stakingRepo.GetStakingOverview(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get staking overview: %w", err)
	}

	// 缓存结果
	if s.cacheManager != nil {
		if err := s.cacheManager.SetOverview(ctx, overview); err != nil {
			s.logger.Error().Err(err).Msg("Failed to set overview cache")
		}
	}

	return overview, nil
}

// GetLevelDistribution 获取等级分布
func (s *LeaderboardService) GetLevelDistribution(ctx context.Context) ([]*models.LevelDistribution, error) {
	// 尝试从缓存获取
	var cached []*models.LevelDistribution
	if s.cacheManager != nil {
		err := s.cacheManager.GetLevelDistribution(ctx, &cached)
		if err == nil {
			s.logger.Debug().Msg("Level distribution cache hit")
			return cached, nil
		}
		if err != cache.ErrCacheNotFound {
			s.logger.Error().Err(err).Msg("Failed to get level distribution from cache")
		}
	}

	// 从数据库获取
	distribution, err := s.stakingRepo.GetLevelDistribution(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get level distribution: %w", err)
	}

	// 缓存结果
	if s.cacheManager != nil {
		if err := s.cacheManager.SetLevelDistribution(ctx, distribution); err != nil {
			s.logger.Error().Err(err).Msg("Failed to set level distribution cache")
		}
	}

	return distribution, nil
}

// GetTopStakers 获取顶级质押者
func (s *LeaderboardService) GetTopStakers(ctx context.Context, limit int) ([]*models.LeaderboardEntry, error) {
	req := &LeaderboardRequest{
		SortBy: "amount",
		Page:   1,
		Limit:  limit,
	}

	response, err := s.GetLeaderboard(ctx, req)
	if err != nil {
		return nil, err
	}

	return response.List, nil
}

// GetUserRanking 获取用户在各个维度的排名
func (s *LeaderboardService) GetUserRanking(ctx context.Context, userAddress string) (map[string]int, error) {
	// 尝试从缓存获取
	var cached map[string]int
	if s.cacheManager != nil {
		err := s.cacheManager.GetUserRanking(ctx, userAddress, &cached)
		if err == nil {
			s.logger.Debug().Str("user", userAddress).Msg("User ranking cache hit")
			return cached, nil
		}
		if err != cache.ErrCacheNotFound {
			s.logger.Error().Err(err).Msg("Failed to get user ranking from cache")
		}
	}

	rankings := make(map[string]int)

	// 获取各个维度的排名
	dimensions := []string{"amount", "mining_power", "level", "rewards"}

	for _, dimension := range dimensions {
		rank, err := s.stakingRepo.GetUserRank(ctx, userAddress, dimension)
		if err != nil {
			s.logger.Error().Err(err).Str("dimension", dimension).Msg("Failed to get user rank")
			rank = 0
		}
		rankings[dimension] = rank
	}

	// 缓存结果
	if s.cacheManager != nil {
		if err := s.cacheManager.SetUserRanking(ctx, userAddress, rankings); err != nil {
			s.logger.Error().Err(err).Msg("Failed to set user ranking cache")
		}
	}

	return rankings, nil
}

// RefreshLeaderboardCache 刷新排行榜缓存
func (s *LeaderboardService) RefreshLeaderboardCache(ctx context.Context) error {
	if s.cacheManager == nil {
		s.logger.Info().Msg("Cache manager not available, skipping cache refresh")
		return nil
	}

	// 清除所有缓存
	err := s.cacheManager.InvalidateAll(ctx)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to invalidate all cache")
		return fmt.Errorf("failed to refresh cache: %w", err)
	}

	s.logger.Info().Msg("Leaderboard cache refreshed")
	return nil
}

// validateLeaderboardRequest 验证排行榜请求参数
func (s *LeaderboardService) validateLeaderboardRequest(req *LeaderboardRequest) error {
	// 验证排序字段
	validSortBy := map[string]bool{
		"amount":       true,
		"mining_power": true,
		"level":        true,
		"rewards":      true,
	}

	if !validSortBy[req.SortBy] {
		req.SortBy = "amount" // 默认按金额排序
	}

	// 验证分页参数
	if req.Page < 1 {
		req.Page = 1
	}

	if req.Limit < 1 {
		req.Limit = 50
	} else if req.Limit > 200 {
		req.Limit = 200 // 最大限制200条
	}

	return nil
}
