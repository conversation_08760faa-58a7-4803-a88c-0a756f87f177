package service

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/cache"
)

// TransactionService 交易服务
type TransactionService struct {
	client       *blockchain.MultiContractClient
	cacheManager *cache.CacheManager
	logger       zerolog.Logger
}

// NewTransactionService 创建新的交易服务
func NewTransactionService(
	client *blockchain.MultiContractClient,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *TransactionService {
	return &TransactionService{
		client:       client,
		cacheManager: cacheManager,
		logger:       logger.With().Str("component", "transaction_service").Logger(),
	}
}

// TransactionPrepareRequest 交易准备请求
type TransactionPrepareRequest struct {
	UserAddress string                 `json:"user_address" binding:"required"`
	Type        string                 `json:"type" binding:"required"` // stake, unstake, vote, delegate
	Data        map[string]interface{} `json:"data" binding:"required"`
}

// TransactionPrepareResponse 交易准备响应
type TransactionPrepareResponse struct {
	TransactionData string `json:"transaction_data"`
	GasEstimate     uint64 `json:"gas_estimate"`
	GasPrice        string `json:"gas_price"`
	Nonce           uint64 `json:"nonce"`
	To              string `json:"to"`
	Value           string `json:"value"`
}

// TransactionBroadcastRequest 交易广播请求
type TransactionBroadcastRequest struct {
	SignedTransaction string `json:"signed_transaction" binding:"required"`
	TransactionType   string `json:"transaction_type" binding:"required"`
}

// TransactionBroadcastResponse 交易广播响应
type TransactionBroadcastResponse struct {
	TransactionHash string `json:"transaction_hash"`
	Status          string `json:"status"`
}

// TransactionStatusResponse 交易状态响应
type TransactionStatusResponse struct {
	TransactionHash string `json:"transaction_hash"`
	Status          string `json:"status"` // pending, confirmed, failed
	BlockNumber     uint64 `json:"block_number,omitempty"`
	Confirmations   uint64 `json:"confirmations,omitempty"`
	GasUsed         uint64 `json:"gas_used,omitempty"`
	Success         bool   `json:"success"`
	ErrorMessage    string `json:"error_message,omitempty"`
}

// NetworkInfoResponse 网络信息响应
type NetworkInfoResponse struct {
	ChainID     int64  `json:"chain_id"`
	NetworkName string `json:"network_name"`
	GasPrice    string `json:"gas_price"`
	LatestBlock uint64 `json:"latest_block"`
}

// PrepareTransaction 准备交易
func (s *TransactionService) PrepareTransaction(ctx context.Context, req *TransactionPrepareRequest) (*TransactionPrepareResponse, error) {
	if !common.IsHexAddress(req.UserAddress) {
		return nil, fmt.Errorf("invalid user address: %s", req.UserAddress)
	}

	userAddress := common.HexToAddress(req.UserAddress)
	contracts := s.client.GetContracts()

	var to common.Address
	var data []byte
	var value *big.Int = big.NewInt(0)
	var err error

	switch strings.ToLower(req.Type) {
	case "stake":
		to, data, err = s.prepareStakeTransaction(req.Data, contracts)
	case "unstake":
		to, data, err = s.prepareUnstakeTransaction(req.Data, contracts)
	case "vote":
		to, data, err = s.prepareVoteTransaction(req.Data, contracts)
	case "delegate":
		to, data, err = s.prepareDelegateTransaction(req.Data, contracts)
	default:
		return nil, fmt.Errorf("unsupported transaction type: %s", req.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to prepare %s transaction: %w", req.Type, err)
	}

	// 获取nonce
	nonce, err := s.client.GetClient().PendingNonceAt(ctx, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// 估算Gas
	gasLimit, err := s.client.GetClient().EstimateGas(ctx, ethereum.CallMsg{
		From: userAddress,
		To:   &to,
		Data: data,
	})
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to estimate gas, using default")
		gasLimit = 200000 // 默认Gas限制
	}

	// 获取Gas价格
	gasPrice, err := s.client.GetClient().SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %w", err)
	}

	return &TransactionPrepareResponse{
		TransactionData: common.Bytes2Hex(data),
		GasEstimate:     gasLimit,
		GasPrice:        gasPrice.String(),
		Nonce:           nonce,
		To:              to.Hex(),
		Value:           value.String(),
	}, nil
}

// prepareStakeTransaction 准备质押交易
func (s *TransactionService) prepareStakeTransaction(data map[string]interface{}, contracts *blockchain.ContractManager) (common.Address, []byte, error) {
	amount, ok := data["amount"].(string)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("missing or invalid amount")
	}

	amountBig, ok := new(big.Int).SetString(amount, 10)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("invalid amount format: %s", amount)
	}

	// 打包质押函数调用
	callData, err := contracts.ABIs.StakingPool.Pack("stake", amountBig)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to pack stake call: %w", err)
	}

	return contracts.Addresses.StakingPool, callData, nil
}

// prepareUnstakeTransaction 准备解质押交易
func (s *TransactionService) prepareUnstakeTransaction(data map[string]interface{}, contracts *blockchain.ContractManager) (common.Address, []byte, error) {
	amount, ok := data["amount"].(string)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("missing or invalid amount")
	}

	amountBig, ok := new(big.Int).SetString(amount, 10)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("invalid amount format: %s", amount)
	}

	// 打包解质押函数调用
	callData, err := contracts.ABIs.StakingPool.Pack("withdraw", amountBig)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to pack withdraw call: %w", err)
	}

	return contracts.Addresses.StakingPool, callData, nil
}

// prepareVoteTransaction 准备投票交易
func (s *TransactionService) prepareVoteTransaction(data map[string]interface{}, contracts *blockchain.ContractManager) (common.Address, []byte, error) {
	proposalIDFloat, ok := data["proposal_id"].(float64)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("missing or invalid proposal_id")
	}
	proposalID := big.NewInt(int64(proposalIDFloat))

	supportFloat, ok := data["support"].(float64)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("missing or invalid support")
	}
	support := uint8(supportFloat)

	reason, _ := data["reason"].(string)

	// 打包投票函数调用
	callData, err := contracts.ABIs.Voting.Pack("castVote", proposalID, support, reason)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to pack castVote call: %w", err)
	}

	return contracts.Addresses.Voting, callData, nil
}

// prepareDelegateTransaction 准备委托交易
func (s *TransactionService) prepareDelegateTransaction(data map[string]interface{}, contracts *blockchain.ContractManager) (common.Address, []byte, error) {
	delegate, ok := data["delegate"].(string)
	if !ok {
		return common.Address{}, nil, fmt.Errorf("missing or invalid delegate")
	}

	if !common.IsHexAddress(delegate) {
		return common.Address{}, nil, fmt.Errorf("invalid delegate address: %s", delegate)
	}

	delegateAddress := common.HexToAddress(delegate)

	// 打包委托函数调用
	callData, err := contracts.ABIs.Voting.Pack("delegate", delegateAddress)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to pack delegate call: %w", err)
	}

	return contracts.Addresses.Voting, callData, nil
}

// BroadcastTransaction 广播交易
func (s *TransactionService) BroadcastTransaction(ctx context.Context, req *TransactionBroadcastRequest) (*TransactionBroadcastResponse, error) {
	// 解析已签名的交易
	txData := common.FromHex(req.SignedTransaction)
	if len(txData) == 0 {
		return nil, fmt.Errorf("invalid signed transaction data")
	}

	tx := new(types.Transaction)
	if err := tx.UnmarshalBinary(txData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal transaction: %w", err)
	}

	// 广播交易
	if err := s.client.GetClient().SendTransaction(ctx, tx); err != nil {
		return nil, fmt.Errorf("failed to broadcast transaction: %w", err)
	}

	s.logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("type", req.TransactionType).
		Msg("Transaction broadcasted")

	return &TransactionBroadcastResponse{
		TransactionHash: tx.Hash().Hex(),
		Status:          "pending",
	}, nil
}

// GetTransactionStatus 获取交易状态
func (s *TransactionService) GetTransactionStatus(ctx context.Context, txHash string) (*TransactionStatusResponse, error) {
	if len(txHash) != 66 || !strings.HasPrefix(txHash, "0x") {
		return nil, fmt.Errorf("invalid transaction hash: %s", txHash)
	}

	hash := common.HexToHash(txHash)

	// 获取交易收据
	receipt, err := s.client.GetClient().TransactionReceipt(ctx, hash)
	if err != nil {
		// 如果没有收据，检查交易是否在pending池中
		_, isPending, err := s.client.GetClient().TransactionByHash(ctx, hash)
		if err != nil {
			return nil, fmt.Errorf("transaction not found: %s", txHash)
		}

		if isPending {
			return &TransactionStatusResponse{
				TransactionHash: txHash,
				Status:          "pending",
				Success:         false,
			}, nil
		}

		return &TransactionStatusResponse{
			TransactionHash: txHash,
			Status:          "failed",
			Success:         false,
			ErrorMessage:    "Transaction not found",
		}, nil
	}

	// 获取最新区块号计算确认数
	latestBlock, err := s.client.GetLatestBlockNumber(ctx)
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get latest block number")
		latestBlock = receipt.BlockNumber.Uint64()
	}

	confirmations := uint64(0)
	if latestBlock >= receipt.BlockNumber.Uint64() {
		confirmations = latestBlock - receipt.BlockNumber.Uint64()
	}

	status := "confirmed"
	success := receipt.Status == types.ReceiptStatusSuccessful

	if !success {
		status = "failed"
	}

	response := &TransactionStatusResponse{
		TransactionHash: txHash,
		Status:          status,
		BlockNumber:     receipt.BlockNumber.Uint64(),
		Confirmations:   confirmations,
		GasUsed:         receipt.GasUsed,
		Success:         success,
	}

	// 如果交易失败，尝试获取错误信息
	if !success {
		response.ErrorMessage = "Transaction execution failed"
	}

	return response, nil
}

// GetNetworkInfo 获取网络信息
func (s *TransactionService) GetNetworkInfo(ctx context.Context) (*NetworkInfoResponse, error) {
	// 获取链ID
	chainID, err := s.client.GetClient().ChainID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 获取Gas价格
	gasPrice, err := s.client.GetClient().SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %w", err)
	}

	// 获取最新区块
	latestBlock, err := s.client.GetLatestBlockNumber(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest block: %w", err)
	}

	// 确定网络名称
	networkName := "unknown"
	switch chainID.Int64() {
	case 97:
		networkName = "BSC Testnet"
	case 56:
		networkName = "BSC Mainnet"
	}

	return &NetworkInfoResponse{
		ChainID:     chainID.Int64(),
		NetworkName: networkName,
		GasPrice:    gasPrice.String(),
		LatestBlock: latestBlock,
	}, nil
}

// GetUserNonce 获取用户nonce
func (s *TransactionService) GetUserNonce(ctx context.Context, userAddress string) (uint64, error) {
	if !common.IsHexAddress(userAddress) {
		return 0, fmt.Errorf("invalid user address: %s", userAddress)
	}

	address := common.HexToAddress(userAddress)
	nonce, err := s.client.GetClient().PendingNonceAt(ctx, address)
	if err != nil {
		return 0, fmt.Errorf("failed to get nonce: %w", err)
	}

	return nonce, nil
}
