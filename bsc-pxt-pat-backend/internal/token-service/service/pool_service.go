package service

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/rs/zerolog"
	"gorm.io/gorm"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// PoolService 池子管理服务
type PoolService struct {
	client     *blockchain.MultiContractClient
	config     *config.BlockchainConfig
	tokenRepo  *repository.TokenRepository
	logger     zerolog.Logger
}

// PoolInfo 池子信息
type PoolInfo struct {
	Address         string    `json:"address"`          // 池子地址
	Name            string    `json:"name"`             // 池子名称
	Type            string    `json:"type"`             // 池子类型 (china_mainland, global)
	PXTBalance      string    `json:"pxt_balance"`      // PXT 余额
	PATBalance      string    `json:"pat_balance"`      // PAT 余额
	ETHBalance      string    `json:"eth_balance"`      // ETH/BNB 余额
	LastActivity    time.Time `json:"last_activity"`    // 最后活动时间
	TransferCount   int64     `json:"transfer_count"`   // 转账次数
	TotalTransfered string    `json:"total_transfered"` // 总转账金额
	IsActive        bool      `json:"is_active"`        // 是否活跃
}

// PoolTransfer 池子转账记录
type PoolTransfer struct {
	ID              uint      `json:"id"`
	PoolAddress     string    `json:"pool_address"`     // 池子地址
	PoolType        string    `json:"pool_type"`        // 池子类型
	TransactionHash string    `json:"transaction_hash"` // 交易哈希
	TokenType       string    `json:"token_type"`       // 代币类型
	Direction       string    `json:"direction"`        // 方向 (in, out)
	FromAddress     string    `json:"from_address"`     // 发送地址
	ToAddress       string    `json:"to_address"`       // 接收地址
	Amount          string    `json:"amount"`           // 金额
	Purpose         string    `json:"purpose"`          // 用途 (reward, transfer, governance)
	BlockNumber     uint64    `json:"block_number"`     // 区块号
	Timestamp       time.Time `json:"timestamp"`        // 时间戳
}

// PoolStats 池子统计
type PoolStats struct {
	TotalPools      int    `json:"total_pools"`       // 总池子数
	ActivePools     int    `json:"active_pools"`      // 活跃池子数
	TotalPXTBalance string `json:"total_pxt_balance"` // 总 PXT 余额
	TotalPATBalance string `json:"total_pat_balance"` // 总 PAT 余额
	DailyTransfers  int64  `json:"daily_transfers"`   // 日转账次数
	WeeklyTransfers int64  `json:"weekly_transfers"`  // 周转账次数
}

// NewPoolService 创建池子服务
func NewPoolService(
	client *blockchain.MultiContractClient,
	config *config.BlockchainConfig,
	tokenRepo *repository.TokenRepository,
	logger zerolog.Logger,
) *PoolService {
	return &PoolService{
		client:    client,
		config:    config,
		tokenRepo: tokenRepo,
		logger:    logger,
	}
}

// GetPoolInfo 获取池子信息
func (s *PoolService) GetPoolInfo(ctx context.Context, poolAddress string) (*PoolInfo, error) {
	poolAddress = strings.ToLower(poolAddress)
	
	// 确定池子类型
	poolType := s.getPoolType(poolAddress)
	if poolType == "" {
		return nil, fmt.Errorf("unknown pool address: %s", poolAddress)
	}

	// 获取池子名称
	poolName := s.getPoolName(poolType)

	// 获取代币余额
	contracts := s.client.GetContracts()
	poolAddr := common.HexToAddress(poolAddress)
	
	pxtBalance, err := s.client.GetTokenBalance(ctx, contracts.Addresses.PXTToken, poolAddr)
	if err != nil {
		s.logger.Warn().Err(err).Str("pool", poolAddress).Msg("Failed to get PXT balance")
		pxtBalance = big.NewInt(0)
	}

	patBalance, err := s.client.GetTokenBalance(ctx, contracts.Addresses.PATToken, poolAddr)
	if err != nil {
		s.logger.Warn().Err(err).Str("pool", poolAddress).Msg("Failed to get PAT balance")
		patBalance = big.NewInt(0)
	}

	// 获取 ETH/BNB 余额
	ethBalance, err := s.client.GetClient().BalanceAt(ctx, poolAddr, nil)
	if err != nil {
		s.logger.Warn().Err(err).Str("pool", poolAddress).Msg("Failed to get ETH balance")
		ethBalance = big.NewInt(0)
	}

	// 获取转账统计
	transferStats, err := s.getPoolTransferStats(ctx, poolAddress)
	if err != nil {
		s.logger.Warn().Err(err).Str("pool", poolAddress).Msg("Failed to get transfer stats")
		transferStats = &struct {
			Count         int64
			TotalAmount   string
			LastActivity  time.Time
		}{0, "0", time.Time{}}
	}

	return &PoolInfo{
		Address:         poolAddress,
		Name:            poolName,
		Type:            poolType,
		PXTBalance:      pxtBalance.String(),
		PATBalance:      patBalance.String(),
		ETHBalance:      ethBalance.String(),
		LastActivity:    transferStats.LastActivity,
		TransferCount:   transferStats.Count,
		TotalTransfered: transferStats.TotalAmount,
		IsActive:        time.Since(transferStats.LastActivity) < 24*time.Hour,
	}, nil
}

// GetAllPoolsInfo 获取所有池子信息
func (s *PoolService) GetAllPoolsInfo(ctx context.Context) ([]*PoolInfo, error) {
	contracts := s.client.GetContracts()
	
	poolAddresses := []string{
		strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex()),
		strings.ToLower(contracts.Addresses.GlobalPool.Hex()),
	}

	var pools []*PoolInfo
	for _, addr := range poolAddresses {
		poolInfo, err := s.GetPoolInfo(ctx, addr)
		if err != nil {
			s.logger.Warn().Err(err).Str("pool", addr).Msg("Failed to get pool info")
			continue
		}
		pools = append(pools, poolInfo)
	}

	return pools, nil
}

// GetPoolTransfers 获取池子转账记录
func (s *PoolService) GetPoolTransfers(ctx context.Context, poolAddress string, limit, offset int) ([]*PoolTransfer, int64, error) {
	poolAddress = strings.ToLower(poolAddress)
	poolType := s.getPoolType(poolAddress)
	
	db := s.tokenRepo.GetDB()
	
	// 查询转账记录
	var transfers []models.TokenTransfer
	var total int64
	
	query := db.Where("from_address = ? OR to_address = ?", poolAddress, poolAddress).
		Order("timestamp DESC")
	
	// 获取总数
	if err := query.Model(&models.TokenTransfer{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count pool transfers: %w", err)
	}
	
	// 获取分页数据
	if err := query.Limit(limit).Offset(offset).Find(&transfers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get pool transfers: %w", err)
	}

	// 转换为池子转账记录
	var poolTransfers []*PoolTransfer
	for _, transfer := range transfers {
		direction := "out"
		if strings.ToLower(transfer.ToAddress) == poolAddress {
			direction = "in"
		}

		purpose := s.determinePurpose(&transfer, poolAddress)

		poolTransfer := &PoolTransfer{
			ID:              transfer.ID,
			PoolAddress:     poolAddress,
			PoolType:        poolType,
			TransactionHash: transfer.TransactionHash,
			TokenType:       transfer.TokenType,
			Direction:       direction,
			FromAddress:     transfer.FromAddress,
			ToAddress:       transfer.ToAddress,
			Amount:          transfer.Amount,
			Purpose:         purpose,
			BlockNumber:     transfer.BlockNumber,
			Timestamp:       transfer.Timestamp,
		}
		poolTransfers = append(poolTransfers, poolTransfer)
	}

	return poolTransfers, total, nil
}

// GetPoolStats 获取池子统计信息
func (s *PoolService) GetPoolStats(ctx context.Context) (*PoolStats, error) {
	pools, err := s.GetAllPoolsInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get pools info: %w", err)
	}

	stats := &PoolStats{
		TotalPools:      len(pools),
		TotalPXTBalance: "0",
		TotalPATBalance: "0",
	}

	// 计算总余额和活跃池子数
	totalPXT := big.NewInt(0)
	totalPAT := big.NewInt(0)
	
	for _, pool := range pools {
		if pool.IsActive {
			stats.ActivePools++
		}
		
		if pxtBalance, ok := new(big.Int).SetString(pool.PXTBalance, 10); ok {
			totalPXT.Add(totalPXT, pxtBalance)
		}
		
		if patBalance, ok := new(big.Int).SetString(pool.PATBalance, 10); ok {
			totalPAT.Add(totalPAT, patBalance)
		}
	}

	stats.TotalPXTBalance = totalPXT.String()
	stats.TotalPATBalance = totalPAT.String()

	// 获取转账统计
	db := s.tokenRepo.GetDB()
	contracts := s.client.GetContracts()
	
	poolAddresses := []string{
		strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex()),
		strings.ToLower(contracts.Addresses.GlobalPool.Hex()),
	}

	// 日转账次数
	dayAgo := time.Now().Add(-24 * time.Hour)
	err = db.Model(&models.TokenTransfer{}).
		Where("(from_address IN ? OR to_address IN ?) AND timestamp >= ?", poolAddresses, poolAddresses, dayAgo).
		Count(&stats.DailyTransfers).Error
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get daily transfers count")
	}

	// 周转账次数
	weekAgo := time.Now().Add(-7 * 24 * time.Hour)
	err = db.Model(&models.TokenTransfer{}).
		Where("(from_address IN ? OR to_address IN ?) AND timestamp >= ?", poolAddresses, poolAddresses, weekAgo).
		Count(&stats.WeeklyTransfers).Error
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get weekly transfers count")
	}

	return stats, nil
}

// 辅助方法

// getPoolType 根据地址确定池子类型
func (s *PoolService) getPoolType(address string) string {
	contracts := s.client.GetContracts()
	address = strings.ToLower(address)
	
	if address == strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex()) {
		return "china_mainland"
	} else if address == strings.ToLower(contracts.Addresses.GlobalPool.Hex()) {
		return "global"
	}
	return ""
}

// getPoolName 根据类型获取池子名称
func (s *PoolService) getPoolName(poolType string) string {
	switch poolType {
	case "china_mainland":
		return "中国大陆池子"
	case "global":
		return "全球池子"
	default:
		return "未知池子"
	}
}

// getPoolTransferStats 获取池子转账统计
func (s *PoolService) getPoolTransferStats(ctx context.Context, poolAddress string) (*struct {
	Count        int64
	TotalAmount  string
	LastActivity time.Time
}, error) {
	db := s.tokenRepo.GetDB()
	
	var count int64
	var lastActivity time.Time
	
	// 获取转账次数
	err := db.Model(&models.TokenTransfer{}).
		Where("from_address = ? OR to_address = ?", poolAddress, poolAddress).
		Count(&count).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count transfers: %w", err)
	}
	
	// 获取最后活动时间
	err = db.Model(&models.TokenTransfer{}).
		Where("from_address = ? OR to_address = ?", poolAddress, poolAddress).
		Select("MAX(timestamp)").
		Scan(&lastActivity).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get last activity: %w", err)
	}
	
	// TODO: 计算总转账金额（需要考虑代币精度）
	totalAmount := "0"
	
	return &struct {
		Count        int64
		TotalAmount  string
		LastActivity time.Time
	}{count, totalAmount, lastActivity}, nil
}

// determinePurpose 确定转账用途
func (s *PoolService) determinePurpose(transfer *models.TokenTransfer, poolAddress string) string {
	// 根据转账模式和地址确定用途
	fromAddr := strings.ToLower(transfer.FromAddress)
	toAddr := strings.ToLower(transfer.ToAddress)
	poolAddr := strings.ToLower(poolAddress)
	
	// 如果是从池子转出
	if fromAddr == poolAddr {
		// 可能是奖励分发、治理操作或普通转账
		// 这里可以根据具体的业务逻辑进一步判断
		return "reward" // 默认认为是奖励分发
	} else if toAddr == poolAddr {
		// 如果是转入池子
		return "deposit" // 存入
	}
	
	return "transfer" // 普通转账
}
