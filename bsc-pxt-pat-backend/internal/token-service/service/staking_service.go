package service

import (
	"context"
	"fmt"
	"math/big"
	"time"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"

	"github.com/rs/zerolog"
)

// StakingService 质押业务逻辑服务
type StakingService struct {
	stakingRepo  *repository.StakingRepository
	eventRepo    *repository.EventRepository
	cacheManager *cache.CacheManager
	logger       zerolog.Logger
}

// NewStakingService 创建新的质押服务
func NewStakingService(
	stakingRepo *repository.StakingRepository,
	eventRepo *repository.EventRepository,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *StakingService {
	return &StakingService{
		stakingRepo:  stakingRepo,
		eventRepo:    eventRepo,
		cacheManager: cacheManager,
		logger: logger.With().
			Str("component", "staking_service").
			Logger(),
	}
}

// HandleStakedEvent 处理质押事件
func (s *StakingService) HandleStakedEvent(ctx context.Context, event *blockchain.StakedEvent, timestamp time.Time) error {
	s.logger.Info().
		Str("user", event.User.Hex()).
		Str("amount", event.Amount.String()).
		Uint8("level", uint8(event.Level)).
		Msg("Handling Staked event")

	// 转换质押等级
	level := models.StakingLevel(event.Level)

	// 创建或更新用户质押信息
	err := s.stakingRepo.CreateOrUpdateUserStake(
		ctx,
		event.User.Hex(),
		event.Amount,
		level,
		timestamp,
	)
	if err != nil {
		return fmt.Errorf("failed to create or update user stake: %w", err)
	}

	// 记录事件
	stakingEvent := &models.StakingEvent{
		UserAddress:     event.User.Hex(),
		EventType:       models.EventTypeStaked,
		Amount:          models.NewBigInt(event.Amount),
		NewLevel:        &level,
		TransactionHash: event.Raw.TxHash.Hex(),
		BlockNumber:     event.Raw.BlockNumber,
		LogIndex:        uint(event.Raw.Index),
		Timestamp:       timestamp,
	}

	if err := s.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 清除相关缓存
	s.clearUserCache(ctx, event.User.Hex())
	s.clearLeaderboardCache(ctx)

	return nil
}

// HandleWithdrawnEvent 处理提取事件
func (s *StakingService) HandleWithdrawnEvent(ctx context.Context, event *blockchain.WithdrawnEvent, timestamp time.Time) error {
	s.logger.Info().
		Str("user", event.User.Hex()).
		Str("amount", event.Amount.String()).
		Msg("Handling Withdrawn event")

	// 获取当前用户质押信息
	userStake, err := s.stakingRepo.GetUserStake(ctx, event.User.Hex())
	if err != nil {
		return fmt.Errorf("failed to get user stake: %w", err)
	}

	if userStake == nil {
		s.logger.Warn().Str("user", event.User.Hex()).Msg("User stake not found for withdrawal")
		return nil
	}

	// 计算新的质押金额
	newAmount := new(big.Int).Sub(userStake.Amount.Int, event.Amount)
	if newAmount.Cmp(big.NewInt(0)) < 0 {
		newAmount = big.NewInt(0)
	}

	// 更新用户质押金额
	err = s.stakingRepo.UpdateUserStakeAmount(ctx, event.User.Hex(), newAmount)
	if err != nil {
		return fmt.Errorf("failed to update user stake amount: %w", err)
	}

	// 记录事件
	stakingEvent := &models.StakingEvent{
		UserAddress:     event.User.Hex(),
		EventType:       models.EventTypeWithdrawn,
		Amount:          models.NewBigInt(event.Amount),
		TransactionHash: event.Raw.TxHash.Hex(),
		BlockNumber:     event.Raw.BlockNumber,
		LogIndex:        uint(event.Raw.Index),
		Timestamp:       timestamp,
	}

	if err := s.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 清除相关缓存
	s.clearUserCache(ctx, event.User.Hex())
	s.clearLeaderboardCache(ctx)

	return nil
}

// HandleRewardClaimedEvent 处理奖励领取事件
func (s *StakingService) HandleRewardClaimedEvent(ctx context.Context, event *blockchain.RewardClaimedEvent, timestamp time.Time) error {
	s.logger.Info().
		Str("user", event.User.Hex()).
		Str("amount", event.Amount.String()).
		Msg("Handling RewardClaimed event")

	// 增加用户已领取奖励
	err := s.stakingRepo.AddUserRewardsClaimed(ctx, event.User.Hex(), event.Amount)
	if err != nil {
		return fmt.Errorf("failed to add user rewards claimed: %w", err)
	}

	// 记录事件
	stakingEvent := &models.StakingEvent{
		UserAddress:     event.User.Hex(),
		EventType:       models.EventTypeRewardClaimed,
		Amount:          models.NewBigInt(event.Amount),
		TransactionHash: event.Raw.TxHash.Hex(),
		BlockNumber:     event.Raw.BlockNumber,
		LogIndex:        uint(event.Raw.Index),
		Timestamp:       timestamp,
	}

	if err := s.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 清除相关缓存
	s.clearUserCache(ctx, event.User.Hex())

	return nil
}

// HandleStakingLevelUpdatedEvent 处理质押等级更新事件
func (s *StakingService) HandleStakingLevelUpdatedEvent(ctx context.Context, event *blockchain.StakingLevelUpdatedEvent, timestamp time.Time) error {
	s.logger.Info().
		Str("user", event.User.Hex()).
		Uint8("old_level", uint8(event.OldLevel)).
		Uint8("new_level", uint8(event.NewLevel)).
		Msg("Handling StakingLevelUpdated event")

	// 转换质押等级
	newLevel := models.StakingLevel(event.NewLevel)
	oldLevel := models.StakingLevel(event.OldLevel)

	// 更新用户质押等级
	err := s.stakingRepo.UpdateUserStakeLevel(ctx, event.User.Hex(), newLevel)
	if err != nil {
		return fmt.Errorf("failed to update user stake level: %w", err)
	}

	// 记录事件
	stakingEvent := &models.StakingEvent{
		UserAddress:     event.User.Hex(),
		EventType:       models.EventTypeStakingLevelUpdated,
		OldLevel:        &oldLevel,
		NewLevel:        &newLevel,
		TransactionHash: event.Raw.TxHash.Hex(),
		BlockNumber:     event.Raw.BlockNumber,
		LogIndex:        uint(event.Raw.Index),
		Timestamp:       timestamp,
	}

	if err := s.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 清除相关缓存
	s.clearUserCache(ctx, event.User.Hex())
	s.clearLeaderboardCache(ctx)

	return nil
}

// HandleMiningPowerUpdatedEvent 处理挖矿算力更新事件
func (s *StakingService) HandleMiningPowerUpdatedEvent(ctx context.Context, event *blockchain.MiningPowerUpdatedEvent, timestamp time.Time) error {
	s.logger.Info().
		Str("user", event.User.Hex()).
		Str("old_power", event.OldPower.String()).
		Str("new_power", event.NewPower.String()).
		Msg("Handling MiningPowerUpdated event")

	// 更新用户挖矿算力
	err := s.stakingRepo.UpdateUserMiningPower(ctx, event.User.Hex(), event.NewPower)
	if err != nil {
		return fmt.Errorf("failed to update user mining power: %w", err)
	}

	// 记录事件
	stakingEvent := &models.StakingEvent{
		UserAddress:     event.User.Hex(),
		EventType:       models.EventTypeMiningPowerUpdated,
		Amount:          models.NewBigInt(event.NewPower), // 使用Amount字段存储新的挖矿算力
		TransactionHash: event.Raw.TxHash.Hex(),
		BlockNumber:     event.Raw.BlockNumber,
		LogIndex:        uint(event.Raw.Index),
		Timestamp:       timestamp,
	}

	if err := s.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 清除相关缓存
	s.clearUserCache(ctx, event.User.Hex())
	s.clearLeaderboardCache(ctx)

	return nil
}

// GetUserStakingStats 获取用户质押统计
func (s *StakingService) GetUserStakingStats(ctx context.Context, userAddress string) (*models.UserStakingStats, error) {
	// 尝试从缓存获取
	var cached models.UserStakingStats
	if s.cacheManager != nil {
		err := s.cacheManager.GetUserStats(ctx, userAddress, &cached)
		if err == nil {
			s.logger.Debug().Str("user", userAddress).Msg("User stats cache hit")
			return &cached, nil
		}
		if err != cache.ErrCacheNotFound {
			s.logger.Error().Err(err).Msg("Failed to get user stats from cache")
		}
	}

	// 从数据库获取用户质押信息
	userStake, err := s.stakingRepo.GetUserStake(ctx, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get user stake: %w", err)
	}

	if userStake == nil {
		return nil, nil
	}

	// 获取用户排名
	rankByAmount, err := s.stakingRepo.GetUserRank(ctx, userAddress, "amount")
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get user rank by amount")
		rankByAmount = 0
	}

	rankByMiningPower, err := s.stakingRepo.GetUserRank(ctx, userAddress, "mining_power")
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get user rank by mining power")
		rankByMiningPower = 0
	}

	// 计算质押天数
	stakingDays := int(time.Since(userStake.StartTime).Hours() / 24)

	stats := &models.UserStakingStats{
		UserAddress:         userStake.UserAddress,
		Amount:              userStake.Amount,
		StakingLevel:        userStake.StakingLevel,
		MiningPower:         userStake.MiningPower,
		TotalRewardsClaimed: userStake.TotalRewardsClaimed,
		PendingRewards:      userStake.PendingRewards,
		StartTime:           userStake.StartTime,
		LastUpdateTime:      userStake.LastUpdateTime,
		RankByAmount:        rankByAmount,
		RankByMiningPower:   rankByMiningPower,
		StakingDays:         stakingDays,
		IsActive:            userStake.IsActive,
	}

	// 缓存结果
	if s.cacheManager != nil {
		if err := s.cacheManager.SetUserStats(ctx, userAddress, stats); err != nil {
			s.logger.Error().Err(err).Msg("Failed to set user stats cache")
		}
	}

	return stats, nil
}

// GetLastProcessedBlock 获取最后处理的区块号
func (s *StakingService) GetLastProcessedBlock(ctx context.Context) (uint64, error) {
	return s.eventRepo.GetLastProcessedBlock(ctx)
}

// UpdateLastProcessedBlock 更新最后处理的区块号
func (s *StakingService) UpdateLastProcessedBlock(ctx context.Context, blockNumber uint64) error {
	return s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "synced", "")
}

// clearUserCache 清除用户相关缓存
func (s *StakingService) clearUserCache(ctx context.Context, userAddress string) {
	if s.cacheManager == nil {
		return
	}

	if err := s.cacheManager.InvalidateUser(ctx, userAddress); err != nil {
		s.logger.Error().Err(err).Str("user", userAddress).Msg("Failed to clear user cache")
	}
}

// clearLeaderboardCache 清除排行榜缓存
func (s *StakingService) clearLeaderboardCache(ctx context.Context) {
	if s.cacheManager == nil {
		return
	}

	if err := s.cacheManager.InvalidateLeaderboard(ctx); err != nil {
		s.logger.Error().Err(err).Msg("Failed to clear leaderboard cache")
	}
}
