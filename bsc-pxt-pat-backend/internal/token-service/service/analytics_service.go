package service

import (
	"context"
	"fmt"
	"math/big"
	"strconv"
	"time"

	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// AnalyticsService 统计分析服务
type AnalyticsService struct {
	tokenRepo      *repository.TokenRepository
	stakingRepo    *repository.StakingRepository
	governanceRepo *repository.GovernanceRepository
	cacheManager   *cache.CacheManager
	logger         zerolog.Logger
}

// NewAnalyticsService 创建新的统计分析服务
func NewAnalyticsService(
	tokenRepo *repository.TokenRepository,
	stakingRepo *repository.StakingRepository,
	governanceRepo *repository.GovernanceRepository,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *AnalyticsService {
	return &AnalyticsService{
		tokenRepo:      tokenRepo,
		stakingRepo:    stakingRepo,
		governanceRepo: governanceRepo,
		cacheManager:   cacheManager,
		logger:         logger.With().Str("component", "analytics_service").<PERSON>gger(),
	}
}

// DashboardStats 仪表板统计数据
type DashboardStats struct {
	TokenStats      TokenOverallStats      `json:"token_stats"`
	StakingStats    StakingOverallStats    `json:"staking_stats"`
	GovernanceStats GovernanceOverallStats `json:"governance_stats"`
	NetworkStats    NetworkStats           `json:"network_stats"`
	LastUpdated     time.Time              `json:"last_updated"`
}

// TokenOverallStats 代币总体统计
type TokenOverallStats struct {
	PXTTotalSupply    string `json:"pxt_total_supply"`
	PATTotalSupply    string `json:"pat_total_supply"`
	PXTHolders        int    `json:"pxt_holders"`
	PATHolders        int    `json:"pat_holders"`
	TotalTransfers24h int    `json:"total_transfers_24h"`
	TotalVolume24h    string `json:"total_volume_24h"`
	BurnedPXT         string `json:"burned_pxt"`
	BurnedPAT         string `json:"burned_pat"`
	MarketCap         string `json:"market_cap"`
}

// StakingOverallStats 质押总体统计
type StakingOverallStats struct {
	TotalStaked        string         `json:"total_staked"`
	TotalStakers       int            `json:"total_stakers"`
	AverageStakeAmount string         `json:"average_stake_amount"`
	StakingRatio       float64        `json:"staking_ratio"`
	TotalRewards       string         `json:"total_rewards"`
	APY                float64        `json:"apy"`
	LevelDistribution  map[string]int `json:"level_distribution"`
}

// GovernanceOverallStats 治理总体统计
type GovernanceOverallStats struct {
	TotalProposals    int     `json:"total_proposals"`
	ActiveProposals   int     `json:"active_proposals"`
	TotalVoters       int     `json:"total_voters"`
	ParticipationRate float64 `json:"participation_rate"`
	TotalVotingPower  string  `json:"total_voting_power"`
	PassedProposals   int     `json:"passed_proposals"`
	RejectedProposals int     `json:"rejected_proposals"`
}

// NetworkStats 网络统计
type NetworkStats struct {
	TotalTransactions int     `json:"total_transactions"`
	TPS               float64 `json:"tps"`
	AverageGasPrice   string  `json:"average_gas_price"`
	NetworkHealth     string  `json:"network_health"`
}

// TrendData 趋势数据
type TrendData struct {
	Date  time.Time `json:"date"`
	Value string    `json:"value"`
}

// TokenTrends 代币趋势数据
type TokenTrends struct {
	SupplyTrend    []TrendData `json:"supply_trend"`
	HoldersTrend   []TrendData `json:"holders_trend"`
	TransfersTrend []TrendData `json:"transfers_trend"`
	VolumeTrend    []TrendData `json:"volume_trend"`
}

// StakingTrends 质押趋势数据
type StakingTrends struct {
	StakedAmountTrend []TrendData `json:"staked_amount_trend"`
	StakersTrend      []TrendData `json:"stakers_trend"`
	RewardsTrend      []TrendData `json:"rewards_trend"`
	APYTrend          []TrendData `json:"apy_trend"`
}

// GetDashboardStats 获取仪表板统计数据
func (s *AnalyticsService) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	// 尝试从缓存获取
	cacheKey := "dashboard_stats"
	var stats DashboardStats
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &stats); err == nil {
			return &stats, nil
		}
	}

	// 并发获取各种统计数据
	tokenStatsChan := make(chan TokenOverallStats, 1)
	stakingStatsChan := make(chan StakingOverallStats, 1)
	governanceStatsChan := make(chan GovernanceOverallStats, 1)
	networkStatsChan := make(chan NetworkStats, 1)
	errorChan := make(chan error, 4)

	// 获取代币统计
	go func() {
		tokenStats, err := s.getTokenOverallStats(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		tokenStatsChan <- *tokenStats
	}()

	// 获取质押统计
	go func() {
		stakingStats, err := s.getStakingOverallStats(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		stakingStatsChan <- *stakingStats
	}()

	// 获取治理统计
	go func() {
		governanceStats, err := s.getGovernanceOverallStats(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		governanceStatsChan <- *governanceStats
	}()

	// 获取网络统计
	go func() {
		networkStats, err := s.getNetworkStats(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		networkStatsChan <- *networkStats
	}()

	// 等待所有结果
	stats = DashboardStats{
		LastUpdated: time.Now(),
	}

	for i := 0; i < 4; i++ {
		select {
		case tokenStats := <-tokenStatsChan:
			stats.TokenStats = tokenStats
		case stakingStats := <-stakingStatsChan:
			stats.StakingStats = stakingStats
		case governanceStats := <-governanceStatsChan:
			stats.GovernanceStats = governanceStats
		case networkStats := <-networkStatsChan:
			stats.NetworkStats = networkStats
		case err := <-errorChan:
			s.logger.Warn().Err(err).Msg("Failed to get some stats")
		case <-time.After(10 * time.Second):
			s.logger.Warn().Msg("Timeout getting dashboard stats")
		}
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, stats, 5*time.Minute)
	}

	return &stats, nil
}

// getTokenOverallStats 获取代币总体统计
func (s *AnalyticsService) getTokenOverallStats(ctx context.Context) (*TokenOverallStats, error) {
	stats := &TokenOverallStats{}

	// 获取PXT供应量
	if pxtSupply, err := s.tokenRepo.GetTokenSupply(ctx, "PXT"); err == nil && pxtSupply != nil {
		stats.PXTTotalSupply = pxtSupply.TotalSupply
		stats.BurnedPXT = pxtSupply.BurnedSupply
	}

	// 获取PAT供应量
	if patSupply, err := s.tokenRepo.GetTokenSupply(ctx, "PAT"); err == nil && patSupply != nil {
		stats.PATTotalSupply = patSupply.TotalSupply
		stats.BurnedPAT = patSupply.BurnedSupply
	}

	// 获取持币者数量
	if pxtHolders, err := s.tokenRepo.GetTopHolders(ctx, "PXT", 10000); err == nil {
		stats.PXTHolders = len(pxtHolders)
	} else {
		// 如果数据库没有持币者数据，使用已知的持币者数量
		s.logger.Debug().Err(err).Msg("No PXT holders data in database, using known holders")
		stats.PXTHolders = s.getKnownHoldersCount("PXT")
	}

	if patHolders, err := s.tokenRepo.GetTopHolders(ctx, "PAT", 10000); err == nil {
		stats.PATHolders = len(patHolders)
	} else {
		// 如果数据库没有持币者数据，使用已知的持币者数量
		s.logger.Debug().Err(err).Msg("No PAT holders data in database, using known holders")
		stats.PATHolders = s.getKnownHoldersCount("PAT")
	}

	// TODO: 计算24小时转账数量和交易量
	stats.TotalTransfers24h = 0
	stats.TotalVolume24h = "0"
	stats.MarketCap = "0"

	return stats, nil
}

// getStakingOverallStats 获取质押总体统计
func (s *AnalyticsService) getStakingOverallStats(ctx context.Context) (*StakingOverallStats, error) {
	stats := &StakingOverallStats{
		LevelDistribution: make(map[string]int),
	}

	// 首先尝试从数据库获取质押总览
	if overview, err := s.stakingRepo.GetStakingOverview(ctx); err == nil && overview.TotalUsers > 0 {
		stats.TotalStaked = overview.TotalStaked.Int.String()
		stats.TotalStakers = int(overview.TotalUsers)
		stats.TotalRewards = overview.TotalRewardsClaimd.Int.String()

		// 计算平均质押金额
		if overview.TotalUsers > 0 {
			avgAmount := new(big.Int).Div(overview.TotalStaked.Int, big.NewInt(int64(overview.TotalUsers)))
			stats.AverageStakeAmount = avgAmount.String()
		}
	} else {
		// 如果数据库没有数据，从区块链获取
		s.logger.Debug().Msg("No staking data in database, fetching from blockchain")

		if blockchainStats, err := s.getStakingStatsFromBlockchain(ctx); err == nil {
			stats.TotalStaked = blockchainStats.TotalStaked
			stats.TotalStakers = blockchainStats.TotalStakers
			stats.TotalRewards = blockchainStats.TotalRewards
			stats.AverageStakeAmount = blockchainStats.AverageStakeAmount
		} else {
			s.logger.Warn().Err(err).Msg("Failed to get staking stats from blockchain")
			// 使用默认值
			stats.TotalStaked = "0"
			stats.TotalStakers = 0
			stats.TotalRewards = "0"
			stats.AverageStakeAmount = "0"
		}
	}

	// 获取等级分布
	if distribution, err := s.stakingRepo.GetLevelDistribution(ctx); err == nil {
		for _, dist := range distribution {
			levelName := s.getLevelName(dist.Level)
			stats.LevelDistribution[levelName] = int(dist.Count)
		}
	}

	// 计算质押比例
	if pxtSupply, err := s.tokenRepo.GetTokenSupply(ctx, "PXT"); err == nil && pxtSupply != nil {
		totalSupplyBig := new(big.Int)
		totalSupplyBig.SetString(pxtSupply.TotalSupply, 10)

		totalStakedBig := new(big.Int)
		totalStakedBig.SetString(stats.TotalStaked, 10)

		if totalSupplyBig.Cmp(big.NewInt(0)) > 0 {
			ratio := new(big.Float).Quo(new(big.Float).SetInt(totalStakedBig), new(big.Float).SetInt(totalSupplyBig))
			ratioFloat, _ := ratio.Float64()
			stats.StakingRatio = ratioFloat * 100 // 转换为百分比
		}
	}

	stats.APY = 12.5 // 固定APY，可以后续从配置或合约获取

	return stats, nil
}

// getGovernanceOverallStats 获取治理总体统计
func (s *AnalyticsService) getGovernanceOverallStats(ctx context.Context) (*GovernanceOverallStats, error) {
	stats := &GovernanceOverallStats{}

	// 获取治理统计
	if govStats, err := s.governanceRepo.GetLatestGovernanceStats(ctx); err == nil && govStats != nil {
		stats.TotalProposals = govStats.TotalProposals
		stats.ActiveProposals = govStats.ActiveProposals
		stats.TotalVoters = govStats.TotalVoters
		stats.ParticipationRate = govStats.ParticipationRate
		stats.TotalVotingPower = govStats.TotalVotingPower
	}

	// 获取提案状态分布
	if statusCounts, err := s.governanceRepo.GetProposalsByStatus(ctx); err == nil {
		stats.PassedProposals = int(statusCounts["succeeded"] + statusCounts["executed"])
		stats.RejectedProposals = int(statusCounts["defeated"])
	}

	return stats, nil
}

// getNetworkStats 获取网络统计
func (s *AnalyticsService) getNetworkStats(ctx context.Context) (*NetworkStats, error) {
	stats := &NetworkStats{
		TotalTransactions: 0,
		TPS:               0.0,
		AverageGasPrice:   "0",
		NetworkHealth:     "healthy",
	}

	// TODO: 实现网络统计逻辑
	// 这里需要从区块链或其他数据源获取网络统计信息

	return stats, nil
}

// GetTokenTrends 获取代币趋势数据
func (s *AnalyticsService) GetTokenTrends(ctx context.Context, tokenType string, days int) (*TokenTrends, error) {
	if days < 1 || days > 365 {
		days = 30
	}

	cacheKey := fmt.Sprintf("token_trends:%s:%d", tokenType, days)
	var trends TokenTrends
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &trends); err == nil {
			return &trends, nil
		}
	}

	// 获取代币指标历史
	metrics, err := s.tokenRepo.GetTokenMetricsHistory(ctx, tokenType, days)
	if err != nil {
		return nil, fmt.Errorf("failed to get token metrics history: %w", err)
	}

	trends = TokenTrends{
		SupplyTrend:    make([]TrendData, 0),
		HoldersTrend:   make([]TrendData, 0),
		TransfersTrend: make([]TrendData, 0),
		VolumeTrend:    make([]TrendData, 0),
	}

	// 转换数据格式
	for _, metric := range metrics {
		date := metric.Date

		// 供应量趋势
		if supply, err := s.tokenRepo.GetTokenSupply(ctx, tokenType); err == nil && supply != nil {
			trends.SupplyTrend = append(trends.SupplyTrend, TrendData{
				Date:  date,
				Value: supply.TotalSupply,
			})
		}

		// 持币者趋势
		trends.HoldersTrend = append(trends.HoldersTrend, TrendData{
			Date:  date,
			Value: strconv.Itoa(metric.ActiveHolders),
		})

		// 转账趋势
		trends.TransfersTrend = append(trends.TransfersTrend, TrendData{
			Date:  date,
			Value: strconv.Itoa(metric.Transactions24h),
		})

		// 交易量趋势
		trends.VolumeTrend = append(trends.VolumeTrend, TrendData{
			Date:  date,
			Value: metric.Volume24h,
		})
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, trends, 30*time.Minute)
	}

	return &trends, nil
}

// GetStakingTrends 获取质押趋势数据
func (s *AnalyticsService) GetStakingTrends(ctx context.Context, days int) (*StakingTrends, error) {
	if days < 1 || days > 365 {
		days = 30
	}

	cacheKey := fmt.Sprintf("staking_trends:%d", days)
	var trends StakingTrends
	if s.cacheManager != nil {
		if err := s.cacheManager.Get(ctx, cacheKey, &trends); err == nil {
			return &trends, nil
		}
	}

	// TODO: 实现质押趋势数据获取
	// 这里需要从数据库获取历史质押数据

	trends = StakingTrends{
		StakedAmountTrend: make([]TrendData, 0),
		StakersTrend:      make([]TrendData, 0),
		RewardsTrend:      make([]TrendData, 0),
		APYTrend:          make([]TrendData, 0),
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, trends, 30*time.Minute)
	}

	return &trends, nil
}

// getLevelName 获取等级名称
func (s *AnalyticsService) getLevelName(level models.StakingLevel) string {
	switch level {
	case models.DingJi:
		return "丁级"
	case models.ChengJi:
		return "丙级"
	case models.YiJi:
		return "乙级"
	case models.JiaJi:
		return "甲级"
	case models.ShiJue:
		return "十爵"
	case models.ShuangShiJue:
		return "双十爵"
	case models.ZhiZun:
		return "至尊"
	default:
		return "未知"
	}
}

// BlockchainStakingStats 从区块链获取的质押统计
type BlockchainStakingStats struct {
	TotalStaked        string
	TotalStakers       int
	TotalRewards       string
	AverageStakeAmount string
}

// getStakingStatsFromBlockchain 从区块链获取质押统计数据
func (s *AnalyticsService) getStakingStatsFromBlockchain(ctx context.Context) (*BlockchainStakingStats, error) {
	// 这里需要调用区块链客户端获取质押数据
	// 由于当前的 AnalyticsService 没有直接的区块链客户端，我们先返回模拟数据
	// 在实际实现中，需要注入区块链客户端或通过其他服务获取数据

	s.logger.Debug().Msg("Fetching staking stats from blockchain (simulated)")

	// TODO: 实现真实的区块链数据获取
	// 这里可以通过以下方式获取：
	// 1. 调用质押合约的 totalStaked() 方法
	// 2. 调用质押合约的 totalParticipants() 方法
	// 3. 调用奖励分配器的相关方法获取总奖励

	// 暂时返回模拟数据，避免编译错误
	return &BlockchainStakingStats{
		TotalStaked:        "0",
		TotalStakers:       0,
		TotalRewards:       "0",
		AverageStakeAmount: "0",
	}, nil
}

// getKnownHoldersCount 获取已知的持币者数量
func (s *AnalyticsService) getKnownHoldersCount(tokenType string) int {
	// 基于最新部署记录中的已知地址 (2025-07-04)
	knownAddresses := map[string][]string{
		"PXT": {
			"0xc4A114182ddbaE513BCFD6976B8856a845f7bB57", // 部署者
			"0x2566916DD0E0E8B4c10B228F92D7418a337df2cb", // 国库
			"0x458ceE5679866a1873B129275bDe34dd8AA8180e", // 操作员
			"0xA2e0E0DCaDe8e81c775adc94F2096a73EF5060a4", // 测试用户1
			"0xe597349db8d3E449A240D7b465cd193d7D6b69e8", // 测试用户2
			"0x4A899142d1ec43bEB066164Fa2514c3E630f074c", // 测试用户3
			"0x1Aa6dA1b4cD18A0fC0B2A513f96209a39A261E6f", // 中国大陆池子
			"0xf8CfA57C19C010A40064fcc801419D52Ce658A43", // 全球池子
		},
		"PAT": {
			"0x2566916DD0E0E8B4c10B228F92D7418a337df2cb", // 国库
			"0xA2e0E0DCaDe8e81c775adc94F2096a73EF5060a4", // 测试用户1
			"0xe597349db8d3E449A240D7b465cd193d7D6b69e8", // 测试用户2
			"0x4A899142d1ec43bEB066164Fa2514c3E630f074c", // 测试用户3
			"0x1Aa6dA1b4cD18A0fC0B2A513f96209a39A261E6f", // 中国大陆池子
			"0xf8CfA57C19C010A40064fcc801419D52Ce658A43", // 全球池子
		},
	}

	if addresses, exists := knownAddresses[tokenType]; exists {
		return len(addresses)
	}

	return 0
}
