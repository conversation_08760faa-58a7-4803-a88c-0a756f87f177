package service

import (
	"context"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/utils"
)

// SyncService 数据同步服务
type SyncService struct {
	client     *blockchain.MultiContractClient
	config     *config.BlockchainConfig
	syncConfig *config.SyncConfig // 🆕 同步配置
	logger     zerolog.Logger
	syncLogger *utils.SyncLogger // 🆕 彩色同步日志器

	// Repositories
	tokenRepo      *repository.TokenRepository
	governanceRepo *repository.GovernanceRepository
	stakingRepo    *repository.StakingRepository

	// 🆕 Cache manager for automatic cache refresh
	cacheManager interface {
		InvalidateOverview() error
		InvalidateAll(ctx context.Context) error
	}

	// Sync state
	isRunning bool
}

// NewSyncService 创建新的同步服务
func NewSyncService(
	client *blockchain.MultiContractClient,
	cfg *config.BlockchainConfig,
	tokenRepo *repository.TokenRepository,
	governanceRepo *repository.GovernanceRepository,
	stakingRepo *repository.StakingRepository,
	cacheManager interface {
		InvalidateOverview() error
		InvalidateAll(ctx context.Context) error
	},
	logger zerolog.Logger,
	syncConfig *config.SyncConfig, // � 同步配置
) *SyncService {
	// 创建彩色同步日志器
	syncLoggerConfig := utils.SyncLoggerConfig{
		EnableColorfulLogs:      true,
		ShowBlockProgress:       true,
		LogSyncProgressInterval: 50,
		LogTransferDetails:      false,
		LogBalanceUpdates:       false,
	}

	// 如果有同步配置，使用配置中的值
	if syncConfig != nil {
		syncLoggerConfig.EnableColorfulLogs = syncConfig.EnableColorfulLogs
		syncLoggerConfig.ShowBlockProgress = syncConfig.ShowBlockProgress
		syncLoggerConfig.LogSyncProgressInterval = syncConfig.LogSyncProgressInterval
		syncLoggerConfig.LogTransferDetails = syncConfig.LogTransferDetails
		syncLoggerConfig.LogBalanceUpdates = syncConfig.LogBalanceUpdates
	}

	syncLogger := utils.NewSyncLogger(logger, syncLoggerConfig)

	return &SyncService{
		client:         client,
		config:         cfg,
		syncConfig:     syncConfig,
		logger:         logger.With().Str("component", "sync_service").Logger(),
		syncLogger:     syncLogger,
		tokenRepo:      tokenRepo,
		governanceRepo: governanceRepo,
		stakingRepo:    stakingRepo,
		cacheManager:   cacheManager,
		isRunning:      false,
	}
}

// Start 启动数据同步
func (s *SyncService) Start(ctx context.Context) error {
	if s.isRunning {
		return fmt.Errorf("sync service is already running")
	}

	s.isRunning = true
	defer func() { s.isRunning = false }()

	s.logger.Info().Msg("Starting data sync service...")

	// 创建定时器
	syncInterval := 30 // 默认30秒
	if s.syncConfig != nil && s.syncConfig.SyncInterval > 0 {
		syncInterval = s.syncConfig.SyncInterval
	}
	ticker := time.NewTicker(time.Duration(syncInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次同步
	if err := s.syncAllData(ctx); err != nil {
		s.logger.Error().Err(err).Msg("Initial sync failed")
	}

	// 定时同步循环
	syncCount := 0
	for {
		select {
		case <-ctx.Done():
			s.logger.Info().Msg("Data sync service stopped")
			return ctx.Err()

		case <-ticker.C:
			syncCount++
			// 每20次同步做一次全量同步，其他时候做轻量级同步
			if syncCount%20 == 0 {
				if err := s.syncAllData(ctx); err != nil {
					s.logger.Error().Err(err).Msg("Full sync failed")
				}
			} else {
				if err := s.syncLightweightData(ctx); err != nil {
					s.logger.Error().Err(err).Msg("Lightweight sync failed")
				}
			}
		}
	}
}

// syncAllData 同步所有数据
func (s *SyncService) syncAllData(ctx context.Context) error {
	startTime := time.Now()
	s.syncLogger.LogSyncStart("全量数据")

	// 并发同步不同类型的数据
	errChan := make(chan error, 8)

	// 同步代币供应量
	go func() {
		errChan <- s.syncTokenSupply(ctx)
	}()

	// 同步代币余额（热门地址）
	go func() {
		errChan <- s.syncTokenBalances(ctx)
	}()

	// 同步持币者排名
	go func() {
		errChan <- s.syncHolderRankings(ctx)
	}()

	// 同步治理统计
	go func() {
		errChan <- s.syncGovernanceStats(ctx)
	}()

	// 同步质押统计
	go func() {
		errChan <- s.syncStakingStats(ctx)
	}()

	// 同步质押数据（从转账数据）
	go func() {
		errChan <- s.syncStakingDataFromTransfers(ctx)
	}()

	// 同步代币销毁数据
	go func() {
		errChan <- s.syncTokenBurnData(ctx)
	}()

	// 同步代币经济指标
	go func() {
		errChan <- s.SyncTokenMetrics(ctx)
	}()

	// 等待所有同步完成
	var errors []error
	for i := 0; i < 8; i++ {
		if err := <-errChan; err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		s.syncLogger.LogSyncError("全量数据", fmt.Errorf("sync failed with %d errors", len(errors)))
		return errors[0] // 返回第一个错误
	}

	s.syncLogger.LogSyncComplete("全量数据", time.Since(startTime))
	return nil
}

// syncLightweightData 轻量级数据同步（只同步关键数据）
func (s *SyncService) syncLightweightData(ctx context.Context) error {
	startTime := time.Now()
	s.syncLogger.LogSyncStart("轻量级数据")

	// 只同步最重要的数据，减少API调用
	errChan := make(chan error, 3)

	// 同步代币余额（热门地址）
	go func() {
		errChan <- s.syncTokenBalances(ctx)
	}()

	// 同步质押统计
	go func() {
		errChan <- s.syncStakingStats(ctx)
	}()

	// 同步代币经济指标
	go func() {
		errChan <- s.SyncTokenMetrics(ctx)
	}()

	// 等待所有同步完成
	var errors []error
	for i := 0; i < 3; i++ {
		if err := <-errChan; err != nil {
			errors = append(errors, err)
		}
	}

	// 如果有错误，记录并返回
	if len(errors) > 0 {
		s.syncLogger.LogSyncError("轻量级数据", fmt.Errorf("sync failed with %d errors", len(errors)))
		return errors[0] // 返回第一个错误
	}

	s.syncLogger.LogSyncComplete("轻量级数据", time.Since(startTime))
	return nil
}

// syncTokenSupply 同步代币供应量
func (s *SyncService) syncTokenSupply(ctx context.Context) error {
	contracts := s.client.GetContracts()

	// 同步PXT供应量
	if err := s.syncTokenSupplyForToken(ctx, contracts.Addresses.PXTToken, "PXT"); err != nil {
		return fmt.Errorf("failed to sync PXT supply: %w", err)
	}

	// 同步PAT供应量
	if err := s.syncTokenSupplyForToken(ctx, contracts.Addresses.PATToken, "PAT"); err != nil {
		return fmt.Errorf("failed to sync PAT supply: %w", err)
	}

	return nil
}

// syncTokenSupplyForToken 同步单个代币的供应量
func (s *SyncService) syncTokenSupplyForToken(ctx context.Context, tokenAddress common.Address, tokenType string) error {
	// 获取总供应量
	totalSupply, err := s.client.GetTokenTotalSupply(ctx, tokenAddress)
	if err != nil {
		s.logger.Warn().Err(err).Str("token_type", tokenType).Str("address", tokenAddress.Hex()).Msg("Failed to get token supply from chain, skipping")
		return nil // 不返回错误，避免影响其他同步任务
	}

	// 检查数值是否过大，如果过大则记录警告但不插入数据库
	supplyStr := totalSupply.String()
	if len(supplyStr) > 60 { // 如果数字位数超过60位，可能会导致数据库溢出
		s.logger.Warn().
			Str("token_type", tokenType).
			Str("total_supply", supplyStr).
			Int("digits", len(supplyStr)).
			Msg("Token supply value too large for database, skipping update")
		return nil
	}

	// 检查是否需要更新（避免重复更新相同数据）
	existingSupply, err := s.tokenRepo.GetTokenSupply(ctx, tokenType)
	if err == nil && existingSupply != nil {
		if existingSupply.TotalSupply == supplyStr {
			// 数据没有变化，跳过更新
			s.logger.Debug().
				Str("token_type", tokenType).
				Str("total_supply", supplyStr).
				Msg("Token supply unchanged, skipping update")
			return nil
		}
	}

	// 计算锁定量和销毁量
	var lockedSupply, burnedSupply string

	if tokenType == "PXT" {
		// 对于PXT代币，计算总锁定量和销毁量
		totalLocked, totalBurned, err := s.calculatePXTSupplyDetails(ctx, tokenAddress)
		if err != nil {
			s.logger.Warn().Err(err).Str("token_type", tokenType).Msg("Failed to calculate supply details, using defaults")
			lockedSupply = "0"
			burnedSupply = "0"
		} else {
			lockedSupply = totalLocked.String()
			burnedSupply = totalBurned.String()
		}
	} else {
		// PAT代币暂时没有锁定和销毁机制
		lockedSupply = "0"
		burnedSupply = "0"
	}

	// 计算流通供应量 = 总供应量 - 锁定量 - 销毁量
	totalSupplyBig := new(big.Int)
	totalSupplyBig.SetString(supplyStr, 10)

	lockedSupplyBig := new(big.Int)
	lockedSupplyBig.SetString(lockedSupply, 10)

	burnedSupplyBig := new(big.Int)
	burnedSupplyBig.SetString(burnedSupply, 10)

	circulatingSupplyBig := new(big.Int)
	circulatingSupplyBig.Sub(totalSupplyBig, lockedSupplyBig)
	circulatingSupplyBig.Sub(circulatingSupplyBig, burnedSupplyBig)

	supply := &models.TokenSupply{
		TokenType:         tokenType,
		TotalSupply:       supplyStr,
		CirculatingSupply: circulatingSupplyBig.String(),
		LockedSupply:      lockedSupply,
		BurnedSupply:      burnedSupply,
	}

	// 保存到数据库
	if err := s.tokenRepo.CreateOrUpdateSupply(ctx, supply); err != nil {
		s.logger.Warn().Err(err).
			Str("token_type", tokenType).
			Str("total_supply", supplyStr).
			Msg("Failed to update token supply in database, possibly due to field overflow")
		return nil // 不返回错误，避免影响其他同步任务
	}

	s.logger.Debug().
		Str("token", tokenType).
		Str("total_supply", supplyStr).
		Msg("Token supply updated")

	return nil
}

// syncTokenBalances 同步代币余额（热门地址）
func (s *SyncService) syncTokenBalances(ctx context.Context) error {
	// 获取需要同步余额的地址列表
	// 这里可以是质押用户、大户等
	addresses, err := s.getAddressesToSync(ctx)
	if err != nil {
		return fmt.Errorf("failed to get addresses to sync: %w", err)
	}

	contracts := s.client.GetContracts()

	// 同步每个地址的PXT和PAT余额
	for _, address := range addresses {
		// 同步PXT余额
		if err := s.syncBalanceForAddress(ctx, address, contracts.Addresses.PXTToken, "PXT"); err != nil {
			s.logger.Warn().Err(err).Str("address", address).Msg("Failed to sync PXT balance")
		}

		// 同步PAT余额
		if err := s.syncBalanceForAddress(ctx, address, contracts.Addresses.PATToken, "PAT"); err != nil {
			s.logger.Warn().Err(err).Str("address", address).Msg("Failed to sync PAT balance")
		}
	}

	return nil
}

// getAddressesToSync 获取需要同步余额的地址列表
func (s *SyncService) getAddressesToSync(ctx context.Context) ([]string, error) {
	addressSet := make(map[string]bool)

	// 1. 从转账记录中获取所有涉及的地址
	transfers, err := s.tokenRepo.GetAllTransfers(ctx)
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get transfers for address sync")
	} else {
		for _, transfer := range transfers {
			// 添加发送方地址（排除零地址）
			if transfer.FromAddress != "0x0000000000000000000000000000000000000000" {
				addressSet[transfer.FromAddress] = true
			}
			// 添加接收方地址
			addressSet[transfer.ToAddress] = true
		}
	}

	// 2. 添加系统合约地址 - 使用真实部署的合约地址 (2025-07-04)
	contracts := s.client.GetContracts()
	knownAddresses := []string{
		strings.ToLower(contracts.Addresses.PXTToken.Hex()),          // PXT 代币合约
		strings.ToLower(contracts.Addresses.PATToken.Hex()),          // PAT 代币合约
		strings.ToLower(contracts.Addresses.StakingPool.Hex()),       // 质押池合约
		strings.ToLower(contracts.Addresses.DAO.Hex()),               // DAO 合约
		strings.ToLower(contracts.Addresses.Treasury.Hex()),          // 国库合约
		strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex()), // 中国大陆池子
		strings.ToLower(contracts.Addresses.GlobalPool.Hex()),        // 全球池子
	}

	for _, addr := range knownAddresses {
		addressSet[strings.ToLower(addr)] = true
	}

	// 3. 从质押记录中获取地址
	stakes, err := s.tokenRepo.GetAllUserStakes(ctx)
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get stakes for address sync")
	} else {
		for _, stake := range stakes {
			addressSet[strings.ToLower(stake.UserAddress)] = true
		}
	}

	// 转换为切片
	addresses := make([]string, 0, len(addressSet))
	for addr := range addressSet {
		addresses = append(addresses, addr)
	}

	s.logger.Debug().Int("address_count", len(addresses)).Msg("Found addresses to sync")
	return addresses, nil
}

// syncBalanceForAddress 同步单个地址的代币余额
func (s *SyncService) syncBalanceForAddress(ctx context.Context, address string, tokenAddress common.Address, tokenType string) error {
	// 标准化地址为小写
	address = strings.ToLower(address)
	userAddress := common.HexToAddress(address)

	// 跳过对代币合约本身的余额查询，避免 execution reverted 错误
	if strings.ToLower(tokenAddress.Hex()) == address {
		s.logger.Debug().
			Str("address", address).
			Str("token_type", tokenType).
			Msg("Skipping balance query for token contract itself")
		return nil
	}

	// 获取余额
	balance, err := s.client.GetTokenBalance(ctx, tokenAddress, userAddress)
	if err != nil {
		return fmt.Errorf("failed to get balance: %w", err)
	}

	// 获取锁定余额（仅对PXT代币）
	var lockedAmount string = "0"
	if tokenType == "PXT" {
		if locked, err := s.client.GetTokenLockedBalance(ctx, tokenAddress, userAddress); err == nil {
			lockedAmount = locked.String()
		} else {
			s.logger.Debug().Err(err).Str("address", address).Msg("Failed to get locked balance")
		}
	}

	// 创建或更新余额记录
	tokenBalance := &models.TokenBalance{
		UserAddress:  strings.ToLower(address),
		TokenType:    tokenType,
		Balance:      balance.String(),
		LockedAmount: lockedAmount,
	}

	if err := s.tokenRepo.CreateOrUpdateBalance(ctx, tokenBalance); err != nil {
		return fmt.Errorf("failed to update balance: %w", err)
	}

	s.logger.Debug().
		Str("address", address).
		Str("token", tokenType).
		Str("balance", balance.String()).
		Str("locked", lockedAmount).
		Msg("Balance updated")

	return nil
}

// syncGovernanceStats 同步治理统计
func (s *SyncService) syncGovernanceStats(ctx context.Context) error {
	s.logger.Debug().Msg("Starting governance stats sync...")

	db := s.tokenRepo.GetDB()

	// 1. 统计提案数量
	var totalProposals, activeProposals, executedProposals int64

	// 总提案数
	if err := db.Model(&models.Proposal{}).Count(&totalProposals).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count total proposals")
		return fmt.Errorf("failed to count total proposals: %w", err)
	}

	// 活跃提案数
	if err := db.Model(&models.Proposal{}).Where("status = ?", "active").Count(&activeProposals).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count active proposals")
		return fmt.Errorf("failed to count active proposals: %w", err)
	}

	// 已执行的提案数
	if err := db.Model(&models.Proposal{}).Where("status = ?", "executed").Count(&executedProposals).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count executed proposals")
		return fmt.Errorf("failed to count executed proposals: %w", err)
	}

	// 2. 统计投票参与度
	var totalVoters, activeVoters int64

	// 总投票者数量（所有时间）
	if err := db.Model(&models.Vote{}).Distinct("voter").Count(&totalVoters).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count total voters")
		return fmt.Errorf("failed to count total voters: %w", err)
	}

	// 活跃投票者数量（最近30天）
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	if err := db.Model(&models.Vote{}).
		Where("timestamp > ?", thirtyDaysAgo).
		Distinct("voter").Count(&activeVoters).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count active voters")
		return fmt.Errorf("failed to count active voters: %w", err)
	}

	// 3. 计算投票权重分布
	var totalVotingPower string
	if err := db.Model(&models.VotingPower{}).
		Select("COALESCE(SUM(CAST(total_power AS NUMERIC)), 0)::TEXT").
		Scan(&totalVotingPower).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to calculate total voting power")
		return fmt.Errorf("failed to calculate total voting power: %w", err)
	}

	// 4. 计算治理参与率和平均法定人数
	var participationRate, averageQuorum float64
	if totalProposals > 0 {
		participationRate = float64(activeVoters) / float64(totalVoters) * 100
	}

	// 计算平均法定人数（最近的提案）
	var avgQuorumStr string
	if err := db.Model(&models.Proposal{}).
		Where("status IN ?", []string{"succeeded", "defeated", "executed"}).
		Select("COALESCE(AVG(CAST(for_votes AS NUMERIC) + CAST(against_votes AS NUMERIC) + CAST(abstain_votes AS NUMERIC)), 0)").
		Scan(&avgQuorumStr).Error; err != nil {
		s.logger.Warn().Err(err).Msg("Failed to calculate average quorum")
		averageQuorum = 0
	} else {
		if avgQuorumFloat, err := strconv.ParseFloat(avgQuorumStr, 64); err == nil {
			averageQuorum = avgQuorumFloat
		}
	}

	// 更新或创建治理统计记录
	today := time.Now().Truncate(24 * time.Hour)
	governanceStats := &models.GovernanceStats{
		TotalProposals:    int(totalProposals),
		ActiveProposals:   int(activeProposals),
		ExecutedProposals: int(executedProposals),
		TotalVoters:       int(totalVoters),
		ActiveVoters:      int(activeVoters),
		TotalVotingPower:  totalVotingPower,
		ParticipationRate: participationRate,
		AverageQuorum:     averageQuorum,
		Date:              today,
	}

	// 使用 CreateOrUpdateGovernanceStats 方法避免重复键错误
	if err := s.governanceRepo.CreateOrUpdateGovernanceStats(ctx, governanceStats); err != nil {
		s.logger.Error().Err(err).Msg("Failed to save governance stats")
		return fmt.Errorf("failed to save governance stats: %w", err)
	}

	s.logger.Info().
		Int64("total_proposals", totalProposals).
		Int64("active_proposals", activeProposals).
		Int64("executed_proposals", executedProposals).
		Int64("total_voters", totalVoters).
		Int64("active_voters", activeVoters).
		Float64("participation_rate", participationRate).
		Float64("average_quorum", averageQuorum).
		Msg("Governance stats sync completed")

	return nil
}

// syncStakingStats 同步质押统计
func (s *SyncService) syncStakingStats(ctx context.Context) error {
	s.logger.Debug().Msg("Starting staking stats sync...")

	// 🆕 优先从数据库计算总质押量（更可靠）
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"
	totalStaked, err := s.calculateTotalStakedFromDB(ctx, treasuryAddress)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to calculate total staked from database")

		// 🆕 如果数据库计算失败，尝试从合约获取
		totalStaked, err = s.getTotalStakedFromContract(ctx)
		if err != nil {
			s.logger.Error().Err(err).Msg("Failed to get total staked from contract")
			return fmt.Errorf("failed to get total staked amount: %w", err)
		}
		s.logger.Info().Msg("Using total staked amount from contract as fallback")
	} else {
		s.logger.Info().Msg("Using total staked amount calculated from database")
	}

	// 🆕 更新质押统计表
	err = s.updateStakingStats(ctx, totalStaked)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to update staking stats")
		return fmt.Errorf("failed to update staking stats: %w", err)
	}

	s.logger.Info().
		Str("total_staked", totalStaked.String()).
		Msg("Successfully synced staking stats")

	return nil
}

// SyncUserStakingInfo 同步用户质押信息
func (s *SyncService) SyncUserStakingInfo(ctx context.Context, userAddress string) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	s.logger.Debug().
		Str("user", userAddress).
		Msg("Starting user staking info sync...")

	// 1. 调用合约获取用户质押信息
	userAddr := common.HexToAddress(userAddress)
	stakingInfo, err := s.client.GetUserStakingInfo(ctx, userAddr)
	if err != nil {
		s.logger.Error().Err(err).Str("user", userAddress).Msg("Failed to get user staking info from contract")
		return fmt.Errorf("failed to get user staking info: %w", err)
	}

	// 2. 获取用户累计奖励
	totalRewardsClaimed, err := s.client.GetUserTotalRewardsClaimed(ctx, userAddr)
	if err != nil {
		s.logger.Error().Err(err).Str("user", userAddress).Msg("Failed to get user total rewards claimed from contract")
		return fmt.Errorf("failed to get user total rewards claimed: %w", err)
	}

	// 3. 更新数据库中的用户质押信息
	err = s.stakingRepo.UpdateUserStakeFromContract(ctx, userAddress, stakingInfo, totalRewardsClaimed)
	if err != nil {
		s.logger.Error().Err(err).Str("user", userAddress).Msg("Failed to update user stake in database")
		return fmt.Errorf("failed to update user stake: %w", err)
	}

	// 🆕 4. 自动刷新质押概览缓存（如果用户质押金额发生变化）
	if stakingInfo.Amount.Cmp(big.NewInt(0)) > 0 {
		s.logger.Debug().
			Str("user", userAddress).
			Msg("User has staking amount, refreshing overview cache...")

		// 刷新质押概览缓存
		if err := s.refreshStakingOverviewCache(ctx); err != nil {
			s.logger.Warn().Err(err).Msg("Failed to refresh staking overview cache")
			// 不返回错误，因为用户数据同步已经成功
		}
	}

	s.logger.Info().
		Str("user", userAddress).
		Str("staked_amount", stakingInfo.Amount.String()).
		Str("total_rewards_claimed", totalRewardsClaimed.String()).
		Msg("User staking info synced successfully")

	return nil
}

// 🆕 refreshStakingOverviewCache 刷新质押概览缓存
func (s *SyncService) refreshStakingOverviewCache(ctx context.Context) error {
	if s.cacheManager == nil {
		s.logger.Debug().Msg("Cache manager not available, skipping cache refresh")
		return nil
	}

	s.logger.Debug().Msg("Refreshing staking overview cache...")

	if err := s.cacheManager.InvalidateOverview(); err != nil {
		return fmt.Errorf("failed to invalidate overview cache: %w", err)
	}

	s.logger.Debug().Msg("Staking overview cache refreshed successfully")
	return nil
}

// 🆕 refreshAllStakingCaches 刷新所有质押相关缓存
func (s *SyncService) refreshAllStakingCaches(ctx context.Context) error {
	if s.cacheManager == nil {
		s.logger.Debug().Msg("Cache manager not available, skipping cache refresh")
		return nil
	}

	s.logger.Debug().Msg("Refreshing all staking caches...")

	// 刷新所有缓存
	if err := s.cacheManager.InvalidateAll(ctx); err != nil {
		s.logger.Warn().Err(err).Msg("Failed to invalidate all caches")
		return err
	}

	s.logger.Debug().Msg("All staking caches refreshed successfully")
	return nil
}

// StartAutoSync 启动自动同步服务
func (s *SyncService) StartAutoSync(ctx context.Context) {
	s.logger.Info().Msg("启动自动同步服务...")

	// 🆕 使用配置中的同步间隔
	transferInterval := 5 * time.Minute // 默认5分钟
	userInterval := 10 * time.Minute    // 默认10分钟
	enableDailySync := true             // 默认启用每日同步
	dailyHour, dailyMinute := 3, 0      // 默认凌晨3点

	if s.syncConfig != nil {
		transferInterval = s.syncConfig.GetTransferSyncInterval()
		userInterval = s.syncConfig.GetUserSyncInterval()
		enableDailySync = s.syncConfig.ShouldEnableDailySync()
		dailyHour, dailyMinute = s.syncConfig.GetDailyFullSyncTime()
	}

	transferTicker := time.NewTicker(transferInterval)
	userTicker := time.NewTicker(userInterval)
	defer transferTicker.Stop()
	defer userTicker.Stop()

	// 🌙 启动每日完整同步调度器（如果启用）
	if enableDailySync {
		go s.startDailyFullSync(ctx)
		s.logger.Info().
			Int("hour", dailyHour).
			Int("minute", dailyMinute).
			Msg("✅ 每日完整同步已启用")
	} else {
		s.logger.Info().Msg("⚠️ 每日完整同步已禁用")
	}

	for {
		select {
		case <-ctx.Done():
			s.logger.Info().Msg("自动同步服务已停止")
			return
		case <-transferTicker.C:
			// 🆕 轻量级定期同步（只同步最新数据）
			s.logger.Debug().Msg("开始轻量级同步...")
			if err := s.syncTransferData(ctx); err != nil {
				s.logger.Error().Err(err).Msg("定期同步转账数据失败")
			}
			if err := s.syncStakingDataFromTransfers(ctx); err != nil {
				s.logger.Error().Err(err).Msg("定期同步质押数据失败")
			}
			// 只同步质押统计，其他重量级同步留给凌晨3点
			if err := s.syncStakingStats(ctx); err != nil {
				s.logger.Error().Err(err).Msg("定期同步质押统计失败")
			}
		case <-userTicker.C:
			s.syncActiveUsers(ctx)
		}
	}
}

// syncActiveUsers 同步活跃用户的数据
func (s *SyncService) syncActiveUsers(ctx context.Context) {
	s.logger.Debug().Msg("开始同步活跃用户数据...")

	// 获取最近24小时内有活动的用户
	activeUsers, err := s.stakingRepo.GetActiveUsers(ctx, 24*time.Hour)
	if err != nil {
		s.logger.Error().Err(err).Msg("获取活跃用户失败")
		return
	}

	s.logger.Info().Int("count", len(activeUsers)).Msg("找到活跃用户")

	// 🚀 并发同步用户数据（优化并发控制）
	const maxConcurrency = 8 // 增加并发数以提高性能
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var syncErrors []error

	for _, userAddress := range activeUsers {
		wg.Add(1)
		go func(addr string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			if err := s.SyncUserStakingInfo(ctx, addr); err != nil {
				mu.Lock()
				syncErrors = append(syncErrors, fmt.Errorf("sync user %s failed: %w", addr, err))
				mu.Unlock()

				s.logger.Error().Err(err).Str("address", addr).Msg("同步用户数据失败")
			} else {
				s.logger.Debug().Str("address", addr).Msg("同步用户数据成功")
			}
		}(userAddress)
	}

	wg.Wait()

	// 记录同步结果
	if len(syncErrors) > 0 {
		s.logger.Warn().
			Int("error_count", len(syncErrors)).
			Int("total_users", len(activeUsers)).
			Msg("部分活跃用户同步失败")
	} else {
		s.logger.Info().
			Int("synced_users", len(activeUsers)).
			Msg("活跃用户数据同步完成")
	}
}

// StartEventListener 启动事件监听器
func (s *SyncService) StartEventListener(ctx context.Context) {
	s.logger.Info().Msg("启动区块链事件监听器...")

	// 监听奖励领取事件
	go s.listenRewardClaimedEvents(ctx)

	// 监听质押事件
	go s.listenStakingEvents(ctx)
}

// listenRewardClaimedEvents 监听奖励领取事件
func (s *SyncService) listenRewardClaimedEvents(ctx context.Context) {
	s.logger.Info().Msg("开始监听奖励领取事件...")

	// 这里应该实现实际的事件监听逻辑
	// 由于当前的区块链客户端可能不支持事件监听，我们先实现一个轮询机制
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info().Msg("奖励领取事件监听器已停止")
			return
		case <-ticker.C:
			// 这里可以实现检查最新区块的逻辑
			// 暂时跳过，等待实际的事件监听实现
		}
	}
}

// listenStakingEvents 监听质押事件
func (s *SyncService) listenStakingEvents(ctx context.Context) {
	s.logger.Info().Msg("开始监听质押事件...")

	ticker := time.NewTicker(60 * time.Second) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info().Msg("质押事件监听器已停止")
			return
		case <-ticker.C:
			// 这里可以实现检查最新质押事件的逻辑
			// 暂时跳过，等待实际的事件监听实现
		}
	}
}

// SyncUserOnDemand 按需同步用户数据（当检测到用户活动时调用）
func (s *SyncService) SyncUserOnDemand(ctx context.Context, userAddress string) error {
	s.logger.Info().Str("address", userAddress).Msg("按需同步用户数据...")

	// 异步执行同步，避免阻塞API响应
	go func() {
		if err := s.SyncUserStakingInfo(context.Background(), userAddress); err != nil {
			s.logger.Error().Err(err).Str("address", userAddress).Msg("按需同步用户数据失败")
		} else {
			s.logger.Info().Str("address", userAddress).Msg("按需同步用户数据成功")
		}
	}()

	return nil
}

// SyncTokenMetrics 同步代币经济指标
func (s *SyncService) SyncTokenMetrics(ctx context.Context) error {
	s.logger.Debug().Msg("Starting token metrics sync...")

	db := s.tokenRepo.GetDB()
	today := time.Now().Truncate(24 * time.Hour)
	yesterday := today.AddDate(0, 0, -1)

	// 计算并保存代币经济指标
	for _, tokenType := range []string{"PXT", "PAT"} {
		s.logger.Debug().Str("token_type", tokenType).Msg("Calculating metrics for token")

		// 1. 计算24小时交易量
		var volume24h string
		if err := db.Model(&models.TokenTransfer{}).
			Where("token_type = ? AND timestamp >= ?", tokenType, yesterday).
			Select("COALESCE(SUM(CAST(amount AS NUMERIC)), 0)::TEXT").
			Scan(&volume24h).Error; err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to calculate 24h volume")
			volume24h = "0"
		}

		// 2. 计算24小时交易数量
		var transactions24h int64
		if err := db.Model(&models.TokenTransfer{}).
			Where("token_type = ? AND timestamp >= ?", tokenType, yesterday).
			Count(&transactions24h).Error; err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to count 24h transactions")
			transactions24h = 0
		}

		// 3. 计算活跃持币者数量（余额大于0）
		var activeHolders int64
		if err := db.Model(&models.TokenBalance{}).
			Where("token_type = ? AND CAST(balance AS NUMERIC) > 0", tokenType).
			Count(&activeHolders).Error; err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to count active holders")
			activeHolders = 0
		}

		// 4. 获取总供应量
		var totalSupply string
		if err := db.Model(&models.TokenSupply{}).
			Where("token_type = ?", tokenType).
			Select("total_supply").
			Scan(&totalSupply).Error; err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to get total supply")
			totalSupply = "0"
		}

		// 5. 计算代币流通速度（24小时交易量 / 流通供应量）
		var velocityRatio float64
		totalSupplyBig, totalSupplyOk := new(big.Int).SetString(totalSupply, 10)
		if totalSupplyOk && totalSupplyBig.Cmp(big.NewInt(0)) > 0 {
			if volume24hBig, ok := new(big.Int).SetString(volume24h, 10); ok {
				velocityFloat := new(big.Float).Quo(new(big.Float).SetInt(volume24hBig), new(big.Float).SetInt(totalSupplyBig))
				velocityRatio, _ = velocityFloat.Float64()
			}
		}

		// 6. 计算质押比率（仅对PXT）
		var stakingRatio float64
		if tokenType == "PXT" && totalSupplyOk && totalSupplyBig.Cmp(big.NewInt(0)) > 0 {
			var totalStaked string
			if err := db.Model(&models.UserStake{}).
				Where("is_active = true").
				Select("COALESCE(SUM(CAST(amount AS NUMERIC)), 0)::TEXT").
				Scan(&totalStaked).Error; err == nil {
				if totalStakedBig, ok := new(big.Int).SetString(totalStaked, 10); ok {
					stakingFloat := new(big.Float).Quo(new(big.Float).SetInt(totalStakedBig), new(big.Float).SetInt(totalSupplyBig))
					stakingRatio, _ = stakingFloat.Float64()
				}
			}
		}

		// 7. 计算通胀率（PAT代币）
		var inflationRate float64
		if tokenType == "PAT" {
			// 获取30天前的供应量
			thirtyDaysAgo := today.AddDate(0, 0, -30)
			var oldSupply string
			if err := db.Model(&models.TokenSupply{}).
				Where("token_type = ? AND last_updated >= ?", tokenType, thirtyDaysAgo).
				Order("last_updated ASC").
				Select("total_supply").
				Limit(1).
				Scan(&oldSupply).Error; err == nil {
				if oldSupplyBig, ok := new(big.Int).SetString(oldSupply, 10); ok && oldSupplyBig.Cmp(big.NewInt(0)) > 0 {
					if totalSupplyBig, ok := new(big.Int).SetString(totalSupply, 10); ok {
						// 计算30天增长率，然后年化
						growthFloat := new(big.Float).Quo(
							new(big.Float).Sub(new(big.Float).SetInt(totalSupplyBig), new(big.Float).SetInt(oldSupplyBig)),
							new(big.Float).SetInt(oldSupplyBig),
						)
						monthlyGrowth, _ := growthFloat.Float64()
						inflationRate = monthlyGrowth * 12 * 100 // 年化百分比
					}
				}
			}
		}

		// 创建指标记录
		metrics := &models.TokenMetrics{
			TokenType:       tokenType,
			Price:           "0", // 价格需要从外部API获取
			MarketCap:       "0", // 市值 = 价格 * 总供应量
			Volume24h:       volume24h,
			Transactions24h: int(transactions24h),
			ActiveHolders:   int(activeHolders),
			VelocityRatio:   velocityRatio,
			InflationRate:   inflationRate,
			StakingRatio:    stakingRatio,
			Date:            today,
		}

		// 使用UPSERT操作保存指标
		if err := db.Where("token_type = ? AND date = ?", tokenType, today).
			Assign(metrics).
			FirstOrCreate(metrics).Error; err != nil {
			s.logger.Error().Err(err).Str("token", tokenType).Msg("Failed to save token metrics")
			continue
		}

		s.logger.Info().
			Str("token_type", tokenType).
			Str("volume_24h", volume24h).
			Int64("transactions_24h", transactions24h).
			Int64("active_holders", activeHolders).
			Float64("velocity_ratio", velocityRatio).
			Float64("inflation_rate", inflationRate).
			Float64("staking_ratio", stakingRatio).
			Msg("Token metrics updated")
	}

	s.logger.Debug().Msg("Token metrics sync completed")
	return nil
}

// syncTokenBurnData 同步代币销毁数据
func (s *SyncService) syncTokenBurnData(ctx context.Context) error {
	s.logger.Debug().Msg("Starting token burn data sync...")

	db := s.tokenRepo.GetDB()

	// 从转账记录中识别销毁交易（转账到0x0地址）
	burnAddress := "0x0000000000000000000000000000000000000000"

	// 获取最后同步的销毁记录时间
	var lastBurnSync time.Time
	err := db.Model(&models.TokenBurn{}).
		Select("COALESCE(MAX(timestamp), '1970-01-01'::timestamp)").
		Scan(&lastBurnSync).Error
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get last burn sync time")
		return fmt.Errorf("failed to get last burn sync time: %w", err)
	}

	// 查找新的销毁交易
	var burnTransfers []models.TokenTransfer
	if err := db.Where("to_address = ? AND timestamp > ?", burnAddress, lastBurnSync).
		Order("timestamp ASC").
		Find(&burnTransfers).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to query burn transfers")
		return fmt.Errorf("failed to query burn transfers: %w", err)
	}

	s.logger.Debug().Int("burn_transfers", len(burnTransfers)).Msg("Found burn transfers")

	// 处理每个销毁交易
	for _, transfer := range burnTransfers {
		// 检查是否已经记录过这个销毁事件
		var existingBurn models.TokenBurn
		if err := db.Where("transaction_hash = ?", transfer.TransactionHash).
			First(&existingBurn).Error; err == nil {
			// 已存在，跳过
			continue
		}

		// 确定销毁原因
		burnReason := s.determineBurnReason(ctx, &transfer)

		// 创建销毁记录
		burnRecord := &models.TokenBurn{
			TransactionHash:  transfer.TransactionHash,
			BlockNumber:      transfer.BlockNumber,
			TokenType:        transfer.TokenType,
			Amount:           transfer.Amount,
			BurnReason:       burnReason,
			InitiatorAddress: transfer.FromAddress,
			Timestamp:        transfer.Timestamp,
		}

		if err := db.Create(burnRecord).Error; err != nil {
			s.logger.Error().Err(err).
				Str("tx_hash", transfer.TransactionHash).
				Msg("Failed to create burn record")
			continue
		}

		s.logger.Info().
			Str("token_type", transfer.TokenType).
			Str("amount", transfer.Amount).
			Str("reason", burnReason).
			Str("tx_hash", transfer.TransactionHash).
			Msg("Burn record created")
	}

	// 更新代币供应量中的销毁数量
	if len(burnTransfers) > 0 {
		if err := s.updateBurnedSupply(ctx); err != nil {
			s.logger.Error().Err(err).Msg("Failed to update burned supply")
		}
	}

	s.logger.Debug().Msg("Token burn data sync completed")
	return nil
}

// determineBurnReason 确定销毁原因
func (s *SyncService) determineBurnReason(ctx context.Context, transfer *models.TokenTransfer) string {
	// 根据发起地址和交易模式确定销毁原因
	fromAddress := strings.ToLower(transfer.FromAddress)

	// 检查是否来自BurnManager合约
	burnManagerAddress := "0x..." // 需要配置实际的BurnManager地址
	if fromAddress == strings.ToLower(burnManagerAddress) {
		return "fee" // 手续费销毁
	}

	// 检查是否来自质押合约（惩罚销毁）
	stakingPoolAddress := "0x6fd63476e4f2cb0b4841500e13ee851679a869f1"
	if fromAddress == strings.ToLower(stakingPoolAddress) {
		return "penalty" // 惩罚销毁
	}

	// 检查是否来自国库（回购销毁）
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"
	if fromAddress == strings.ToLower(treasuryAddress) {
		return "buyback" // 回购销毁
	}

	// 默认为其他销毁
	return "other"
}

// updateBurnedSupply 更新代币供应量中的销毁数量
func (s *SyncService) updateBurnedSupply(ctx context.Context) error {
	db := s.tokenRepo.GetDB()

	for _, tokenType := range []string{"PXT", "PAT"} {
		// 计算总销毁量
		var totalBurned string
		if err := db.Model(&models.TokenBurn{}).
			Where("token_type = ?", tokenType).
			Select("COALESCE(SUM(CAST(amount AS NUMERIC)), 0)::TEXT").
			Scan(&totalBurned).Error; err != nil {
			s.logger.Error().Err(err).Str("token", tokenType).Msg("Failed to calculate total burned")
			continue
		}

		// 更新供应量表
		if err := db.Model(&models.TokenSupply{}).
			Where("token_type = ?", tokenType).
			Update("burned_supply", totalBurned).Error; err != nil {
			s.logger.Error().Err(err).Str("token", tokenType).Msg("Failed to update burned supply")
			continue
		}

		s.logger.Debug().
			Str("token_type", tokenType).
			Str("total_burned", totalBurned).
			Msg("Updated burned supply")
	}

	return nil
}

// Stop 停止同步服务
func (s *SyncService) Stop() {
	s.logger.Info().Msg("Stopping sync service...")
	s.isRunning = false
}

// IsRunning 检查是否正在运行
func (s *SyncService) IsRunning() bool {
	return s.isRunning
}

// calculatePXTSupplyDetails 计算PXT代币的锁定量和销毁量
func (s *SyncService) calculatePXTSupplyDetails(ctx context.Context, tokenAddress common.Address) (*big.Int, *big.Int, error) {
	// 获取总销毁量
	totalBurned, err := s.client.GetTokenTotalBurned(ctx, tokenAddress)
	if err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get total burned amount")
		totalBurned = big.NewInt(0)
	}

	// 计算总锁定量 - 需要查询所有已知的锁定地址
	totalLocked := big.NewInt(0)

	// 已知的锁定地址列表（从部署记录中获取）
	lockedAddresses := []string{
		"0x5bC882C17E33E4FFB255E7E5ac8eba06b57f6E37", // 操作员地址
		"0x4BB608E31Ab7E78FBB02134b9609FF0F95e4ca99", // 国库地址
	}

	for _, addrStr := range lockedAddresses {
		userAddr := common.HexToAddress(addrStr)
		lockedBalance, err := s.client.GetTokenLockedBalance(ctx, tokenAddress, userAddr)
		if err != nil {
			s.logger.Warn().Err(err).Str("address", addrStr).Msg("Failed to get locked balance for address")
			continue
		}
		totalLocked.Add(totalLocked, lockedBalance)
	}

	s.logger.Debug().
		Str("total_locked", totalLocked.String()).
		Str("total_burned", totalBurned.String()).
		Msg("Calculated PXT supply details")

	return totalLocked, totalBurned, nil
}

// syncHolderRankings 同步持币者排名
func (s *SyncService) syncHolderRankings(ctx context.Context) error {
	s.logger.Debug().Msg("Starting holder rankings sync")

	// 分别同步PXT和PAT的持币者排名
	for _, tokenType := range []string{"PXT", "PAT"} {
		if err := s.syncTokenHolderRanking(ctx, tokenType); err != nil {
			s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to sync holder ranking")
			continue
		}
	}

	s.logger.Debug().Msg("Holder rankings sync completed")
	return nil
}

// syncTokenHolderRanking 同步单个代币的持币者排名
func (s *SyncService) syncTokenHolderRanking(ctx context.Context, tokenType string) error {
	// 从数据库获取所有有余额的地址
	balances, err := s.tokenRepo.GetTokenBalances(ctx, tokenType)
	if err != nil {
		return fmt.Errorf("failed to get token balances: %w", err)
	}

	if len(balances) == 0 {
		s.logger.Debug().Str("token", tokenType).Msg("No holders found")
		return nil
	}

	// 获取总供应量用于计算百分比
	supply, err := s.tokenRepo.GetTokenSupply(ctx, tokenType)
	if err != nil {
		s.logger.Warn().Err(err).Str("token", tokenType).Msg("Failed to get token supply")
		return err
	}

	// 检查supply是否为nil或TotalSupply是否为空
	totalSupplyBig := new(big.Int)
	if supply == nil || supply.TotalSupply == "" {
		s.logger.Warn().Str("token", tokenType).Msg("Token supply is nil or empty, using default value")
		totalSupplyBig.SetString("0", 10)
	} else {
		// 处理包含小数点的数字格式
		totalSupplyStr := supply.TotalSupply
		if strings.Contains(totalSupplyStr, ".") {
			// 如果包含小数点，只取整数部分
			parts := strings.Split(totalSupplyStr, ".")
			totalSupplyStr = parts[0]
		}

		if _, ok := totalSupplyBig.SetString(totalSupplyStr, 10); !ok {
			s.logger.Warn().Str("token", tokenType).Str("total_supply", supply.TotalSupply).Msg("Invalid total supply format, using 0")
			totalSupplyBig.SetString("0", 10)
		}
	}

	// 构建持币者排名数据
	var holders []models.TokenHolder
	for i, balance := range balances {
		balanceBig := new(big.Int)
		balanceBig.SetString(balance.Balance, 10)

		// 计算持币百分比
		var percentage float64
		if totalSupplyBig.Cmp(big.NewInt(0)) > 0 {
			ratio := new(big.Float).Quo(new(big.Float).SetInt(balanceBig), new(big.Float).SetInt(totalSupplyBig))
			percentage, _ = ratio.Float64()
			percentage *= 100 // 转换为百分比
		}

		holder := models.TokenHolder{
			TokenType:   tokenType,
			UserAddress: balance.UserAddress,
			Balance:     balance.Balance,
			Percentage:  percentage,
			Rank:        i + 1,
		}
		holders = append(holders, holder)
	}

	// 更新数据库中的排名数据
	if err := s.tokenRepo.UpdateHolderRanking(ctx, tokenType, holders); err != nil {
		return fmt.Errorf("failed to update holder ranking: %w", err)
	}

	s.logger.Debug().
		Str("token", tokenType).
		Int("holders_count", len(holders)).
		Msg("Token holder ranking updated")

	return nil
}

// syncStakingDataFromTransfers 从转账数据同步质押事件和用户质押信息
func (s *SyncService) syncStakingDataFromTransfers(ctx context.Context) error {
	s.logger.Debug().Msg("Starting staking data sync from transfers...")

	// 质押池和国库地址
	stakingPoolAddress := "0x6fd63476e4f2cb0b4841500e13ee851679a869f1"
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	// 获取数据库连接
	db := s.tokenRepo.GetDB()

	// 获取最后同步的质押事件时间戳
	var lastStakingSync time.Time
	err := db.Raw(`
		SELECT COALESCE(MAX(timestamp), '1970-01-01'::timestamp)
		FROM token_staking_events
	`).Scan(&lastStakingSync).Error
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get last staking sync time")
		return fmt.Errorf("failed to get last staking sync time: %w", err)
	}

	// 同步新的STAKED事件（转账到质押池，排除国库）
	result := db.Exec(`
		INSERT INTO token_staking_events (
			user_address,
			event_type,
			amount,
			transaction_hash,
			block_number,
			log_index,
			timestamp,
			created_at
		)
		SELECT
			from_address as user_address,
			'STAKED' as event_type,
			amount,
			transaction_hash,
			block_number,
			1 as log_index,
			timestamp,
			NOW() as created_at
		FROM token_transfers
		WHERE to_address = ?
		  AND from_address != ?
		  AND timestamp > ?
		  AND NOT EXISTS (
			  SELECT 1
			  FROM token_staking_events
			  WHERE token_staking_events.transaction_hash = token_transfers.transaction_hash
				AND token_staking_events.event_type = 'STAKED'
				AND token_staking_events.log_index = 1
		  )
		ORDER BY timestamp
	`, stakingPoolAddress, treasuryAddress, lastStakingSync)

	if result.Error != nil {
		s.logger.Error().Err(result.Error).Msg("Failed to sync STAKED events")
		return fmt.Errorf("failed to sync STAKED events: %w", result.Error)
	}

	newStakedEvents := result.RowsAffected

	// 同步新的WITHDRAWN事件（从质押池转出）
	result = db.Exec(`
		INSERT INTO token_staking_events (
			user_address,
			event_type,
			amount,
			transaction_hash,
			block_number,
			log_index,
			timestamp,
			created_at
		)
		SELECT
			to_address as user_address,
			'WITHDRAWN' as event_type,
			amount,
			transaction_hash,
			block_number,
			2 as log_index,
			timestamp,
			NOW() as created_at
		FROM token_transfers
		WHERE from_address = ?
		  AND timestamp > ?
		  AND NOT EXISTS (
			  SELECT 1
			  FROM token_staking_events
			  WHERE token_staking_events.transaction_hash = token_transfers.transaction_hash
				AND token_staking_events.event_type = 'WITHDRAWN'
				AND token_staking_events.log_index = 2
		  )
		ORDER BY timestamp
	`, stakingPoolAddress, lastStakingSync)

	if result.Error != nil {
		s.logger.Error().Err(result.Error).Msg("Failed to sync WITHDRAWN events")
		return fmt.Errorf("failed to sync WITHDRAWN events: %w", result.Error)
	}

	newWithdrawnEvents := result.RowsAffected

	// 如果有新事件，更新用户质押信息
	if newStakedEvents > 0 || newWithdrawnEvents > 0 {
		s.logger.Info().
			Int64("staked_events", newStakedEvents).
			Int64("withdrawn_events", newWithdrawnEvents).
			Msg("Synced new staking events, updating user stakes...")

		// 重新计算用户质押信息
		err = s.updateUserStakesFromEvents(ctx, treasuryAddress)
		if err != nil {
			s.logger.Error().Err(err).Msg("Failed to update user stakes")
			return fmt.Errorf("failed to update user stakes: %w", err)
		}

		// 🆕 自动同步新地址的合约数据
		err = s.syncNewUsersFromContracts(ctx, treasuryAddress)
		if err != nil {
			s.logger.Error().Err(err).Msg("Failed to sync new users from contracts")
			// 不返回错误，继续执行
		}

		// 🆕 立即刷新所有相关缓存
		s.logger.Info().Msg("New staking events detected, refreshing all caches...")
		if err := s.refreshAllStakingCaches(ctx); err != nil {
			s.logger.Warn().Err(err).Msg("Failed to refresh staking caches")
		}

		s.logger.Info().Msg("Successfully updated user staking information")
	} else {
		s.logger.Debug().Msg("No new staking events to sync")
	}

	return nil
}

// updateUserStakesFromEvents 从事件重新计算用户质押信息
func (s *SyncService) updateUserStakesFromEvents(ctx context.Context, treasuryAddress string) error {
	db := s.tokenRepo.GetDB()

	// 删除现有记录并重新计算
	err := db.Exec(`
		DELETE FROM token_user_stakes;

		WITH user_staking_summary AS (
			SELECT
				user_address,
				SUM(CASE WHEN event_type = 'STAKED' THEN amount::numeric ELSE 0 END) as total_staked,
				SUM(CASE WHEN event_type = 'WITHDRAWN' THEN amount::numeric ELSE 0 END) as total_withdrawn,
				SUM(CASE WHEN event_type = 'STAKED' THEN amount::numeric ELSE 0 END) -
				SUM(CASE WHEN event_type = 'WITHDRAWN' THEN amount::numeric ELSE 0 END) as net_amount,
				MIN(CASE WHEN event_type = 'STAKED' THEN timestamp END) as first_stake_time,
				MAX(timestamp) as last_update_time
			FROM token_staking_events
			GROUP BY user_address
			HAVING SUM(CASE WHEN event_type = 'STAKED' THEN amount::numeric ELSE 0 END) -
				   SUM(CASE WHEN event_type = 'WITHDRAWN' THEN amount::numeric ELSE 0 END) > 0
			   AND user_address != ?
		)
		INSERT INTO token_user_stakes (
			user_address,
			amount,
			staking_level,
			mining_power,
			total_rewards_claimed,
			start_time,
			last_update_time,
			created_at,
			updated_at
		)
		SELECT
			user_address,
			net_amount,
			CASE
				WHEN net_amount >= 500000 * 1e18 THEN 6
				WHEN net_amount >= 100000 * 1e18 THEN 5
				WHEN net_amount >= 20000 * 1e18 THEN 4
				WHEN net_amount >= 5000 * 1e18 THEN 3
				WHEN net_amount >= 1000 * 1e18 THEN 2
				WHEN net_amount >= 100 * 1e18 THEN 1
				ELSE 0
			END as staking_level,
			net_amount as mining_power,
			0 as total_rewards_claimed,
			first_stake_time,
			last_update_time,
			NOW() as created_at,
			NOW() as updated_at
		FROM user_staking_summary
	`, treasuryAddress).Error

	return err
}

// syncNewUsersFromContracts 自动同步新地址的合约数据
func (s *SyncService) syncNewUsersFromContracts(ctx context.Context, treasuryAddress string) error {
	s.logger.Debug().Msg("Starting sync of new users from contracts...")

	db := s.tokenRepo.GetDB()

	// 获取最近有质押事件但没有合约数据的地址
	var newUserAddresses []string
	err := db.Raw(`
		SELECT DISTINCT se.user_address
		FROM token_staking_events se
		LEFT JOIN token_user_stakes us ON LOWER(se.user_address) = LOWER(us.user_address)
		WHERE se.event_type = 'STAKED'
		  AND se.timestamp > NOW() - INTERVAL '1 hour'
		  AND LOWER(se.user_address) != LOWER(?)
		  AND (us.user_address IS NULL OR us.amount = '0')
		ORDER BY se.user_address
	`, treasuryAddress).Scan(&newUserAddresses).Error

	if err != nil {
		return fmt.Errorf("failed to get new user addresses: %w", err)
	}

	if len(newUserAddresses) == 0 {
		s.logger.Debug().Msg("No new users to sync from contracts")
		return nil
	}

	s.logger.Info().
		Int("count", len(newUserAddresses)).
		Msg("Found new users to sync from contracts")

	// 🚀 并发同步新用户的合约数据（提高性能）
	const maxConcurrency = 5 // 最大并发数
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var syncErrors []error

	for _, address := range newUserAddresses {
		wg.Add(1)
		go func(addr string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			s.logger.Debug().
				Str("address", addr).
				Msg("Syncing new user from contract...")

			err := s.SyncUserStakingInfo(ctx, addr)
			if err != nil {
				mu.Lock()
				syncErrors = append(syncErrors, fmt.Errorf("failed to sync user %s: %w", addr, err))
				mu.Unlock()

				s.logger.Error().
					Err(err).
					Str("address", addr).
					Msg("Failed to sync user from contract")
			} else {
				s.logger.Info().
					Str("address", addr).
					Msg("Successfully synced new user from contract")
			}
		}(address)
	}

	// 等待所有并发任务完成
	wg.Wait()

	// 记录同步结果
	if len(syncErrors) > 0 {
		s.logger.Warn().
			Int("error_count", len(syncErrors)).
			Int("total_users", len(newUserAddresses)).
			Msg("Some users failed to sync from contracts")
	} else {
		s.logger.Info().
			Int("synced_users", len(newUserAddresses)).
			Msg("All new users synced successfully from contracts")
	}

	return nil
}

// syncTransferData 同步转账数据
func (s *SyncService) syncTransferData(ctx context.Context) error {
	s.logger.Debug().Msg("Starting transfer data sync...")

	// 这里应该调用BSCScan API或区块链事件监听器来同步转账数据
	// 由于当前系统主要依赖BSCScan事件监听器，我们检查监听器是否正常工作

	// 获取最近的转账记录数量作为健康检查
	transfers, _, err := s.tokenRepo.GetRecentTransfers(ctx, "", 1, 5)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get recent transfers for health check")
		return fmt.Errorf("failed to check transfer data health: %w", err)
	}

	// 检查最新转账记录的时间
	if len(transfers) > 0 {
		latestTransfer := transfers[0]
		timeSinceLatest := time.Since(latestTransfer.Timestamp)

		if timeSinceLatest > 10*time.Minute {
			s.logger.Warn().
				Dur("time_since_latest", timeSinceLatest).
				Msg("Latest transfer data is old, transfer sync may be lagging")
		} else {
			s.logger.Debug().
				Dur("time_since_latest", timeSinceLatest).
				Msg("Transfer data is up to date")
		}
	} else {
		s.logger.Warn().Msg("No transfer records found in database")
	}

	// 注意：实际的转账数据同步由BSCScan事件监听器处理
	// 这个方法主要用于健康检查和监控
	return nil
}

// calculateTotalStakedFromDB 从数据库计算总质押量
func (s *SyncService) calculateTotalStakedFromDB(ctx context.Context, treasuryAddress string) (*big.Int, error) {
	s.logger.Debug().Msg("Calculating total staked from database...")

	db := s.tokenRepo.GetDB()

	// 从用户质押表计算总质押量（排除国库地址）
	var totalStakedStr string
	err := db.Raw(`
		SELECT COALESCE(SUM(CAST(amount AS NUMERIC)), 0)::TEXT as total
		FROM token_user_stakes
		WHERE is_active = true
		  AND LOWER(user_address) != LOWER(?)
	`, treasuryAddress).Scan(&totalStakedStr).Error

	if err != nil {
		return nil, fmt.Errorf("failed to calculate total staked from database: %w", err)
	}

	totalStaked := new(big.Int)
	totalStaked.SetString(totalStakedStr, 10)

	s.logger.Debug().
		Str("total_staked", totalStaked.String()).
		Msg("Calculated total staked from database")

	return totalStaked, nil
}

// getTotalStakedFromContract 从合约获取总质押量
func (s *SyncService) getTotalStakedFromContract(ctx context.Context) (*big.Int, error) {
	s.logger.Debug().Msg("Getting total staked from contract...")

	contracts := s.client.GetContracts()
	if contracts == nil {
		return nil, fmt.Errorf("contract manager not available")
	}

	// 使用合约调用获取总质押量
	stakingPoolAddress := contracts.Addresses.StakingPool

	// 调用 getTotalStaked 方法
	callData, err := contracts.ABIs.StakingPool.Pack("getTotalStaked")
	if err != nil {
		return nil, fmt.Errorf("failed to pack getTotalStaked call: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &stakingPoolAddress,
		Data: callData,
	}

	client := s.client.GetClient()
	result, err := client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getTotalStaked: %w", err)
	}

	// 解析总质押量
	var totalStaked *big.Int
	err = contracts.ABIs.StakingPool.UnpackIntoInterface(&totalStaked, "getTotalStaked", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getTotalStaked result: %w", err)
	}

	s.logger.Debug().
		Str("total_staked", totalStaked.String()).
		Msg("Retrieved total staked from contract")

	return totalStaked, nil
}

// updateStakingStats 更新质押统计数据
func (s *SyncService) updateStakingStats(ctx context.Context, totalStaked *big.Int) error {
	s.logger.Debug().Msg("Updating staking stats...")

	db := s.tokenRepo.GetDB()

	// 计算其他统计数据
	var totalUsers int64
	var activeUsers int64

	// 总用户数
	err := db.Raw(`
		SELECT COUNT(*)
		FROM token_user_stakes
		WHERE amount != '0'
	`).Scan(&totalUsers).Error
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to count total users")
		totalUsers = 0
	}

	// 活跃用户数
	err = db.Raw(`
		SELECT COUNT(*)
		FROM token_user_stakes
		WHERE is_active = true AND amount != '0'
	`).Scan(&activeUsers).Error
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to count active users")
		activeUsers = 0
	}

	// 更新或插入质押统计记录
	now := time.Now()

	// 先尝试更新现有记录
	result := db.Exec(`
		UPDATE token_staking_stats SET
			total_staked = ?,
			total_users = ?,
			active_users = ?,
			updated_at = ?
		WHERE id = 1
	`, totalStaked.String(), totalUsers, activeUsers, now)

	if result.Error != nil {
		return fmt.Errorf("failed to update staking stats: %w", result.Error)
	}

	// 如果没有更新任何记录，则插入新记录
	if result.RowsAffected == 0 {
		err = db.Exec(`
			INSERT INTO token_staking_stats (
				id,
				total_staked,
				total_users,
				active_users,
				updated_at
			) VALUES (1, ?, ?, ?, ?)
		`, totalStaked.String(), totalUsers, activeUsers, now).Error

		if err != nil {
			return fmt.Errorf("failed to insert staking stats: %w", err)
		}
	}

	if err != nil {
		return fmt.Errorf("failed to update staking stats: %w", err)
	}

	err = result.Error

	s.logger.Info().
		Str("total_staked", totalStaked.String()).
		Int64("total_users", totalUsers).
		Int64("active_users", activeUsers).
		Msg("Updated staking stats successfully")

	return nil
}

// syncContentOnChainData 同步内容上链数据
func (s *SyncService) syncContentOnChainData(ctx context.Context) error {
	s.logger.Debug().Msg("Starting content on-chain data sync...")

	db := s.tokenRepo.GetDB()

	// 获取最后同步的内容记录时间
	var lastContentSync time.Time
	err := db.Model(&models.TokenContentRecord{}).
		Select("COALESCE(MAX(on_chain_date), '1970-01-01'::timestamp)").
		Scan(&lastContentSync).Error
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get last content sync time")
		return fmt.Errorf("failed to get last content sync time: %w", err)
	}

	// 查找新的内容上链交易（通过转账记录识别PAT费用支付）
	var contentTransfers []models.TokenTransfer
	if err := db.Where("token_type = ? AND timestamp > ?", "PAT", lastContentSync).
		Where("amount > ?", "0"). // PAT费用大于0
		Order("timestamp ASC").
		Limit(100). // 限制批次大小
		Find(&contentTransfers).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to query content transfers")
		return fmt.Errorf("failed to query content transfers: %w", err)
	}

	s.logger.Debug().Int("content_transfers", len(contentTransfers)).Msg("Found potential content transfers")

	// 处理每个可能的内容上链交易
	for _, transfer := range contentTransfers {
		// 检查是否已经记录过这个内容
		var existingContent models.TokenContentRecord
		if err := db.Where("transaction_hash = ?", transfer.TransactionHash).
			First(&existingContent).Error; err == nil {
			// 已存在，跳过
			continue
		}

		// 验证是否为内容上链交易（这里需要根据实际业务逻辑判断）
		if !s.isContentOnChainTransaction(ctx, &transfer) {
			continue
		}

		// 创建内容记录
		contentRecord := &models.TokenContentRecord{
			ContentID:       s.generateContentID(&transfer),
			Title:           "Content " + transfer.TransactionHash[:8], // 临时标题
			ContentType:     "unknown",                                 // 需要从实际数据源获取
			PublisherKSUID:  transfer.FromAddress,                      // 使用发送方地址作为发布者
			OnChainDate:     transfer.Timestamp,
			TransactionHash: transfer.TransactionHash,
			BlockNumber:     transfer.BlockNumber,
			PATFee:          transfer.Amount,
			Status:          "confirmed",
		}

		if err := db.Create(contentRecord).Error; err != nil {
			s.logger.Error().Err(err).
				Str("tx_hash", transfer.TransactionHash).
				Msg("Failed to create content record")
			continue
		}

		s.logger.Info().
			Str("content_id", contentRecord.ContentID).
			Str("pat_fee", transfer.Amount).
			Str("tx_hash", transfer.TransactionHash).
			Msg("Content record created")
	}

	// 统计内容上链费用
	if len(contentTransfers) > 0 {
		if err := s.updateContentStats(ctx); err != nil {
			s.logger.Error().Err(err).Msg("Failed to update content stats")
		}
	}

	s.logger.Debug().Msg("Content on-chain data sync completed")
	return nil
}

// isContentOnChainTransaction 判断是否为内容上链交易
func (s *SyncService) isContentOnChainTransaction(ctx context.Context, transfer *models.TokenTransfer) bool {
	// 这里需要根据实际业务逻辑判断
	// 例如：检查接收方地址是否为内容管理合约地址
	contentManagerAddress := "0x..." // 需要配置实际的内容管理合约地址

	// 简单判断：如果转账金额在合理的内容费用范围内
	if amount, ok := new(big.Int).SetString(transfer.Amount, 10); ok {
		// 假设内容费用在1-1000 PAT之间
		minFee := new(big.Int).Mul(big.NewInt(1), big.NewInt(1e18))    // 1 PAT
		maxFee := new(big.Int).Mul(big.NewInt(1000), big.NewInt(1e18)) // 1000 PAT

		if amount.Cmp(minFee) >= 0 && amount.Cmp(maxFee) <= 0 {
			return true
		}
	}

	// 检查接收方地址
	if strings.EqualFold(transfer.ToAddress, contentManagerAddress) {
		return true
	}

	return false
}

// generateContentID 生成内容ID
func (s *SyncService) generateContentID(transfer *models.TokenTransfer) string {
	// 使用交易哈希的前8位作为内容ID
	return "content_" + transfer.TransactionHash[:8]
}

// updateContentStats 更新内容统计数据
func (s *SyncService) updateContentStats(ctx context.Context) error {
	db := s.tokenRepo.GetDB()

	// 计算今日内容上链数量
	today := time.Now().Truncate(24 * time.Hour)
	var todayContentCount int64
	if err := db.Model(&models.TokenContentRecord{}).
		Where("DATE(on_chain_date) = ?", today.Format("2006-01-02")).
		Count(&todayContentCount).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to count today's content")
		return err
	}

	// 计算今日PAT费用总额
	var todayPATFees string
	if err := db.Model(&models.TokenContentRecord{}).
		Where("DATE(on_chain_date) = ?", today.Format("2006-01-02")).
		Select("COALESCE(SUM(CAST(pat_fee AS NUMERIC)), 0)::TEXT").
		Scan(&todayPATFees).Error; err != nil {
		s.logger.Error().Err(err).Msg("Failed to calculate today's PAT fees")
		return err
	}

	s.logger.Info().
		Int64("today_content_count", todayContentCount).
		Str("today_pat_fees", todayPATFees).
		Msg("Content stats updated")

	return nil
}

// startDailyFullSync 启动每日凌晨3点完整同步调度器
func (s *SyncService) startDailyFullSync(ctx context.Context) {
	s.logger.Info().Msg("🌙 启动每日凌晨3点完整同步调度器...")

	for {
		select {
		case <-ctx.Done():
			s.logger.Info().Msg("每日完整同步调度器已停止")
			return
		default:
			// 计算下次凌晨3点的时间
			now := time.Now()
			next3AM := time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, now.Location())

			// 如果当前时间已经过了今天的3点，则计算明天的3点
			if now.After(next3AM) {
				next3AM = next3AM.Add(24 * time.Hour)
			}

			duration := next3AM.Sub(now)
			s.logger.Info().
				Time("next_full_sync", next3AM).
				Dur("wait_duration", duration).
				Msg("⏰ 下次完整同步时间已安排")

			// 等待到凌晨3点
			timer := time.NewTimer(duration)
			select {
			case <-ctx.Done():
				timer.Stop()
				return
			case <-timer.C:
				// 执行完整同步
				s.performDailyFullSync(ctx)
			}
		}
	}
}

// performDailyFullSync 执行每日完整同步
func (s *SyncService) performDailyFullSync(ctx context.Context) {
	startTime := time.Now()
	s.logger.Info().Msg("🚀 开始执行每日完整同步（凌晨3点）...")

	// 记录同步开始
	s.logger.Info().
		Time("start_time", startTime).
		Msg("📊 每日完整数据同步开始")

	// 执行完整同步
	if err := s.syncAllData(ctx); err != nil {
		s.logger.Error().
			Err(err).
			Dur("duration", time.Since(startTime)).
			Msg("❌ 每日完整同步失败")
		return
	}

	// 额外执行一些重量级的同步任务
	s.logger.Info().Msg("📈 执行额外的重量级同步任务...")

	// 同步代币经济指标（计算密集型）
	if err := s.SyncTokenMetrics(ctx); err != nil {
		s.logger.Error().Err(err).Msg("同步代币经济指标失败")
	}

	// 同步代币销毁数据（完整扫描）
	if err := s.syncTokenBurnData(ctx); err != nil {
		s.logger.Error().Err(err).Msg("同步代币销毁数据失败")
	}

	// 同步内容上链数据（完整扫描）
	if err := s.syncContentOnChainData(ctx); err != nil {
		s.logger.Error().Err(err).Msg("同步内容上链数据失败")
	}

	// 刷新所有缓存
	if err := s.refreshAllStakingCaches(ctx); err != nil {
		s.logger.Error().Err(err).Msg("刷新缓存失败")
	}

	duration := time.Since(startTime)
	s.logger.Info().
		Dur("total_duration", duration).
		Time("completed_at", time.Now()).
		Msg("✅ 每日完整同步成功完成")

	// 如果同步时间过长，记录警告
	if duration > 30*time.Minute {
		s.logger.Warn().
			Dur("duration", duration).
			Msg("⚠️ 完整同步耗时较长，建议优化")
	}
}
