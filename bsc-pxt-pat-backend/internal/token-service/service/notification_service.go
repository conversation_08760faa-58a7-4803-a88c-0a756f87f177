package service

import (
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/models"
)

// NotificationService 通知服务
type NotificationService struct {
	websocketService *WebSocketService
	logger           zerolog.Logger
}

// NewNotificationService 创建新的通知服务
func NewNotificationService(
	websocketService *WebSocketService,
	logger zerolog.Logger,
) *NotificationService {
	return &NotificationService{
		websocketService: websocketService,
		logger:           logger.With().Str("component", "notification_service").Logger(),
	}
}

// NotificationType 通知类型
type NotificationType string

const (
	// 代币相关通知
	NotificationBalanceUpdated   NotificationType = "balance_updated"
	NotificationTransferReceived NotificationType = "transfer_received"
	NotificationTransferSent     NotificationType = "transfer_sent"

	// 质押相关通知
	NotificationStakeConfirmed     NotificationType = "stake_confirmed"
	NotificationUnstakeConfirmed   NotificationType = "unstake_confirmed"
	NotificationRewardClaimed      NotificationType = "reward_claimed"
	NotificationLevelUpgraded      NotificationType = "level_upgraded"
	NotificationMiningPowerUpdated NotificationType = "mining_power_updated"

	// 治理相关通知
	NotificationProposalCreated   NotificationType = "proposal_created"
	NotificationVoteCast          NotificationType = "vote_cast"
	NotificationProposalExecuted  NotificationType = "proposal_executed"
	NotificationDelegationUpdated NotificationType = "delegation_updated"

	// 排行榜相关通知
	NotificationRankingUpdated  NotificationType = "ranking_updated"
	NotificationTopRankAchieved NotificationType = "top_rank_achieved"

	// 系统通知
	NotificationSystemMaintenance NotificationType = "system_maintenance"
	NotificationNetworkCongestion NotificationType = "network_congestion"
)

// Notification 通知结构
type Notification struct {
	ID          string           `json:"id"`
	Type        NotificationType `json:"type"`
	Title       string           `json:"title"`
	Message     string           `json:"message"`
	Data        interface{}      `json:"data,omitempty"`
	UserAddress string           `json:"user_address,omitempty"`
	Timestamp   time.Time        `json:"timestamp"`
	Read        bool             `json:"read"`
}

// BalanceUpdateData 余额更新数据
type BalanceUpdateData struct {
	TokenType  string `json:"token_type"`
	OldBalance string `json:"old_balance"`
	NewBalance string `json:"new_balance"`
	Change     string `json:"change"`
	TxHash     string `json:"tx_hash,omitempty"`
}

// StakeEventData 质押事件数据
type StakeEventData struct {
	Amount      string              `json:"amount"`
	Level       models.StakingLevel `json:"level"`
	LevelName   string              `json:"level_name"`
	MiningPower string              `json:"mining_power"`
	TxHash      string              `json:"tx_hash"`
}

// GovernanceEventData 治理事件数据
type GovernanceEventData struct {
	ProposalID    uint64 `json:"proposal_id"`
	ProposalTitle string `json:"proposal_title"`
	VoteSupport   bool   `json:"vote_support,omitempty"`
	VotingPower   string `json:"voting_power,omitempty"`
	TxHash        string `json:"tx_hash"`
}

// RankingUpdateData 排行榜更新数据
type RankingUpdateData struct {
	RankingType string `json:"ranking_type"`
	OldRank     int    `json:"old_rank"`
	NewRank     int    `json:"new_rank"`
	Change      int    `json:"change"`
}

// NotifyBalanceUpdate 通知余额更新
func (s *NotificationService) NotifyBalanceUpdate(userAddress, tokenType, oldBalance, newBalance, txHash string) {
	userAddress = strings.ToLower(userAddress)

	// 计算变化量
	oldBal, _ := new(big.Int).SetString(oldBalance, 10)
	newBal, _ := new(big.Int).SetString(newBalance, 10)
	change := new(big.Int).Sub(newBal, oldBal)

	data := BalanceUpdateData{
		TokenType:  tokenType,
		OldBalance: oldBalance,
		NewBalance: newBalance,
		Change:     change.String(),
		TxHash:     txHash,
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationBalanceUpdated,
		Title:       fmt.Sprintf("%s余额更新", tokenType),
		Message:     fmt.Sprintf("您的%s余额已更新", tokenType),
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("token", tokenType).
		Str("change", change.String()).
		Msg("Balance update notification sent")
}

// NotifyStakeConfirmed 通知质押确认
func (s *NotificationService) NotifyStakeConfirmed(userAddress, amount string, level models.StakingLevel, miningPower, txHash string) {
	userAddress = strings.ToLower(userAddress)

	levelName := s.getLevelName(level)
	data := StakeEventData{
		Amount:      amount,
		Level:       level,
		LevelName:   levelName,
		MiningPower: miningPower,
		TxHash:      txHash,
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationStakeConfirmed,
		Title:       "质押成功",
		Message:     fmt.Sprintf("您已成功质押 %s 代币，等级：%s", amount, levelName),
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("amount", amount).
		Str("level", levelName).
		Msg("Stake confirmation notification sent")
}

// NotifyUnstakeConfirmed 通知解质押确认
func (s *NotificationService) NotifyUnstakeConfirmed(userAddress, amount, txHash string) {
	userAddress = strings.ToLower(userAddress)

	data := StakeEventData{
		Amount: amount,
		TxHash: txHash,
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationUnstakeConfirmed,
		Title:       "解质押成功",
		Message:     fmt.Sprintf("您已成功解质押 %s 代币", amount),
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("amount", amount).
		Msg("Unstake confirmation notification sent")
}

// NotifyRewardClaimed 通知奖励领取
func (s *NotificationService) NotifyRewardClaimed(userAddress, amount, txHash string) {
	userAddress = strings.ToLower(userAddress)

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationRewardClaimed,
		Title:       "奖励领取成功",
		Message:     fmt.Sprintf("您已成功领取 %s 奖励", amount),
		Data:        map[string]string{"amount": amount, "tx_hash": txHash},
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("amount", amount).
		Msg("Reward claim notification sent")
}

// NotifyLevelUpgrade 通知等级升级
func (s *NotificationService) NotifyLevelUpgrade(userAddress string, oldLevel, newLevel models.StakingLevel) {
	userAddress = strings.ToLower(userAddress)

	oldLevelName := s.getLevelName(oldLevel)
	newLevelName := s.getLevelName(newLevel)

	data := map[string]interface{}{
		"old_level":      oldLevel,
		"new_level":      newLevel,
		"old_level_name": oldLevelName,
		"new_level_name": newLevelName,
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationLevelUpgraded,
		Title:       "等级升级",
		Message:     fmt.Sprintf("恭喜！您的质押等级从 %s 升级到 %s", oldLevelName, newLevelName),
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("old_level", oldLevelName).
		Str("new_level", newLevelName).
		Msg("Level upgrade notification sent")
}

// NotifyVoteCast 通知投票成功
func (s *NotificationService) NotifyVoteCast(userAddress string, proposalID uint64, proposalTitle string, support bool, votingPower, txHash string) {
	userAddress = strings.ToLower(userAddress)

	supportText := "反对"
	if support {
		supportText = "支持"
	}

	data := GovernanceEventData{
		ProposalID:    proposalID,
		ProposalTitle: proposalTitle,
		VoteSupport:   support,
		VotingPower:   votingPower,
		TxHash:        txHash,
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationVoteCast,
		Title:       "投票成功",
		Message:     fmt.Sprintf("您已对提案 #%d 投出%s票", proposalID, supportText),
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Uint64("proposal_id", proposalID).
		Bool("support", support).
		Msg("Vote cast notification sent")
}

// NotifyProposalCreated 通知新提案创建
func (s *NotificationService) NotifyProposalCreated(proposalID uint64, title, creator string) {
	data := GovernanceEventData{
		ProposalID:    proposalID,
		ProposalTitle: title,
	}

	notification := Notification{
		ID:        s.generateNotificationID(),
		Type:      NotificationProposalCreated,
		Title:     "新提案创建",
		Message:   fmt.Sprintf("新的治理提案 #%d 已创建：%s", proposalID, title),
		Data:      data,
		Timestamp: time.Now(),
	}

	// 广播给所有订阅治理频道的用户
	s.websocketService.BroadcastToChannel("governance", string(NotificationProposalCreated), notification)
	s.logger.Info().
		Uint64("proposal_id", proposalID).
		Str("creator", creator).
		Msg("Proposal creation notification broadcasted")
}

// NotifyRankingUpdate 通知排名更新
func (s *NotificationService) NotifyRankingUpdate(userAddress, rankingType string, oldRank, newRank int) {
	userAddress = strings.ToLower(userAddress)

	change := oldRank - newRank // 排名越小越好，所以是oldRank - newRank

	data := RankingUpdateData{
		RankingType: rankingType,
		OldRank:     oldRank,
		NewRank:     newRank,
		Change:      change,
	}

	var message string
	if change > 0 {
		message = fmt.Sprintf("您在%s排行榜中的排名上升了%d位，当前排名：#%d", rankingType, change, newRank)
	} else if change < 0 {
		message = fmt.Sprintf("您在%s排行榜中的排名下降了%d位，当前排名：#%d", rankingType, -change, newRank)
	} else {
		message = fmt.Sprintf("您在%s排行榜中的排名保持不变：#%d", rankingType, newRank)
	}

	notification := Notification{
		ID:          s.generateNotificationID(),
		Type:        NotificationRankingUpdated,
		Title:       "排名更新",
		Message:     message,
		Data:        data,
		UserAddress: userAddress,
		Timestamp:   time.Now(),
	}

	s.sendNotification(notification)
	s.logger.Info().
		Str("user", userAddress).
		Str("ranking_type", rankingType).
		Int("change", change).
		Msg("Ranking update notification sent")
}

// sendNotification 发送通知
func (s *NotificationService) sendNotification(notification Notification) {
	if notification.UserAddress != "" {
		// 发送给特定用户
		s.websocketService.BroadcastToUser(notification.UserAddress, string(notification.Type), notification)
	} else {
		// 全局广播
		s.websocketService.BroadcastGlobal(string(notification.Type), notification)
	}
}

// generateNotificationID 生成通知ID
func (s *NotificationService) generateNotificationID() string {
	return fmt.Sprintf("notif_%d", time.Now().UnixNano())
}

// getLevelName 获取等级名称
func (s *NotificationService) getLevelName(level models.StakingLevel) string {
	switch level {
	case models.DingJi:
		return "丁级"
	case models.ChengJi:
		return "丙级"
	case models.YiJi:
		return "乙级"
	case models.JiaJi:
		return "甲级"
	case models.ShiJue:
		return "十爵"
	case models.ShuangShiJue:
		return "双十爵"
	case models.ZhiZun:
		return "至尊"
	default:
		return "未知"
	}
}
