package service

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// ContentTypeConfig 内容类型配置结构
type ContentTypeConfig struct {
	ContentTypes     map[string]ContentTypeDefinition `yaml:"content_types"`
	CommonAttributes map[string]string                `yaml:"common_attributes"`
	CommonNumerics   map[string]string                `yaml:"common_numerics"`
}

// ContentTypeDefinition 内容类型定义
type ContentTypeDefinition struct {
	Name         string            `yaml:"name"`
	Description  string            `yaml:"description"`
	DefaultFee   float64           `yaml:"default_fee"`
	Attributes   map[string]string `yaml:"attributes"`
	Numerics     map[string]string `yaml:"numerics"`
	Creators     []string          `yaml:"creators"`
	Contributors []string          `yaml:"contributors"`
}

// ContentTypeConfigService 内容类型配置服务
type ContentTypeConfigService struct {
	config *ContentTypeConfig
}

// NewContentTypeConfigService 创建内容类型配置服务
func NewContentTypeConfigService(configPath string) (*ContentTypeConfigService, error) {
	config, err := loadContentTypeConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load content type config: %w", err)
	}

	return &ContentTypeConfigService{
		config: config,
	}, nil
}

// loadContentTypeConfig 加载内容类型配置
func loadContentTypeConfig(configPath string) (*ContentTypeConfig, error) {
	// 如果没有提供路径，使用默认路径
	if configPath == "" {
		configPath = "internal/token-service/config/content_types.yaml"
	}

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 如果文件不存在，返回默认配置
		return getDefaultContentTypeConfig(), nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析YAML
	var config ContentTypeConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &config, nil
}

// getDefaultContentTypeConfig 获取默认配置 (如果配置文件不存在)
func getDefaultContentTypeConfig() *ContentTypeConfig {
	return &ContentTypeConfig{
		ContentTypes: map[string]ContentTypeDefinition{
			"video": {
				Name:        "视频",
				Description: "视频内容",
				DefaultFee:  1.0,
				Attributes: map[string]string{
					"genre":      "视频类型",
					"resolution": "分辨率",
					"format":     "视频格式",
				},
				Numerics: map[string]string{
					"duration_seconds": "时长(秒)",
				},
				Creators:     []string{"director:导演", "actor:演员"},
				Contributors: []string{"editor:剪辑师"},
			},
			"novel": {
				Name:        "小说",
				Description: "小说内容",
				DefaultFee:  0.1,
				Attributes: map[string]string{
					"genre":  "小说类型",
					"status": "状态",
				},
				Numerics: map[string]string{
					"word_count": "字数",
				},
				Creators:     []string{"author:作者"},
				Contributors: []string{"editor:编辑"},
			},
			"music": {
				Name:        "音乐",
				Description: "音乐内容",
				DefaultFee:  0.5,
				Attributes: map[string]string{
					"genre":  "音乐类型",
					"format": "音频格式",
				},
				Numerics: map[string]string{
					"duration_seconds": "时长(秒)",
				},
				Creators:     []string{"artist:艺术家", "composer:作曲者"},
				Contributors: []string{"producer:制作人"},
			},
			"article": {
				Name:        "文章",
				Description: "文章内容",
				DefaultFee:  0.05,
				Attributes: map[string]string{
					"category": "文章分类",
					"format":   "文档格式",
				},
				Numerics: map[string]string{
					"word_count": "字数",
				},
				Creators:     []string{"author:作者"},
				Contributors: []string{"editor:编辑"},
			},
			"image": {
				Name:        "图片",
				Description: "图片内容",
				DefaultFee:  0.3,
				Attributes: map[string]string{
					"category":   "图片分类",
					"format":     "图片格式",
					"resolution": "分辨率",
				},
				Numerics: map[string]string{
					"width":  "宽度(像素)",
					"height": "高度(像素)",
				},
				Creators:     []string{"photographer:摄影师", "artist:艺术家"},
				Contributors: []string{"editor:编辑师"},
			},
		},
		CommonAttributes: map[string]string{
			"language": "语言",
			"region":   "地区",
		},
		CommonNumerics: map[string]string{
			"creation_timestamp": "创作时间戳",
		},
	}
}

// GetContentTypeDefinition 获取内容类型定义
func (s *ContentTypeConfigService) GetContentTypeDefinition(contentType string) (ContentTypeDefinition, bool) {
	def, exists := s.config.ContentTypes[contentType]
	return def, exists
}

// GetAllContentTypes 获取所有内容类型
func (s *ContentTypeConfigService) GetAllContentTypes() map[string]ContentTypeDefinition {
	return s.config.ContentTypes
}

// GetRecommendedAttributes 获取推荐属性 (合并通用属性和特定类型属性)
func (s *ContentTypeConfigService) GetRecommendedAttributes(contentType string) map[string]interface{} {
	result := map[string]interface{}{
		"content_type": contentType,
		"attributes":   map[string]string{},
		"numerics":     map[string]string{},
		"creators":     []string{},
		"contributors": []string{},
	}

	// 添加通用属性
	attributes := make(map[string]string)
	numerics := make(map[string]string)

	for key, desc := range s.config.CommonAttributes {
		attributes[key] = desc
	}

	for key, desc := range s.config.CommonNumerics {
		numerics[key] = desc
	}

	// 添加特定类型属性
	if def, exists := s.config.ContentTypes[contentType]; exists {
		for key, desc := range def.Attributes {
			attributes[key] = desc
		}

		for key, desc := range def.Numerics {
			numerics[key] = desc
		}

		result["creators"] = def.Creators
		result["contributors"] = def.Contributors
		result["name"] = def.Name
		result["description"] = def.Description
		result["default_fee"] = def.DefaultFee
	}

	result["attributes"] = attributes
	result["numerics"] = numerics

	return result
}

// GetContentTypeFee 获取内容类型费用
func (s *ContentTypeConfigService) GetContentTypeFee(contentType string) float64 {
	if def, exists := s.config.ContentTypes[contentType]; exists {
		return def.DefaultFee
	}
	return 0.1 // 默认费用
}

// IsValidContentType 检查内容类型是否有效
func (s *ContentTypeConfigService) IsValidContentType(contentType string) bool {
	_, exists := s.config.ContentTypes[contentType]
	return exists
}

// GetContentTypesList 获取内容类型列表
func (s *ContentTypeConfigService) GetContentTypesList() []ContentTypeInfo {
	var types []ContentTypeInfo

	for typeName, def := range s.config.ContentTypes {
		types = append(types, ContentTypeInfo{
			TypeName:    typeName,
			DefaultFee:  def.DefaultFee,
			IsActive:    true,
			Description: def.Description,
		})
	}

	return types
}

// ReloadConfig 重新加载配置 (用于热更新)
func (s *ContentTypeConfigService) ReloadConfig(configPath string) error {
	config, err := loadContentTypeConfig(configPath)
	if err != nil {
		return fmt.Errorf("failed to reload config: %w", err)
	}

	s.config = config
	return nil
}

// ValidateContentTypeConfig 验证配置文件格式
func ValidateContentTypeConfig(configPath string) error {
	_, err := loadContentTypeConfig(configPath)
	return err
}

// GetConfigPath 获取配置文件路径
func GetConfigPath() string {
	// 尝试多个可能的路径
	possiblePaths := []string{
		"internal/token-service/config/content_types.yaml",
		"config/content_types.yaml",
		"content_types.yaml",
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			absPath, _ := filepath.Abs(path)
			return absPath
		}
	}

	return possiblePaths[0] // 返回默认路径
}
