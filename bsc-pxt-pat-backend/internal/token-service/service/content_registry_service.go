package service

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
)

// ContentRegistryService ContentRegistry合约服务
type ContentRegistryService struct {
	client              *ethclient.Client
	contractAddress     common.Address
	patTokenAddress     common.Address
	systemPrivateKey    string
	systemAddress       common.Address
	gasLimit            uint64
	gasPrice            int64
	logger              zerolog.Logger
	contractABI         abi.ABI
}

// ContentRegistryConfig ContentRegistry服务配置
type ContentRegistryConfig struct {
	ContractAddress  string
	PATTokenAddress  string
	SystemPrivateKey string
	SystemAddress    string
	GasLimit         uint64
	GasPrice         int64
}

// RegisterContentRequest 注册内容请求
type RegisterContentRequest struct {
	ContentType string
	Title       string
	IPFSHash    string
	MetadataURI string
	Reviewers   []string
}

// RegisterContentResult 注册内容结果
type RegisterContentResult struct {
	ContentID   *big.Int
	TxHash      string
	BlockNumber uint64
	GasUsed     uint64
}

// NewContentRegistryService 创建ContentRegistry服务
func NewContentRegistryService(client *ethclient.Client, cfg *ContentRegistryConfig, logger zerolog.Logger) (*ContentRegistryService, error) {
	// 解析合约ABI
	contractABI, err := abi.JSON(strings.NewReader(blockchain.ContentRegistryABIJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ContentRegistry ABI: %w", err)
	}

	return &ContentRegistryService{
		client:              client,
		contractAddress:     common.HexToAddress(cfg.ContractAddress),
		patTokenAddress:     common.HexToAddress(cfg.PATTokenAddress),
		systemPrivateKey:    cfg.SystemPrivateKey,
		systemAddress:       common.HexToAddress(cfg.SystemAddress),
		gasLimit:            cfg.GasLimit,
		gasPrice:            cfg.GasPrice,
		logger:              logger,
		contractABI:         contractABI,
	}, nil
}

// RegisterContent 注册内容到区块链
func (s *ContentRegistryService) RegisterContent(ctx context.Context, req *RegisterContentRequest) (*RegisterContentResult, error) {
	s.logger.Info().
		Str("content_type", req.ContentType).
		Str("title", req.Title).
		Str("ipfs_hash", req.IPFSHash).
		Msg("Registering content to ContentRegistry contract")

	// 解析私钥
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(s.systemPrivateKey, "0x"))
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	// 创建交易选项
	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, big.NewInt(97)) // BSC Testnet
	if err != nil {
		return nil, fmt.Errorf("failed to create transactor: %w", err)
	}

	// 获取nonce
	nonce, err := s.client.PendingNonceAt(ctx, s.systemAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// 设置交易参数
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0) // 不发送ETH
	auth.GasLimit = s.gasLimit
	if s.gasPrice > 0 {
		auth.GasPrice = big.NewInt(s.gasPrice)
	} else {
		gasPrice, err := s.client.SuggestGasPrice(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to suggest gas price: %w", err)
		}
		auth.GasPrice = gasPrice
	}

	// 构造调用数据
	data, err := s.contractABI.Pack("registerContent",
		req.ContentType,
		req.Title,
		req.IPFSHash,
		req.MetadataURI,
		req.Reviewers,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to pack registerContent data: %w", err)
	}

	// 创建交易
	tx := types.NewTransaction(
		nonce,
		s.contractAddress,
		big.NewInt(0),
		s.gasLimit,
		auth.GasPrice,
		data,
	)

	// 获取链ID
	chainID, err := s.client.NetworkID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 签名交易
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 发送交易
	err = s.client.SendTransaction(ctx, signedTx)
	if err != nil {
		return nil, fmt.Errorf("failed to send transaction: %w", err)
	}

	txHash := signedTx.Hash().Hex()
	s.logger.Info().
		Str("tx_hash", txHash).
		Msg("ContentRegistry transaction sent, waiting for confirmation")

	// 等待交易确认
	receipt, err := bind.WaitMined(ctx, s.client, signedTx)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for transaction confirmation: %w", err)
	}

	if receipt.Status != 1 {
		return nil, fmt.Errorf("transaction failed with status: %d", receipt.Status)
	}

	// 解析事件获取ContentID
	var contentID *big.Int
	for _, log := range receipt.Logs {
		if log.Address == s.contractAddress && len(log.Topics) > 0 {
			// 检查是否是ContentRegistered事件
			eventSignature := crypto.Keccak256Hash([]byte("ContentRegistered(uint256,address,string,string,uint256)"))
			if log.Topics[0] == eventSignature {
				// 解析ContentID (第一个indexed参数)
				if len(log.Topics) > 1 {
					contentID = new(big.Int).SetBytes(log.Topics[1].Bytes())
				}
				break
			}
		}
	}

	s.logger.Info().
		Str("tx_hash", txHash).
		Uint64("block_number", receipt.BlockNumber.Uint64()).
		Uint64("gas_used", receipt.GasUsed).
		Interface("content_id", contentID).
		Msg("Content registered successfully")

	return &RegisterContentResult{
		ContentID:   contentID,
		TxHash:      txHash,
		BlockNumber: receipt.BlockNumber.Uint64(),
		GasUsed:     receipt.GasUsed,
	}, nil
}

// GetContentFee 获取内容类型费用
func (s *ContentRegistryService) GetContentFee(ctx context.Context, contentType string) (*big.Int, error) {
	// 构造调用数据
	data, err := s.contractABI.Pack("getContentFee", contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to pack getContentFee data: %w", err)
	}

	// 调用合约
	msg := ethereum.CallMsg{
		To:   &s.contractAddress,
		Data: data,
	}
	result, err := s.client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getContentFee: %w", err)
	}

	// 解析结果
	var fee *big.Int
	err = s.contractABI.UnpackIntoInterface(&fee, "getContentFee", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getContentFee result: %w", err)
	}

	return fee, nil
}

// ContentTypeExists 检查内容类型是否存在
func (s *ContentRegistryService) ContentTypeExists(ctx context.Context, contentType string) (bool, error) {
	// 构造调用数据
	data, err := s.contractABI.Pack("contentTypeExists", contentType)
	if err != nil {
		return false, fmt.Errorf("failed to pack contentTypeExists data: %w", err)
	}

	// 调用合约
	msg := ethereum.CallMsg{
		To:   &s.contractAddress,
		Data: data,
	}
	result, err := s.client.CallContract(ctx, msg, nil)
	if err != nil {
		return false, fmt.Errorf("failed to call contentTypeExists: %w", err)
	}

	// 解析结果
	var exists bool
	err = s.contractABI.UnpackIntoInterface(&exists, "contentTypeExists", result)
	if err != nil {
		return false, fmt.Errorf("failed to unpack contentTypeExists result: %w", err)
	}

	return exists, nil
}

// CreateContentRegistryServiceFromConfig 从配置创建ContentRegistry服务
func CreateContentRegistryServiceFromConfig(client *ethclient.Client, contractAddr, patTokenAddr, systemAddr, systemPrivateKey string, gasLimit uint64, gasPrice int64, logger zerolog.Logger) (*ContentRegistryService, error) {
	registryConfig := &ContentRegistryConfig{
		ContractAddress:  contractAddr,
		PATTokenAddress:  patTokenAddr,
		SystemPrivateKey: systemPrivateKey,
		SystemAddress:    systemAddr,
		GasLimit:         gasLimit,
		GasPrice:         gasPrice,
	}

	return NewContentRegistryService(client, registryConfig, logger)
}
