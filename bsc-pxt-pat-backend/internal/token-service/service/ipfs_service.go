package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/rs/zerolog"
)

// IPFSService IPFS存储服务 - 学习xLog的IPFS集成方案
type IPFSService struct {
	logger     zerolog.Logger
	apiURL     string // IPFS API URL (如 Pinata, Infura IPFS等)
	apiKey     string // API密钥
	apiSecret  string // API密钥
	gatewayURL string // IPFS网关URL
	client     *http.Client
}

// IPFSUploadResponse IPFS上传响应
type IPFSUploadResponse struct {
	Hash      string `json:"hash"`
	Name      string `json:"name"`
	Size      string `json:"size"`
	Timestamp string `json:"timestamp"`
}

// ContentMetadata 通用内容元数据结构 (存储在IPFS) - 支持所有内容类型
type ContentMetadata struct {
	// 基础信息 (所有内容类型通用)
	Title         string   `json:"title"`                    // 标题
	Description   string   `json:"description,omitempty"`    // 描述
	OriginalTitle string   `json:"original_title,omitempty"` // 原标题
	ContentType   string   `json:"content_type"`             // 内容类型
	Tags          []string `json:"tags,omitempty"`           // 标签
	Language      string   `json:"language,omitempty"`       // 语言
	LicenseNumber string   `json:"license_number,omitempty"` // 许可证号
	ReleaseDate   string   `json:"release_date,omitempty"`   // 发布日期
	CoverImage    string   `json:"cover_image,omitempty"`    // 封面图片IPFS哈希
	Attachments   []string `json:"attachments,omitempty"`    // 附件IPFS哈希列表
	Reviewers     []string `json:"reviewers"`                // 审核员列表
	Version       int      `json:"version"`                  // 版本号
	CreatedAt     string   `json:"created_at"`               // 创建时间
	UpdatedAt     string   `json:"updated_at,omitempty"`     // 更新时间

	// 创作者信息 (灵活数组，支持多种角色)
	Creators     []string `json:"creators,omitempty"`     // 创作者列表 (格式: "role:name")
	Contributors []string `json:"contributors,omitempty"` // 贡献者列表 (格式: "role:name")

	// 扩展属性 (键值对，支持任意自定义属性)
	Attributes map[string]string `json:"attributes,omitempty"` // 字符串属性
	Numerics   map[string]int64  `json:"numerics,omitempty"`   // 数值属性
}

// NewIPFSService 创建IPFS服务实例
func NewIPFSService(apiURL, apiKey, apiSecret, gatewayURL string, logger zerolog.Logger) *IPFSService {
	return &IPFSService{
		logger:     logger.With().Str("component", "ipfs_service").Logger(),
		apiURL:     apiURL,
		apiKey:     apiKey,
		apiSecret:  apiSecret,
		gatewayURL: gatewayURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// UploadContent 上传内容到IPFS
func (s *IPFSService) UploadContent(ctx context.Context, content []byte, filename string) (*IPFSUploadResponse, error) {
	s.logger.Info().
		Str("filename", filename).
		Int("size", len(content)).
		Msg("Uploading content to IPFS")

	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	_, err = part.Write(content)
	if err != nil {
		return nil, fmt.Errorf("failed to write content: %w", err)
	}

	// 关闭writer
	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close writer: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", s.apiURL+"/pinning/pinFileToIPFS", &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+s.apiKey)

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("IPFS upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应 (Pinata格式)
	var pinataResp struct {
		IpfsHash  string `json:"IpfsHash"`
		PinSize   int    `json:"PinSize"`
		Timestamp string `json:"Timestamp"`
	}

	err = json.Unmarshal(body, &pinataResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	result := &IPFSUploadResponse{
		Hash:      pinataResp.IpfsHash,
		Name:      filename,
		Size:      fmt.Sprintf("%d", pinataResp.PinSize),
		Timestamp: pinataResp.Timestamp,
	}

	s.logger.Info().
		Str("hash", result.Hash).
		Str("size", result.Size).
		Msg("Content uploaded to IPFS successfully")

	return result, nil
}

// UploadMetadata 上传元数据到IPFS
func (s *IPFSService) UploadMetadata(ctx context.Context, metadata *ContentMetadata) (*IPFSUploadResponse, error) {
	s.logger.Info().
		Str("title", metadata.Title).
		Str("content_type", metadata.ContentType).
		Msg("Uploading metadata to IPFS")

	// 设置时间戳
	now := time.Now().UTC().Format(time.RFC3339)
	if metadata.CreatedAt == "" {
		metadata.CreatedAt = now
	}
	metadata.UpdatedAt = now

	// 序列化元数据
	metadataBytes, err := json.MarshalIndent(metadata, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("metadata_%s_%d.json",
		strings.ReplaceAll(metadata.ContentType, " ", "_"),
		time.Now().Unix())

	return s.UploadContent(ctx, metadataBytes, filename)
}

// GetContent 从IPFS获取内容
func (s *IPFSService) GetContent(ctx context.Context, hash string) ([]byte, error) {
	s.logger.Info().
		Str("hash", hash).
		Msg("Retrieving content from IPFS")

	// 构造IPFS网关URL
	url := fmt.Sprintf("%s/ipfs/%s", s.gatewayURL, hash)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get content with status %d", resp.StatusCode)
	}

	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read content: %w", err)
	}

	s.logger.Info().
		Str("hash", hash).
		Int("size", len(content)).
		Msg("Content retrieved from IPFS successfully")

	return content, nil
}

// GetMetadata 从IPFS获取元数据
func (s *IPFSService) GetMetadata(ctx context.Context, hash string) (*ContentMetadata, error) {
	s.logger.Info().
		Str("hash", hash).
		Msg("Retrieving metadata from IPFS")

	content, err := s.GetContent(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("failed to get content: %w", err)
	}

	var metadata ContentMetadata
	err = json.Unmarshal(content, &metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
	}

	return &metadata, nil
}

// ValidateIPFSHash 验证IPFS哈希格式
func (s *IPFSService) ValidateIPFSHash(hash string) bool {
	if len(hash) == 0 {
		return false
	}

	// 检查IPFS v0格式 (以Qm开头，46字符)
	if len(hash) == 46 && strings.HasPrefix(hash, "Qm") {
		return true
	}

	// 检查IPFS v1格式 (以bafy开头，59字符)
	if len(hash) == 59 && strings.HasPrefix(hash, "bafy") {
		return true
	}

	return false
}

// GetIPFSURL 获取IPFS内容的完整URL
func (s *IPFSService) GetIPFSURL(hash string) string {
	if !s.ValidateIPFSHash(hash) {
		return ""
	}
	return fmt.Sprintf("%s/ipfs/%s", s.gatewayURL, hash)
}

// PinContent 固定内容到IPFS (防止被垃圾回收)
func (s *IPFSService) PinContent(ctx context.Context, hash string) error {
	s.logger.Info().
		Str("hash", hash).
		Msg("Pinning content to IPFS")

	// 构造请求体
	reqBody := map[string]interface{}{
		"hashToPin": hash,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.apiURL+"/pinning/pinByHash", bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.apiKey)

	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to pin content with status %d: %s", resp.StatusCode, string(body))
	}

	s.logger.Info().
		Str("hash", hash).
		Msg("Content pinned to IPFS successfully")

	return nil
}
