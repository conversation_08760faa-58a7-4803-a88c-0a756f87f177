package service

import (
	"fmt"
	"pxpat-backend/internal/token-service/models"
	"time"

	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

// BridgeService 跨链桥服务
type BridgeService struct {
	db     *gorm.DB
	logger zerolog.Logger
}

// NewBridgeService 创建跨链桥服务
func NewBridgeService(db *gorm.DB, logger zerolog.Logger) *BridgeService {
	return &BridgeService{
		db:     db,
		logger: logger,
	}
}

// GetBridgeOverview 获取跨链概览
func (s *BridgeService) GetBridgeOverview() (*models.BridgeOverviewDTO, error) {
	var overview models.BridgeOverviewDTO
	var totalRequests, successfulRequests, failedRequests, pendingRequests int64

	// 获取总请求数
	if err := s.db.Model(&models.BridgeRequest{}).Count(&totalRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to get total requests: %w", err)
	}
	overview.TotalRequests = uint64(totalRequests)

	// 获取成功请求数
	if err := s.db.Model(&models.BridgeRequest{}).Where("status = ?", "confirmed").Count(&successfulRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to get successful requests: %w", err)
	}
	overview.SuccessfulRequests = uint64(successfulRequests)

	// 获取失败请求数
	if err := s.db.Model(&models.BridgeRequest{}).Where("status = ?", "failed").Count(&failedRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to get failed requests: %w", err)
	}
	overview.FailedRequests = uint64(failedRequests)

	// 获取待处理请求数
	if err := s.db.Model(&models.BridgeRequest{}).Where("status = ?", "pending").Count(&pendingRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to get pending requests: %w", err)
	}
	overview.PendingRequests = uint64(pendingRequests)

	// 计算成功率
	if overview.TotalRequests > 0 {
		successRate := float64(overview.SuccessfulRequests) / float64(overview.TotalRequests) * 100
		overview.SuccessRate = fmt.Sprintf("%.2f%%", successRate)
	} else {
		overview.SuccessRate = "0.00%"
	}

	// 获取总跨链量和手续费
	var volumeResult struct {
		TotalVolume string
		TotalFees   string
	}
	
	if err := s.db.Model(&models.BridgeRequest{}).
		Select("COALESCE(SUM(CAST(amount AS DECIMAL)), 0) as total_volume, COALESCE(SUM(CAST(fee AS DECIMAL)), 0) as total_fees").
		Where("status = ?", "confirmed").
		Scan(&volumeResult).Error; err != nil {
		s.logger.Warn().Err(err).Msg("Failed to get volume stats, using defaults")
		overview.TotalVolume = "0"
		overview.TotalFees = "0"
	} else {
		overview.TotalVolume = volumeResult.TotalVolume
		overview.TotalFees = volumeResult.TotalFees
	}

	// 获取活跃验证者数量
	var activeValidators int64
	if err := s.db.Model(&models.BridgeValidator{}).Where("is_active = ?", true).Count(&activeValidators).Error; err != nil {
		return nil, fmt.Errorf("failed to get active validators: %w", err)
	}
	overview.ActiveValidators = uint64(activeValidators)

	return &overview, nil
}

// GetBridgeRequests 获取跨链请求列表
func (s *BridgeService) GetBridgeRequests(page, pageSize int, status string) ([]models.BridgeRequestDTO, int64, error) {
	var requests []models.BridgeRequest
	var total int64

	query := s.db.Model(&models.BridgeRequest{})
	
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bridge requests: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&requests).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get bridge requests: %w", err)
	}

	// 转换为DTO
	var requestDTOs []models.BridgeRequestDTO
	for _, req := range requests {
		progress := fmt.Sprintf("%d/%d", req.ConfirmationCount, req.RequiredConfirms)
		
		dto := models.BridgeRequestDTO{
			RequestID:         req.RequestID,
			FromChain:         req.FromChain,
			ToChain:           req.ToChain,
			TokenSymbol:       s.getTokenSymbol(req.TokenAddress),
			Sender:            req.Sender,
			Receiver:          req.Receiver,
			Amount:            req.Amount,
			Fee:               req.Fee,
			Status:            req.Status,
			TxHash:            req.TxHash,
			ConfirmationCount: req.ConfirmationCount,
			RequiredConfirms:  req.RequiredConfirms,
			Progress:          progress,
			CreatedAt:         req.CreatedAt,
		}
		requestDTOs = append(requestDTOs, dto)
	}

	return requestDTOs, total, nil
}

// GetBridgeStats 获取跨链统计
func (s *BridgeService) GetBridgeStats(days int) ([]models.BridgeStatsDTO, error) {
	var stats []models.BridgeStats

	startDate := time.Now().AddDate(0, 0, -days)
	
	if err := s.db.Where("date >= ?", startDate).Order("date DESC").Find(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to get bridge stats: %w", err)
	}

	// 转换为DTO
	var statsDTOs []models.BridgeStatsDTO
	for _, stat := range stats {
		dto := models.BridgeStatsDTO{
			Date:               stat.Date.Format("2006-01-02"),
			TotalRequests:      stat.TotalRequests,
			SuccessfulRequests: stat.SuccessfulRequests,
			FailedRequests:     stat.FailedRequests,
			TotalVolume:        stat.TotalVolume,
			TotalFees:          stat.TotalFees,
			AverageConfirmTime: stat.AverageConfirmTime,
		}
		statsDTOs = append(statsDTOs, dto)
	}

	return statsDTOs, nil
}

// GetBridgeValidators 获取验证者列表
func (s *BridgeService) GetBridgeValidators() ([]models.BridgeValidatorDTO, error) {
	var validators []models.BridgeValidator

	if err := s.db.Order("total_confirms DESC").Find(&validators).Error; err != nil {
		return nil, fmt.Errorf("failed to get bridge validators: %w", err)
	}

	// 转换为DTO
	var validatorDTOs []models.BridgeValidatorDTO
	for _, validator := range validators {
		dto := models.BridgeValidatorDTO{
			Address:         validator.Address,
			IsActive:        validator.IsActive,
			TotalConfirms:   validator.TotalConfirms,
			LastConfirmTime: validator.LastConfirmTime,
		}
		validatorDTOs = append(validatorDTOs, dto)
	}

	return validatorDTOs, nil
}

// GetBridgeRequestByID 根据ID获取跨链请求详情
func (s *BridgeService) GetBridgeRequestByID(requestID string) (*models.BridgeRequestDTO, error) {
	var request models.BridgeRequest

	if err := s.db.Where("request_id = ?", requestID).First(&request).Error; err != nil {
		return nil, fmt.Errorf("failed to get bridge request: %w", err)
	}

	// 获取确认记录
	var confirmations []models.BridgeConfirmation
	s.db.Where("request_id = ?", requestID).Find(&confirmations)

	progress := fmt.Sprintf("%d/%d", request.ConfirmationCount, request.RequiredConfirms)

	dto := &models.BridgeRequestDTO{
		RequestID:         request.RequestID,
		FromChain:         request.FromChain,
		ToChain:           request.ToChain,
		TokenSymbol:       s.getTokenSymbol(request.TokenAddress),
		Sender:            request.Sender,
		Receiver:          request.Receiver,
		Amount:            request.Amount,
		Fee:               request.Fee,
		Status:            request.Status,
		TxHash:            request.TxHash,
		ConfirmationCount: request.ConfirmationCount,
		RequiredConfirms:  request.RequiredConfirms,
		Progress:          progress,
		CreatedAt:         request.CreatedAt,
	}

	return dto, nil
}

// getTokenSymbol 根据合约地址获取代币符号
func (s *BridgeService) getTokenSymbol(tokenAddress string) string {
	// 这里可以根据配置文件或数据库查询代币符号
	// 暂时硬编码常用代币
	switch tokenAddress {
	case "0x920b936D5e650e54F7940D171B54F6495d8a6711": // PAT代币地址
		return "PAT"
	case "0x833a05A4aFD3D04a5a57A40d40476a7e45a73efe": // PXT代币地址
		return "PXT"
	default:
		return "UNKNOWN"
	}
}

// CreateBridgeRequest 创建跨链请求记录
func (s *BridgeService) CreateBridgeRequest(request *models.BridgeRequest) error {
	if err := s.db.Create(request).Error; err != nil {
		return fmt.Errorf("failed to create bridge request: %w", err)
	}
	return nil
}

// UpdateBridgeRequestStatus 更新跨链请求状态
func (s *BridgeService) UpdateBridgeRequestStatus(requestID string, status string, confirmationCount uint) error {
	if err := s.db.Model(&models.BridgeRequest{}).
		Where("request_id = ?", requestID).
		Updates(map[string]interface{}{
			"status":             status,
			"confirmation_count": confirmationCount,
			"updated_at":         time.Now(),
		}).Error; err != nil {
		return fmt.Errorf("failed to update bridge request status: %w", err)
	}
	return nil
}

// AddBridgeConfirmation 添加跨链确认记录
func (s *BridgeService) AddBridgeConfirmation(confirmation *models.BridgeConfirmation) error {
	if err := s.db.Create(confirmation).Error; err != nil {
		return fmt.Errorf("failed to add bridge confirmation: %w", err)
	}
	return nil
}
