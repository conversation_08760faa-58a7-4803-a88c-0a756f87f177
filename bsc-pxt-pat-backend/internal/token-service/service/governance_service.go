package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// GovernanceService 治理服务
type GovernanceService struct {
	governanceRepo *repository.GovernanceRepository
	stakingRepo    *repository.StakingRepository
	client         *blockchain.MultiContractClient
	cacheManager   *cache.CacheManager
	logger         zerolog.Logger
}

// NewGovernanceService 创建新的治理服务
func NewGovernanceService(
	governanceRepo *repository.GovernanceRepository,
	stakingRepo *repository.StakingRepository,
	client *blockchain.MultiContractClient,
	cacheManager *cache.CacheManager,
	logger zerolog.Logger,
) *GovernanceService {
	return &GovernanceService{
		governanceRepo: governanceRepo,
		stakingRepo:    stakingRepo,
		client:         client,
		cacheManager:   cacheManager,
		logger:         logger.With().Str("component", "governance_service").Logger(),
	}
}

// ProposalListResponse 提案列表响应
type ProposalListResponse struct {
	Proposals []models.Proposal `json:"proposals"`
	Total     int64             `json:"total"`
	Page      int               `json:"page"`
	Limit     int               `json:"limit"`
	HasMore   bool              `json:"has_more"`
}

// ProposalDetailResponse 提案详情响应
type ProposalDetailResponse struct {
	Proposal   models.Proposal            `json:"proposal"`
	Parameters []models.ProposalParameter `json:"parameters,omitempty"`
	VoteStats  ProposalVoteStats          `json:"vote_stats"`
}

// ProposalVoteStats 提案投票统计
type ProposalVoteStats struct {
	TotalVotes        string  `json:"total_votes"`
	ForVotes          string  `json:"for_votes"`
	AgainstVotes      string  `json:"against_votes"`
	AbstainVotes      string  `json:"abstain_votes"`
	ParticipationRate float64 `json:"participation_rate"`
	QuorumReached     bool    `json:"quorum_reached"`
	MajorityReached   bool    `json:"majority_reached"`
}

// UserVotingPowerResponse 用户投票权重响应
type UserVotingPowerResponse struct {
	UserAddress     string    `json:"user_address"`
	StakedAmount    string    `json:"staked_amount"`
	StakingLevel    int       `json:"staking_level"`
	LevelMultiplier float64   `json:"level_multiplier"`
	DelegatedPower  string    `json:"delegated_power"`
	TotalPower      string    `json:"total_power"`
	LastUpdated     time.Time `json:"last_updated"`
}

// VoteHistoryResponse 投票历史响应
type VoteHistoryResponse struct {
	Votes   []models.Vote `json:"votes"`
	Total   int64         `json:"total"`
	Page    int           `json:"page"`
	Limit   int           `json:"limit"`
	HasMore bool          `json:"has_more"`
}

// GovernanceStatsResponse 治理统计响应
type GovernanceStatsResponse struct {
	TotalProposals    int       `json:"total_proposals"`
	ActiveProposals   int       `json:"active_proposals"`
	ExecutedProposals int       `json:"executed_proposals"`
	TotalVoters       int       `json:"total_voters"`
	ActiveVoters      int       `json:"active_voters"`
	TotalVotingPower  string    `json:"total_voting_power"`
	ParticipationRate float64   `json:"participation_rate"`
	AverageQuorum     float64   `json:"average_quorum"`
	LastUpdated       time.Time `json:"last_updated"`
}

// GetProposals 获取提案列表
func (s *GovernanceService) GetProposals(ctx context.Context, status, category string, page, limit int) (*ProposalListResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 验证状态参数
	if status != "" {
		validStatuses := []string{"pending", "active", "succeeded", "defeated", "executed", "cancelled"}
		if !contains(validStatuses, status) {
			return nil, fmt.Errorf("invalid status: %s", status)
		}
	}

	// 验证分类参数
	if category != "" {
		validCategories := []string{"parameter", "upgrade", "treasury"}
		if !contains(validCategories, category) {
			return nil, fmt.Errorf("invalid category: %s", category)
		}
	}

	// 获取提案列表
	proposals, total, err := s.governanceRepo.GetProposals(ctx, status, category, page, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get proposals: %w", err)
	}

	return &ProposalListResponse{
		Proposals: proposals,
		Total:     total,
		Page:      page,
		Limit:     limit,
		HasMore:   int64(page*limit) < total,
	}, nil
}

// GetProposalDetail 获取提案详情
func (s *GovernanceService) GetProposalDetail(ctx context.Context, proposalID uint64) (*ProposalDetailResponse, error) {
	// 获取提案基本信息
	proposal, err := s.governanceRepo.GetProposal(ctx, proposalID)
	if err != nil {
		return nil, fmt.Errorf("failed to get proposal: %w", err)
	}
	if proposal == nil {
		return nil, fmt.Errorf("proposal not found: %d", proposalID)
	}

	// 获取提案参数（如果有）
	parameters, err := s.governanceRepo.GetProposalParameters(ctx, proposalID)
	if err != nil {
		s.logger.Warn().Err(err).Uint64("proposal_id", proposalID).Msg("Failed to get proposal parameters")
	}

	// 计算投票统计
	voteStats := s.calculateVoteStats(proposal)

	return &ProposalDetailResponse{
		Proposal:   *proposal,
		Parameters: parameters,
		VoteStats:  voteStats,
	}, nil
}

// calculateVoteStats 计算投票统计
func (s *GovernanceService) calculateVoteStats(proposal *models.Proposal) ProposalVoteStats {
	// 计算参与率
	participationRate := 0.0
	if proposal.QuorumRequired != "0" {
		// TODO: 计算实际参与率
		participationRate = 0.0
	}

	// 检查是否达到法定人数
	quorumReached := proposal.TotalVotes >= proposal.QuorumRequired

	// 检查是否达到多数票
	majorityReached := false
	if proposal.TotalVotes != "0" {
		// TODO: 根据具体的多数票规则计算
		majorityReached = false
	}

	return ProposalVoteStats{
		TotalVotes:        proposal.TotalVotes,
		ForVotes:          proposal.ForVotes,
		AgainstVotes:      proposal.AgainstVotes,
		AbstainVotes:      proposal.AbstainVotes,
		ParticipationRate: participationRate,
		QuorumReached:     quorumReached,
		MajorityReached:   majorityReached,
	}
}

// GetUserVotingPower 获取用户投票权重
func (s *GovernanceService) GetUserVotingPower(ctx context.Context, userAddress string) (*UserVotingPowerResponse, error) {
	if !common.IsHexAddress(userAddress) {
		return nil, fmt.Errorf("invalid address format: %s", userAddress)
	}

	userAddress = strings.ToLower(userAddress)

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("voting_power:%s", userAddress)
	if s.cacheManager != nil {
		var power UserVotingPowerResponse
		if err := s.cacheManager.Get(ctx, cacheKey, &power); err == nil {
			return &power, nil
		}
	}

	// 从数据库获取投票权重
	votingPower, err := s.governanceRepo.GetVotingPower(ctx, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get voting power: %w", err)
	}

	var response *UserVotingPowerResponse
	if votingPower != nil {
		response = &UserVotingPowerResponse{
			UserAddress:     userAddress,
			StakedAmount:    votingPower.StakedAmount,
			StakingLevel:    votingPower.StakingLevel,
			LevelMultiplier: votingPower.LevelMultiplier,
			DelegatedPower:  votingPower.DelegatedPower,
			TotalPower:      votingPower.TotalPower,
			LastUpdated:     votingPower.LastUpdated,
		}
	} else {
		// 如果数据库没有数据，尝试从质押信息计算
		response = &UserVotingPowerResponse{
			UserAddress:     userAddress,
			StakedAmount:    "0",
			StakingLevel:    0,
			LevelMultiplier: 1.0,
			DelegatedPower:  "0",
			TotalPower:      "0",
			LastUpdated:     time.Now(),
		}

		// 从质押信息计算投票权重
		if err := s.calculateVotingPowerFromStaking(ctx, userAddress, response); err != nil {
			s.logger.Warn().Err(err).Str("user", userAddress).Msg("Failed to calculate voting power from staking")
		}
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, response, 5*time.Minute)
	}

	return response, nil
}

// calculateVotingPowerFromStaking 从质押信息计算投票权重
func (s *GovernanceService) calculateVotingPowerFromStaking(ctx context.Context, userAddress string, response *UserVotingPowerResponse) error {
	// 获取用户质押信息
	userStake, err := s.stakingRepo.GetUserStake(ctx, userAddress)
	if err != nil {
		return fmt.Errorf("failed to get user stake: %w", err)
	}

	if userStake != nil && userStake.IsActive {
		response.StakedAmount = userStake.Amount.String()
		response.StakingLevel = int(userStake.StakingLevel)

		// 根据质押等级计算倍数
		response.LevelMultiplier = s.getLevelMultiplier(userStake.StakingLevel)

		// 计算总投票权重 = 质押金额 * 等级倍数
		// TODO: 实现具体的投票权重计算逻辑
		response.TotalPower = userStake.Amount.String()
		response.LastUpdated = userStake.LastUpdateTime
	}

	return nil
}

// getLevelMultiplier 获取等级倍数
func (s *GovernanceService) getLevelMultiplier(level models.StakingLevel) float64 {
	switch level {
	case models.DingJi:
		return 1.0
	case models.ChengJi:
		return 1.2
	case models.YiJi:
		return 1.4
	case models.JiaJi:
		return 1.6
	case models.ShiJue:
		return 1.8
	case models.ShuangShiJue:
		return 2.0
	case models.ZhiZun:
		return 2.5
	default:
		return 1.0
	}
}

// GetUserVoteHistory 获取用户投票历史
func (s *GovernanceService) GetUserVoteHistory(ctx context.Context, userAddress string, page, limit int) (*VoteHistoryResponse, error) {
	if !common.IsHexAddress(userAddress) {
		return nil, fmt.Errorf("invalid address format: %s", userAddress)
	}

	userAddress = strings.ToLower(userAddress)

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 获取投票历史
	votes, total, err := s.governanceRepo.GetUserVotes(ctx, userAddress, page, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get user votes: %w", err)
	}

	return &VoteHistoryResponse{
		Votes:   votes,
		Total:   total,
		Page:    page,
		Limit:   limit,
		HasMore: int64(page*limit) < total,
	}, nil
}

// GetGovernanceStats 获取治理统计
func (s *GovernanceService) GetGovernanceStats(ctx context.Context) (*GovernanceStatsResponse, error) {
	// 尝试从缓存获取
	cacheKey := "governance_stats"
	if s.cacheManager != nil {
		var stats GovernanceStatsResponse
		if err := s.cacheManager.Get(ctx, cacheKey, &stats); err == nil {
			return &stats, nil
		}
	}

	// 从数据库获取最新统计
	dbStats, err := s.governanceRepo.GetLatestGovernanceStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get governance stats: %w", err)
	}

	var response *GovernanceStatsResponse
	if dbStats != nil {
		response = &GovernanceStatsResponse{
			TotalProposals:    dbStats.TotalProposals,
			ActiveProposals:   dbStats.ActiveProposals,
			ExecutedProposals: dbStats.ExecutedProposals,
			TotalVoters:       dbStats.TotalVoters,
			ActiveVoters:      dbStats.ActiveVoters,
			TotalVotingPower:  dbStats.TotalVotingPower,
			ParticipationRate: dbStats.ParticipationRate,
			AverageQuorum:     dbStats.AverageQuorum,
			LastUpdated:       dbStats.Date,
		}
	} else {
		// 如果没有统计数据，返回默认值
		response = &GovernanceStatsResponse{
			TotalProposals:    0,
			ActiveProposals:   0,
			ExecutedProposals: 0,
			TotalVoters:       0,
			ActiveVoters:      0,
			TotalVotingPower:  "0",
			ParticipationRate: 0.0,
			AverageQuorum:     0.0,
			LastUpdated:       time.Now(),
		}
	}

	// 缓存结果
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, response, 10*time.Minute)
	}

	return response, nil
}

// GetProposalVotes 获取提案的投票详情
func (s *GovernanceService) GetProposalVotes(ctx context.Context, proposalID uint64, page, limit int) (*VoteHistoryResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 获取提案投票
	votes, total, err := s.governanceRepo.GetProposalVotes(ctx, proposalID, page, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get proposal votes: %w", err)
	}

	return &VoteHistoryResponse{
		Votes:   votes,
		Total:   total,
		Page:    page,
		Limit:   limit,
		HasMore: int64(page*limit) < total,
	}, nil
}

// contains 检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
