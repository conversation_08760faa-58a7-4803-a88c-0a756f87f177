package service

import (
	"context"
	"math/big"
	"strings"

	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/models"
)

// EventNotifier 事件通知器
type EventNotifier struct {
	notificationService *NotificationService
	rankingService      *RankingService
	logger              zerolog.Logger
}

// NewEventNotifier 创建新的事件通知器
func NewEventNotifier(
	notificationService *NotificationService,
	rankingService *RankingService,
	logger zerolog.Logger,
) *EventNotifier {
	return &EventNotifier{
		notificationService: notificationService,
		rankingService:      rankingService,
		logger:              logger.With().Str("component", "event_notifier").Logger(),
	}
}

// OnStakeEvent 处理质押事件
func (e *EventNotifier) OnStakeEvent(ctx context.Context, event *models.StakingEvent) {
	userAddress := strings.ToLower(event.UserAddress)

	switch event.EventType {
	case "Staked":
		// 通知质押成功
		level := models.DingJi
		if event.NewLevel != nil {
			level = *event.NewLevel
		}
		e.notificationService.NotifyStakeConfirmed(
			userAddress,
			event.Amount.String(),
			level,
			"0", // MiningPower需要从其他地方获取
			event.TransactionHash,
		)

		// 检查排名变化
		go e.checkRankingChanges(ctx, userAddress)

	case "Withdrawn":
		// 通知解质押成功
		e.notificationService.NotifyUnstakeConfirmed(
			userAddress,
			event.Amount.String(),
			event.TransactionHash,
		)

		// 检查排名变化
		go e.checkRankingChanges(ctx, userAddress)

	case "RewardClaimed":
		// 通知奖励领取
		e.notificationService.NotifyRewardClaimed(
			userAddress,
			event.Amount.String(),
			event.TransactionHash,
		)

	case "StakingLevelUpdated":
		// 通知等级升级
		oldLevel := models.DingJi
		newLevel := models.DingJi

		if event.OldLevel != nil {
			oldLevel = *event.OldLevel
		}
		if event.NewLevel != nil {
			newLevel = *event.NewLevel
		}

		e.notificationService.NotifyLevelUpgrade(
			userAddress,
			oldLevel,
			newLevel,
		)

	case "MiningPowerUpdated":
		// 挖矿算力更新，可以发送WebSocket更新
		e.notificationService.websocketService.BroadcastToUser(
			userAddress,
			"mining_power_updated",
			map[string]interface{}{
				"user_address": userAddress,
				"mining_power": "0", // MiningPower需要从其他地方获取
				"tx_hash":      event.TransactionHash,
			},
		)
	}

	e.logger.Info().
		Str("user", userAddress).
		Str("event_type", string(event.EventType)).
		Str("amount", event.Amount.String()).
		Msg("Stake event notification processed")
}

// OnTransferEvent 处理转账事件
func (e *EventNotifier) OnTransferEvent(ctx context.Context, transfer *models.TokenTransfer) {
	fromAddress := strings.ToLower(transfer.FromAddress)
	toAddress := strings.ToLower(transfer.ToAddress)

	// 通知发送方余额更新
	if fromAddress != "0x0000000000000000000000000000000000000000" {
		e.notificationService.websocketService.BroadcastToUser(
			fromAddress,
			"balance_updated",
			map[string]interface{}{
				"token_type": transfer.TokenType,
				"change":     "-" + transfer.Amount,
				"tx_hash":    transfer.TransactionHash,
				"type":       "sent",
			},
		)
	}

	// 通知接收方余额更新
	if toAddress != "0x0000000000000000000000000000000000000000" {
		e.notificationService.websocketService.BroadcastToUser(
			toAddress,
			"balance_updated",
			map[string]interface{}{
				"token_type": transfer.TokenType,
				"change":     "+" + transfer.Amount,
				"tx_hash":    transfer.TransactionHash,
				"type":       "received",
			},
		)
	}

	e.logger.Debug().
		Str("from", fromAddress).
		Str("to", toAddress).
		Str("token", transfer.TokenType).
		Str("amount", transfer.Amount).
		Msg("Transfer event notification processed")
}

// OnGovernanceEvent 处理治理事件
func (e *EventNotifier) OnGovernanceEvent(ctx context.Context, event interface{}) {
	switch v := event.(type) {
	case *models.Proposal:
		// 新提案创建
		e.notificationService.NotifyProposalCreated(
			v.ProposalID,
			v.Title,
			v.Proposer,
		)

	case *models.Vote:
		// 投票事件
		e.notificationService.NotifyVoteCast(
			v.Voter,
			v.ProposalID,
			"", // 提案标题需要从数据库获取
			v.Support == 1,
			v.VotingPower,
			v.TransactionHash,
		)

		// 检查投票权重排名变化
		go e.checkVotingPowerRankingChanges(ctx, v.Voter)
	}

	e.logger.Info().
		Str("event_type", "governance").
		Msg("Governance event notification processed")
}

// checkRankingChanges 检查排名变化
func (e *EventNotifier) checkRankingChanges(ctx context.Context, userAddress string) {
	// 获取用户当前排名
	ranking, err := e.rankingService.GetUserRanking(ctx, userAddress)
	if err != nil {
		e.logger.Warn().Err(err).Str("user", userAddress).Msg("Failed to get user ranking")
		return
	}

	// TODO: 这里需要比较历史排名来确定变化
	// 暂时跳过排名变化通知

	e.logger.Debug().
		Str("user", userAddress).
		Int("staking_rank", ranking.StakingRank).
		Msg("Checked ranking changes")
}

// checkVotingPowerRankingChanges 检查投票权重排名变化
func (e *EventNotifier) checkVotingPowerRankingChanges(ctx context.Context, userAddress string) {
	// 获取用户当前排名
	ranking, err := e.rankingService.GetUserRanking(ctx, userAddress)
	if err != nil {
		e.logger.Warn().Err(err).Str("user", userAddress).Msg("Failed to get user voting power ranking")
		return
	}

	// TODO: 这里需要比较历史排名来确定变化
	// 暂时跳过排名变化通知

	e.logger.Debug().
		Str("user", userAddress).
		Int("voting_power_rank", ranking.VotingPowerRank).
		Msg("Checked voting power ranking changes")
}

// BroadcastSystemUpdate 广播系统更新
func (e *EventNotifier) BroadcastSystemUpdate(updateType string, data interface{}) {
	e.notificationService.websocketService.BroadcastGlobal(updateType, data)

	e.logger.Info().
		Str("update_type", updateType).
		Msg("System update broadcasted")
}

// BroadcastRankingUpdate 广播排行榜更新
func (e *EventNotifier) BroadcastRankingUpdate(rankingType string) {
	updateData := map[string]interface{}{
		"ranking_type": rankingType,
		"updated_at":   "now",
	}

	e.notificationService.websocketService.BroadcastToChannel(
		"rankings",
		"ranking_updated",
		updateData,
	)

	e.logger.Info().
		Str("ranking_type", rankingType).
		Msg("Ranking update broadcasted")
}

// BroadcastMarketUpdate 广播市场更新
func (e *EventNotifier) BroadcastMarketUpdate(tokenType string, data interface{}) {
	e.notificationService.websocketService.BroadcastToChannel(
		"market",
		"market_updated",
		map[string]interface{}{
			"token_type": tokenType,
			"data":       data,
		},
	)

	e.logger.Info().
		Str("token_type", tokenType).
		Msg("Market update broadcasted")
}

// BroadcastNetworkStatus 广播网络状态
func (e *EventNotifier) BroadcastNetworkStatus(status string, data interface{}) {
	e.notificationService.websocketService.BroadcastToChannel(
		"network",
		"network_status",
		map[string]interface{}{
			"status": status,
			"data":   data,
		},
	)

	e.logger.Info().
		Str("status", status).
		Msg("Network status broadcasted")
}

// OnLargeTransfer 处理大额转账事件
func (e *EventNotifier) OnLargeTransfer(ctx context.Context, transfer *models.TokenTransfer) {
	// 定义大额转账阈值
	threshold := new(big.Int)
	threshold.SetString("1000000000000000000000", 10) // 1000 tokens

	amount := new(big.Int)
	amount.SetString(transfer.Amount, 10)

	if amount.Cmp(threshold) >= 0 {
		// 广播大额转账警报
		alertData := map[string]interface{}{
			"from_address": transfer.FromAddress,
			"to_address":   transfer.ToAddress,
			"token_type":   transfer.TokenType,
			"amount":       transfer.Amount,
			"tx_hash":      transfer.TransactionHash,
			"block_number": transfer.BlockNumber,
		}

		e.notificationService.websocketService.BroadcastToChannel(
			"alerts",
			"large_transfer",
			alertData,
		)

		e.logger.Warn().
			Str("from", transfer.FromAddress).
			Str("to", transfer.ToAddress).
			Str("amount", transfer.Amount).
			Str("token", transfer.TokenType).
			Msg("Large transfer detected")
	}
}

// OnAbnormalActivity 处理异常活动事件
func (e *EventNotifier) OnAbnormalActivity(ctx context.Context, activityType string, data interface{}) {
	alertData := map[string]interface{}{
		"activity_type": activityType,
		"data":          data,
		"timestamp":     "now",
	}

	e.notificationService.websocketService.BroadcastToChannel(
		"alerts",
		"abnormal_activity",
		alertData,
	)

	e.logger.Warn().
		Str("activity_type", activityType).
		Msg("Abnormal activity detected")
}
