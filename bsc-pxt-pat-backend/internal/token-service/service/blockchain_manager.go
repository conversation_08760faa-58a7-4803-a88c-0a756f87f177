package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/listener"
	"pxpat-backend/internal/token-service/repository"
)

// BlockchainManager 区块链数据管理器
type BlockchainManager struct {
	client     *blockchain.MultiContractClient
	config     *config.BlockchainConfig
	syncConfig *config.SyncConfig // 🆕 添加同步配置
	logger     zerolog.Logger

	// Repositories
	stakingRepo    *repository.StakingRepository
	eventRepo      *repository.EventRepository
	tokenRepo      *repository.TokenRepository
	governanceRepo *repository.GovernanceRepository

	// Services
	eventListener *listener.MultiEventListener
	syncService   *SyncService

	// State
	isRunning bool
	mu        sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

// NewBlockchainManager 创建新的区块链管理器
func NewBlockchainManager(
	client *blockchain.MultiContractClient,
	cfg *config.BlockchainConfig,
	stakingRepo *repository.StakingRepository,
	eventRepo *repository.EventRepository,
	tokenRepo *repository.TokenRepository,
	governanceRepo *repository.GovernanceRepository,
	cacheManager interface {
		InvalidateOverview() error
		InvalidateAll(ctx context.Context) error
	},
	logger zerolog.Logger,
	syncConfig *config.SyncConfig, // 🆕 同步配置
) *BlockchainManager {
	// 创建事件监听器
	eventListener := listener.NewMultiEventListener(
		client, cfg, stakingRepo, eventRepo, tokenRepo, governanceRepo, logger,
	)

	// 创建同步服务
	syncService := NewSyncService(
		client, cfg, tokenRepo, governanceRepo, stakingRepo, cacheManager, logger,
		syncConfig, // 🌙 使用传入的同步配置
	)

	return &BlockchainManager{
		client:         client,
		config:         cfg,
		syncConfig:     syncConfig, // 🆕 保存同步配置
		logger:         logger.With().Str("component", "blockchain_manager").Logger(),
		stakingRepo:    stakingRepo,
		eventRepo:      eventRepo,
		tokenRepo:      tokenRepo,
		governanceRepo: governanceRepo,
		eventListener:  eventListener,
		syncService:    syncService,
	}
}

// Start 启动区块链管理器
func (bm *BlockchainManager) Start(ctx context.Context) error {
	bm.mu.Lock()
	defer bm.mu.Unlock()

	if bm.isRunning {
		return fmt.Errorf("blockchain manager is already running")
	}

	bm.logger.Info().Msg("Starting blockchain manager...")

	// 创建上下文
	bm.ctx, bm.cancel = context.WithCancel(ctx)
	bm.isRunning = true

	// 检查区块链连接
	if err := bm.client.IsHealthy(bm.ctx); err != nil {
		bm.isRunning = false
		return fmt.Errorf("blockchain client is unhealthy: %w", err)
	}

	// 启动事件监听器
	if bm.syncConfig != nil && bm.syncConfig.Enabled {
		// 根据配置选择事件监听器
		if bm.config.UseBSCScanForEvents && bm.config.BSCScanAPIKey != "" {
			// 使用BSCScan API事件监听器
			bscListener := listener.NewBSCScanEventListener(
				bm.config,
				bm.stakingRepo,
				bm.eventRepo,
				bm.tokenRepo,
				bm.governanceRepo,
				bm.logger,
			)

			bm.wg.Add(1)
			go func() {
				defer bm.wg.Done()
				if err := bscListener.Start(bm.ctx); err != nil && err != context.Canceled {
					bm.logger.Error().Err(err).Msg("BSCScan event listener stopped with error")
				}
			}()

			bm.logger.Info().Msg("Using BSCScan API for event listening")
		} else {
			// 使用传统RPC事件监听器
			bm.wg.Add(1)
			go func() {
				defer bm.wg.Done()
				if err := bm.eventListener.Start(bm.ctx); err != nil && err != context.Canceled {
					bm.logger.Error().Err(err).Msg("RPC event listener stopped with error")
				}
			}()

			bm.logger.Info().Msg("Using RPC for event listening")
		}

		// 启动数据同步服务
		bm.wg.Add(1)
		go func() {
			defer bm.wg.Done()
			if err := bm.syncService.Start(bm.ctx); err != nil && err != context.Canceled {
				bm.logger.Error().Err(err).Msg("Sync service stopped with error")
			}
		}()

		bm.logger.Info().Msg("Event listener and sync service started")
	} else {
		bm.logger.Info().Msg("Sync is disabled, only providing query services")
	}

	// 启动健康检查
	bm.wg.Add(1)
	go func() {
		defer bm.wg.Done()
		bm.healthCheckLoop()
	}()

	bm.logger.Info().Msg("Blockchain manager started successfully")
	return nil
}

// Stop 停止区块链管理器
func (bm *BlockchainManager) Stop() error {
	bm.mu.Lock()
	defer bm.mu.Unlock()

	if !bm.isRunning {
		return nil
	}

	bm.logger.Info().Msg("Stopping blockchain manager...")

	// 取消上下文
	if bm.cancel != nil {
		bm.cancel()
	}

	// 停止服务
	bm.eventListener.Stop()
	bm.syncService.Stop()

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		bm.wg.Wait()
		close(done)
	}()

	// 等待最多30秒
	select {
	case <-done:
		bm.logger.Info().Msg("All services stopped gracefully")
	case <-time.After(30 * time.Second):
		bm.logger.Warn().Msg("Timeout waiting for services to stop")
	}

	// 关闭客户端连接
	bm.client.Close()

	bm.isRunning = false
	bm.logger.Info().Msg("Blockchain manager stopped")

	return nil
}

// healthCheckLoop 健康检查循环
func (bm *BlockchainManager) healthCheckLoop() {
	ticker := time.NewTicker(60 * time.Second) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-bm.ctx.Done():
			return
		case <-ticker.C:
			if err := bm.client.IsHealthy(bm.ctx); err != nil {
				bm.logger.Error().Err(err).Msg("Blockchain client health check failed")
				// TODO: 可以在这里实现重连逻辑
			}
		}
	}
}

// IsRunning 检查是否正在运行
func (bm *BlockchainManager) IsRunning() bool {
	bm.mu.RLock()
	defer bm.mu.RUnlock()
	return bm.isRunning
}

// GetSyncStatus 获取同步状态
func (bm *BlockchainManager) GetSyncStatus() map[string]interface{} {
	bm.mu.RLock()
	defer bm.mu.RUnlock()

	syncEnabled := false
	if bm.syncConfig != nil {
		syncEnabled = bm.syncConfig.Enabled
	}

	status := map[string]interface{}{
		"is_running":        bm.isRunning,
		"sync_enabled":      syncEnabled,
		"event_listener":    bm.eventListener.IsRunning(),
		"sync_service":      bm.syncService.IsRunning(),
		"last_synced_block": bm.eventListener.GetLastSyncedBlock(),
	}

	// 获取最新区块号
	if bm.isRunning {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if latestBlock, err := bm.client.GetLatestBlockNumber(ctx); err == nil {
			status["latest_block"] = latestBlock
			status["blocks_behind"] = latestBlock - bm.eventListener.GetLastSyncedBlock()
		}
	}

	return status
}

// SyncUserData 手动同步用户数据
func (bm *BlockchainManager) SyncUserData(ctx context.Context, userAddress string) error {
	if !bm.isRunning {
		return fmt.Errorf("blockchain manager is not running")
	}

	bm.logger.Info().Str("user", userAddress).Msg("Manual user data sync requested")

	// 同步用户质押信息
	if err := bm.syncService.SyncUserStakingInfo(ctx, userAddress); err != nil {
		return fmt.Errorf("failed to sync user staking info: %w", err)
	}

	// TODO: 同步用户代币余额、投票权重等

	bm.logger.Info().Str("user", userAddress).Msg("User data sync completed")
	return nil
}

// ForceSync 强制同步所有数据
func (bm *BlockchainManager) ForceSync(ctx context.Context) error {
	if !bm.isRunning {
		return fmt.Errorf("blockchain manager is not running")
	}

	bm.logger.Info().Msg("Force sync requested")

	// 同步代币指标
	if err := bm.syncService.SyncTokenMetrics(ctx); err != nil {
		return fmt.Errorf("failed to sync token metrics: %w", err)
	}

	bm.logger.Info().Msg("Force sync completed")
	return nil
}

// GetClient 获取区块链客户端（用于其他服务）
func (bm *BlockchainManager) GetClient() *blockchain.MultiContractClient {
	return bm.client
}

// GetEventListener 获取事件监听器
func (bm *BlockchainManager) GetEventListener() *listener.MultiEventListener {
	return bm.eventListener
}

// GetSyncService 获取同步服务
func (bm *BlockchainManager) GetSyncService() *SyncService {
	return bm.syncService
}

// RestartServices 重启服务（用于配置更新后）
func (bm *BlockchainManager) RestartServices(ctx context.Context) error {
	bm.logger.Info().Msg("Restarting blockchain services...")

	// 停止当前服务
	if err := bm.Stop(); err != nil {
		return fmt.Errorf("failed to stop services: %w", err)
	}

	// 等待一秒
	time.Sleep(1 * time.Second)

	// 重新启动
	if err := bm.Start(ctx); err != nil {
		return fmt.Errorf("failed to restart services: %w", err)
	}

	bm.logger.Info().Msg("Blockchain services restarted successfully")
	return nil
}

// GetNetworkInfo 获取网络信息
func (bm *BlockchainManager) GetNetworkInfo(ctx context.Context) (map[string]interface{}, error) {
	if !bm.isRunning {
		return nil, fmt.Errorf("blockchain manager is not running")
	}

	// 获取链ID
	chainID, err := bm.client.GetClient().ChainID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 获取最新区块
	latestBlock, err := bm.client.GetLatestBlockNumber(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest block: %w", err)
	}

	// 获取网络Gas价格
	gasPrice, err := bm.client.GetClient().SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %w", err)
	}

	return map[string]interface{}{
		"network":      bm.config.Network,
		"chain_id":     chainID.Int64(),
		"rpc_url":      bm.config.RPCURL,
		"latest_block": latestBlock,
		"gas_price":    gasPrice.String(),
		"contracts":    bm.getContractAddresses(),
	}, nil
}

// getContractAddresses 获取合约地址信息 - 更新为最新部署地址 (2025-07-04)
func (bm *BlockchainManager) getContractAddresses() map[string]string {
	contracts := bm.client.GetContracts()

	return map[string]string{
		// 核心代币合约
		"pxt_token": contracts.Addresses.PXTToken.Hex(),
		"pat_token": contracts.Addresses.PATToken.Hex(),

		// 质押系统合约
		"staking_pool":       contracts.Addresses.StakingPool.Hex(),
		"staking_factory":    contracts.Addresses.StakingFactory.Hex(),
		"reward_distributor": contracts.Addresses.RewardDistributor.Hex(),

		// 治理系统合约
		"dao":              contracts.Addresses.DAO.Hex(),
		"voting":           contracts.Addresses.Voting.Hex(),
		"proposal_manager": contracts.Addresses.ProposalManager.Hex(),
		"treasury":         contracts.Addresses.Treasury.Hex(),
		"role_manager":     contracts.Addresses.RoleManager.Hex(),

		// 其他系统合约
		"token_registry": contracts.Addresses.TokenRegistry.Hex(),
		"pxt_factory":    contracts.Addresses.PXTFactory.Hex(),
		"pat_factory":    contracts.Addresses.PATFactory.Hex(),

		// 池子地址配置 - 重要：用于监听转账和奖励分发
		"china_mainland_pool": contracts.Addresses.ChinaMainlandPool.Hex(),
		"global_pool":         contracts.Addresses.GlobalPool.Hex(),
	}
}
