package service

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/rs/zerolog"
)

// WebSocketService WebSocket服务
type WebSocketService struct {
	clients    map[*websocket.Conn]*Client
	clientsMux sync.RWMutex
	upgrader   websocket.Upgrader
	logger     zerolog.Logger
}

// Client WebSocket客户端
type Client struct {
	Conn          *websocket.Conn
	UserAddress   string
	Subscriptions map[string]bool
	LastPing      time.Time
	Send          chan []byte
}

// Message WebSocket消息
type Message struct {
	Type        string      `json:"type"`
	Channel     string      `json:"channel,omitempty"`
	Data        interface{} `json:"data,omitempty"`
	Timestamp   time.Time   `json:"timestamp"`
	UserAddress string      `json:"user_address,omitempty"`
}

// SubscribeRequest 订阅请求
type SubscribeRequest struct {
	Type        string   `json:"type"`
	Channels    []string `json:"channels"`
	UserAddress string   `json:"user_address,omitempty"`
}

// NewWebSocketService 创建新的WebSocket服务
func NewWebSocketService(logger zerolog.Logger) *WebSocketService {
	return &WebSocketService{
		clients: make(map[*websocket.Conn]*Client),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		logger: logger.With().Str("component", "websocket_service").Logger(),
	}
}

// HandleWebSocket 处理WebSocket连接
func (s *WebSocketService) HandleWebSocket(c *gin.Context) {
	conn, err := s.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to upgrade websocket connection")
		return
	}

	client := &Client{
		Conn:          conn,
		Subscriptions: make(map[string]bool),
		LastPing:      time.Now(),
		Send:          make(chan []byte, 256),
	}

	s.clientsMux.Lock()
	s.clients[conn] = client
	s.clientsMux.Unlock()

	s.logger.Info().Str("remote_addr", conn.RemoteAddr().String()).Msg("New WebSocket connection")

	// 启动客户端处理协程
	go s.handleClient(client)
	go s.writeToClient(client)

	// 发送欢迎消息
	welcomeMsg := Message{
		Type:      "welcome",
		Data:      "Connected to Token Service WebSocket",
		Timestamp: time.Now(),
	}
	s.sendToClient(client, welcomeMsg)
}

// handleClient 处理客户端消息
func (s *WebSocketService) handleClient(client *Client) {
	defer func() {
		s.removeClient(client)
		client.Conn.Close()
	}()

	// 设置读取超时
	client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.Conn.SetPongHandler(func(string) error {
		client.LastPing = time.Now()
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, messageBytes, err := client.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				s.logger.Error().Err(err).Msg("WebSocket error")
			}
			break
		}

		var req SubscribeRequest
		if err := json.Unmarshal(messageBytes, &req); err != nil {
			s.logger.Warn().Err(err).Msg("Invalid message format")
			continue
		}

		s.handleSubscribeRequest(client, &req)
	}
}

// writeToClient 向客户端写入消息
func (s *WebSocketService) writeToClient(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := client.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				s.logger.Error().Err(err).Msg("Failed to write message")
				return
			}

		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleSubscribeRequest 处理订阅请求
func (s *WebSocketService) handleSubscribeRequest(client *Client, req *SubscribeRequest) {
	switch req.Type {
	case "subscribe":
		client.UserAddress = strings.ToLower(req.UserAddress)
		for _, channel := range req.Channels {
			client.Subscriptions[channel] = true
			s.logger.Debug().
				Str("user", client.UserAddress).
				Str("channel", channel).
				Msg("Client subscribed to channel")
		}

		// 发送订阅确认
		response := Message{
			Type:      "subscribed",
			Data:      req.Channels,
			Timestamp: time.Now(),
		}
		s.sendToClient(client, response)

	case "unsubscribe":
		for _, channel := range req.Channels {
			delete(client.Subscriptions, channel)
			s.logger.Debug().
				Str("user", client.UserAddress).
				Str("channel", channel).
				Msg("Client unsubscribed from channel")
		}

		// 发送取消订阅确认
		response := Message{
			Type:      "unsubscribed",
			Data:      req.Channels,
			Timestamp: time.Now(),
		}
		s.sendToClient(client, response)

	default:
		s.logger.Warn().Str("type", req.Type).Msg("Unknown request type")
	}
}

// sendToClient 向单个客户端发送消息
func (s *WebSocketService) sendToClient(client *Client, message Message) {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to marshal message")
		return
	}

	select {
	case client.Send <- messageBytes:
	default:
		s.removeClient(client)
		close(client.Send)
	}
}

// removeClient 移除客户端
func (s *WebSocketService) removeClient(client *Client) {
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	if _, exists := s.clients[client.Conn]; exists {
		delete(s.clients, client.Conn)
		s.logger.Info().
			Str("user", client.UserAddress).
			Msg("Client disconnected")
	}
}

// BroadcastToChannel 向指定频道广播消息
func (s *WebSocketService) BroadcastToChannel(channel string, messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		Channel:   channel,
		Data:      data,
		Timestamp: time.Now(),
	}

	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	for _, client := range s.clients {
		if client.Subscriptions[channel] {
			s.sendToClient(client, message)
		}
	}

	s.logger.Debug().
		Str("channel", channel).
		Str("type", messageType).
		Int("clients", len(s.clients)).
		Msg("Broadcasted message to channel")
}

// BroadcastToUser 向指定用户发送消息
func (s *WebSocketService) BroadcastToUser(userAddress string, messageType string, data interface{}) {
	userAddress = strings.ToLower(userAddress)
	message := Message{
		Type:        messageType,
		Data:        data,
		Timestamp:   time.Now(),
		UserAddress: userAddress,
	}

	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	sent := 0
	for _, client := range s.clients {
		if client.UserAddress == userAddress {
			s.sendToClient(client, message)
			sent++
		}
	}

	s.logger.Debug().
		Str("user", userAddress).
		Str("type", messageType).
		Int("sent", sent).
		Msg("Sent message to user")
}

// BroadcastGlobal 全局广播消息
func (s *WebSocketService) BroadcastGlobal(messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		Data:      data,
		Timestamp: time.Now(),
	}

	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	for _, client := range s.clients {
		s.sendToClient(client, message)
	}

	s.logger.Debug().
		Str("type", messageType).
		Int("clients", len(s.clients)).
		Msg("Broadcasted global message")
}

// GetStats 获取WebSocket统计信息
func (s *WebSocketService) GetStats() map[string]interface{} {
	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	stats := map[string]interface{}{
		"total_connections": len(s.clients),
		"channels":          make(map[string]int),
		"users":             make(map[string]int),
	}

	channelCounts := make(map[string]int)
	userCounts := make(map[string]int)

	for _, client := range s.clients {
		// 统计频道订阅
		for channel := range client.Subscriptions {
			channelCounts[channel]++
		}

		// 统计用户连接
		if client.UserAddress != "" {
			userCounts[client.UserAddress]++
		}
	}

	stats["channels"] = channelCounts
	stats["users"] = userCounts

	return stats
}

// CleanupInactiveClients 清理不活跃的客户端
func (s *WebSocketService) CleanupInactiveClients() {
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	now := time.Now()
	inactiveThreshold := 2 * time.Minute

	for conn, client := range s.clients {
		if now.Sub(client.LastPing) > inactiveThreshold {
			s.logger.Info().
				Str("user", client.UserAddress).
				Msg("Removing inactive client")

			delete(s.clients, conn)
			close(client.Send)
			conn.Close()
		}
	}
}

// StartCleanupRoutine 启动清理协程
func (s *WebSocketService) StartCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.CleanupInactiveClients()
		case <-ctx.Done():
			s.logger.Info().Msg("WebSocket cleanup routine stopped")
			return
		}
	}
}
