package service

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog"
	"gorm.io/gorm"

	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
)

// BlockRecoveryService 区块恢复服务
type BlockRecoveryService struct {
	db          *gorm.DB
	eventRepo   *repository.EventRepository
	syncService *SyncService
	logger      zerolog.Logger
}

// NewBlockRecoveryService 创建区块恢复服务
func NewBlockRecoveryService(
	db *gorm.DB,
	eventRepo *repository.EventRepository,
	syncService *SyncService,
	logger zerolog.Logger,
) *BlockRecoveryService {
	return &BlockRecoveryService{
		db:          db,
		eventRepo:   eventRepo,
		syncService: syncService,
		logger:      logger.With().Str("service", "block_recovery").Logger(),
	}
}

// FailedBlockInfo 失败区块信息
type FailedBlockInfo struct {
	BlockNumber  uint64    `json:"block_number"`
	ErrorMessage string    `json:"error_message"`
	FailedAt     time.Time `json:"failed_at"`
	RetryCount   int       `json:"retry_count"`
}

// GetFailedBlocks 获取所有失败的区块
func (s *BlockRecoveryService) GetFailedBlocks(ctx context.Context) ([]FailedBlockInfo, error) {
	var syncStatuses []models.BlockSyncStatus

	// 查询所有失败状态的区块
	err := s.db.WithContext(ctx).
		Where("sync_status = ?", "failed").
		Order("last_synced_block ASC").
		Find(&syncStatuses).Error

	if err != nil {
		return nil, fmt.Errorf("failed to query failed blocks: %w", err)
	}

	var failedBlocks []FailedBlockInfo
	for _, status := range syncStatuses {
		failedBlocks = append(failedBlocks, FailedBlockInfo{
			BlockNumber:  status.LastSyncedBlock,
			ErrorMessage: status.ErrorMessage,
			FailedAt:     status.UpdatedAt,
			RetryCount:   0, // TODO: 可以添加重试计数字段
		})
	}

	return failedBlocks, nil
}

// RetryFailedBlock 重试单个失败的区块
func (s *BlockRecoveryService) RetryFailedBlock(ctx context.Context, blockNumber uint64) error {
	s.logger.Info().
		Uint64("block", blockNumber).
		Msg("开始重试失败的区块同步")

	// 检查区块是否确实失败
	var syncStatus models.BlockSyncStatus
	err := s.db.WithContext(ctx).
		Where("last_synced_block = ? AND sync_status = ?", blockNumber, "failed").
		First(&syncStatus).Error

	if err == gorm.ErrRecordNotFound {
		return fmt.Errorf("区块 %d 不在失败列表中", blockNumber)
	}
	if err != nil {
		return fmt.Errorf("查询区块状态失败: %w", err)
	}

	// 更新状态为重试中
	err = s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "retrying", "手动重试中...")
	if err != nil {
		s.logger.Error().Err(err).Uint64("block", blockNumber).Msg("更新重试状态失败")
	}

	// 尝试重新同步这个区块
	// 这里需要调用具体的区块同步逻辑
	// 由于我们使用的是BSCScan API，需要特殊处理
	err = s.retrySingleBlock(ctx, blockNumber)
	if err != nil {
		// 重试失败，更新错误信息
		updateErr := s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "failed",
			fmt.Sprintf("手动重试失败: %v", err))
		if updateErr != nil {
			s.logger.Error().Err(updateErr).Uint64("block", blockNumber).Msg("更新失败状态失败")
		}
		return fmt.Errorf("重试区块 %d 失败: %w", blockNumber, err)
	}

	// 重试成功，更新状态
	err = s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "synced", "手动重试成功")
	if err != nil {
		s.logger.Error().Err(err).Uint64("block", blockNumber).Msg("更新成功状态失败")
	}

	s.logger.Info().
		Uint64("block", blockNumber).
		Msg("区块重试同步成功")

	return nil
}

// retrySingleBlock 重试单个区块的同步
func (s *BlockRecoveryService) retrySingleBlock(ctx context.Context, blockNumber uint64) error {
	// 这里需要实现具体的区块同步逻辑
	// 由于我们使用BSCScan API，需要调用相应的事件监听器

	s.logger.Debug().
		Uint64("block", blockNumber).
		Msg("开始重新同步单个区块")

	// TODO: 这里需要根据实际的事件监听器实现来调用
	// 可能需要调用 BSCScanEventListener 的 syncEventsBatch 方法

	// 模拟同步过程（实际实现时需要替换）
	time.Sleep(time.Second * 2)

	// 这里应该调用实际的同步逻辑
	// 例如：return s.eventListener.syncEventsBatch(ctx, blockNumber, blockNumber)

	return nil
}

// RetryAllFailedBlocks 重试所有失败的区块
func (s *BlockRecoveryService) RetryAllFailedBlocks(ctx context.Context) error {
	failedBlocks, err := s.GetFailedBlocks(ctx)
	if err != nil {
		return fmt.Errorf("获取失败区块列表失败: %w", err)
	}

	if len(failedBlocks) == 0 {
		s.logger.Info().Msg("没有失败的区块需要重试")
		return nil
	}

	s.logger.Info().
		Int("count", len(failedBlocks)).
		Msg("开始重试所有失败的区块")

	successCount := 0
	failCount := 0

	for _, block := range failedBlocks {
		s.logger.Info().
			Uint64("block", block.BlockNumber).
			Str("error", block.ErrorMessage).
			Msg("重试失败区块")

		err := s.RetryFailedBlock(ctx, block.BlockNumber)
		if err != nil {
			s.logger.Error().
				Err(err).
				Uint64("block", block.BlockNumber).
				Msg("重试区块失败")
			failCount++
		} else {
			successCount++
		}

		// 添加延迟避免API限制
		time.Sleep(time.Second * 2)
	}

	s.logger.Info().
		Int("success", successCount).
		Int("failed", failCount).
		Int("total", len(failedBlocks)).
		Msg("批量重试完成")

	return nil
}

// GetBlockSyncStatus 获取特定区块的同步状态
func (s *BlockRecoveryService) GetBlockSyncStatus(ctx context.Context, blockNumber uint64) (*models.BlockSyncStatus, error) {
	var syncStatus models.BlockSyncStatus

	err := s.db.WithContext(ctx).
		Where("last_synced_block = ?", blockNumber).
		First(&syncStatus).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("查询区块同步状态失败: %w", err)
	}

	return &syncStatus, nil
}

// MarkBlockForRetry 标记区块需要重试
func (s *BlockRecoveryService) MarkBlockForRetry(ctx context.Context, blockNumber uint64, reason string) error {
	s.logger.Info().
		Uint64("block", blockNumber).
		Str("reason", reason).
		Msg("标记区块需要重试")

	return s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "pending_retry", reason)
}

// ClearFailedBlock 清除失败区块记录（标记为已处理）
func (s *BlockRecoveryService) ClearFailedBlock(ctx context.Context, blockNumber uint64) error {
	s.logger.Info().
		Uint64("block", blockNumber).
		Msg("清除失败区块记录")

	return s.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "manually_cleared", "手动清除")
}

// GetSyncStatistics 获取同步统计信息
func (s *BlockRecoveryService) GetSyncStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计各种状态的区块数量
	var counts []struct {
		Status string
		Count  int64
	}

	err := s.db.WithContext(ctx).
		Model(&models.BlockSyncStatus{}).
		Select("sync_status as status, count(*) as count").
		Group("sync_status").
		Find(&counts).Error

	if err != nil {
		return nil, fmt.Errorf("查询同步统计失败: %w", err)
	}

	for _, count := range counts {
		stats[count.Status] = count.Count
	}

	// 获取最新同步的区块
	var latestSync models.BlockSyncStatus
	err = s.db.WithContext(ctx).
		Order("last_synced_block DESC").
		First(&latestSync).Error

	if err == nil {
		stats["latest_synced_block"] = latestSync.LastSyncedBlock
		stats["latest_sync_time"] = latestSync.LastSyncedTime
	}

	return stats, nil
}
