package repository

import (
	"context"
	"fmt"
	"time"

	"pxpat-backend/internal/token-service/models"

	"gorm.io/gorm"
)

// ContentRepository 内容上链记录仓库
type ContentRepository struct {
	db *gorm.DB
}

// NewContentRepository 创建内容仓库实例
func NewContentRepository(db *gorm.DB) *ContentRepository {
	return &ContentRepository{
		db: db,
	}
}

// CreateContentRecord 创建内容上链记录
func (r *ContentRepository) CreateContentRecord(ctx context.Context, record *models.TokenContentRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

// GetContentRecordByID 根据ID获取内容记录
func (r *ContentRepository) GetContentRecordByID(ctx context.Context, id uint) (*models.TokenContentRecord, error) {
	var record models.TokenContentRecord
	err := r.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetContentRecordByOnChainID 根据上链ID获取内容记录
func (r *ContentRepository) GetContentRecordByOnChainID(ctx context.Context, onChainID string) (*models.TokenContentRecord, error) {
	var record models.TokenContentRecord
	err := r.db.WithContext(ctx).Where("on_chain_id = ?", onChainID).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetContentRecordByContentID 根据内容ID获取内容记录
func (r *ContentRepository) GetContentRecordByContentID(ctx context.Context, contentID string) (*models.TokenContentRecord, error) {
	var record models.TokenContentRecord
	err := r.db.WithContext(ctx).Where("content_id = ?", contentID).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// UpdateContentRecord 更新内容记录
func (r *ContentRepository) UpdateContentRecord(ctx context.Context, record *models.TokenContentRecord) error {
	return r.db.WithContext(ctx).Save(record).Error
}

// UpdateTransactionInfo 更新交易信息
func (r *ContentRepository) UpdateTransactionInfo(ctx context.Context, id uint, txHash string, blockNumber uint64, status string) error {
	return r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"transaction_hash": txHash,
			"block_number":     blockNumber,
			"status":           status,
			"updated_at":       time.Now(),
		}).Error
}

// GetContentRecords 分页获取内容记录
func (r *ContentRepository) GetContentRecords(ctx context.Context, page, limit int, contentType string, status string) ([]*models.TokenContentRecord, int64, error) {
	var records []*models.TokenContentRecord
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenContentRecord{})

	// 添加筛选条件
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetContentRecordsByPublisher 根据发布者获取内容记录
func (r *ContentRepository) GetContentRecordsByPublisher(ctx context.Context, publisherKSUID string, page, limit int) ([]*models.TokenContentRecord, int64, error) {
	var records []*models.TokenContentRecord
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("publisher_ksuid = ?", publisherKSUID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetContentRecordsByDateRange 根据日期范围获取内容记录
func (r *ContentRepository) GetContentRecordsByDateRange(ctx context.Context, startDate, endDate time.Time, page, limit int) ([]*models.TokenContentRecord, int64, error) {
	var records []*models.TokenContentRecord
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("on_chain_date BETWEEN ? AND ?", startDate, endDate)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Order("on_chain_date DESC").Offset(offset).Limit(limit).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetContentStats 获取内容统计信息
func (r *ContentRepository) GetContentStats(ctx context.Context) (map[string]interface{}, error) {
	var stats struct {
		TotalContent     int64   `json:"total_content"`
		ConfirmedContent int64   `json:"confirmed_content"`
		PendingContent   int64   `json:"pending_content"`
		FailedContent    int64   `json:"failed_content"`
		TotalPATFees     float64 `json:"total_pat_fees"`
	}

	// 获取总内容数
	if err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).Count(&stats.TotalContent).Error; err != nil {
		return nil, err
	}

	// 获取各状态内容数
	if err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("status = ?", "confirmed").Count(&stats.ConfirmedContent).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("status = ?", "pending").Count(&stats.PendingContent).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Where("status = ?", "failed").Count(&stats.FailedContent).Error; err != nil {
		return nil, err
	}

	// 获取总PAT费用
	var totalFees string
	if err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Select("COALESCE(SUM(CAST(pat_fee AS DECIMAL)), 0)").
		Where("status = ?", "confirmed").
		Scan(&totalFees).Error; err != nil {
		return nil, err
	}

	// 转换为float64
	if totalFees == "" {
		stats.TotalPATFees = 0
	} else {
		fmt.Sscanf(totalFees, "%f", &stats.TotalPATFees)
	}

	return map[string]interface{}{
		"total_content":     stats.TotalContent,
		"confirmed_content": stats.ConfirmedContent,
		"pending_content":   stats.PendingContent,
		"failed_content":    stats.FailedContent,
		"total_pat_fees":    stats.TotalPATFees,
	}, nil
}

// GetContentTypeStats 获取内容类型统计
func (r *ContentRepository) GetContentTypeStats(ctx context.Context) ([]map[string]interface{}, error) {
	var results []struct {
		ContentType string `json:"content_type"`
		Count       int64  `json:"count"`
		TotalFees   string `json:"total_fees"`
	}

	err := r.db.WithContext(ctx).Model(&models.TokenContentRecord{}).
		Select("content_type, COUNT(*) as count, COALESCE(SUM(CAST(pat_fee AS DECIMAL)), 0) as total_fees").
		Where("status = ?", "confirmed").
		Group("content_type").
		Order("count DESC").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// 转换为map格式
	stats := make([]map[string]interface{}, len(results))
	for i, result := range results {
		var totalFees float64
		if result.TotalFees != "" {
			fmt.Sscanf(result.TotalFees, "%f", &totalFees)
		}

		stats[i] = map[string]interface{}{
			"content_type": result.ContentType,
			"count":        result.Count,
			"total_fees":   totalFees,
		}
	}

	return stats, nil
}

// DeleteContentRecord 删除内容记录
func (r *ContentRepository) DeleteContentRecord(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.TokenContentRecord{}, id).Error
}
