package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"pxpat-backend/internal/token-service/models"
)

// GovernanceRepository 治理数据访问层
type GovernanceRepository struct {
	db *gorm.DB
}

// NewGovernanceRepository 创建新的治理仓库
func NewGovernanceRepository(db *gorm.DB) *GovernanceRepository {
	return &GovernanceRepository{
		db: db,
	}
}

// CreateProposal 创建提案
func (r *GovernanceRepository) CreateProposal(ctx context.Context, proposal *models.Proposal) error {
	return r.db.WithContext(ctx).Create(proposal).Error
}

// UpdateProposal 更新提案
func (r *GovernanceRepository) UpdateProposal(ctx context.Context, proposal *models.Proposal) error {
	return r.db.WithContext(ctx).Save(proposal).Error
}

// GetProposals 获取提案列表
func (r *GovernanceRepository) GetProposals(ctx context.Context, status, category string, page, limit int) ([]models.Proposal, int64, error) {
	var proposals []models.Proposal
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Proposal{})

	// 添加状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 添加分类过滤
	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&proposals).Error; err != nil {
		return nil, 0, err
	}

	return proposals, total, nil
}

// GetProposal 获取单个提案
func (r *GovernanceRepository) GetProposal(ctx context.Context, proposalID uint64) (*models.Proposal, error) {
	var proposal models.Proposal
	err := r.db.WithContext(ctx).Where("proposal_id = ?", proposalID).First(&proposal).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &proposal, nil
}

// GetProposalParameters 获取提案参数
func (r *GovernanceRepository) GetProposalParameters(ctx context.Context, proposalID uint64) ([]models.ProposalParameter, error) {
	var parameters []models.ProposalParameter
	err := r.db.WithContext(ctx).Where("proposal_id = ?", proposalID).Find(&parameters).Error
	return parameters, err
}

// CreateVote 创建投票记录
func (r *GovernanceRepository) CreateVote(ctx context.Context, vote *models.Vote) error {
	return r.db.WithContext(ctx).Create(vote).Error
}

// GetVote 获取投票记录
func (r *GovernanceRepository) GetVote(ctx context.Context, proposalID uint64, voter string) (*models.Vote, error) {
	var vote models.Vote
	err := r.db.WithContext(ctx).Where("proposal_id = ? AND voter = ?", proposalID, voter).First(&vote).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &vote, err
}

// GetProposalVotes 获取提案的所有投票
func (r *GovernanceRepository) GetProposalVotes(ctx context.Context, proposalID uint64, page, limit int) ([]models.Vote, int64, error) {
	var votes []models.Vote
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Vote{}).Where("proposal_id = ?", proposalID)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("timestamp DESC").Offset(offset).Limit(limit).Find(&votes).Error

	return votes, total, err
}

// GetUserVotes 获取用户投票历史
func (r *GovernanceRepository) GetUserVotes(ctx context.Context, voter string, page, limit int) ([]models.Vote, int64, error) {
	var votes []models.Vote
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Vote{}).Where("voter = ?", voter)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("timestamp DESC").Offset(offset).Limit(limit).Find(&votes).Error

	return votes, total, err
}

// CreateOrUpdateVotingPower 创建或更新投票权重
func (r *GovernanceRepository) CreateOrUpdateVotingPower(ctx context.Context, power *models.VotingPower) error {
	var existing models.VotingPower
	err := r.db.WithContext(ctx).Where("user_address = ?", power.UserAddress).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		power.LastUpdated = time.Now()
		return r.db.WithContext(ctx).Create(power).Error
	} else if err != nil {
		return fmt.Errorf("failed to query existing voting power: %w", err)
	}

	// 更新现有记录
	existing.StakedAmount = power.StakedAmount
	existing.StakingLevel = power.StakingLevel
	existing.LevelMultiplier = power.LevelMultiplier
	existing.DelegatedPower = power.DelegatedPower
	existing.TotalPower = power.TotalPower
	existing.LastUpdated = time.Now()

	return r.db.WithContext(ctx).Save(&existing).Error
}

// GetVotingPower 获取用户投票权重
func (r *GovernanceRepository) GetVotingPower(ctx context.Context, userAddress string) (*models.VotingPower, error) {
	var power models.VotingPower
	err := r.db.WithContext(ctx).Where("user_address = ?", userAddress).First(&power).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &power, err
}

// GetTopVotingPowers 获取投票权重排行榜
func (r *GovernanceRepository) GetTopVotingPowers(ctx context.Context, limit int) ([]models.VotingPower, error) {
	var powers []models.VotingPower
	err := r.db.WithContext(ctx).Order("total_power DESC").Limit(limit).Find(&powers).Error
	return powers, err
}

// CreateDelegation 创建委托记录
func (r *GovernanceRepository) CreateDelegation(ctx context.Context, delegation *models.Delegation) error {
	return r.db.WithContext(ctx).Create(delegation).Error
}

// GetUserDelegations 获取用户委托记录
func (r *GovernanceRepository) GetUserDelegations(ctx context.Context, userAddress string, isActive bool) ([]models.Delegation, error) {
	var delegations []models.Delegation
	query := r.db.WithContext(ctx).Where("delegator = ? OR delegate = ?", userAddress, userAddress)

	if isActive {
		query = query.Where("is_active = true")
	}

	err := query.Order("created_at DESC").Find(&delegations).Error
	return delegations, err
}

// EndDelegation 结束委托
func (r *GovernanceRepository) EndDelegation(ctx context.Context, delegationID uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.Delegation{}).
		Where("id = ?", delegationID).
		Updates(map[string]interface{}{
			"is_active": false,
			"end_time":  now,
		}).Error
}

// CreateOrUpdateGovernanceStats 创建或更新治理统计
func (r *GovernanceRepository) CreateOrUpdateGovernanceStats(ctx context.Context, stats *models.GovernanceStats) error {
	var existing models.GovernanceStats
	err := r.db.WithContext(ctx).Where("date = ?", stats.Date).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		return r.db.WithContext(ctx).Create(stats).Error
	} else if err != nil {
		return fmt.Errorf("failed to query existing governance stats: %w", err)
	}

	// 更新现有记录
	existing.TotalProposals = stats.TotalProposals
	existing.ActiveProposals = stats.ActiveProposals
	existing.ExecutedProposals = stats.ExecutedProposals
	existing.TotalVoters = stats.TotalVoters
	existing.ActiveVoters = stats.ActiveVoters
	existing.TotalVotingPower = stats.TotalVotingPower
	existing.ParticipationRate = stats.ParticipationRate
	existing.AverageQuorum = stats.AverageQuorum

	return r.db.WithContext(ctx).Save(&existing).Error
}

// GetLatestGovernanceStats 获取最新治理统计
func (r *GovernanceRepository) GetLatestGovernanceStats(ctx context.Context) (*models.GovernanceStats, error) {
	var stats models.GovernanceStats
	err := r.db.WithContext(ctx).Order("date DESC").First(&stats).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &stats, err
}

// GetGovernanceStatsHistory 获取治理统计历史
func (r *GovernanceRepository) GetGovernanceStatsHistory(ctx context.Context, days int) ([]models.GovernanceStats, error) {
	var stats []models.GovernanceStats
	startDate := time.Now().AddDate(0, 0, -days)

	err := r.db.WithContext(ctx).Where("date >= ?", startDate).
		Order("date ASC").Find(&stats).Error

	return stats, err
}

// CreateProposalParameter 创建提案参数记录
func (r *GovernanceRepository) CreateProposalParameter(ctx context.Context, param *models.ProposalParameter) error {
	return r.db.WithContext(ctx).Create(param).Error
}

// CreateTreasuryOperation 创建国库操作记录
func (r *GovernanceRepository) CreateTreasuryOperation(ctx context.Context, operation *models.TreasuryOperation) error {
	return r.db.WithContext(ctx).Create(operation).Error
}

// GetTreasuryOperations 获取国库操作历史
func (r *GovernanceRepository) GetTreasuryOperations(ctx context.Context, operationType, tokenType string, page, limit int) ([]models.TreasuryOperation, int64, error) {
	var operations []models.TreasuryOperation
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TreasuryOperation{})

	if operationType != "" {
		query = query.Where("operation_type = ?", operationType)
	}

	if tokenType != "" {
		query = query.Where("token_type = ?", tokenType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("executed_at DESC").Offset(offset).Limit(limit).Find(&operations).Error

	return operations, total, err
}

// GetProposalsByStatus 按状态获取提案数量
func (r *GovernanceRepository) GetProposalsByStatus(ctx context.Context) (map[string]int64, error) {
	var results []struct {
		Status string
		Count  int64
	}

	err := r.db.WithContext(ctx).Model(&models.Proposal{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	statusCounts := make(map[string]int64)
	for _, result := range results {
		statusCounts[result.Status] = result.Count
	}

	return statusCounts, nil
}
