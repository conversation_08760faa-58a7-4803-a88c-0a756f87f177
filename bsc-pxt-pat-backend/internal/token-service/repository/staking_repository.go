package repository

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/models"

	"gorm.io/gorm"
)

// StakingRepository 质押数据访问层
type StakingRepository struct {
	db *gorm.DB
}

// NewStakingRepository 创建新的质押数据访问层
func NewStakingRepository(db *gorm.DB) *StakingRepository {
	return &StakingRepository{
		db: db,
	}
}

// GetDB 获取数据库连接
func (r *StakingRepository) GetDB() *gorm.DB {
	return r.db
}

// CreateOrUpdateUserStake 创建或更新用户质押信息
func (r *StakingRepository) CreateOrUpdateUserStake(ctx context.Context, userAddress string, amount *big.Int, level models.StakingLevel, startTime time.Time) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	var userStake models.UserStake

	// 使用大小写不敏感的查询
	err := r.db.WithContext(ctx).Where("LOWER(user_address) = ?", userAddress).First(&userStake).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to query user stake: %w", err)
	}

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		userStake = models.UserStake{
			UserAddress:         userAddress, // 使用标准化的小写地址
			Amount:              models.NewBigInt(amount),
			StakingLevel:        level,
			StartTime:           startTime,
			LastUpdateTime:      time.Now(),
			IsActive:            true,
			TotalRewardsClaimed: models.NewBigInt(big.NewInt(0)),
			MiningPower:         models.NewBigInt(big.NewInt(0)),
			PendingRewards:      models.NewBigInt(big.NewInt(0)),
		}

		if err := r.db.WithContext(ctx).Create(&userStake).Error; err != nil {
			return fmt.Errorf("failed to create user stake: %w", err)
		}
	} else {
		// 更新现有记录
		updates := map[string]interface{}{
			"amount":           amount,
			"staking_level":    level,
			"last_update_time": time.Now(),
			"is_active":        true,
		}

		// 如果是第一次质押，更新开始时间
		if userStake.Amount.Int.Cmp(big.NewInt(0)) == 0 {
			updates["start_time"] = startTime
		}

		// 同时标准化地址格式
		updates["user_address"] = userAddress

		if err := r.db.WithContext(ctx).Model(&userStake).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update user stake: %w", err)
		}
	}

	return nil
}

// GetUserStake 获取用户质押信息
func (r *StakingRepository) GetUserStake(ctx context.Context, userAddress string) (*models.UserStake, error) {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	var userStake models.UserStake
	err := r.db.WithContext(ctx).Where("LOWER(user_address) = ?", userAddress).First(&userStake).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user stake: %w", err)
	}
	return &userStake, nil
}

// UpdateUserStakeAmount 更新用户质押金额
func (r *StakingRepository) UpdateUserStakeAmount(ctx context.Context, userAddress string, newAmount *big.Int) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	updates := map[string]interface{}{
		"amount":           newAmount,
		"last_update_time": time.Now(),
		"is_active":        newAmount.Cmp(big.NewInt(0)) > 0,
		"user_address":     userAddress, // 同时标准化地址格式
	}

	result := r.db.WithContext(ctx).Model(&models.UserStake{}).
		Where("LOWER(user_address) = ?", userAddress).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update user stake amount: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user stake not found: %s", userAddress)
	}

	return nil
}

// UpdateUserStakeLevel 更新用户质押等级
func (r *StakingRepository) UpdateUserStakeLevel(ctx context.Context, userAddress string, newLevel models.StakingLevel) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	updates := map[string]interface{}{
		"staking_level":    newLevel,
		"last_update_time": time.Now(),
		"user_address":     userAddress, // 同时标准化地址格式
	}

	result := r.db.WithContext(ctx).Model(&models.UserStake{}).
		Where("LOWER(user_address) = ?", userAddress).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update user stake level: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user stake not found: %s", userAddress)
	}

	return nil
}

// UpdateUserMiningPower 更新用户挖矿算力
func (r *StakingRepository) UpdateUserMiningPower(ctx context.Context, userAddress string, newPower *big.Int) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	updates := map[string]interface{}{
		"mining_power":     newPower,
		"last_update_time": time.Now(),
		"user_address":     userAddress, // 同时标准化地址格式
	}

	result := r.db.WithContext(ctx).Model(&models.UserStake{}).
		Where("LOWER(user_address) = ?", userAddress).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update user mining power: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user stake not found: %s", userAddress)
	}

	return nil
}

// AddUserRewardsClaimed 增加用户已领取奖励
func (r *StakingRepository) AddUserRewardsClaimed(ctx context.Context, userAddress string, rewardAmount *big.Int) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	// 使用原生SQL来进行原子性的加法操作
	sql := `
		UPDATE token_user_stakes
		SET total_rewards_claimed = total_rewards_claimed + ?,
		    last_reward_time = ?,
		    last_update_time = ?,
		    user_address = ?
		WHERE LOWER(user_address) = ?
	`

	result := r.db.WithContext(ctx).Exec(sql, rewardAmount.String(), time.Now(), time.Now(), userAddress, userAddress)
	if result.Error != nil {
		return fmt.Errorf("failed to add user rewards claimed: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user stake not found: %s", userAddress)
	}

	return nil
}

// GetLeaderboard 获取排行榜
func (r *StakingRepository) GetLeaderboard(ctx context.Context, sortBy string, offset, limit int) ([]*models.LeaderboardEntry, error) {
	var entries []*models.LeaderboardEntry

	// 构建排序字段
	orderBy := "amount DESC"
	switch sortBy {
	case "mining_power":
		orderBy = "mining_power DESC"
	case "level":
		orderBy = "staking_level DESC, amount DESC"
	case "rewards":
		orderBy = "total_rewards_claimed DESC"
	default:
		orderBy = "amount DESC"
	}

	// 使用窗口函数计算排名，并去重
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	sql := fmt.Sprintf(`
		SELECT
			user_address,
			amount,
			staking_level,
			mining_power,
			total_rewards_claimed,
			start_time,
			ROW_NUMBER() OVER (ORDER BY %s) as rank
		FROM (
			SELECT DISTINCT ON (LOWER(user_address))
				user_address,
				amount,
				staking_level,
				mining_power,
				total_rewards_claimed,
				start_time,
				last_update_time
			FROM token_user_stakes
			WHERE is_active = true AND LOWER(user_address) != LOWER('%s')
			ORDER BY LOWER(user_address), last_update_time DESC
		) unique_users
		ORDER BY %s
		LIMIT ? OFFSET ?
	`, orderBy, treasuryAddress, orderBy)

	err := r.db.WithContext(ctx).Raw(sql, limit, offset).Scan(&entries).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get leaderboard: %w", err)
	}

	return entries, nil
}

// GetUserRank 获取用户排名
func (r *StakingRepository) GetUserRank(ctx context.Context, userAddress string, rankType string) (int, error) {
	var rank int

	orderBy := "amount DESC"
	switch rankType {
	case "mining_power":
		orderBy = "mining_power DESC"
	case "level":
		orderBy = "staking_level DESC, amount DESC"
	case "rewards":
		orderBy = "total_rewards_claimed DESC"
	}

	sql := fmt.Sprintf(`
		SELECT rank FROM (
			SELECT
				user_address,
				ROW_NUMBER() OVER (ORDER BY %s) as rank
			FROM token_user_stakes
			WHERE is_active = true
		) ranked
		WHERE user_address = ?
	`, orderBy)

	err := r.db.WithContext(ctx).Raw(sql, userAddress).Scan(&rank).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, fmt.Errorf("failed to get user rank: %w", err)
	}

	return rank, nil
}

// GetStakingOverview 获取质押总览统计
func (r *StakingRepository) GetStakingOverview(ctx context.Context) (*models.StakingOverview, error) {
	// 使用临时结构体来接收查询结果
	var result struct {
		TotalUsers          int64     `db:"total_users"`
		ActiveUsers         int64     `db:"active_users"`
		TotalStaked         string    `db:"total_staked"`
		TotalRewardsClaimed string    `db:"total_rewards_claimed"`
		TotalMiningPower    string    `db:"total_mining_power"`
		AverageStakeAmount  string    `db:"average_stake_amount"`
		LastUpdateTime      time.Time `db:"last_update_time"`
	}

	// 排除国库地址
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	sql := `
		SELECT
			COUNT(*) as total_users,
			COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
			COALESCE(SUM(CASE WHEN is_active = true THEN amount ELSE 0 END), 0)::text as total_staked,
			COALESCE(SUM(COALESCE(total_rewards_claimed, 0)), 0)::text as total_rewards_claimed,
			COALESCE(SUM(CASE WHEN is_active = true THEN COALESCE(mining_power, 0) ELSE 0 END), 0)::text as total_mining_power,
			COALESCE(AVG(CASE WHEN is_active = true THEN amount END), 0)::text as average_stake_amount,
			COALESCE(MAX(updated_at), NOW()) as last_update_time
		FROM token_user_stakes
		WHERE user_address != $1
	`

	err := r.db.WithContext(ctx).Raw(sql, treasuryAddress).Scan(&result).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get staking overview: %w", err)
	}

	// 转换字符串为 big.Int（去除小数部分）
	totalStaked, ok := new(big.Int).SetString(strings.Split(strings.TrimSpace(result.TotalStaked), ".")[0], 10)
	if !ok {
		totalStaked = big.NewInt(0)
	}

	totalRewardsClaimed, ok := new(big.Int).SetString(strings.Split(strings.TrimSpace(result.TotalRewardsClaimed), ".")[0], 10)
	if !ok {
		totalRewardsClaimed = big.NewInt(0)
	}

	totalMiningPower, ok := new(big.Int).SetString(strings.Split(strings.TrimSpace(result.TotalMiningPower), ".")[0], 10)
	if !ok {
		totalMiningPower = big.NewInt(0)
	}

	averageStakeAmount, ok := new(big.Int).SetString(strings.Split(strings.TrimSpace(result.AverageStakeAmount), ".")[0], 10)
	if !ok {
		averageStakeAmount = big.NewInt(0)
	}

	// 构建最终结果
	overview := &models.StakingOverview{
		TotalStaked:        models.NewBigInt(totalStaked),
		TotalUsers:         result.TotalUsers,
		ActiveUsers:        result.ActiveUsers,
		TotalRewardsClaimd: models.NewBigInt(totalRewardsClaimed),
		TotalMiningPower:   models.NewBigInt(totalMiningPower),
		AverageStakeAmount: models.NewBigInt(averageStakeAmount),
		LastUpdateTime:     result.LastUpdateTime,
	}

	return overview, nil
}

// UpdateUserStakeFromContract 从合约数据更新用户质押信息
func (r *StakingRepository) UpdateUserStakeFromContract(ctx context.Context, userAddress string, stakingInfo *blockchain.UserStakingInfo, totalRewardsClaimed *big.Int) error {
	// 标准化地址为小写
	userAddress = strings.ToLower(userAddress)

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 转换时间戳
	startTime := time.Unix(stakingInfo.StartTime.Int64(), 0)
	currentTime := time.Now()

	// 使用大小写不敏感的查询查找或创建用户质押记录
	var userStake models.UserStake
	err := tx.Where("LOWER(user_address) = ?", userAddress).First(&userStake).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			userStake = models.UserStake{
				UserAddress:         userAddress, // 使用标准化的小写地址
				Amount:              models.NewBigInt(stakingInfo.Amount),
				StakingLevel:        models.StakingLevel(stakingInfo.Level),
				MiningPower:         models.NewBigInt(stakingInfo.Amount), // 使用质押金额作为挖矿算力
				TotalRewardsClaimed: models.NewBigInt(totalRewardsClaimed),
				StartTime:           startTime,
				LastUpdateTime:      currentTime,
				IsActive:            stakingInfo.Amount.Cmp(big.NewInt(0)) > 0,
			}

			if err := tx.Create(&userStake).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to create user stake: %w", err)
			}
		} else {
			tx.Rollback()
			return fmt.Errorf("failed to query user stake: %w", err)
		}
	} else {
		// 更新现有记录
		updates := map[string]interface{}{
			"amount":                stakingInfo.Amount.String(),
			"staking_level":         int(stakingInfo.Level),
			"mining_power":          stakingInfo.Amount.String(), // 使用质押金额作为挖矿算力
			"total_rewards_claimed": totalRewardsClaimed.String(),
			"last_update_time":      currentTime,
			"is_active":             stakingInfo.Amount.Cmp(big.NewInt(0)) > 0,
			"user_address":          userAddress, // 同时标准化地址格式
		}

		// 如果是第一次质押，更新开始时间
		if userStake.Amount.Int.Cmp(big.NewInt(0)) == 0 && stakingInfo.Amount.Cmp(big.NewInt(0)) > 0 {
			updates["start_time"] = startTime
		}

		if err := tx.Model(&userStake).Updates(updates).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update user stake: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetActiveUsers 获取活跃用户列表
func (r *StakingRepository) GetActiveUsers(ctx context.Context, duration time.Duration) ([]string, error) {
	var userAddresses []string

	// 获取最近指定时间内有更新的用户
	cutoffTime := time.Now().Add(-duration)

	err := r.db.WithContext(ctx).
		Model(&models.UserStake{}).
		Where("last_update_time > ? AND is_active = true", cutoffTime).
		Pluck("user_address", &userAddresses).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active users: %w", err)
	}

	return userAddresses, nil
}

// GetLevelDistribution 获取等级分布统计
func (r *StakingRepository) GetLevelDistribution(ctx context.Context) ([]*models.LevelDistribution, error) {
	// 使用临时结构体来接收查询结果
	var results []struct {
		Level       int     `db:"level"`
		Count       int64   `db:"count"`
		TotalAmount string  `db:"total_amount"`
		Percentage  float64 `db:"percentage"`
	}

	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	sql := `
		SELECT
			staking_level as level,
			COUNT(*) as count,
			SPLIT_PART(COALESCE(SUM(amount), 0)::text, '.', 1) as total_amount,
			ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
		FROM token_user_stakes
		WHERE is_active = true AND user_address != $1
		GROUP BY staking_level
		ORDER BY staking_level
	`

	err := r.db.WithContext(ctx).Raw(sql, treasuryAddress).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get level distribution: %w", err)
	}

	// 转换结果
	distributions := make([]*models.LevelDistribution, len(results))
	for i, result := range results {
		totalAmount, ok := new(big.Int).SetString(result.TotalAmount, 10)
		if !ok {
			// 如果解析失败，使用0作为默认值
			totalAmount = big.NewInt(0)
		}

		distributions[i] = &models.LevelDistribution{
			Level:       models.StakingLevel(result.Level),
			Count:       result.Count,
			TotalAmount: models.NewBigInt(totalAmount),
			Percentage:  result.Percentage,
		}
	}

	return distributions, nil
}

// GetActiveUsersCount 获取活跃用户数量
func (r *StakingRepository) GetActiveUsersCount(ctx context.Context) (int64, error) {
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	var count int64
	err := r.db.WithContext(ctx).Model(&models.UserStake{}).
		Where("is_active = ? AND user_address != ?", true, treasuryAddress).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get active users count: %w", err)
	}
	return count, nil
}

// GetTotalStakedAmount 获取总质押金额
func (r *StakingRepository) GetTotalStakedAmount(ctx context.Context) (*big.Int, error) {
	treasuryAddress := "0x4bb608e31ab7e78fbb02134b9609ff0f95e4ca99"

	var totalStr string
	err := r.db.WithContext(ctx).Model(&models.UserStake{}).
		Select("COALESCE(SUM(amount), 0)").
		Where("is_active = ? AND user_address != ?", true, treasuryAddress).
		Scan(&totalStr).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get total staked amount: %w", err)
	}

	total, ok := new(big.Int).SetString(totalStr, 10)
	if !ok {
		return big.NewInt(0), nil
	}

	return total, nil
}

// GetUserRanking 获取用户排名
func (r *StakingRepository) GetUserRanking(ctx context.Context, userAddress string) (*models.UserRanking, error) {
	var ranking models.UserRanking

	// 获取用户质押排名
	rank, err := r.GetUserRank(ctx, userAddress, "amount")
	if err != nil {
		return nil, err
	}

	ranking.UserAddress = userAddress
	ranking.Rank = int64(rank)

	return &ranking, nil
}
