package repository

import (
	"context"
	"fmt"
	"math/big"
	"time"

	"pxpat-backend/internal/token-service/models"

	"gorm.io/gorm"
)

// EventRepository 事件数据访问层
type EventRepository struct {
	db *gorm.DB
}

// NewEventRepository 创建新的事件数据访问层
func NewEventRepository(db *gorm.DB) *EventRepository {
	return &EventRepository{
		db: db,
	}
}

// CreateStakingEvent 创建质押事件记录
func (r *EventRepository) CreateStakingEvent(ctx context.Context, event *models.StakingEvent) error {
	// 检查事件是否已存在（防止重复处理）
	var existingEvent models.StakingEvent
	err := r.db.WithContext(ctx).
		Where("transaction_hash = ? AND log_index = ?", event.TransactionHash, event.LogIndex).
		First(&existingEvent).Error

	if err == nil {
		// 事件已存在，跳过
		return nil
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing event: %w", err)
	}

	// 创建新事件记录
	if err := r.db.WithContext(ctx).Create(event).Error; err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	return nil
}

// GetUserStakingEvents 获取用户质押事件历史
func (r *EventRepository) GetUserStakingEvents(ctx context.Context, userAddress string, eventType models.EventType, offset, limit int) ([]*models.StakingEvent, error) {
	var events []*models.StakingEvent

	query := r.db.WithContext(ctx).Where("user_address = ?", userAddress)

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	err := query.Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user staking events: %w", err)
	}

	return events, nil
}

// GetEventsByBlockRange 获取指定区块范围内的事件
func (r *EventRepository) GetEventsByBlockRange(ctx context.Context, fromBlock, toBlock uint64) ([]*models.StakingEvent, error) {
	var events []*models.StakingEvent

	err := r.db.WithContext(ctx).
		Where("block_number >= ? AND block_number <= ?", fromBlock, toBlock).
		Order("block_number ASC, log_index ASC").
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get events by block range: %w", err)
	}

	return events, nil
}

// GetLatestEventByUser 获取用户最新的事件
func (r *EventRepository) GetLatestEventByUser(ctx context.Context, userAddress string, eventType models.EventType) (*models.StakingEvent, error) {
	var event models.StakingEvent

	query := r.db.WithContext(ctx).Where("user_address = ?", userAddress)

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	err := query.Order("timestamp DESC").First(&event).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get latest event by user: %w", err)
	}

	return &event, nil
}

// GetEventStatistics 获取事件统计信息
func (r *EventRepository) GetEventStatistics(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error) {
	var stats []struct {
		EventType   string `json:"event_type"`
		Count       int64  `json:"count"`
		TotalAmount string `json:"total_amount"`
	}

	sql := `
		SELECT 
			event_type,
			COUNT(*) as count,
			COALESCE(SUM(amount), 0) as total_amount
		FROM staking_events 
		WHERE timestamp >= ? AND timestamp <= ?
		GROUP BY event_type
		ORDER BY event_type
	`

	err := r.db.WithContext(ctx).Raw(sql, startTime, endTime).Scan(&stats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get event statistics: %w", err)
	}

	result := make(map[string]interface{})
	for _, stat := range stats {
		result[string(stat.EventType)] = map[string]interface{}{
			"count":        stat.Count,
			"total_amount": stat.TotalAmount,
		}
	}

	return result, nil
}

// GetDailyEventCounts 获取每日事件统计
func (r *EventRepository) GetDailyEventCounts(ctx context.Context, days int) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	sql := `
		SELECT 
			DATE(timestamp) as date,
			event_type,
			COUNT(*) as count
		FROM staking_events 
		WHERE timestamp >= NOW() - INTERVAL ? DAY
		GROUP BY DATE(timestamp), event_type
		ORDER BY date DESC, event_type
	`

	err := r.db.WithContext(ctx).Raw(sql, days).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get daily event counts: %w", err)
	}

	return results, nil
}

// GetBlockSyncStatus 获取区块同步状态
func (r *EventRepository) GetBlockSyncStatus(ctx context.Context) (*models.BlockSyncStatus, error) {
	var status models.BlockSyncStatus

	err := r.db.WithContext(ctx).Order("id DESC").First(&status).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get block sync status: %w", err)
	}

	return &status, nil
}

// UpdateBlockSyncStatus 更新区块同步状态
func (r *EventRepository) UpdateBlockSyncStatus(ctx context.Context, lastBlock uint64, status string, errorMsg string) error {
	// 获取现有状态记录
	var syncStatus models.BlockSyncStatus
	err := r.db.WithContext(ctx).Order("id DESC").First(&syncStatus).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		syncStatus = models.BlockSyncStatus{
			LastSyncedBlock: lastBlock,
			LastSyncedTime:  time.Now(),
			SyncStatus:      status,
			ErrorMessage:    errorMsg,
		}

		if err := r.db.WithContext(ctx).Create(&syncStatus).Error; err != nil {
			return fmt.Errorf("failed to create block sync status: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("failed to get block sync status: %w", err)
	} else {
		// 更新现有记录
		updates := map[string]interface{}{
			"last_synced_block": lastBlock,
			"last_synced_time":  time.Now(),
			"sync_status":       status,
			"error_message":     errorMsg,
		}

		if err := r.db.WithContext(ctx).Model(&syncStatus).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update block sync status: %w", err)
		}
	}

	return nil
}

// GetLastProcessedBlock 获取最后处理的区块号
func (r *EventRepository) GetLastProcessedBlock(ctx context.Context) (uint64, error) {
	status, err := r.GetBlockSyncStatus(ctx)
	if err != nil {
		return 0, err
	}

	if status == nil {
		return 0, nil
	}

	return status.LastSyncedBlock, nil
}

// CleanupOldEvents 清理旧事件记录
func (r *EventRepository) CleanupOldEvents(ctx context.Context, beforeTime time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("timestamp < ?", beforeTime).
		Delete(&models.StakingEvent{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old events: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// GetEventCountByUser 获取用户事件数量
func (r *EventRepository) GetEventCountByUser(ctx context.Context, userAddress string, eventType models.EventType) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(&models.StakingEvent{}).
		Where("user_address = ?", userAddress)

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get event count by user: %w", err)
	}

	return count, nil
}

// GetRecentEvents 获取最近的事件
func (r *EventRepository) GetRecentEvents(ctx context.Context, limit int) ([]*models.StakingEvent, error) {
	var events []*models.StakingEvent

	err := r.db.WithContext(ctx).
		Order("timestamp DESC").
		Limit(limit).
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get recent events: %w", err)
	}

	return events, nil
}

// GetUserTotalRewards 获取用户总奖励金额
func (r *EventRepository) GetUserTotalRewards(ctx context.Context, userAddress string) (*big.Int, error) {
	var totalStr string

	err := r.db.WithContext(ctx).Model(&models.StakingEvent{}).
		Select("COALESCE(SUM(amount), 0)").
		Where("user_address = ? AND event_type = ?", userAddress, models.EventTypeRewardClaimed).
		Scan(&totalStr).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user total rewards: %w", err)
	}

	total, ok := new(big.Int).SetString(totalStr, 10)
	if !ok {
		return big.NewInt(0), nil
	}

	return total, nil
}
