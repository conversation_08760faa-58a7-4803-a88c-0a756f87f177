package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"pxpat-backend/internal/token-service/models"
)

// TokenRepository 代币数据访问层
type TokenRepository struct {
	db *gorm.DB
}

// NewTokenRepository 创建新的代币仓库
func NewTokenRepository(db *gorm.DB) *TokenRepository {
	return &TokenRepository{
		db: db,
	}
}

// GetDB 获取数据库连接
func (r *TokenRepository) GetDB() *gorm.DB {
	return r.db
}

// CreateOrUpdateBalance 创建或更新代币余额
func (r *TokenRepository) CreateOrUpdateBalance(ctx context.Context, balance *models.TokenBalance) error {
	// 标准化地址为小写
	balance.UserAddress = strings.ToLower(balance.UserAddress)

	var existing models.TokenBalance
	err := r.db.WithContext(ctx).Where("LOWER(user_address) = ? AND token_type = ?",
		balance.UserAddress, balance.TokenType).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		balance.LastUpdated = time.Now()
		return r.db.WithContext(ctx).Create(balance).Error
	} else if err != nil {
		return fmt.Errorf("failed to query existing balance: %w", err)
	}

	// 更新现有记录
	existing.Balance = balance.Balance
	existing.LockedAmount = balance.LockedAmount
	existing.LastUpdated = time.Now()
	existing.UserAddress = balance.UserAddress // 同时标准化地址格式

	return r.db.WithContext(ctx).Save(&existing).Error
}

// GetUserBalance 获取用户代币余额
func (r *TokenRepository) GetUserBalance(ctx context.Context, userAddress, tokenType string) (*models.TokenBalance, error) {
	var balance models.TokenBalance
	err := r.db.WithContext(ctx).Where("user_address = ? AND token_type = ?",
		userAddress, tokenType).First(&balance).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &balance, err
}

// GetUserBalances 获取用户所有代币余额
func (r *TokenRepository) GetUserBalances(ctx context.Context, userAddress string) ([]models.TokenBalance, error) {
	var balances []models.TokenBalance
	err := r.db.WithContext(ctx).Where("user_address = ?", userAddress).Find(&balances).Error
	return balances, err
}

// CreateTransfer 创建转账记录
func (r *TokenRepository) CreateTransfer(ctx context.Context, transfer *models.TokenTransfer) error {
	return r.db.WithContext(ctx).Create(transfer).Error
}

// GetUserTransfers 获取用户转账历史
func (r *TokenRepository) GetUserTransfers(ctx context.Context, userAddress, tokenType string, page, limit int) ([]models.TokenTransfer, int64, error) {
	var transfers []models.TokenTransfer
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenTransfer{}).
		Where("(from_address = ? OR to_address = ?)", userAddress, userAddress)

	if tokenType != "" {
		query = query.Where("token_type = ?", tokenType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("timestamp DESC").Offset(offset).Limit(limit).Find(&transfers).Error

	return transfers, total, err
}

// CreateOrUpdateSupply 创建或更新代币供应量
func (r *TokenRepository) CreateOrUpdateSupply(ctx context.Context, supply *models.TokenSupply) error {
	var existing models.TokenSupply
	err := r.db.WithContext(ctx).Where("token_type = ?", supply.TokenType).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		supply.LastUpdated = time.Now()
		return r.db.WithContext(ctx).Create(supply).Error
	} else if err != nil {
		return fmt.Errorf("failed to query existing supply: %w", err)
	}

	// 更新现有记录
	existing.TotalSupply = supply.TotalSupply
	existing.CirculatingSupply = supply.CirculatingSupply
	existing.LockedSupply = supply.LockedSupply
	existing.BurnedSupply = supply.BurnedSupply
	existing.LastUpdated = time.Now()

	return r.db.WithContext(ctx).Save(&existing).Error
}

// GetTokenSupply 获取代币供应量信息
func (r *TokenRepository) GetTokenSupply(ctx context.Context, tokenType string) (*models.TokenSupply, error) {
	var supply models.TokenSupply
	err := r.db.WithContext(ctx).Where("token_type = ?", tokenType).First(&supply).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &supply, err
}

// UpdateHolderRanking 更新持币者排名
func (r *TokenRepository) UpdateHolderRanking(ctx context.Context, tokenType string, holders []models.TokenHolder) error {
	// 开启事务
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除旧的排名数据
		err := tx.Where("token_type = ?", tokenType).Delete(&models.TokenHolder{}).Error
		if err != nil {
			return fmt.Errorf("failed to delete old holder rankings: %w", err)
		}

		// 批量插入新的排名数据
		if len(holders) > 0 {
			err = tx.CreateInBatches(holders, 100).Error
			if err != nil {
				return fmt.Errorf("failed to create holder rankings: %w", err)
			}
		}

		return nil
	})
}

// GetTopHolders 获取代币持有者排行榜
func (r *TokenRepository) GetTopHolders(ctx context.Context, tokenType string, limit int) ([]models.TokenHolder, error) {
	var holders []models.TokenHolder
	err := r.db.WithContext(ctx).Where("token_type = ?", tokenType).
		Order("rank ASC").Limit(limit).Find(&holders).Error
	return holders, err
}

// GetTokenMetricsHistory 获取代币指标历史
func (r *TokenRepository) GetTokenMetricsHistory(ctx context.Context, tokenType string, days int) ([]models.TokenMetrics, error) {
	var metrics []models.TokenMetrics

	// 计算开始日期
	startDate := time.Now().AddDate(0, 0, -days)

	err := r.db.WithContext(ctx).
		Where("token_type = ? AND date >= ?", tokenType, startDate).
		Order("date ASC").
		Find(&metrics).Error

	if err != nil {
		return nil, err
	}

	return metrics, nil
}

// CreateTokenMetrics 创建代币经济指标
func (r *TokenRepository) CreateTokenMetrics(ctx context.Context, metrics *models.TokenMetrics) error {
	return r.db.WithContext(ctx).Create(metrics).Error
}

// GetLatestTokenMetrics 获取最新的代币经济指标
func (r *TokenRepository) GetLatestTokenMetrics(ctx context.Context, tokenType string) (*models.TokenMetrics, error) {
	var metrics models.TokenMetrics
	err := r.db.WithContext(ctx).Where("token_type = ?", tokenType).
		Order("date DESC").First(&metrics).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &metrics, err
}

// CreateTokenLock 创建代币锁定记录
func (r *TokenRepository) CreateTokenLock(ctx context.Context, lock *models.TokenLock) error {
	return r.db.WithContext(ctx).Create(lock).Error
}

// GetUserTokenLocks 获取用户代币锁定记录
func (r *TokenRepository) GetUserTokenLocks(ctx context.Context, userAddress, tokenType string) ([]models.TokenLock, error) {
	var locks []models.TokenLock
	query := r.db.WithContext(ctx).Where("user_address = ?", userAddress)

	if tokenType != "" {
		query = query.Where("token_type = ?", tokenType)
	}

	err := query.Order("created_at DESC").Find(&locks).Error
	return locks, err
}

// UnlockTokens 解锁代币
func (r *TokenRepository) UnlockTokens(ctx context.Context, lockID uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.TokenLock{}).
		Where("id = ? AND end_time <= ? AND is_unlocked = false", lockID, now).
		Updates(map[string]interface{}{
			"is_unlocked": true,
			"unlock_time": now,
		}).Error
}

// CreateTokenBurn 创建代币销毁记录
func (r *TokenRepository) CreateTokenBurn(ctx context.Context, burn *models.TokenBurn) error {
	return r.db.WithContext(ctx).Create(burn).Error
}

// GetTokenBurnHistory 获取代币销毁历史
func (r *TokenRepository) GetTokenBurnHistory(ctx context.Context, tokenType string, page, limit int) ([]models.TokenBurn, int64, error) {
	var burns []models.TokenBurn
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenBurn{})
	if tokenType != "" {
		query = query.Where("token_type = ?", tokenType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("timestamp DESC").Offset(offset).Limit(limit).Find(&burns).Error

	return burns, total, err
}

// GetTokenBalances 获取代币余额列表（用于排名计算）
func (r *TokenRepository) GetTokenBalances(ctx context.Context, tokenType string) ([]models.TokenBalance, error) {
	var balances []models.TokenBalance
	err := r.db.WithContext(ctx).
		Where("token_type = ? AND balance != '0'", tokenType).
		Order("CAST(balance AS DECIMAL(78,18)) DESC").
		Find(&balances).Error
	return balances, err
}

// GetAllTransfers 获取所有转账记录
func (r *TokenRepository) GetAllTransfers(ctx context.Context) ([]models.TokenTransfer, error) {
	var transfers []models.TokenTransfer
	err := r.db.WithContext(ctx).Find(&transfers).Error
	return transfers, err
}

// GetRecentTransfers 获取最近的转账记录（分页）
func (r *TokenRepository) GetRecentTransfers(ctx context.Context, tokenType string, page, limit int) ([]models.TokenTransfer, int64, error) {
	var transfers []models.TokenTransfer
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TokenTransfer{})

	if tokenType != "" {
		query = query.Where("token_type = ?", tokenType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * limit
	err = query.Order("timestamp DESC").Offset(offset).Limit(limit).Find(&transfers).Error

	return transfers, total, err
}

// GetAllUserStakes 获取所有用户质押记录
func (r *TokenRepository) GetAllUserStakes(ctx context.Context) ([]models.UserStake, error) {
	var stakes []models.UserStake
	err := r.db.WithContext(ctx).Find(&stakes).Error
	return stakes, err
}

// CreateOrUpdateUserStake 创建或更新用户质押记录
func (r *TokenRepository) CreateOrUpdateUserStake(ctx context.Context, userStake *models.UserStake) error {
	// 标准化地址为小写
	userStake.UserAddress = strings.ToLower(userStake.UserAddress)

	// 查找现有记录（使用大小写不敏感的查询）
	var existing models.UserStake
	err := r.db.WithContext(ctx).Where("LOWER(user_address) = ?", userStake.UserAddress).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		userStake.LastUpdateTime = time.Now()
		return r.db.WithContext(ctx).Create(userStake).Error
	} else if err != nil {
		return fmt.Errorf("failed to query existing user stake: %w", err)
	}

	// 更新现有记录
	userStake.ID = existing.ID // 保持原有ID
	userStake.LastUpdateTime = time.Now()
	return r.db.WithContext(ctx).Save(userStake).Error
}

// GetTotalBurnedAmount 获取总销毁数量
func (r *TokenRepository) GetTotalBurnedAmount(ctx context.Context, tokenType string) (string, error) {
	var result struct {
		Total string
	}

	err := r.db.WithContext(ctx).Model(&models.TokenBurn{}).
		Select("COALESCE(SUM(amount), '0') as total").
		Where("token_type = ?", tokenType).
		Scan(&result).Error

	return result.Total, err
}

// ===== 池子相关方法 =====

// CreatePoolActivity 创建池子活动记录
func (r *TokenRepository) CreatePoolActivity(ctx context.Context, activity *models.PoolActivity) error {
	return r.db.WithContext(ctx).Create(activity).Error
}

// CreateOrUpdatePoolBalance 创建或更新池子余额
func (r *TokenRepository) CreateOrUpdatePoolBalance(ctx context.Context, balance *models.PoolBalance) error {
	return r.db.WithContext(ctx).
		Where("pool_address = ? AND token_type = ?", balance.PoolAddress, balance.TokenType).
		Assign(balance).
		FirstOrCreate(balance).Error
}

// GetPoolActivities 获取池子活动记录
func (r *TokenRepository) GetPoolActivities(ctx context.Context, poolAddress string, limit, offset int) ([]models.PoolActivity, int64, error) {
	var activities []models.PoolActivity
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&models.PoolActivity{}).
		Where("pool_address = ?", poolAddress).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := r.db.WithContext(ctx).
		Where("pool_address = ?", poolAddress).
		Order("timestamp DESC").
		Limit(limit).Offset(offset).
		Find(&activities).Error; err != nil {
		return nil, 0, err
	}

	return activities, total, nil
}

// GetPoolBalance 获取池子余额
func (r *TokenRepository) GetPoolBalance(ctx context.Context, poolAddress, tokenType string) (*models.PoolBalance, error) {
	var balance models.PoolBalance
	err := r.db.WithContext(ctx).
		Where("pool_address = ? AND token_type = ?", poolAddress, tokenType).
		First(&balance).Error
	if err != nil {
		return nil, err
	}
	return &balance, nil
}

// GetAllPoolBalances 获取所有池子余额
func (r *TokenRepository) GetAllPoolBalances(ctx context.Context) ([]models.PoolBalance, error) {
	var balances []models.PoolBalance
	err := r.db.WithContext(ctx).Find(&balances).Error
	return balances, err
}

// UpdatePoolStats 更新池子统计
func (r *TokenRepository) UpdatePoolStats(ctx context.Context, stats *models.PoolStats) error {
	return r.db.WithContext(ctx).
		Where("pool_address = ?", stats.PoolAddress).
		Assign(stats).
		FirstOrCreate(stats).Error
}
