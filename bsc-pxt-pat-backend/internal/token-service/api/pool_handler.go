package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/service"
)

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code      int       `json:"code"`
	Error     string    `json:"error"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Code      int       `json:"code"`
	Message   string    `json:"message"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, error, message string) ErrorResponse {
	return ErrorResponse{
		Code:      code,
		Error:     error,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data any) SuccessResponse {
	return SuccessResponse{
		Code:      200,
		Message:   "success",
		Data:      data,
		Timestamp: time.Now(),
	}
}

// PoolHandler 池子API处理器
type PoolHandler struct {
	poolService *service.PoolService
	logger      zerolog.Logger
}

// NewPoolHandler 创建池子处理器
func NewPoolHandler(poolService *service.PoolService, logger zerolog.Logger) *PoolHandler {
	return &PoolHandler{
		poolService: poolService,
		logger:      logger,
	}
}

// GetPoolInfo 获取池子信息
// @Summary 获取池子信息
// @Description 获取指定池子的详细信息，包括余额、活动统计等
// @Tags 池子管理
// @Accept json
// @Produce json
// @Param address path string true "池子地址"
// @Success 200 {object} service.PoolInfo "池子信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "池子不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pools/{address} [get]
func (h *PoolHandler) GetPoolInfo(c *gin.Context) {
	address := c.Param("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_parameter",
			Message: "池子地址不能为空",
		})
		return
	}

	poolInfo, err := h.poolService.GetPoolInfo(c.Request.Context(), address)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get pool info")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "获取池子信息失败",
		})
		return
	}

	if poolInfo == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "pool_not_found",
			Message: "池子不存在",
		})
		return
	}

	c.JSON(http.StatusOK, poolInfo)
}

// GetAllPools 获取所有池子信息
// @Summary 获取所有池子信息
// @Description 获取系统中所有池子的信息列表
// @Tags 池子管理
// @Accept json
// @Produce json
// @Success 200 {array} service.PoolInfo "池子信息列表"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pools [get]
func (h *PoolHandler) GetAllPools(c *gin.Context) {
	pools, err := h.poolService.GetAllPoolsInfo(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get all pools info")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "获取池子信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pools": pools,
		"total": len(pools),
	})
}

// GetPoolTransfers 获取池子转账记录
// @Summary 获取池子转账记录
// @Description 获取指定池子的转账历史记录
// @Tags 池子管理
// @Accept json
// @Produce json
// @Param address path string true "池子地址"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} object{transfers=[]service.PoolTransfer,total=int64,page=int,limit=int} "转账记录"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pools/{address}/transfers [get]
func (h *PoolHandler) GetPoolTransfers(c *gin.Context) {
	address := c.Param("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_parameter",
			Message: "池子地址不能为空",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	transfers, total, err := h.poolService.GetPoolTransfers(c.Request.Context(), address, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get pool transfers")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "获取转账记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transfers": transfers,
		"total":     total,
		"page":      page,
		"limit":     limit,
	})
}

// GetPoolStats 获取池子统计信息
// @Summary 获取池子统计信息
// @Description 获取所有池子的统计信息，包括总余额、转账次数等
// @Tags 池子管理
// @Accept json
// @Produce json
// @Success 200 {object} service.PoolStats "池子统计信息"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pools/stats [get]
func (h *PoolHandler) GetPoolStats(c *gin.Context) {
	stats, err := h.poolService.GetPoolStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get pool stats")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "获取池子统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetPoolActivities 获取池子活动记录
// @Summary 获取池子活动记录
// @Description 获取指定池子的活动记录，包括转账、奖励分发等
// @Tags 池子管理
// @Accept json
// @Produce json
// @Param address path string true "池子地址"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param purpose query string false "活动类型过滤"
// @Success 200 {object} object{activities=[]models.PoolActivity,total=int64,page=int,limit=int} "活动记录"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pools/{address}/activities [get]
func (h *PoolHandler) GetPoolActivities(c *gin.Context) {
	address := c.Param("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_parameter",
			Message: "池子地址不能为空",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	purpose := c.Query("purpose")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 这里需要扩展 PoolService 来支持按用途过滤
	// 当前先使用基本的获取方法
	transfers, total, err := h.poolService.GetPoolTransfers(c.Request.Context(), address, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("address", address).Msg("Failed to get pool activities")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "获取活动记录失败",
		})
		return
	}

	// 如果有用途过滤，在这里进行过滤
	if purpose != "" {
		var filteredTransfers []*service.PoolTransfer
		for _, transfer := range transfers {
			if transfer.Purpose == purpose {
				filteredTransfers = append(filteredTransfers, transfer)
			}
		}
		transfers = filteredTransfers
		total = int64(len(filteredTransfers))
	}

	c.JSON(http.StatusOK, gin.H{
		"activities": transfers,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"purpose":    purpose,
	})
}

// RegisterPoolRoutes 注册池子相关路由
func RegisterPoolRoutes(r *gin.RouterGroup, handler *PoolHandler) {
	pools := r.Group("/pools")
	{
		pools.GET("", handler.GetAllPools)                           // 获取所有池子信息
		pools.GET("/stats", handler.GetPoolStats)                    // 获取池子统计信息
		pools.GET("/:address", handler.GetPoolInfo)                  // 获取指定池子信息
		pools.GET("/:address/transfers", handler.GetPoolTransfers)   // 获取池子转账记录
		pools.GET("/:address/activities", handler.GetPoolActivities) // 获取池子活动记录
	}
}
