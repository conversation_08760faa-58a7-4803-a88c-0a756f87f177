package external

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/rs/zerolog"
)

// BSCScanClient BSCScan API客户端
type BSCScanClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     zerolog.Logger
}

// NewBSCScanClient 创建新的BSCScan客户端
func NewBSCScanClient(apiKey string, logger zerolog.Logger) *BSCScanClient {
	if apiKey == "" {
		logger.Warn().Msg("BSCScan API key is empty, API calls may be rate limited")
	}

	return &BSCScanClient{
		apiKey: apiKey,
		// baseURL: "https://api.bscscan.com/api", // 主网
		baseURL: "https://api-testnet.bscscan.com/api", // 测试网
		httpClient: &http.Client{
			Timeout: 60 * time.Second, // 增加超时时间
		},
		logger: logger.With().Str("component", "bscscan_client").Logger(),
	}
}

// LogEntry BSCScan日志条目
type LogEntry struct {
	Address          string   `json:"address"`
	Topics           []string `json:"topics"`
	Data             string   `json:"data"`
	BlockNumber      string   `json:"blockNumber"`
	TimeStamp        string   `json:"timeStamp"`
	GasPrice         string   `json:"gasPrice"`
	GasUsed          string   `json:"gasUsed"`
	LogIndex         string   `json:"logIndex"`
	TransactionHash  string   `json:"transactionHash"`
	TransactionIndex string   `json:"transactionIndex"`
}

// BSCScanResponse BSCScan API响应
type BSCScanResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Result  interface{} `json:"result"` // 可能是数组或字符串
}

// GetContractLogs 获取合约日志
func (c *BSCScanClient) GetContractLogs(ctx context.Context, params LogQueryParams) ([]LogEntry, error) {
	// 添加API调用延迟，避免频率限制 - 增加到1秒确保不超过2次/秒
	time.Sleep(1 * time.Second)
	// 构建查询参数
	queryParams := url.Values{}
	queryParams.Set("module", "logs")
	queryParams.Set("action", "getLogs")
	queryParams.Set("address", params.Address)
	// 使用十六进制格式，这样更符合BSCScan API的预期
	queryParams.Set("fromBlock", fmt.Sprintf("0x%x", params.FromBlock))
	queryParams.Set("toBlock", fmt.Sprintf("0x%x", params.ToBlock))
	queryParams.Set("apikey", c.apiKey)

	// 添加主题过滤器
	if len(params.Topics) > 0 {
		for i, topic := range params.Topics {
			if topic != "" {
				queryParams.Set(fmt.Sprintf("topic%d", i), topic)
			}
		}
	}

	// 构建完整URL
	fullURL := fmt.Sprintf("%s?%s", c.baseURL, queryParams.Encode())

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 解析响应
	var bscResponse BSCScanResponse
	if err := json.Unmarshal(body, &bscResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 检查API响应状态
	if bscResponse.Status != "1" {
		if bscResponse.Message == "No records found" || bscResponse.Message == "No transactions found" {
			return []LogEntry{}, nil // 没有记录是正常情况
		}

		// 检查是否是API限制错误或其他临时错误
		if isRateLimitError(bscResponse.Message) || bscResponse.Status == "0" {
			c.logger.Debug(). // 降低日志级别，减少噪音
						Str("message", bscResponse.Message).
						Uint64("from_block", params.FromBlock).
						Uint64("to_block", params.ToBlock).
						Msg("BSCScan API temporary error, will retry later")
			// 返回空结果而不是错误，让调用者稍后重试
			return []LogEntry{}, nil
		}

		// 提供更详细的错误信息
		errorMsg := bscResponse.Message
		if errorMsg == "" {
			errorMsg = "Unknown API error"
		}
		c.logger.Error().
			Str("status", bscResponse.Status).
			Str("message", bscResponse.Message).
			Str("url", fullURL).
			Msg("BSCScan API error")
		return nil, fmt.Errorf("BSCScan API error: %s (status: %s)", errorMsg, bscResponse.Status)
	}

	// 解析 result 字段
	var logs []LogEntry
	switch result := bscResponse.Result.(type) {
	case []interface{}:
		// result 是数组，转换为 LogEntry
		for _, item := range result {
			if logMap, ok := item.(map[string]interface{}); ok {
				log := LogEntry{}
				if addr, ok := logMap["address"].(string); ok {
					log.Address = addr
				}
				if topics, ok := logMap["topics"].([]interface{}); ok {
					for _, topic := range topics {
						if topicStr, ok := topic.(string); ok {
							log.Topics = append(log.Topics, topicStr)
						}
					}
				}
				if data, ok := logMap["data"].(string); ok {
					log.Data = data
				}
				if blockNumber, ok := logMap["blockNumber"].(string); ok {
					log.BlockNumber = blockNumber
				}
				if timeStamp, ok := logMap["timeStamp"].(string); ok {
					log.TimeStamp = timeStamp
				}
				if gasPrice, ok := logMap["gasPrice"].(string); ok {
					log.GasPrice = gasPrice
				}
				if gasUsed, ok := logMap["gasUsed"].(string); ok {
					log.GasUsed = gasUsed
				}
				if logIndex, ok := logMap["logIndex"].(string); ok {
					log.LogIndex = logIndex
				}
				if txHash, ok := logMap["transactionHash"].(string); ok {
					log.TransactionHash = txHash
				}
				if txIndex, ok := logMap["transactionIndex"].(string); ok {
					log.TransactionIndex = txIndex
				}
				logs = append(logs, log)
			}
		}
	case string:
		// result 是字符串，通常是错误信息
		c.logger.Warn().
			Str("result_string", result).
			Uint64("from_block", params.FromBlock).
			Uint64("to_block", params.ToBlock).
			Msg("BSCScan API returned string result")
		return []LogEntry{}, nil
	default:
		c.logger.Warn().
			Str("result_type", fmt.Sprintf("%T", result)).
			Uint64("from_block", params.FromBlock).
			Uint64("to_block", params.ToBlock).
			Msg("BSCScan API returned unexpected result type")
		return []LogEntry{}, nil
	}

	// 过滤结果，确保只返回指定区块范围内的事件
	filteredLogs := make([]LogEntry, 0)
	for _, log := range logs {
		// 解析区块号
		if log.BlockNumber == "" {
			continue
		}

		blockNumberHex := strings.TrimPrefix(log.BlockNumber, "0x")
		if blockNumberHex == "" {
			continue
		}

		blockNumber, err := strconv.ParseUint(blockNumberHex, 16, 64)
		if err != nil {
			c.logger.Warn().
				Str("block_number_hex", log.BlockNumber).
				Err(err).
				Msg("Failed to parse block number in log entry")
			continue
		}

		// 只包含指定区块范围内的事件
		if blockNumber >= params.FromBlock && blockNumber <= params.ToBlock {
			filteredLogs = append(filteredLogs, log)
		}
	}

	c.logger.Debug().
		Str("address", params.Address).
		Uint64("from_block", params.FromBlock).
		Uint64("to_block", params.ToBlock).
		Int("total_logs", len(logs)).
		Int("filtered_logs", len(filteredLogs)).
		Msg("Retrieved and filtered logs from BSCScan")

	return filteredLogs, nil
}

// LogQueryParams 日志查询参数
type LogQueryParams struct {
	Address   string   // 合约地址
	FromBlock uint64   // 起始区块
	ToBlock   uint64   // 结束区块
	Topics    []string // 事件主题过滤器
}

// GetTokenTransfers 获取代币转账记录
func (c *BSCScanClient) GetTokenTransfers(ctx context.Context, contractAddress string, fromBlock, toBlock uint64) ([]LogEntry, error) {
	// Transfer事件的主题：keccak256("Transfer(address,address,uint256)")
	transferTopic := "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

	params := LogQueryParams{
		Address:   contractAddress,
		FromBlock: fromBlock,
		ToBlock:   toBlock,
		Topics:    []string{transferTopic},
	}

	return c.GetContractLogs(ctx, params)
}

// GetStakingEvents 获取质押相关事件
func (c *BSCScanClient) GetStakingEvents(ctx context.Context, contractAddress string, fromBlock, toBlock uint64) ([]LogEntry, error) {
	// 这里可以添加质押相关的事件主题
	// 例如：Staked, Withdrawn, RewardClaimed等事件的主题

	params := LogQueryParams{
		Address:   contractAddress,
		FromBlock: fromBlock,
		ToBlock:   toBlock,
		Topics:    []string{}, // 获取所有事件，后续在处理器中过滤
	}

	return c.GetContractLogs(ctx, params)
}

// GetLatestBlockNumber 获取最新区块号
func (c *BSCScanClient) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	queryParams := url.Values{}
	queryParams.Set("module", "proxy")
	queryParams.Set("action", "eth_blockNumber")
	queryParams.Set("apikey", c.apiKey)

	fullURL := fmt.Sprintf("%s?%s", c.baseURL, queryParams.Encode())

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response: %w", err)
	}

	// 尝试解析JSON-RPC格式响应（proxy模块使用此格式）
	var jsonRpcResponse struct {
		JsonRPC string `json:"jsonrpc"`
		ID      int    `json:"id"`
		Result  string `json:"result"`
		Error   *struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.Unmarshal(body, &jsonRpcResponse); err == nil && jsonRpcResponse.JsonRPC != "" {
		// JSON-RPC格式响应
		if jsonRpcResponse.Error != nil {
			c.logger.Error().
				Int("error_code", jsonRpcResponse.Error.Code).
				Str("error_message", jsonRpcResponse.Error.Message).
				Msg("BSCScan JSON-RPC API error")
			return 0, fmt.Errorf("BSCScan JSON-RPC error: %s (code: %d)",
				jsonRpcResponse.Error.Message, jsonRpcResponse.Error.Code)
		}

		if jsonRpcResponse.Result == "" {
			return 0, fmt.Errorf("empty result in JSON-RPC response")
		}

		// 解析十六进制区块号
		if len(jsonRpcResponse.Result) < 3 || jsonRpcResponse.Result[:2] != "0x" {
			return 0, fmt.Errorf("invalid block number format: %s", jsonRpcResponse.Result)
		}

		blockNumber, err := strconv.ParseUint(jsonRpcResponse.Result[2:], 16, 64)
		if err != nil {
			return 0, fmt.Errorf("failed to parse block number: %w", err)
		}

		c.logger.Debug().
			Uint64("block_number", blockNumber).
			Str("hex", jsonRpcResponse.Result).
			Msg("Retrieved latest block number")

		return blockNumber, nil
	}

	// 尝试解析标准BSCScan API格式响应
	var response struct {
		Status  string `json:"status"`
		Message string `json:"message"`
		Result  string `json:"result"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return 0, fmt.Errorf("failed to parse response: %w", err)
	}

	if response.Status != "1" {
		errorMsg := response.Message
		if errorMsg == "" {
			errorMsg = "Unknown API error"
		}
		c.logger.Error().
			Str("status", response.Status).
			Str("message", response.Message).
			Str("result", response.Result).
			Msg("BSCScan GetLatestBlockNumber API error")
		return 0, fmt.Errorf("BSCScan API error: %s (status: %s)", errorMsg, response.Status)
	}

	// 解析十六进制区块号
	if len(response.Result) < 3 || response.Result[:2] != "0x" {
		return 0, fmt.Errorf("invalid block number format: %s", response.Result)
	}

	blockNumber, err := strconv.ParseUint(response.Result[2:], 16, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse block number: %w", err)
	}

	return blockNumber, nil
}

// SetTestnet 设置为测试网
func (c *BSCScanClient) SetTestnet() {
	c.baseURL = "https://api-testnet.bscscan.com/api"
}

// SetMainnet 设置为主网
func (c *BSCScanClient) SetMainnet() {
	c.baseURL = "https://api.bscscan.com/api"
}

// BlockInfo 区块信息
type BlockInfo struct {
	Number    string `json:"number"`
	Timestamp string `json:"timestamp"`
	Hash      string `json:"hash"`
}

// GetBlockByNumber 根据区块号获取区块信息
func (c *BSCScanClient) GetBlockByNumber(ctx context.Context, blockNumber uint64) (*BlockInfo, error) {
	// 添加API调用延迟，避免频率限制
	time.Sleep(1 * time.Second)

	blockHex := fmt.Sprintf("0x%x", blockNumber)

	queryParams := url.Values{}
	queryParams.Set("module", "proxy")
	queryParams.Set("action", "eth_getBlockByNumber")
	queryParams.Set("tag", blockHex)
	queryParams.Set("boolean", "false")
	queryParams.Set("apikey", c.apiKey)

	fullURL := fmt.Sprintf("%s?%s", c.baseURL, queryParams.Encode())

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 解析JSON-RPC响应 - 使用interface{}来处理不同的响应格式
	var jsonRpcResponse struct {
		JsonRPC string      `json:"jsonrpc"`
		ID      int         `json:"id"`
		Result  interface{} `json:"result"`
		Error   *struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.Unmarshal(body, &jsonRpcResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if jsonRpcResponse.Error != nil {
		return nil, fmt.Errorf("BSCScan API error: %s (code: %d)",
			jsonRpcResponse.Error.Message, jsonRpcResponse.Error.Code)
	}

	if jsonRpcResponse.Result == nil {
		return nil, fmt.Errorf("block not found")
	}

	// 处理不同的响应格式
	switch result := jsonRpcResponse.Result.(type) {
	case map[string]interface{}:
		// 标准的区块信息格式
		blockInfo := &BlockInfo{}
		if number, ok := result["number"].(string); ok {
			blockInfo.Number = number
		}
		if timestamp, ok := result["timestamp"].(string); ok {
			blockInfo.Timestamp = timestamp
		}
		if hash, ok := result["hash"].(string); ok {
			blockInfo.Hash = hash
		}
		return blockInfo, nil
	case string:
		// 如果返回字符串，可能是错误或特殊情况
		c.logger.Warn().Str("result", result).Msg("BSCScan returned string result for block info")
		return nil, fmt.Errorf("unexpected string result: %s", result)
	default:
		// 尝试直接解析为BlockInfo
		blockInfoBytes, err := json.Marshal(jsonRpcResponse.Result)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal result: %w", err)
		}

		var blockInfo BlockInfo
		if err := json.Unmarshal(blockInfoBytes, &blockInfo); err != nil {
			return nil, fmt.Errorf("failed to parse block info: %w", err)
		}
		return &blockInfo, nil
	}
}

// isRateLimitError 检查是否是API限制错误
func isRateLimitError(message string) bool {
	rateLimitMessages := []string{
		"Max rate limit reached",
		"rate limit",
		"too many requests",
		"limit exceeded",
		"query returned more than",
	}

	for _, limitMsg := range rateLimitMessages {
		if len(message) >= len(limitMsg) {
			for i := 0; i <= len(message)-len(limitMsg); i++ {
				if message[i:i+len(limitMsg)] == limitMsg {
					return true
				}
			}
		}
	}
	return false
}
