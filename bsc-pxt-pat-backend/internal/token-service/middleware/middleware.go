package middleware

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	config := cors.Config{
		AllowOrigins:     []string{"*"}, // 生产环境应该限制具体域名
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	return cors.New(config)
}

// LoggerMiddleware 日志中间件
func LoggerMiddleware(logger zerolog.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.Info().
			Str("method", param.Method).
			Str("path", param.Path).
			Int("status", param.StatusCode).
			Dur("latency", param.Latency).
			Str("client_ip", param.ClientIP).
			Str("user_agent", param.Request.UserAgent()).
			Msg("HTTP Request")
		return ""
	})
}

// ErrorHandlerMiddleware 错误处理中间件
func ErrorHandlerMiddleware(logger zerolog.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		logger.Error().
			Interface("error", recovered).
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Msg("Panic recovered")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Internal server error",
		})
	})
}

// RateLimitMiddleware 简单的限流中间件
func RateLimitMiddleware() gin.HandlerFunc {
	// 这里可以集成更复杂的限流库，如 golang.org/x/time/rate
	return func(c *gin.Context) {
		// 简单的限流逻辑，可以根据需要扩展
		c.Next()
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// SecurityMiddleware 安全中间件
func SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	}
}

// PaginationMiddleware 分页参数验证中间件
func PaginationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 验证分页参数
		pageStr := c.DefaultQuery("page", "1")
		limitStr := c.DefaultQuery("limit", "50")

		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid page parameter",
			})
			c.Abort()
			return
		}

		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit < 1 || limit > 200 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid limit parameter (1-200)",
			})
			c.Abort()
			return
		}

		c.Set("page", page)
		c.Set("limit", limit)
		c.Next()
	}
}

// AddressValidationMiddleware 地址验证中间件
func AddressValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		address := c.Param("address")
		if address == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Address parameter is required",
			})
			c.Abort()
			return
		}

		// 验证以太坊地址格式
		if !isValidEthereumAddress(address) {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid Ethereum address format",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CacheControlMiddleware 缓存控制中间件
func CacheControlMiddleware(maxAge int) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "GET" {
			c.Header("Cache-Control", "public, max-age="+strconv.Itoa(maxAge))
		}
		c.Next()
	}
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/health" {
			c.JSON(http.StatusOK, gin.H{
				"status":    "healthy",
				"timestamp": time.Now().Unix(),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		
		// 这里可以收集指标数据
		duration := time.Since(start)
		status := c.Writer.Status()
		
		// 可以发送到Prometheus或其他监控系统
		_ = duration
		_ = status
	}
}

// 辅助函数
func generateRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

func isValidEthereumAddress(address string) bool {
	// 简单的以太坊地址验证
	if len(address) != 42 {
		return false
	}
	if address[:2] != "0x" && address[:2] != "0X" {
		return false
	}
	
	// 检查是否为有效的十六进制字符
	for i := 2; i < len(address); i++ {
		c := address[i]
		if !((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')) {
			return false
		}
	}
	
	return true
}
