package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/content/ipfs/service"
	"pxpat-backend/internal/token-service/handler"
)

// IPFSContentHandler IPFS内容处理器
type IPFSContentHandler struct {
	contentService *service.IPFSContentService
	logger         zerolog.Logger
}

// NewIPFSContentHandler 创建IPFS内容处理器
func NewIPFSContentHandler(contentService *service.IPFSContentService, logger zerolog.Logger) *IPFSContentHandler {
	return &IPFSContentHandler{
		contentService: contentService,
		logger:         logger.With().Str("component", "ipfs_content_handler").Logger(),
	}
}

// PublishContentWithIPFS 发布IPFS内容到区块链
// @Summary 发布IPFS内容到区块链
// @Description 将内容上传到IPFS并发布到区块链，包含完整的元数据
// @Tags IPFS内容管理
// @Accept json
// @Produce json
// @Param request body service.IPFSContentPublishRequest true "IPFS内容发布请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/ipfs/publish [post]
func (h *IPFSContentHandler) PublishContentWithIPFS(c *gin.Context) {
	var req service.IPFSContentPublishRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind IPFS content request")
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Title == "" || req.ContentType == "" || req.PublisherKSUID == "" || req.OnChainID == "" {
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Missing required fields", "id, title, content_type, publisher_ksuid, on_chain_id are required"))
		return
	}

	h.logger.Info().
		Str("id", req.ID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("publisher", req.PublisherKSUID).
		Str("on_chain_id", req.OnChainID).
		Str("ipfs_hash", req.IPFSHash).
		Msg("Publishing IPFS content to blockchain")

	// 调用服务层发布IPFS内容
	result, err := h.contentService.PublishContentWithIPFS(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to publish IPFS content")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to publish IPFS content", err.Error()))
		return
	}

	h.logger.Info().
		Interface("result", result).
		Msg("IPFS content published successfully")

	c.JSON(http.StatusOK, handler.NewSuccessResponse(result))
}

// GetIPFSContent 获取IPFS内容信息
// @Summary 获取IPFS内容信息
// @Description 根据上链ID获取IPFS内容的详细信息，包括IPFS哈希和元数据
// @Tags IPFS内容管理
// @Accept json
// @Produce json
// @Param on_chain_id path string true "上链ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/ipfs/{on_chain_id} [get]
func (h *IPFSContentHandler) GetIPFSContent(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Missing on_chain_id parameter", ""))
		return
	}

	h.logger.Info().Str("on_chain_id", onChainID).Msg("Getting IPFS content")

	content, err := h.contentService.GetIPFSContentByOnChainID(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get IPFS content")
		c.JSON(http.StatusNotFound, handler.NewErrorResponse(http.StatusNotFound, "IPFS content not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(content))
}

// ListIPFSContents 获取IPFS内容列表
// @Summary 获取IPFS内容列表
// @Description 获取IPFS内容列表，支持分页和过滤
// @Tags IPFS内容管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param content_type query string false "内容类型过滤"
// @Param status query string false "状态过滤"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/ipfs [get]
func (h *IPFSContentHandler) ListIPFSContents(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	contentType := c.Query("content_type")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	h.logger.Info().
		Int("page", page).
		Int("limit", limit).
		Str("content_type", contentType).
		Str("status", status).
		Msg("Listing IPFS contents")

	contents, total, err := h.contentService.ListIPFSContents(c.Request.Context(), page, limit, contentType, status)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to list IPFS contents")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to list IPFS contents", err.Error()))
		return
	}

	result := map[string]interface{}{
		"contents": contents,
		"total":    total,
		"page":     page,
		"limit":    limit,
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(result))
}

// GetIPFSContentStats 获取IPFS内容统计信息
// @Summary 获取IPFS内容统计信息
// @Description 获取IPFS内容的统计信息，包括总数、各状态数量等
// @Tags IPFS内容管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/ipfs/stats [get]
func (h *IPFSContentHandler) GetIPFSContentStats(c *gin.Context) {
	h.logger.Info().Msg("Getting IPFS content stats")

	stats, err := h.contentService.GetIPFSContentStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get IPFS content stats")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to get IPFS content stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(stats))
}

// UploadToIPFS 上传文件到IPFS
// @Summary 上传文件到IPFS
// @Description 将文件上传到IPFS并返回哈希值
// @Tags IPFS内容管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "要上传的文件"
// @Param name formData string false "文件名称"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/ipfs/upload [post]
func (h *IPFSContentHandler) UploadToIPFS(c *gin.Context) {
	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get uploaded file")
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Failed to get uploaded file", err.Error()))
		return
	}
	defer file.Close()

	// 获取文件名
	fileName := c.PostForm("name")
	if fileName == "" {
		fileName = header.Filename
	}

	h.logger.Info().
		Str("filename", fileName).
		Int64("size", header.Size).
		Msg("Uploading file to IPFS")

	// 调用服务层上传到IPFS
	ipfsHash, err := h.contentService.UploadFileToIPFS(c.Request.Context(), file, fileName)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to upload file to IPFS")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to upload to IPFS", err.Error()))
		return
	}

	result := map[string]interface{}{
		"ipfs_hash": ipfsHash,
		"filename":  fileName,
		"size":      header.Size,
	}

	h.logger.Info().
		Str("ipfs_hash", ipfsHash).
		Str("filename", fileName).
		Msg("File uploaded to IPFS successfully")

	c.JSON(http.StatusOK, handler.NewSuccessResponse(result))
}
