package service

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/rs/zerolog"

	ipfsModels "pxpat-backend/internal/token-service/content/ipfs/models"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/service"
)

// IPFSContentService IPFS内容服务
type IPFSContentService struct {
	contentRepo            *repository.ContentRepository
	contentRegistryService *service.ContentRegistryService
	ipfsService            *service.IPFSService
	logger                 zerolog.Logger
}

// NewIPFSContentService 创建IPFS内容服务
func NewIPFSContentService(
	contentRepo *repository.ContentRepository,
	contentRegistryService *service.ContentRegistryService,
	ipfsService *service.IPFSService,
	logger zerolog.Logger,
) *IPFSContentService {
	return &IPFSContentService{
		contentRepo:            contentRepo,
		contentRegistryService: contentRegistryService,
		ipfsService:            ipfsService,
		logger:                 logger.With().Str("component", "ipfs_content_service").Logger(),
	}
}

// IPFSContentPublishRequest IPFS内容发布请求
type IPFSContentPublishRequest struct {
	ID              string            `json:"id" binding:"required"`
	Title           string            `json:"title" binding:"required"`
	Description     string            `json:"description"`
	ContentType     string            `json:"content_type" binding:"required"`
	LicenseNumber   string            `json:"license_number"`
	PublisherKSUID  string            `json:"publisher_ksuid" binding:"required"`
	OnChainID       string            `json:"on_chain_id" binding:"required"`
	IPFSHash        string            `json:"ipfs_hash,omitempty"` // 可选，如果为空则自动生成
	Reviewers       []string          `json:"reviewers"`
	Creators        []string          `json:"creators"`
	Contributors    []string          `json:"contributors"`
	Attributes      map[string]string `json:"attributes"`
}

// IPFSContentPublishResult IPFS内容发布结果
type IPFSContentPublishResult struct {
	ID              uint      `json:"id"`
	OnChainID       string    `json:"on_chain_id"`
	Status          string    `json:"status"`
	IPFSHash        string    `json:"ipfs_hash"`
	MetadataIPFS    string    `json:"metadata_ipfs"`
	TransactionHash string    `json:"transaction_hash,omitempty"`
	BlockNumber     uint64    `json:"block_number,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
}

// PublishContentWithIPFS 发布IPFS内容
func (s *IPFSContentService) PublishContentWithIPFS(ctx context.Context, req *IPFSContentPublishRequest) (*IPFSContentPublishResult, error) {
	s.logger.Info().
		Str("id", req.ID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("on_chain_id", req.OnChainID).
		Str("ipfs_hash", req.IPFSHash).
		Msg("Publishing IPFS content")

	// 检查是否已存在
	existing, _ := s.contentRepo.GetContentRecordByOnChainID(ctx, req.OnChainID)
	if existing != nil {
		return nil, fmt.Errorf("content with on_chain_id %s already exists", req.OnChainID)
	}

	// 如果没有提供IPFS哈希，生成一个默认的
	contentIPFSHash := req.IPFSHash
	if contentIPFSHash == "" {
		contentIPFSHash = fmt.Sprintf("QmZ4tDuvesekSs4qM5ZBKpXiZGun7S2CYtEZRB3DYXkjGx") // 默认测试哈希
	}

	// 创建内容记录
	record := &models.TokenContentRecord{
		ContentID:      req.ID,
		Title:          req.Title,
		Description:    req.Description,
		ContentType:    req.ContentType,
		LicenseNumber:  req.LicenseNumber,
		PublisherKSUID: req.PublisherKSUID,
		OnChainID:      req.OnChainID,
		Status:         "pending",
		ContentHash:    contentIPFSHash,
		IPFSHash:       contentIPFSHash,
		Reviewers:      fmt.Sprintf(`["%s"]`, strings.Join(req.Reviewers, `","`)), // 转换为JSON字符串
		OnChainDate:    time.Now(),
		PATFee:         "0.5", // IPFS内容费用更高
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 保存到数据库
	if err := s.contentRepo.CreateContentRecord(ctx, record); err != nil {
		s.logger.Error().Err(err).Msg("Failed to create IPFS content record")
		return nil, fmt.Errorf("failed to create content record: %w", err)
	}

	s.logger.Info().
		Uint("record_id", record.ID).
		Str("on_chain_id", req.OnChainID).
		Msg("IPFS content record created, starting IPFS upload and blockchain publishing")

	// 异步处理IPFS上传和区块链上链，设置5分钟超时
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()
		s.processIPFSContentToBlockchain(ctx, record, contentIPFSHash)
	}()

	return &IPFSContentPublishResult{
		ID:        record.ID,
		OnChainID: record.OnChainID,
		Status:    record.Status,
		IPFSHash:  contentIPFSHash,
		CreatedAt: record.CreatedAt,
	}, nil
}

// processIPFSContentToBlockchain 处理IPFS内容上链
func (s *IPFSContentService) processIPFSContentToBlockchain(ctx context.Context, record *models.TokenContentRecord, contentIPFSHash string) {
	s.logger.Info().
		Uint("record_id", record.ID).
		Str("on_chain_id", record.OnChainID).
		Str("content_ipfs", contentIPFSHash).
		Msg("🚀 Starting IPFS content to blockchain processing")

	// 添加defer来确保错误被记录
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error().
				Interface("panic", r).
				Uint("record_id", record.ID).
				Msg("❌ Panic in IPFS content processing")
			s.updateRecordStatus(ctx, record.ID, "failed", "", 0, contentIPFSHash, "")
		}
	}()

	// 1. 创建元数据并上传到IPFS

	s.logger.Info().
		Uint("record_id", record.ID).
		Msg("📤 Uploading metadata to IPFS")

	// 创建ContentMetadata结构
	contentMetadata := &service.ContentMetadata{
		Title:         record.Title,
		Description:   record.Description,
		ContentType:   record.ContentType,
		LicenseNumber: record.LicenseNumber,
		Reviewers:     []string{}, // 简化处理
		Version:       1,
		CreatedAt:     record.CreatedAt.Format(time.RFC3339),
	}

	s.logger.Debug().
		Interface("metadata", contentMetadata).
		Msg("📋 Created metadata structure")

	metadataResult, err := s.ipfsService.UploadMetadata(ctx, contentMetadata)
	if err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", record.ID).
			Str("title", record.Title).
			Str("content_type", record.ContentType).
			Msg("❌ Failed to upload metadata to IPFS")
		s.updateRecordStatus(ctx, record.ID, "failed", "", 0, "", "")
		return
	}

	metadataIPFSHash := metadataResult.Hash

	s.logger.Info().
		Str("metadata_ipfs", metadataIPFSHash).
		Msg("Metadata uploaded to IPFS successfully")

	// 2. 调用ContentRegistry合约注册内容
	s.logger.Info().Msg("Registering content to ContentRegistry")
	registryReq := &service.RegisterContentRequest{
		ContentType: record.ContentType,
		Title:       record.Title,
		IPFSHash:    contentIPFSHash,
		MetadataURI: metadataIPFSHash,
		Reviewers:   []string{}, // 简化处理
	}

	registryResult, err := s.contentRegistryService.RegisterContent(ctx, registryReq)
	if err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", record.ID).
			Str("content_type", record.ContentType).
			Str("title", record.Title).
			Str("ipfs_hash", contentIPFSHash).
			Str("metadata_ipfs", metadataIPFSHash).
			Msg("Failed to register IPFS content to ContentRegistry")
		s.updateRecordStatus(ctx, record.ID, "failed", "", 0, contentIPFSHash, metadataIPFSHash)
		return
	}

	s.logger.Info().
		Str("tx_hash", registryResult.TxHash).
		Uint64("block_number", registryResult.BlockNumber).
		Interface("content_id", registryResult.ContentID).
		Msg("IPFS content registered to ContentRegistry successfully")

	// 3. 更新记录状态
	s.updateRecordStatus(ctx, record.ID, "confirmed", registryResult.TxHash, registryResult.BlockNumber, contentIPFSHash, metadataIPFSHash)
}

// updateRecordStatus 更新记录状态
func (s *IPFSContentService) updateRecordStatus(ctx context.Context, recordID uint, status, txHash string, blockNumber uint64, contentIPFS, metadataIPFS string) {
	updateData := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if txHash != "" {
		updateData["transaction_hash"] = txHash
	}
	if blockNumber > 0 {
		updateData["block_number"] = blockNumber
	}
	if contentIPFS != "" {
		updateData["content_hash"] = contentIPFS
	}
	if metadataIPFS != "" {
		updateData["metadata_ipfs"] = metadataIPFS
	}

	if err := s.contentRepo.UpdateTransactionInfo(ctx, recordID, txHash, blockNumber, status); err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", recordID).
			Str("status", status).
			Msg("Failed to update IPFS content record status")
	} else {
		s.logger.Info().
			Uint("record_id", recordID).
			Str("status", status).
			Str("tx_hash", txHash).
			Str("content_ipfs", contentIPFS).
			Str("metadata_ipfs", metadataIPFS).
			Msg("IPFS content record status updated")
	}
}

// UploadFileToIPFS 上传文件到IPFS
func (s *IPFSContentService) UploadFileToIPFS(ctx context.Context, file io.Reader, fileName string) (string, error) {
	s.logger.Info().
		Str("filename", fileName).
		Msg("Uploading file to IPFS")

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	result, err := s.ipfsService.UploadContent(ctx, content, fileName)
	if err != nil {
		s.logger.Error().Err(err).
			Str("filename", fileName).
			Msg("Failed to upload file to IPFS")
		return "", fmt.Errorf("failed to upload file to IPFS: %w", err)
	}

	s.logger.Info().
		Str("filename", fileName).
		Str("ipfs_hash", result.Hash).
		Msg("File uploaded to IPFS successfully")

	return result.Hash, nil
}

// GetIPFSContentByOnChainID 根据上链ID获取IPFS内容
func (s *IPFSContentService) GetIPFSContentByOnChainID(ctx context.Context, onChainID string) (*ipfsModels.IPFSContentInfo, error) {
	record, err := s.contentRepo.GetContentRecordByOnChainID(ctx, onChainID)
	if err != nil {
		return nil, fmt.Errorf("IPFS content not found: %w", err)
	}

	return &ipfsModels.IPFSContentInfo{
		ID:              record.ID,
		Title:           record.Title,
		Description:     record.Description,
		ContentType:     record.ContentType,
		LicenseNumber:   record.LicenseNumber,
		PublisherKSUID:  record.PublisherKSUID,
		OnChainID:       record.OnChainID,
		Status:          record.Status,
		IPFSHash:        record.IPFSHash,
		MetadataIPFS:    record.MetadataIPFS,
		TransactionHash: record.TransactionHash,
		BlockNumber:     record.BlockNumber,
		Reviewers:       []string{}, // 简化处理
		Creators:        []string{}, // 简化处理
		Contributors:    []string{}, // 简化处理
		Attributes:      make(map[string]string), // 简化处理
		CreatedAt:       record.CreatedAt,
		UpdatedAt:       record.UpdatedAt,
	}, nil
}

// ListIPFSContents 获取IPFS内容列表
func (s *IPFSContentService) ListIPFSContents(ctx context.Context, page, limit int, contentType, status string) ([]*ipfsModels.IPFSContentInfo, int64, error) {
	records, total, err := s.contentRepo.GetContentRecords(ctx, page, limit, contentType, status)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list IPFS contents: %w", err)
	}

	contents := make([]*ipfsModels.IPFSContentInfo, len(records))
	for i, record := range records {
		contents[i] = &ipfsModels.IPFSContentInfo{
			ID:              record.ID,
			Title:           record.Title,
			Description:     record.Description,
			ContentType:     record.ContentType,
			LicenseNumber:   record.LicenseNumber,
			PublisherKSUID:  record.PublisherKSUID,
			OnChainID:       record.OnChainID,
			Status:          record.Status,
			IPFSHash:        record.IPFSHash,
			MetadataIPFS:    record.MetadataIPFS,
			TransactionHash: record.TransactionHash,
			BlockNumber:     record.BlockNumber,
			Reviewers:       []string{}, // 简化处理
			Creators:        []string{}, // 简化处理
			Contributors:    []string{}, // 简化处理
			Attributes:      make(map[string]string), // 简化处理
			CreatedAt:       record.CreatedAt,
			UpdatedAt:       record.UpdatedAt,
		}
	}

	return contents, total, nil
}

// GetIPFSContentStats 获取IPFS内容统计信息
func (s *IPFSContentService) GetIPFSContentStats(ctx context.Context) (*ipfsModels.IPFSContentStats, error) {
	statsMap, err := s.contentRepo.GetContentStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get IPFS content stats: %w", err)
	}

	// 转换map到结构体
	totalCount, _ := statsMap["total_content"].(int64)
	pendingCount, _ := statsMap["pending_content"].(int64)
	confirmedCount, _ := statsMap["confirmed_content"].(int64)
	failedCount, _ := statsMap["failed_content"].(int64)

	return &ipfsModels.IPFSContentStats{
		TotalCount:     totalCount,
		PendingCount:   pendingCount,
		ConfirmedCount: confirmedCount,
		FailedCount:    failedCount,
		TypeStats:      make(map[string]int64), // 简化处理
		IPFSStats: map[string]interface{}{
			"total_ipfs_uploads": totalCount,
			"metadata_uploads":   confirmedCount,
		},
	}, nil
}
