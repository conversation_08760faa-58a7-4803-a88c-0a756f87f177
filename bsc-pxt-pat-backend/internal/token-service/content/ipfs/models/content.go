package models

import (
	"fmt"
	"time"
)

// IPFSContentInfo IPFS内容信息
type IPFSContentInfo struct {
	ID              uint              `json:"id"`
	Title           string            `json:"title"`
	Description     string            `json:"description"`
	ContentType     string            `json:"content_type"`
	LicenseNumber   string            `json:"license_number"`
	PublisherKSUID  string            `json:"publisher_ksuid"`
	OnChainID       string            `json:"on_chain_id"`
	Status          string            `json:"status"`
	IPFSHash        string            `json:"ipfs_hash"`
	MetadataIPFS    string            `json:"metadata_ipfs"`
	TransactionHash string            `json:"transaction_hash,omitempty"`
	BlockNumber     uint64            `json:"block_number,omitempty"`
	Reviewers       []string          `json:"reviewers"`
	Creators        []string          `json:"creators"`
	Contributors    []string          `json:"contributors"`
	Attributes      map[string]string `json:"attributes"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
}

// IPFSContentStats IPFS内容统计信息
type IPFSContentStats struct {
	TotalCount     int64                    `json:"total_count"`
	PendingCount   int64                    `json:"pending_count"`
	ConfirmedCount int64                    `json:"confirmed_count"`
	FailedCount    int64                    `json:"failed_count"`
	TypeStats      map[string]int64         `json:"type_stats"`
	IPFSStats      map[string]interface{}   `json:"ipfs_stats"`
}

// IPFSContentFilter IPFS内容过滤器
type IPFSContentFilter struct {
	ContentType    string `json:"content_type,omitempty"`
	Status         string `json:"status,omitempty"`
	PublisherKSUID string `json:"publisher_ksuid,omitempty"`
	HasIPFS        *bool  `json:"has_ipfs,omitempty"`
	HasMetadata    *bool  `json:"has_metadata,omitempty"`
	DateFrom       string `json:"date_from,omitempty"`
	DateTo         string `json:"date_to,omitempty"`
}

// IPFSContentSortBy IPFS内容排序字段
type IPFSContentSortBy string

const (
	IPFSSortByCreatedAt IPFSContentSortBy = "created_at"
	IPFSSortByUpdatedAt IPFSContentSortBy = "updated_at"
	IPFSSortByTitle     IPFSContentSortBy = "title"
	IPFSSortByStatus    IPFSContentSortBy = "status"
)

// IPFSContentSortOrder IPFS内容排序顺序
type IPFSContentSortOrder string

const (
	IPFSSortOrderAsc  IPFSContentSortOrder = "asc"
	IPFSSortOrderDesc IPFSContentSortOrder = "desc"
)

// IPFSContentListRequest IPFS内容列表请求
type IPFSContentListRequest struct {
	Page      int                  `json:"page" form:"page"`
	Limit     int                  `json:"limit" form:"limit"`
	Filter    IPFSContentFilter    `json:"filter" form:"filter"`
	SortBy    IPFSContentSortBy    `json:"sort_by" form:"sort_by"`
	SortOrder IPFSContentSortOrder `json:"sort_order" form:"sort_order"`
}

// IPFSContentListResponse IPFS内容列表响应
type IPFSContentListResponse struct {
	Contents []*IPFSContentInfo `json:"contents"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	Limit    int                `json:"limit"`
	HasNext  bool               `json:"has_next"`
}

// IPFSUploadRequest IPFS上传请求
type IPFSUploadRequest struct {
	FileName    string            `json:"file_name"`
	ContentType string            `json:"content_type"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// IPFSUploadResponse IPFS上传响应
type IPFSUploadResponse struct {
	IPFSHash    string            `json:"ipfs_hash"`
	FileName    string            `json:"file_name"`
	Size        int64             `json:"size"`
	ContentType string            `json:"content_type"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	UploadedAt  time.Time         `json:"uploaded_at"`
}

// IPFSMetadata IPFS元数据结构
type IPFSMetadata struct {
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	ContentType  string            `json:"content_type"`
	License      string            `json:"license,omitempty"`
	Publisher    string            `json:"publisher"`
	Creators     []string          `json:"creators,omitempty"`
	Contributors []string          `json:"contributors,omitempty"`
	Attributes   map[string]string `json:"attributes,omitempty"`
	ContentHash  string            `json:"content_hash"`
	CreatedAt    string            `json:"created_at"`
	Version      string            `json:"version,omitempty"`
}

// IPFSGatewayURL IPFS网关URL配置
type IPFSGatewayURL struct {
	Gateway string `json:"gateway"`
	Hash    string `json:"hash"`
	URL     string `json:"url"`
}

// GetIPFSGatewayURLs 获取IPFS网关URL列表
func GetIPFSGatewayURLs(hash string) []IPFSGatewayURL {
	gateways := []string{
		"https://gateway.pinata.cloud",
		"https://ipfs.io",
		"https://cloudflare-ipfs.com",
		"https://dweb.link",
	}

	urls := make([]IPFSGatewayURL, len(gateways))
	for i, gateway := range gateways {
		urls[i] = IPFSGatewayURL{
			Gateway: gateway,
			Hash:    hash,
			URL:     fmt.Sprintf("%s/ipfs/%s", gateway, hash),
		}
	}

	return urls
}

// ValidateIPFSHash 验证IPFS哈希格式
func ValidateIPFSHash(hash string) bool {
	if len(hash) == 0 {
		return false
	}
	
	// 基本的IPFS哈希格式检查
	// Qm开头的v0哈希或baf开头的v1哈希
	if len(hash) == 46 && hash[:2] == "Qm" {
		return true
	}
	if len(hash) >= 50 && hash[:3] == "baf" {
		return true
	}
	
	return false
}

// GetContentTypeIcon 获取内容类型图标
func GetContentTypeIcon(contentType string) string {
	switch contentType {
	case "video":
		return "🎥"
	case "audio", "music":
		return "🎵"
	case "image":
		return "🖼️"
	case "document":
		return "📄"
	default:
		return "📁"
	}
}

// GetStatusColor 获取状态颜色
func GetStatusColor(status string) string {
	switch status {
	case "pending":
		return "#FFA500" // 橙色
	case "confirmed":
		return "#00FF00" // 绿色
	case "failed":
		return "#FF0000" // 红色
	default:
		return "#808080" // 灰色
	}
}
