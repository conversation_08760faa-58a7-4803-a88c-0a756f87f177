package content

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	basicHandler "pxpat-backend/internal/token-service/content/basic/handler"
	basicService "pxpat-backend/internal/token-service/content/basic/service"
	ipfsHandler "pxpat-backend/internal/token-service/content/ipfs/handler"
	ipfsContentService "pxpat-backend/internal/token-service/content/ipfs/service"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/service"
)

// ContentRoutes 内容路由配置
type ContentRoutes struct {
	basicHandler *basicHandler.BasicContentHandler
	ipfsHandler  *ipfsHandler.IPFSContentHandler
	logger       zerolog.Logger
}

// NewContentRoutes 创建内容路由
func NewContentRoutes(
	contentRepo *repository.ContentRepository,
	contentRegistryService *service.ContentRegistryService,
	ipfsService *service.IPFSService,
	logger zerolog.Logger,
) *ContentRoutes {
	// 创建基础内容服务和处理器
	basicContentService := basicService.NewBasicContentService(
		contentRepo,
		contentRegistryService,
		logger,
	)
	basicContentHandler := basicHandler.NewBasicContentHandler(basicContentService, logger)

	// 创建IPFS内容服务和处理器
	ipfsContentServiceInstance := ipfsContentService.NewIPFSContentService(
		contentRepo,
		contentRegistryService,
		ipfsService,
		logger,
	)
	ipfsContentHandler := ipfsHandler.NewIPFSContentHandler(ipfsContentServiceInstance, logger)

	return &ContentRoutes{
		basicHandler: basicContentHandler,
		ipfsHandler:  ipfsContentHandler,
		logger:       logger.With().Str("component", "content_routes").Logger(),
	}
}

// RegisterRoutes 注册内容相关路由
func (r *ContentRoutes) RegisterRoutes(router *gin.RouterGroup) {
	r.logger.Info().Msg("Registering content routes")

	// 内容管理主路由组
	contentGroup := router.Group("/content")
	{
		// 基础内容路由
		basicGroup := contentGroup.Group("/basic")
		{
			basicGroup.POST("/publish", r.basicHandler.PublishContent)
			basicGroup.GET("/:on_chain_id", r.basicHandler.GetContent)
			basicGroup.GET("", r.basicHandler.ListContents)
			basicGroup.GET("/stats", r.basicHandler.GetContentStats)
		}

		// IPFS内容路由
		ipfsGroup := contentGroup.Group("/ipfs")
		{
			ipfsGroup.POST("/publish", r.ipfsHandler.PublishContentWithIPFS)
			ipfsGroup.GET("/:on_chain_id", r.ipfsHandler.GetIPFSContent)
			ipfsGroup.GET("", r.ipfsHandler.ListIPFSContents)
			ipfsGroup.GET("/stats", r.ipfsHandler.GetIPFSContentStats)
			ipfsGroup.POST("/upload", r.ipfsHandler.UploadToIPFS)
		}

		// 兼容性路由已在RegisterLegacyRoutes中注册，这里不重复注册
	}

	r.logger.Info().Msg("Content routes registered successfully")
}

// getContentCompatible 兼容性内容获取 - 自动判断是基础内容还是IPFS内容
func (r *ContentRoutes) getContentCompatible(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(400, gin.H{"error": "Missing on_chain_id parameter"})
		return
	}

	// 先尝试从IPFS内容获取
	r.ipfsHandler.GetIPFSContent(c)

	// 如果响应已经发送，直接返回
	if c.Writer.Written() {
		return
	}

	// 如果IPFS内容不存在，尝试基础内容
	r.basicHandler.GetContent(c)
}

// RegisterLegacyRoutes 注册兼容性路由（保持原有API结构）
func (r *ContentRoutes) RegisterLegacyRoutes(router *gin.RouterGroup) {
	r.logger.Info().Msg("Registering legacy content routes for backward compatibility")

	// 保持原有的API路径不变
	router.POST("/content/publish", r.basicHandler.PublishContent)
	router.POST("/content/publish-ipfs", r.ipfsHandler.PublishContentWithIPFS)
	router.GET("/content/:on_chain_id", r.getContentCompatible)

	r.logger.Info().Msg("Legacy content routes registered successfully")
}
