package models

import (
	"time"
)

// BasicContentInfo 基础内容信息
type BasicContentInfo struct {
	ID              uint              `json:"id"`
	Title           string            `json:"title"`
	Description     string            `json:"description"`
	ContentType     string            `json:"content_type"`
	LicenseNumber   string            `json:"license_number"`
	PublisherKSUID  string            `json:"publisher_ksuid"`
	OnChainID       string            `json:"on_chain_id"`
	Status          string            `json:"status"`
	ContentHash     string            `json:"content_hash"`
	TransactionHash string            `json:"transaction_hash,omitempty"`
	BlockNumber     uint64            `json:"block_number,omitempty"`
	Reviewers       []string          `json:"reviewers"`
	Creators        []string          `json:"creators"`
	Contributors    []string          `json:"contributors"`
	Attributes      map[string]string `json:"attributes"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
}

// BasicContentStats 基础内容统计信息
type BasicContentStats struct {
	TotalCount     int64                    `json:"total_count"`
	PendingCount   int64                    `json:"pending_count"`
	ConfirmedCount int64                    `json:"confirmed_count"`
	FailedCount    int64                    `json:"failed_count"`
	TypeStats      map[string]int64         `json:"type_stats"`
}

// BasicContentFilter 基础内容过滤器
type BasicContentFilter struct {
	ContentType    string `json:"content_type,omitempty"`
	Status         string `json:"status,omitempty"`
	PublisherKSUID string `json:"publisher_ksuid,omitempty"`
	DateFrom       string `json:"date_from,omitempty"`
	DateTo         string `json:"date_to,omitempty"`
}

// BasicContentSortBy 基础内容排序字段
type BasicContentSortBy string

const (
	SortByCreatedAt BasicContentSortBy = "created_at"
	SortByUpdatedAt BasicContentSortBy = "updated_at"
	SortByTitle     BasicContentSortBy = "title"
	SortByStatus    BasicContentSortBy = "status"
)

// BasicContentSortOrder 基础内容排序顺序
type BasicContentSortOrder string

const (
	SortOrderAsc  BasicContentSortOrder = "asc"
	SortOrderDesc BasicContentSortOrder = "desc"
)

// BasicContentListRequest 基础内容列表请求
type BasicContentListRequest struct {
	Page      int                   `json:"page" form:"page"`
	Limit     int                   `json:"limit" form:"limit"`
	Filter    BasicContentFilter    `json:"filter" form:"filter"`
	SortBy    BasicContentSortBy    `json:"sort_by" form:"sort_by"`
	SortOrder BasicContentSortOrder `json:"sort_order" form:"sort_order"`
}

// BasicContentListResponse 基础内容列表响应
type BasicContentListResponse struct {
	Contents []*BasicContentInfo `json:"contents"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	Limit    int                 `json:"limit"`
	HasNext  bool                `json:"has_next"`
}

// ContentStatus 内容状态枚举
type ContentStatus string

const (
	StatusPending   ContentStatus = "pending"   // 待处理
	StatusConfirmed ContentStatus = "confirmed" // 已确认
	StatusFailed    ContentStatus = "failed"    // 失败
)

// ContentType 内容类型枚举
type ContentType string

const (
	TypeVideo    ContentType = "video"    // 视频
	TypeAudio    ContentType = "audio"    // 音频
	TypeImage    ContentType = "image"    // 图片
	TypeDocument ContentType = "document" // 文档
	TypeMusic    ContentType = "music"    // 音乐
	TypeOther    ContentType = "other"    // 其他
)

// IsValidContentType 检查内容类型是否有效
func IsValidContentType(contentType string) bool {
	switch ContentType(contentType) {
	case TypeVideo, TypeAudio, TypeImage, TypeDocument, TypeMusic, TypeOther:
		return true
	default:
		return false
	}
}

// IsValidContentStatus 检查内容状态是否有效
func IsValidContentStatus(status string) bool {
	switch ContentStatus(status) {
	case StatusPending, StatusConfirmed, StatusFailed:
		return true
	default:
		return false
	}
}

// GetContentTypeDisplayName 获取内容类型显示名称
func GetContentTypeDisplayName(contentType string) string {
	switch ContentType(contentType) {
	case TypeVideo:
		return "视频"
	case TypeAudio:
		return "音频"
	case TypeImage:
		return "图片"
	case TypeDocument:
		return "文档"
	case TypeMusic:
		return "音乐"
	case TypeOther:
		return "其他"
	default:
		return "未知"
	}
}

// GetContentStatusDisplayName 获取内容状态显示名称
func GetContentStatusDisplayName(status string) string {
	switch ContentStatus(status) {
	case StatusPending:
		return "待确认"
	case StatusConfirmed:
		return "已确认"
	case StatusFailed:
		return "失败"
	default:
		return "未知"
	}
}
