package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog"

	basicModels "pxpat-backend/internal/token-service/content/basic/models"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/service"
)

// BasicContentService 基础内容服务
type BasicContentService struct {
	contentRepo            *repository.ContentRepository
	contentRegistryService *service.ContentRegistryService
	logger                 zerolog.Logger
}

// NewBasicContentService 创建基础内容服务
func NewBasicContentService(
	contentRepo *repository.ContentRepository,
	contentRegistryService *service.ContentRegistryService,
	logger zerolog.Logger,
) *BasicContentService {
	return &BasicContentService{
		contentRepo:            contentRepo,
		contentRegistryService: contentRegistryService,
		logger:                 logger.With().Str("component", "basic_content_service").Logger(),
	}
}

// BasicContentPublishRequest 基础内容发布请求
type BasicContentPublishRequest struct {
	ID              string            `json:"id" binding:"required"`
	Title           string            `json:"title" binding:"required"`
	Description     string            `json:"description"`
	ContentType     string            `json:"content_type" binding:"required"`
	LicenseNumber   string            `json:"license_number"`
	PublisherKSUID  string            `json:"publisher_ksuid" binding:"required"`
	OnChainID       string            `json:"on_chain_id" binding:"required"`
	Reviewers       []string          `json:"reviewers"`
	Creators        []string          `json:"creators"`
	Contributors    []string          `json:"contributors"`
	Attributes      map[string]string `json:"attributes"`
}

// BasicContentPublishResult 基础内容发布结果
type BasicContentPublishResult struct {
	ID              uint      `json:"id"`
	OnChainID       string    `json:"on_chain_id"`
	Status          string    `json:"status"`
	TransactionHash string    `json:"transaction_hash,omitempty"`
	BlockNumber     uint64    `json:"block_number,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
}

// PublishContent 发布基础内容
func (s *BasicContentService) PublishContent(ctx context.Context, req *BasicContentPublishRequest) (*BasicContentPublishResult, error) {
	s.logger.Info().
		Str("id", req.ID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("on_chain_id", req.OnChainID).
		Msg("Publishing basic content")

	// 检查是否已存在
	existing, _ := s.contentRepo.GetContentRecordByOnChainID(ctx, req.OnChainID)
	if existing != nil {
		return nil, fmt.Errorf("content with on_chain_id %s already exists", req.OnChainID)
	}

	// 创建内容记录
	record := &models.TokenContentRecord{
		ContentID:      req.ID,
		Title:          req.Title,
		Description:    req.Description,
		ContentType:    req.ContentType,
		LicenseNumber:  req.LicenseNumber,
		PublisherKSUID: req.PublisherKSUID,
		OnChainID:      req.OnChainID,
		Status:         "pending",
		ContentHash:    fmt.Sprintf("basic_%s_%d", req.ContentType, time.Now().Unix()),
		Reviewers:      fmt.Sprintf(`["%s"]`, strings.Join(req.Reviewers, `","`)), // 转换为JSON字符串
		OnChainDate:    time.Now(),
		PATFee:         "0.1", // 默认费用
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 保存到数据库
	if err := s.contentRepo.CreateContentRecord(ctx, record); err != nil {
		s.logger.Error().Err(err).Msg("Failed to create basic content record")
		return nil, fmt.Errorf("failed to create content record: %w", err)
	}

	s.logger.Info().
		Uint("record_id", record.ID).
		Str("on_chain_id", req.OnChainID).
		Msg("Basic content record created, starting blockchain publishing")

	// 异步处理区块链上链，设置5分钟超时
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()
		s.processBasicContentToBlockchain(ctx, record)
	}()

	return &BasicContentPublishResult{
		ID:        record.ID,
		OnChainID: record.OnChainID,
		Status:    record.Status,
		CreatedAt: record.CreatedAt,
	}, nil
}

// processBasicContentToBlockchain 处理基础内容上链
func (s *BasicContentService) processBasicContentToBlockchain(ctx context.Context, record *models.TokenContentRecord) {
	s.logger.Info().
		Uint("record_id", record.ID).
		Str("on_chain_id", record.OnChainID).
		Msg("Processing basic content to blockchain")

	// 调用ContentRegistry合约注册内容
	registryReq := &service.RegisterContentRequest{
		ContentType: record.ContentType,
		Title:       record.Title,
		IPFSHash:    record.ContentHash, // 使用内容哈希作为IPFS哈希
		MetadataURI: record.ContentHash, // 使用内容哈希作为元数据URI
		Reviewers:   []string{}, // 简化处理
	}

	registryResult, err := s.contentRegistryService.RegisterContent(ctx, registryReq)
	if err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", record.ID).
			Msg("Failed to register basic content to ContentRegistry")
		s.updateRecordStatus(ctx, record.ID, "failed", "", 0)
		return
	}

	s.logger.Info().
		Str("tx_hash", registryResult.TxHash).
		Uint64("block_number", registryResult.BlockNumber).
		Interface("content_id", registryResult.ContentID).
		Msg("Basic content registered to ContentRegistry successfully")

	// 更新记录状态
	s.updateRecordStatus(ctx, record.ID, "confirmed", registryResult.TxHash, registryResult.BlockNumber)
}

// updateRecordStatus 更新记录状态
func (s *BasicContentService) updateRecordStatus(ctx context.Context, recordID uint, status, txHash string, blockNumber uint64) {
	updateData := map[string]interface{}{
		"status":      status,
		"updated_at":  time.Now(),
	}

	if txHash != "" {
		updateData["transaction_hash"] = txHash
	}
	if blockNumber > 0 {
		updateData["block_number"] = blockNumber
	}

	if err := s.contentRepo.UpdateTransactionInfo(ctx, recordID, txHash, blockNumber, status); err != nil {
		s.logger.Error().Err(err).
			Uint("record_id", recordID).
			Str("status", status).
			Msg("Failed to update basic content record status")
	} else {
		s.logger.Info().
			Uint("record_id", recordID).
			Str("status", status).
			Str("tx_hash", txHash).
			Msg("Basic content record status updated")
	}
}

// GetContentByOnChainID 根据上链ID获取基础内容
func (s *BasicContentService) GetContentByOnChainID(ctx context.Context, onChainID string) (*basicModels.BasicContentInfo, error) {
	record, err := s.contentRepo.GetContentRecordByOnChainID(ctx, onChainID)
	if err != nil {
		return nil, fmt.Errorf("content not found: %w", err)
	}

	return &basicModels.BasicContentInfo{
		ID:              record.ID,
		Title:           record.Title,
		Description:     record.Description,
		ContentType:     record.ContentType,
		LicenseNumber:   record.LicenseNumber,
		PublisherKSUID:  record.PublisherKSUID,
		OnChainID:       record.OnChainID,
		Status:          record.Status,
		ContentHash:     record.ContentHash,
		TransactionHash: record.TransactionHash,
		BlockNumber:     record.BlockNumber,
		Reviewers:       []string{}, // 简化处理，从JSON字符串解析
		Creators:        []string{}, // 简化处理
		Contributors:    []string{}, // 简化处理
		Attributes:      make(map[string]string), // 简化处理
		CreatedAt:       record.CreatedAt,
		UpdatedAt:       record.UpdatedAt,
	}, nil
}

// ListContents 获取基础内容列表
func (s *BasicContentService) ListContents(ctx context.Context, page, limit int, contentType, status string) ([]*basicModels.BasicContentInfo, int64, error) {
	records, total, err := s.contentRepo.GetContentRecords(ctx, page, limit, contentType, status)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list contents: %w", err)
	}

	contents := make([]*basicModels.BasicContentInfo, len(records))
	for i, record := range records {
		contents[i] = &basicModels.BasicContentInfo{
			ID:              record.ID,
			Title:           record.Title,
			Description:     record.Description,
			ContentType:     record.ContentType,
			LicenseNumber:   record.LicenseNumber,
			PublisherKSUID:  record.PublisherKSUID,
			OnChainID:       record.OnChainID,
			Status:          record.Status,
			ContentHash:     record.ContentHash,
			TransactionHash: record.TransactionHash,
			BlockNumber:     record.BlockNumber,
			Reviewers:       []string{}, // 简化处理
			Creators:        []string{}, // 简化处理
			Contributors:    []string{}, // 简化处理
			Attributes:      make(map[string]string), // 简化处理
			CreatedAt:       record.CreatedAt,
			UpdatedAt:       record.UpdatedAt,
		}
	}

	return contents, total, nil
}

// GetContentStats 获取基础内容统计信息
func (s *BasicContentService) GetContentStats(ctx context.Context) (*basicModels.BasicContentStats, error) {
	statsMap, err := s.contentRepo.GetContentStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get content stats: %w", err)
	}

	// 转换map到结构体
	totalCount, _ := statsMap["total_content"].(int64)
	pendingCount, _ := statsMap["pending_content"].(int64)
	confirmedCount, _ := statsMap["confirmed_content"].(int64)
	failedCount, _ := statsMap["failed_content"].(int64)

	return &basicModels.BasicContentStats{
		TotalCount:     totalCount,
		PendingCount:   pendingCount,
		ConfirmedCount: confirmedCount,
		FailedCount:    failedCount,
		TypeStats:      make(map[string]int64), // 简化处理
	}, nil
}
