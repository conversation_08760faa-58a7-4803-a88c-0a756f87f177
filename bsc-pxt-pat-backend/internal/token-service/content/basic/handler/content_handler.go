package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/content/basic/service"
	"pxpat-backend/internal/token-service/handler"
)

// BasicContentHandler 基础内容处理器
type BasicContentHandler struct {
	contentService *service.BasicContentService
	logger         zerolog.Logger
}

// NewBasicContentHandler 创建基础内容处理器
func NewBasicContentHandler(contentService *service.BasicContentService, logger zerolog.Logger) *BasicContentHandler {
	return &BasicContentHandler{
		contentService: contentService,
		logger:         logger.With().Str("component", "basic_content_handler").Logger(),
	}
}

// PublishContent 发布基础内容到区块链
// @Summary 发布基础内容到区块链
// @Description 将基础内容发布到区块链并记录相关信息
// @Tags 基础内容管理
// @Accept json
// @Produce json
// @Param request body service.BasicContentPublishRequest true "基础内容发布请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/basic/publish [post]
func (h *BasicContentHandler) PublishContent(c *gin.Context) {
	var req service.BasicContentPublishRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind basic content request")
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Title == "" || req.ContentType == "" || req.PublisherKSUID == "" || req.OnChainID == "" {
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Missing required fields", "id, title, content_type, publisher_ksuid, on_chain_id are required"))
		return
	}

	h.logger.Info().
		Str("id", req.ID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Str("publisher", req.PublisherKSUID).
		Str("on_chain_id", req.OnChainID).
		Msg("Publishing basic content to blockchain")

	// 调用服务层发布内容
	result, err := h.contentService.PublishContent(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to publish basic content")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to publish content", err.Error()))
		return
	}

	h.logger.Info().
		Interface("result", result).
		Msg("Basic content published successfully")

	c.JSON(http.StatusOK, handler.NewSuccessResponse(result))
}

// GetContent 获取基础内容信息
// @Summary 获取基础内容信息
// @Description 根据上链ID获取基础内容的详细信息
// @Tags 基础内容管理
// @Accept json
// @Produce json
// @Param on_chain_id path string true "上链ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/basic/{on_chain_id} [get]
func (h *BasicContentHandler) GetContent(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, handler.NewErrorResponse(http.StatusBadRequest, "Missing on_chain_id parameter", ""))
		return
	}

	h.logger.Info().Str("on_chain_id", onChainID).Msg("Getting basic content")

	content, err := h.contentService.GetContentByOnChainID(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get basic content")
		c.JSON(http.StatusNotFound, handler.NewErrorResponse(http.StatusNotFound, "Content not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(content))
}

// ListContents 获取基础内容列表
// @Summary 获取基础内容列表
// @Description 获取基础内容列表，支持分页和过滤
// @Tags 基础内容管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param content_type query string false "内容类型过滤"
// @Param status query string false "状态过滤"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/basic [get]
func (h *BasicContentHandler) ListContents(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	contentType := c.Query("content_type")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	h.logger.Info().
		Int("page", page).
		Int("limit", limit).
		Str("content_type", contentType).
		Str("status", status).
		Msg("Listing basic contents")

	contents, total, err := h.contentService.ListContents(c.Request.Context(), page, limit, contentType, status)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to list basic contents")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to list contents", err.Error()))
		return
	}

	result := map[string]interface{}{
		"contents": contents,
		"total":    total,
		"page":     page,
		"limit":    limit,
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(result))
}

// GetContentStats 获取基础内容统计信息
// @Summary 获取基础内容统计信息
// @Description 获取基础内容的统计信息，包括总数、各状态数量等
// @Tags 基础内容管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/content/basic/stats [get]
func (h *BasicContentHandler) GetContentStats(c *gin.Context) {
	h.logger.Info().Msg("Getting basic content stats")

	stats, err := h.contentService.GetContentStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get basic content stats")
		c.JSON(http.StatusInternalServerError, handler.NewErrorResponse(http.StatusInternalServerError, "Failed to get content stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, handler.NewSuccessResponse(stats))
}
