package listener

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"

	"pxpat-backend/internal/token-service/models"
)

// PoolTransferEventHandler 池子转账事件处理器
type PoolTransferEventHandler struct {
	listener *MultiEventListener
}

// Handle 处理池子转账事件
func (h *PoolTransferEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 检查是否是池子相关的转账
	if !h.isPoolRelatedTransfer(log.Address) {
		return nil // 不是池子相关的转账，跳过
	}

	// 解析转账事件
	var event struct {
		From  common.Address
		To    common.Address
		Value *big.Int
	}

	// 解析indexed参数
	if len(log.Topics) >= 3 {
		event.From = common.HexToAddress(log.Topics[1].Hex())
		event.To = common.HexToAddress(log.Topics[2].Hex())
	}

	// 解析Value（在data中）
	if len(log.Data) >= 32 {
		event.Value = new(big.Int).SetBytes(log.Data[:32])
	}

	// 确定代币类型
	tokenType := h.getTokenType(log.Address)
	if tokenType == "" {
		return nil // 不是我们关心的代币
	}

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 检查是否涉及池子地址
	fromAddr := strings.ToLower(event.From.Hex())
	toAddr := strings.ToLower(event.To.Hex())

	chinaPoolAddr := strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex())
	globalPoolAddr := strings.ToLower(contracts.Addresses.GlobalPool.Hex())

	isPoolTransfer := fromAddr == chinaPoolAddr || fromAddr == globalPoolAddr ||
		toAddr == chinaPoolAddr || toAddr == globalPoolAddr

	if !isPoolTransfer {
		return nil // 不涉及池子，跳过
	}

	// 创建转账记录
	transfer := &models.TokenTransfer{
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint64(log.Index),
		TokenType:       tokenType,
		FromAddress:     fromAddr,
		ToAddress:       toAddr,
		Amount:          event.Value.String(),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存转账记录
	if err := h.listener.tokenRepo.CreateTransfer(ctx, transfer); err != nil {
		return fmt.Errorf("failed to create pool transfer record: %w", err)
	}

	// 处理池子特定逻辑
	if err := h.handlePoolSpecificLogic(ctx, transfer, log); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to handle pool specific logic")
	}

	// 更新池子余额
	if err := h.updatePoolBalances(ctx, event.From, event.To, tokenType); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to update pool balances")
	}

	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Str("from", fromAddr).
		Str("to", toAddr).
		Str("amount", event.Value.String()).
		Str("token", tokenType).
		Msg("Pool transfer event processed")

	return nil
}

// isPoolRelatedTransfer 检查是否是池子相关的转账
func (h *PoolTransferEventHandler) isPoolRelatedTransfer(contractAddress common.Address) bool {
	contracts := h.listener.client.GetContracts()

	// 检查是否是代币合约的转账事件
	return contractAddress == contracts.Addresses.PXTToken ||
		contractAddress == contracts.Addresses.PATToken
}

// getTokenType 根据合约地址确定代币类型
func (h *PoolTransferEventHandler) getTokenType(contractAddress common.Address) string {
	contracts := h.listener.client.GetContracts()

	if contractAddress == contracts.Addresses.PXTToken {
		return "PXT"
	} else if contractAddress == contracts.Addresses.PATToken {
		return "PAT"
	}
	return ""
}

// handlePoolSpecificLogic 处理池子特定逻辑
func (h *PoolTransferEventHandler) handlePoolSpecificLogic(ctx context.Context, transfer *models.TokenTransfer, _ types.Log) error {
	contracts := h.listener.client.GetContracts()

	chinaPoolAddr := strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex())
	globalPoolAddr := strings.ToLower(contracts.Addresses.GlobalPool.Hex())

	// 确定池子类型和转账方向
	var poolType, direction string
	var poolAddress string

	if transfer.FromAddress == chinaPoolAddr {
		poolType = "china_mainland"
		poolAddress = chinaPoolAddr
		direction = "out"
	} else if transfer.FromAddress == globalPoolAddr {
		poolType = "global"
		poolAddress = globalPoolAddr
		direction = "out"
	} else if transfer.ToAddress == chinaPoolAddr {
		poolType = "china_mainland"
		poolAddress = chinaPoolAddr
		direction = "in"
	} else if transfer.ToAddress == globalPoolAddr {
		poolType = "global"
		poolAddress = globalPoolAddr
		direction = "in"
	} else {
		return nil // 不涉及池子
	}

	// 创建池子活动记录
	poolActivity := &models.PoolActivity{
		PoolAddress:     poolAddress,
		PoolType:        poolType,
		TransactionHash: transfer.TransactionHash,
		TokenType:       transfer.TokenType,
		Direction:       direction,
		FromAddress:     transfer.FromAddress,
		ToAddress:       transfer.ToAddress,
		Amount:          transfer.Amount,
		Purpose:         h.determinePurpose(transfer, direction),
		BlockNumber:     transfer.BlockNumber,
		Timestamp:       transfer.Timestamp,
	}

	// 保存池子活动记录
	if err := h.listener.tokenRepo.CreatePoolActivity(ctx, poolActivity); err != nil {
		return fmt.Errorf("failed to create pool activity: %w", err)
	}

	// 发送通知
	h.sendPoolNotification(ctx, poolActivity)

	return nil
}

// determinePurpose 确定转账用途
func (h *PoolTransferEventHandler) determinePurpose(transfer *models.TokenTransfer, direction string) string {
	// 根据转账金额、地址等信息判断用途
	amount, ok := new(big.Int).SetString(transfer.Amount, 10)
	if !ok {
		return "unknown"
	}

	// 大额转账可能是奖励分发
	rewardThreshold := new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil) // 1 token
	rewardThreshold.Mul(rewardThreshold, big.NewInt(1000))                   // 1000 tokens

	if amount.Cmp(rewardThreshold) >= 0 {
		if direction == "out" {
			return "reward_distribution" // 奖励分发
		} else {
			return "large_deposit" // 大额存入
		}
	}

	// 中等金额可能是普通奖励
	normalRewardThreshold := new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil) // 1 token
	normalRewardThreshold.Mul(normalRewardThreshold, big.NewInt(10))               // 10 tokens

	if amount.Cmp(normalRewardThreshold) >= 0 {
		if direction == "out" {
			return "reward" // 普通奖励
		} else {
			return "deposit" // 存入
		}
	}

	// 小额转账
	if direction == "out" {
		return "small_reward" // 小额奖励
	} else {
		return "small_deposit" // 小额存入
	}
}

// updatePoolBalances 更新池子余额
func (h *PoolTransferEventHandler) updatePoolBalances(ctx context.Context, from, to common.Address, tokenType string) error {
	contracts := h.listener.client.GetContracts()
	var tokenAddress common.Address

	// 获取代币合约地址
	if tokenType == "PXT" {
		tokenAddress = contracts.Addresses.PXTToken
	} else if tokenType == "PAT" {
		tokenAddress = contracts.Addresses.PATToken
	} else {
		return fmt.Errorf("unknown token type: %s", tokenType)
	}

	// 检查并更新池子余额
	poolAddresses := []common.Address{
		contracts.Addresses.ChinaMainlandPool,
		contracts.Addresses.GlobalPool,
	}

	for _, poolAddr := range poolAddresses {
		if from == poolAddr || to == poolAddr {
			if err := h.updateSinglePoolBalance(ctx, poolAddr, tokenAddress, tokenType); err != nil {
				h.listener.logger.Warn().Err(err).Str("pool", poolAddr.Hex()).Msg("Failed to update pool balance")
			}
		}
	}

	return nil
}

// updateSinglePoolBalance 更新单个池子余额
func (h *PoolTransferEventHandler) updateSinglePoolBalance(ctx context.Context, poolAddress common.Address, tokenAddress common.Address, tokenType string) error {
	// 获取当前余额
	balance, err := h.listener.client.GetTokenBalance(ctx, tokenAddress, poolAddress)
	if err != nil {
		return fmt.Errorf("failed to get pool balance: %w", err)
	}

	// 创建或更新余额记录
	poolBalance := &models.PoolBalance{
		PoolAddress: strings.ToLower(poolAddress.Hex()),
		TokenType:   tokenType,
		Balance:     balance.String(),
		UpdatedAt:   time.Now(),
	}

	// 保存余额记录
	if err := h.listener.tokenRepo.CreateOrUpdatePoolBalance(ctx, poolBalance); err != nil {
		return fmt.Errorf("failed to update pool balance: %w", err)
	}

	h.listener.logger.Debug().
		Str("pool", poolAddress.Hex()).
		Str("token", tokenType).
		Str("balance", balance.String()).
		Msg("Pool balance updated")

	return nil
}

// sendPoolNotification 发送池子通知
func (h *PoolTransferEventHandler) sendPoolNotification(_ context.Context, activity *models.PoolActivity) {
	// 记录池子活动日志
	h.listener.logger.Info().
		Str("pool_address", activity.PoolAddress).
		Str("pool_type", activity.PoolType).
		Str("direction", activity.Direction).
		Str("amount", activity.Amount).
		Str("token", activity.TokenType).
		Str("purpose", activity.Purpose).
		Msg("Pool activity notification")

	// 检查是否是大额转账并发送警报
	amount, ok := new(big.Int).SetString(activity.Amount, 10)
	if ok {
		alertThreshold := new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil) // 1 token
		alertThreshold.Mul(alertThreshold, big.NewInt(10000))                   // 10000 tokens

		if amount.Cmp(alertThreshold) >= 0 {
			h.listener.logger.Warn().
				Str("pool", activity.PoolAddress).
				Str("pool_type", activity.PoolType).
				Str("amount", activity.Amount).
				Str("token", activity.TokenType).
				Str("direction", activity.Direction).
				Str("purpose", activity.Purpose).
				Msg("Large pool transfer detected - Alert!")
		}
	}

	// TODO: 在这里可以添加WebSocket通知或其他通知机制
	// 当前先记录日志，后续可以集成通知服务
}
