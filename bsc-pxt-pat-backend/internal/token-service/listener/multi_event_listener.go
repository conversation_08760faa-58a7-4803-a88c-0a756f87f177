package listener

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/repository"
)

// MultiEventListener 多合约事件监听器
type MultiEventListener struct {
	client *blockchain.MultiContractClient
	config *config.BlockchainConfig
	logger zerolog.Logger

	// Repositories
	stakingRepo    *repository.StakingRepository
	eventRepo      *repository.EventRepository
	tokenRepo      *repository.TokenRepository
	governanceRepo *repository.GovernanceRepository

	// Event handlers
	eventHandlers map[common.Hash]EventHandler

	// Sync state
	lastSyncedBlock  uint64
	currentBatchSize uint64 // 动态批次大小
	isRunning        bool
}

// EventHandler 事件处理器接口
type EventHandler interface {
	Handle(ctx context.Context, log types.Log) error
}

// NewMultiEventListener 创建新的多事件监听器
func NewMultiEventListener(
	client *blockchain.MultiContractClient,
	cfg *config.BlockchainConfig,
	stakingRepo *repository.StakingRepository,
	eventRepo *repository.EventRepository,
	tokenRepo *repository.TokenRepository,
	governanceRepo *repository.GovernanceRepository,
	logger zerolog.Logger,
) *MultiEventListener {
	listener := &MultiEventListener{
		client:           client,
		config:           cfg,
		logger:           logger.With().Str("component", "multi_event_listener").Logger(),
		stakingRepo:      stakingRepo,
		eventRepo:        eventRepo,
		tokenRepo:        tokenRepo,
		governanceRepo:   governanceRepo,
		eventHandlers:    make(map[common.Hash]EventHandler),
		lastSyncedBlock:  cfg.StartBlock,
		currentBatchSize: uint64(cfg.BatchSize), // 初始化动态批次大小
	}

	// 注册事件处理器
	listener.registerEventHandlers()

	return listener
}

// registerEventHandlers 注册所有事件处理器
func (l *MultiEventListener) registerEventHandlers() {
	contracts := l.client.GetContracts()

	// 质押相关事件
	l.eventHandlers[contracts.ABIs.StakingPool.Events["Staked"].ID] = &StakedEventHandler{l}
	l.eventHandlers[contracts.ABIs.StakingPool.Events["Withdrawn"].ID] = &WithdrawnEventHandler{l}
	l.eventHandlers[contracts.ABIs.StakingPool.Events["RewardClaimed"].ID] = &RewardClaimedEventHandler{l}
	l.eventHandlers[contracts.ABIs.StakingPool.Events["StakingLevelUpdated"].ID] = &StakingLevelUpdatedEventHandler{l}
	l.eventHandlers[contracts.ABIs.StakingPool.Events["MiningPowerUpdated"].ID] = &MiningPowerUpdatedEventHandler{l}

	// 代币相关事件
	l.eventHandlers[contracts.ABIs.ERC20Token.Events["Transfer"].ID] = &TransferEventHandler{l}
	l.eventHandlers[contracts.ABIs.ERC20Token.Events["Approval"].ID] = &ApprovalEventHandler{l}

	// 池子相关事件 - 重要：监听池子转账活动
	// 注意：池子转账实际上也是Transfer事件，但我们需要特殊处理
	// 这里我们复用Transfer事件处理器，但在处理器内部会检查是否涉及池子地址

	// 治理相关事件
	l.eventHandlers[contracts.ABIs.ProposalManager.Events["ProposalCreated"].ID] = &ProposalCreatedEventHandler{l}
	l.eventHandlers[contracts.ABIs.Voting.Events["VoteCast"].ID] = &VoteCastEventHandler{l}
	l.eventHandlers[contracts.ABIs.ProposalManager.Events["ProposalExecuted"].ID] = &ProposalExecutedEventHandler{l}
	l.eventHandlers[contracts.ABIs.ProposalManager.Events["ProposalCancelled"].ID] = &ProposalCancelledEventHandler{l}

	// 奖励分配事件
	l.eventHandlers[contracts.ABIs.RewardDistributor.Events["RewardDistributed"].ID] = &RewardDistributedEventHandler{l}

	// 国库事件
	l.eventHandlers[contracts.ABIs.Treasury.Events["TreasuryTransfer"].ID] = &TreasuryTransferEventHandler{l}

	// 内容注册事件 - 监听ContentRegistry合约的ContentRegistered事件
	if event, exists := contracts.ABIs.ContentRegistry.Events["ContentRegistered"]; exists {
		l.eventHandlers[event.ID] = &ContentRegisteredEventHandler{l}
	}
}

// Start 启动事件监听
func (l *MultiEventListener) Start(ctx context.Context) error {
	l.logger.Info().Msg("Starting multi-contract event listener...")

	if l.isRunning {
		return fmt.Errorf("event listener is already running")
	}

	l.isRunning = true
	defer func() { l.isRunning = false }()

	// 获取最后同步的区块
	syncStatus, err := l.eventRepo.GetBlockSyncStatus(ctx)
	if err != nil {
		l.logger.Warn().Err(err).Msg("Failed to get sync status, using start block")
	} else if syncStatus != nil {
		l.lastSyncedBlock = syncStatus.LastSyncedBlock
	}

	l.logger.Info().Uint64("start_block", l.lastSyncedBlock).Msg("Event listener started")

	// 主监听循环
	// 使用默认的事件监听间隔（30秒）
	eventListenInterval := 30 * time.Second
	ticker := time.NewTicker(eventListenInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			l.logger.Info().Msg("Event listener stopped")
			return ctx.Err()

		case <-ticker.C:
			if err := l.syncEvents(ctx); err != nil {
				l.logger.Error().Err(err).Msg("Failed to sync events")
			}
		}
	}
}

// syncEvents 同步事件
func (l *MultiEventListener) syncEvents(ctx context.Context) error {
	// 获取最新区块号
	latestBlock, err := l.client.GetLatestBlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("failed to get latest block: %w", err)
	}

	// 计算确认后的区块
	confirmedBlock := latestBlock
	if latestBlock > l.config.ConfirmBlocks {
		confirmedBlock = latestBlock - l.config.ConfirmBlocks
	}

	// 如果没有新区块，跳过
	if confirmedBlock <= l.lastSyncedBlock {
		return nil
	}

	// 分批同步
	fromBlock := l.lastSyncedBlock + 1

	for fromBlock <= confirmedBlock {
		toBlock := fromBlock + l.currentBatchSize - 1
		if toBlock > confirmedBlock {
			toBlock = confirmedBlock
		}

		l.logger.Debug().
			Uint64("from_block", fromBlock).
			Uint64("to_block", toBlock).
			Uint64("batch_size", l.currentBatchSize).
			Msg("Syncing events batch")

		success, err := l.syncEventsBatch(ctx, fromBlock, toBlock)
		if err != nil {
			l.logger.Error().Err(err).
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Msg("Failed to sync events batch")

			// 如果批次大小大于1，减小批次大小并重试
			if l.currentBatchSize > 1 {
				l.currentBatchSize = l.currentBatchSize / 2
				if l.currentBatchSize < 1 {
					l.currentBatchSize = 1
				}
				l.logger.Info().
					Uint64("new_batch_size", l.currentBatchSize).
					Msg("Reduced batch size due to error, retrying")
				continue // 重试当前批次
			} else {
				// 单个区块也失败，记录错误但继续下一个区块
				l.logger.Warn().
					Uint64("block", fromBlock).
					Msg("Single block sync failed, skipping to next block")
			}
		}

		if !success {
			// 如果批次太大，减小批次大小并重试
			if l.currentBatchSize > 1 {
				l.currentBatchSize = l.currentBatchSize / 2
				if l.currentBatchSize < 1 {
					l.currentBatchSize = 1
				}
				l.logger.Info().
					Uint64("new_batch_size", l.currentBatchSize).
					Msg("Reduced batch size due to RPC limits")
				continue // 重试当前批次
			} else {
				// 批次已经是1个区块了，记录警告但继续
				l.logger.Warn().
					Uint64("from_block", fromBlock).
					Uint64("to_block", toBlock).
					Msg("Single block failed due to persistent RPC limits, continuing")
			}
		} else {
			// 成功处理，可以尝试增加批次大小
			maxBatchSize := uint64(l.config.BatchSize)
			if l.currentBatchSize < maxBatchSize {
				newSize := l.currentBatchSize * 2
				if newSize > maxBatchSize {
					newSize = maxBatchSize
				}
				l.currentBatchSize = newSize
			}
		}

		// 只有成功处理才更新同步状态
		if err == nil && success {
			if err := l.updateSyncStatus(ctx, toBlock); err != nil {
				l.logger.Warn().Err(err).Uint64("block", toBlock).Msg("Failed to update sync status")
			}
			l.lastSyncedBlock = toBlock
		}

		fromBlock = toBlock + 1
	}

	return nil
}

// syncEventsBatch 同步一批事件
// 返回 (success, error)，success表示是否成功处理，false表示需要减小批次大小
func (l *MultiEventListener) syncEventsBatch(ctx context.Context, fromBlock, toBlock uint64) (bool, error) {
	// 获取核心合约地址（包含治理合约） - 更新为最新部署地址 (2025-07-04)
	contracts := l.client.GetContracts()
	addresses := []common.Address{
		// 核心代币合约 - 重要：监听所有转账事件
		contracts.Addresses.PXTToken, // PXT 治理代币
		contracts.Addresses.PATToken, // PAT 功能代币

		// 质押系统合约 - 重要：监听质押和奖励事件
		contracts.Addresses.StakingPool,       // 质押池合约
		contracts.Addresses.RewardDistributor, // 奖励分配器

		// 治理系统合约 - 重要：监听治理相关事件
		contracts.Addresses.ProposalManager, // 提案管理器
		contracts.Addresses.Voting,          // 投票合约
		contracts.Addresses.DAO,             // DAO 主合约

		// 内容系统合约 - 重要：监听内容注册事件
		contracts.Addresses.ContentRegistry, // 内容注册合约

		// 池子地址 - 重要：监听池子转账和奖励分发
		contracts.Addresses.ChinaMainlandPool, // 中国大陆池子
		contracts.Addresses.GlobalPool,        // 全球池子

		// 暂时注释掉一些合约，避免RPC限制
		// contracts.Addresses.Treasury,       // 国库合约
		// contracts.Addresses.TokenRegistry,  // 代币注册表
		// contracts.Addresses.StakingFactory, // 质押工厂
	}

	// 构建过滤查询
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: addresses,
	}

	// 获取日志，添加重试机制
	logs, err := l.client.GetClient().FilterLogs(ctx, query)
	if err != nil {
		// 如果是限制错误，返回false表示需要减小批次大小
		if strings.Contains(err.Error(), "limit exceeded") || strings.Contains(err.Error(), "query returned more than") {
			l.logger.Warn().
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Uint64("range", toBlock-fromBlock+1).
				Int("contract_count", len(addresses)).
				Str("error", err.Error()).
				Msg("Batch size too large, need to reduce batch size")
			return false, nil // 返回false表示需要减小批次大小
		}
		l.logger.Error().
			Err(err).
			Uint64("from_block", fromBlock).
			Uint64("to_block", toBlock).
			Msg("Failed to filter logs with unknown error")
		return false, fmt.Errorf("failed to filter logs: %w", err)
	}

	l.logger.Debug().
		Int("logs_count", len(logs)).
		Uint64("from_block", fromBlock).
		Uint64("to_block", toBlock).
		Msg("Processing events batch")

	// 处理每个日志
	for _, log := range logs {
		if err := l.processLog(ctx, log); err != nil {
			l.logger.Error().
				Err(err).
				Str("tx_hash", log.TxHash.Hex()).
				Uint("log_index", log.Index).
				Msg("Failed to process log")
			// 继续处理其他日志，不中断整个批次
		}
	}

	return true, nil // 成功处理
}

// processLog 处理单个日志
func (l *MultiEventListener) processLog(ctx context.Context, log types.Log) error {
	// 检查是否有对应的事件处理器
	if len(log.Topics) == 0 {
		return nil
	}

	eventSignature := log.Topics[0]
	handler, exists := l.eventHandlers[eventSignature]
	if !exists {
		// 未知事件，记录但不处理
		l.logger.Debug().
			Str("event_signature", eventSignature.Hex()).
			Str("contract", log.Address.Hex()).
			Msg("Unknown event signature")
		return nil
	}

	// 处理事件
	return handler.Handle(ctx, log)
}

// updateSyncStatus 更新同步状态
func (l *MultiEventListener) updateSyncStatus(ctx context.Context, blockNumber uint64) error {
	return l.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "syncing", "")
}

// Stop 停止事件监听
func (l *MultiEventListener) Stop() {
	l.logger.Info().Msg("Stopping event listener...")
	l.isRunning = false
}

// IsRunning 检查是否正在运行
func (l *MultiEventListener) IsRunning() bool {
	return l.isRunning
}

// GetLastSyncedBlock 获取最后同步的区块
func (l *MultiEventListener) GetLastSyncedBlock() uint64 {
	return l.lastSyncedBlock
}
