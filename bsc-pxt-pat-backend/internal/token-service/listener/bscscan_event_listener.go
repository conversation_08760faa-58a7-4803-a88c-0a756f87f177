package listener

import (
	"context"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/external"
	"pxpat-backend/internal/token-service/models"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/utils"
)

// BSCScanEventListener 基于BSCScan API的事件监听器
type BSCScanEventListener struct {
	bscClient  *external.BSCScanClient
	config     *config.BlockchainConfig
	logger     zerolog.Logger
	syncLogger *utils.SyncLogger // 🆕 彩色同步日志器

	// Repositories
	stakingRepo    *repository.StakingRepository
	eventRepo      *repository.EventRepository
	tokenRepo      *repository.TokenRepository
	governanceRepo *repository.GovernanceRepository

	// Event handlers
	eventHandlers map[common.Hash]EventHandler

	// Sync state
	lastSyncedBlock uint64
	isRunning       bool

	// Rate limiting
	currentBatchSize uint64
	lastAPICallTime  time.Time
	rateLimitDelay   time.Duration

	// 🆕 区块时间戳缓存，减少API调用
	blockTimestampCache map[uint64]time.Time
}

// NewBSCScanEventListener 创建新的BSCScan事件监听器
func NewBSCScanEventListener(
	cfg *config.BlockchainConfig,
	stakingRepo *repository.StakingRepository,
	eventRepo *repository.EventRepository,
	tokenRepo *repository.TokenRepository,
	governanceRepo *repository.GovernanceRepository,
	logger zerolog.Logger,
) *BSCScanEventListener {
	bscClient := external.NewBSCScanClient(cfg.BSCScanAPIKey, logger)

	// 根据网络设置API端点
	if cfg.Network == "bsc_testnet" {
		bscClient.SetTestnet()
	} else {
		bscClient.SetMainnet()
	}

	// 创建彩色同步日志器
	syncLoggerConfig := utils.SyncLoggerConfig{
		EnableColorfulLogs:      true,
		ShowBlockProgress:       true,
		LogSyncProgressInterval: 50,
		LogTransferDetails:      true,
		LogBalanceUpdates:       false,
	}
	syncLogger := utils.NewSyncLogger(logger, syncLoggerConfig)

	listener := &BSCScanEventListener{
		bscClient:           bscClient,
		config:              cfg,
		logger:              logger.With().Str("component", "bscscan_event_listener").Logger(),
		syncLogger:          syncLogger,
		stakingRepo:         stakingRepo,
		eventRepo:           eventRepo,
		tokenRepo:           tokenRepo,
		governanceRepo:      governanceRepo,
		eventHandlers:       make(map[common.Hash]EventHandler),
		lastSyncedBlock:     cfg.StartBlock,
		currentBatchSize:    uint64(cfg.BatchSize),
		rateLimitDelay:      3 * time.Second,            // BSCScan 限制 2 calls/sec，增加到3秒确保安全
		blockTimestampCache: make(map[uint64]time.Time), // 🆕 初始化区块时间戳缓存
	}

	// 注册事件处理器（复用现有的处理器）
	listener.registerEventHandlers()

	return listener
}

// GetCachedBlockTimestamp 获取缓存的区块时间戳，减少API调用
func (l *BSCScanEventListener) GetCachedBlockTimestamp(ctx context.Context, blockNumber uint64) (time.Time, error) {
	// 先检查缓存
	if timestamp, exists := l.blockTimestampCache[blockNumber]; exists {
		return timestamp, nil
	}

	// 缓存未命中，调用API获取
	blockInfo, err := l.bscClient.GetBlockByNumber(ctx, blockNumber)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get block info: %w", err)
	}

	// 解析时间戳
	if blockInfo.Timestamp == "" {
		return time.Time{}, fmt.Errorf("empty timestamp in block info")
	}

	timestampHex := strings.TrimPrefix(blockInfo.Timestamp, "0x")
	timestampInt, err := strconv.ParseInt(timestampHex, 16, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	timestamp := time.Unix(timestampInt, 0)

	// 缓存结果（限制缓存大小，避免内存泄漏）
	if len(l.blockTimestampCache) > 1000 {
		// 清理一半的缓存
		for k := range l.blockTimestampCache {
			delete(l.blockTimestampCache, k)
			if len(l.blockTimestampCache) <= 500 {
				break
			}
		}
	}
	l.blockTimestampCache[blockNumber] = timestamp

	return timestamp, nil
}

// registerEventHandlers 注册事件处理器
func (l *BSCScanEventListener) registerEventHandlers() {
	// Transfer事件处理器
	transferTopic := common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")
	l.eventHandlers[transferTopic] = &BSCScanTransferEventHandler{
		listener: l,
	}

	// 质押相关事件处理器
	// Staked事件: 实际的事件签名（从BSCScan获取）
	stakedTopic := common.HexToHash("0x28c92e6adbdb7af267ba584f4118b04e5a97c97568ce192b5eeba798c265ca50")
	l.eventHandlers[stakedTopic] = &BSCScanStakedEventHandler{
		listener: l,
	}

	// Withdrawn事件: 需要从实际事件中确认签名
	withdrawnTopic := common.HexToHash("0xbde7f0ba1630d25515c7ab99ba47d5640b7ffb4c673b2a5464ae679195589298")
	l.eventHandlers[withdrawnTopic] = &BSCScanWithdrawnEventHandler{
		listener: l,
	}

	// RewardClaimed事件: 需要从实际事件中确认签名
	rewardClaimedTopic := common.HexToHash("0x8240b028a99c4e9072aa34709c50adfe75e1cf4000aa5492e79ccd3ddb49fa5a")
	l.eventHandlers[rewardClaimedTopic] = &BSCScanRewardClaimedEventHandler{
		listener: l,
	}

	// 治理相关事件处理器
	// ProposalCreated事件: ProposalCreated(uint256,address,uint8,uint256,uint256,string,string)
	proposalCreatedTopic := common.HexToHash("0xd57021c6feb9daa5c828ba4ebebc1c54cc1a9318d0a6104045fc439dfd98e405")
	l.eventHandlers[proposalCreatedTopic] = &BSCScanProposalCreatedEventHandler{
		listener: l,
	}

	// VoteCast事件: VoteCast(uint256,address,uint8,uint256,string)
	voteCastTopic := common.HexToHash("0x761cce3a1af5d9cfc0d57222f605ce4bd2be345f0ad9ebb7b83a84a91a0c2014")
	l.eventHandlers[voteCastTopic] = &BSCScanVoteCastEventHandler{
		listener: l,
	}

	// ProposalExecuted事件: ProposalExecuted(uint256)
	proposalExecutedTopic := common.HexToHash("0x712ae1383f79ac853f8d882153778e0260ef8f03b504e2866e0593e04d2b291f")
	l.eventHandlers[proposalExecutedTopic] = &BSCScanProposalExecutedEventHandler{
		listener: l,
	}

	// ContentRegistered事件: ContentRegistered(uint256,address,string,string,uint256)
	// 实际事件签名哈希从BSCScan获取: 0x46985f4104a2f6e2306667ac59cd130f9c4a6d1a0face4a30dcdbdb7b1aa661d
	contentRegisteredTopic := common.HexToHash("0x46985f4104a2f6e2306667ac59cd130f9c4a6d1a0face4a30dcdbdb7b1aa661d")
	l.eventHandlers[contentRegisteredTopic] = &BSCScanContentRegisteredEventHandler{
		listener: l,
	}
}

// Start 启动事件监听
func (l *BSCScanEventListener) Start(ctx context.Context) error {
	l.logger.Info().Msg("Starting BSCScan event listener...")

	if l.isRunning {
		return fmt.Errorf("BSCScan event listener is already running")
	}

	l.isRunning = true
	defer func() { l.isRunning = false }()

	// 获取最后同步的区块
	syncStatus, err := l.eventRepo.GetBlockSyncStatus(ctx)
	if err != nil {
		l.logger.Warn().Err(err).Msg("Failed to get sync status, using start block")
	} else if syncStatus != nil {
		l.lastSyncedBlock = syncStatus.LastSyncedBlock
	}

	l.logger.Info().Uint64("start_block", l.lastSyncedBlock).Msg("BSCScan event listener started")

	// 主监听循环
	ticker := time.NewTicker(time.Duration(l.config.Sync.SyncInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			l.logger.Info().Msg("BSCScan event listener stopped")
			return ctx.Err()

		case <-ticker.C:
			if err := l.syncEvents(ctx); err != nil {
				l.logger.Error().Err(err).Msg("Failed to sync events")
			}
		}
	}
}

// syncEvents 同步事件
func (l *BSCScanEventListener) syncEvents(ctx context.Context) error {
	// 获取最新区块号
	latestBlock, err := l.bscClient.GetLatestBlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("failed to get latest block: %w", err)
	}

	// 计算确认后的区块
	confirmedBlock := latestBlock
	if latestBlock > l.config.ConfirmBlocks {
		confirmedBlock = latestBlock - l.config.ConfirmBlocks
	}

	// 如果没有新区块，跳过
	if confirmedBlock <= l.lastSyncedBlock {
		return nil
	}

	// 分批同步
	fromBlock := l.lastSyncedBlock + 1
	batchSize := l.currentBatchSize

	// 显示同步进度
	if confirmedBlock > l.lastSyncedBlock {
		progress := float64(l.lastSyncedBlock-l.config.StartBlock) / float64(confirmedBlock-l.config.StartBlock) * 100
		l.syncLogger.LogSyncProgress(l.lastSyncedBlock, confirmedBlock, progress)
	}

	for fromBlock <= confirmedBlock {
		// 速率限制：确保两次 API 调用之间有足够间隔
		if time.Since(l.lastAPICallTime) < l.rateLimitDelay {
			sleepTime := l.rateLimitDelay - time.Since(l.lastAPICallTime)
			l.logger.Debug().
				Dur("sleep_time", sleepTime).
				Msg("Rate limiting: sleeping before next API call")
			time.Sleep(sleepTime)
		}

		toBlock := fromBlock + batchSize - 1
		if toBlock > confirmedBlock {
			toBlock = confirmedBlock
		}

		// 只在有较大区块范围时才记录日志，减少日志输出
		if toBlock-fromBlock >= 1000 {
			l.logger.Info().
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Uint64("batch_size", batchSize).
				Msg("Syncing large events batch with BSCScan")
		}

		// 记录 API 调用时间
		l.lastAPICallTime = time.Now()

		_, err := l.syncEventsBatch(ctx, fromBlock, toBlock)
		if err != nil {
			l.logger.Error().Err(err).
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Msg("Failed to sync events batch")

			// 如果批次大小大于1，减小批次大小并重试
			if batchSize > 1 {
				batchSize = batchSize / 2
				if batchSize < 1 {
					batchSize = 1
				}
				l.currentBatchSize = batchSize // 更新当前批次大小
				l.logger.Info().
					Uint64("new_batch_size", batchSize).
					Msg("Reduced batch size due to sync error, retrying")
				continue // 重试当前批次
			} else {
				// 单个区块也失败，实施重试策略而不是跳过
				retryCount := 0
				maxRetries := 5
				retryDelay := time.Second * 2

				for retryCount < maxRetries {
					retryCount++
					l.logger.Warn().
						Uint64("block", fromBlock).
						Int("retry", retryCount).
						Int("max_retries", maxRetries).
						Msg("Single block sync failed, retrying...")

					// 等待一段时间后重试
					time.Sleep(retryDelay)

					// 重试同步单个区块
					_, retryErr := l.syncEventsBatch(ctx, fromBlock, fromBlock)
					if retryErr == nil {
						l.logger.Info().
							Uint64("block", fromBlock).
							Int("retry", retryCount).
							Msg("Single block sync succeeded on retry")
						err = nil // 清除错误，表示成功
						break
					}

					// 指数退避
					retryDelay *= 2
					if retryDelay > time.Minute {
						retryDelay = time.Minute
					}
				}

				// 如果所有重试都失败，记录严重错误但不跳过区块
				if err != nil {
					l.logger.Error().
						Uint64("block", fromBlock).
						Int("retries", maxRetries).
						Msg("Single block sync failed after all retries - CRITICAL: Block will be marked for manual review")

					// 记录失败的区块到数据库，供后续手动处理
					if markErr := l.markBlockAsFailed(ctx, fromBlock, err.Error()); markErr != nil {
						l.logger.Error().Err(markErr).Uint64("block", fromBlock).Msg("Failed to mark block as failed")
					}

					// 即使失败也要继续，但不更新同步状态
					// 这样下次启动时会重新尝试这个区块
					fromBlock = toBlock + 1
					continue
				}
			}
		}

		// 只有成功处理才更新同步状态
		if err == nil {
			if err := l.updateSyncStatus(ctx, toBlock); err != nil {
				l.logger.Warn().Err(err).Uint64("block", toBlock).Msg("Failed to update sync status")
			}
			l.lastSyncedBlock = toBlock

			// 成功时，逐渐增加批次大小（但不超过配置的最大值）
			maxBatchSize := uint64(l.config.BatchSize)
			if batchSize < maxBatchSize {
				batchSize = batchSize * 2
				if batchSize > maxBatchSize {
					batchSize = maxBatchSize
				}
				l.currentBatchSize = batchSize
				l.logger.Debug().
					Uint64("new_batch_size", batchSize).
					Msg("Increased batch size after success")
			}
		}

		fromBlock = toBlock + 1
	}

	return nil
}

// syncEventsBatch 同步一批事件，返回处理的日志数量和错误
func (l *BSCScanEventListener) syncEventsBatch(ctx context.Context, fromBlock, toBlock uint64) (int, error) {
	// 获取合约地址列表 - 专注于代币合约，减少 API 调用 (2025-07-04)
	contracts := []string{
		// 核心代币合约 - 最重要：监听所有转账事件
		l.config.Contracts.PXTToken, // PXT 治理代币
		l.config.Contracts.PATToken, // PAT 功能代币

		// 内容系统合约 - 重要：监听内容注册事件
		l.config.Contracts.ContentRegistry, // ContentRegistry 合约

		// 池子地址 - 重要：监听池子转账和奖励分发
		l.config.Contracts.ChinaMainlandPool, // 中国大陆池子
		l.config.Contracts.GlobalPool,        // 全球池子

		// 暂时注释其他合约，专注于转账事件同步
		// 等转账同步稳定后再逐步启用其他合约

		// l.config.Contracts.StakingPool,       // 质押池合约
		// l.config.Contracts.RewardDistributor, // 奖励分配器
		// l.config.Contracts.ProposalManager,   // 提案管理器
		// l.config.Contracts.Voting,            // 投票合约
		// l.config.Contracts.DAO,               // DAO 主合约
		// l.config.Contracts.Treasury,          // 国库合约
	}

	totalLogs := 0

	// 为每个合约获取事件
	for _, contractAddr := range contracts {
		if contractAddr == "" {
			continue
		}

		logs, err := l.bscClient.GetContractLogs(ctx, external.LogQueryParams{
			Address:   contractAddr,
			FromBlock: fromBlock,
			ToBlock:   toBlock,
			Topics:    []string{}, // 获取所有事件
		})

		if err != nil {
			l.logger.Warn().Err(err).
				Str("contract", contractAddr).
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Msg("Failed to get logs for contract")
			continue
		}

		totalLogs += len(logs)

		// 处理每个日志
		for _, log := range logs {
			if err := l.processLog(ctx, log); err != nil {
				l.logger.Error().
					Err(err).
					Str("tx_hash", log.TransactionHash).
					Str("contract", contractAddr).
					Msg("Failed to process log")
				// 继续处理其他日志
			}
		}
	}

	// 使用彩色日志显示区块同步进度
	if totalLogs > 0 {
		l.syncLogger.LogBlockSync(fromBlock, toBlock, totalLogs, totalLogs)
	} else if toBlock-fromBlock >= 1000 {
		l.syncLogger.LogBlockSync(fromBlock, toBlock, 0, 0)
	}

	return totalLogs, nil
}

// processLog 处理单个日志
func (l *BSCScanEventListener) processLog(ctx context.Context, log external.LogEntry) error {
	// 转换BSCScan日志格式为以太坊日志格式
	ethLog, err := l.convertToEthLog(log)
	if err != nil {
		return fmt.Errorf("failed to convert log: %w", err)
	}

	// 检查是否有对应的事件处理器
	if len(ethLog.Topics) == 0 {
		return nil
	}

	eventSignature := ethLog.Topics[0]
	handler, exists := l.eventHandlers[eventSignature]
	if !exists {
		// 未知事件，记录但不处理
		l.logger.Debug().
			Str("event_signature", eventSignature.Hex()).
			Str("contract", ethLog.Address.Hex()).
			Msg("Unknown event signature")
		return nil
	}

	// 处理事件
	return handler.Handle(ctx, ethLog)
}

// convertToEthLog 将BSCScan日志转换为以太坊日志格式
func (l *BSCScanEventListener) convertToEthLog(log external.LogEntry) (types.Log, error) {
	// 解析区块号
	blockNumber, err := strconv.ParseUint(log.BlockNumber[2:], 16, 64) // 去掉"0x"前缀
	if err != nil {
		return types.Log{}, fmt.Errorf("failed to parse block number: %w", err)
	}

	// 解析日志索引 - 处理空字符串的情况
	var logIndex uint64
	if log.LogIndex != "" && log.LogIndex != "0x" {
		var err error
		logIndex, err = strconv.ParseUint(log.LogIndex[2:], 16, 64)
		if err != nil {
			return types.Log{}, fmt.Errorf("failed to parse log index: %w", err)
		}
	} else {
		// 如果LogIndex为空，使用0作为默认值
		logIndex = 0
	}

	// 解析交易索引 - 处理空字符串的情况
	var txIndex uint64
	if log.TransactionIndex != "" && log.TransactionIndex != "0x" {
		var err error
		txIndex, err = strconv.ParseUint(log.TransactionIndex[2:], 16, 64)
		if err != nil {
			return types.Log{}, fmt.Errorf("failed to parse transaction index: %w", err)
		}
	} else {
		// 如果TransactionIndex为空，使用0作为默认值
		txIndex = 0
	}

	// 转换主题
	topics := make([]common.Hash, len(log.Topics))
	for i, topic := range log.Topics {
		topics[i] = common.HexToHash(topic)
	}

	// 转换数据
	data := common.FromHex(log.Data)

	return types.Log{
		Address:     common.HexToAddress(log.Address),
		Topics:      topics,
		Data:        data,
		BlockNumber: blockNumber,
		TxHash:      common.HexToHash(log.TransactionHash),
		TxIndex:     uint(txIndex),
		BlockHash:   common.Hash{}, // BSCScan API不提供BlockHash
		Index:       uint(logIndex),
		Removed:     false,
	}, nil
}

// updateSyncStatus 更新同步状态
func (l *BSCScanEventListener) updateSyncStatus(ctx context.Context, blockNumber uint64) error {
	return l.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "syncing", "")
}

// markBlockAsFailed 标记区块为失败状态
func (l *BSCScanEventListener) markBlockAsFailed(ctx context.Context, blockNumber uint64, errorMsg string) error {
	l.logger.Error().
		Uint64("block", blockNumber).
		Str("error", errorMsg).
		Msg("Marking block as failed for manual review")

	// 更新同步状态为失败
	return l.eventRepo.UpdateBlockSyncStatus(ctx, blockNumber, "failed", errorMsg)
}

// Stop 停止事件监听
func (l *BSCScanEventListener) Stop() {
	l.logger.Info().Msg("Stopping BSCScan event listener...")
	l.isRunning = false
}

// IsRunning 检查是否正在运行
func (l *BSCScanEventListener) IsRunning() bool {
	return l.isRunning
}

// GetLastSyncedBlock 获取最后同步的区块
func (l *BSCScanEventListener) GetLastSyncedBlock() uint64 {
	return l.lastSyncedBlock
}

// ===== BSCScan专用事件处理器 =====

// BSCScanTransferEventHandler BSCScan转账事件处理器
type BSCScanTransferEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理转账事件
func (h *BSCScanTransferEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析Transfer事件的参数
	if len(log.Topics) < 3 {
		return fmt.Errorf("invalid Transfer event: insufficient topics")
	}

	// 解析事件参数
	fromAddress := common.HexToAddress(log.Topics[1].Hex())
	toAddress := common.HexToAddress(log.Topics[2].Hex())

	// 解析金额（从Data字段）
	if len(log.Data) < 32 {
		return fmt.Errorf("invalid Transfer event: insufficient data")
	}

	// 金额是32字节的大端序整数
	amount := common.BytesToHash(log.Data[:32]).Big()

	// 确定代币类型
	tokenType := h.getTokenType(log.Address)
	if tokenType == "" {
		return nil // 不是我们关心的代币
	}

	// 获取真实的区块时间戳（使用缓存）
	blockTimestamp, err := h.listener.GetCachedBlockTimestamp(ctx, log.BlockNumber)
	if err != nil {
		h.listener.logger.Warn().
			Err(err).
			Uint64("block_number", log.BlockNumber).
			Msg("Failed to get block timestamp, using current time")
		blockTimestamp = time.Now()
	}

	// 创建转账记录
	transfer := &models.TokenTransfer{
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint64(log.Index),
		TokenType:       tokenType,
		FromAddress:     strings.ToLower(fromAddress.Hex()),
		ToAddress:       strings.ToLower(toAddress.Hex()),
		Amount:          amount.String(),
		Timestamp:       blockTimestamp,
	}

	// 保存转账记录
	if err := h.listener.tokenRepo.CreateTransfer(ctx, transfer); err != nil {
		return fmt.Errorf("failed to create transfer record: %w", err)
	}

	// 更新相关地址的余额
	if err := h.updateBalances(ctx, fromAddress, toAddress, tokenType); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to update balances")
	}

	// 只在调试模式下记录详细的转账信息
	if h.listener.logger.GetLevel() <= zerolog.DebugLevel {
		h.listener.logger.Debug().
			Str("from", fromAddress.Hex()).
			Str("to", toAddress.Hex()).
			Str("amount", amount.String()).
			Str("token", tokenType).
			Str("tx_hash", log.TxHash.Hex()).
			Msg("Transfer event processed")
	}

	return nil
}

// getTokenType 根据合约地址确定代币类型
func (h *BSCScanTransferEventHandler) getTokenType(address common.Address) string {
	addressStr := strings.ToLower(address.Hex())

	if addressStr == strings.ToLower(h.listener.config.Contracts.PXTToken) {
		return "PXT"
	} else if addressStr == strings.ToLower(h.listener.config.Contracts.PATToken) {
		return "PAT"
	}

	return ""
}

// getBlockTimestamp 获取区块的真实时间戳
func (h *BSCScanTransferEventHandler) getBlockTimestamp(ctx context.Context, blockNumber uint64) (time.Time, error) {
	// 使用BSCScan客户端获取区块信息
	blockInfo, err := h.listener.bscClient.GetBlockByNumber(ctx, blockNumber)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get block info: %w", err)
	}

	// 解析时间戳（十六进制）
	if blockInfo.Timestamp == "" {
		return time.Time{}, fmt.Errorf("empty timestamp in block info")
	}

	// 移除0x前缀
	timestampHex := strings.TrimPrefix(blockInfo.Timestamp, "0x")

	// 转换为十进制
	timestampInt, err := strconv.ParseInt(timestampHex, 16, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	return time.Unix(timestampInt, 0), nil
}

// updateBalances 更新转账相关地址的余额
// 注意：BSCScan监听器主要负责事件同步，余额更新由同步服务处理
func (h *BSCScanTransferEventHandler) updateBalances(ctx context.Context, from, to common.Address, tokenType string) error {
	// BSCScan监听器暂时跳过余额更新，因为：
	// 1. BSCScan API不支持直接获取余额
	// 2. 余额更新由同步服务的syncTokenBalances方法处理
	// 3. 这样可以避免重复的RPC调用

	// 移除余额更新的调试日志，减少输出
	// h.listener.logger.Debug().
	//	Str("from", from.Hex()).
	//	Str("to", to.Hex()).
	//	Str("token", tokenType).
	//	Msg("Transfer recorded, balance update will be handled by sync service")

	return nil
}

// BSCScanStakedEventHandler BSCScan质押事件处理器
type BSCScanStakedEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理质押事件
func (h *BSCScanStakedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析Staked事件的参数
	if len(log.Topics) < 2 {
		return fmt.Errorf("invalid Staked event: insufficient topics")
	}

	// 解析用户地址（indexed参数）
	userAddress := common.HexToAddress(log.Topics[1].Hex())

	// 解析Data字段中的参数：根据实际事件结构，包含两个uint256值
	if len(log.Data) < 64 { // 2个uint256参数，每个32字节
		return fmt.Errorf("invalid Staked event: insufficient data")
	}

	// 解析参数
	amount := common.BytesToHash(log.Data[0:32]).Big()
	var secondValue *big.Int
	if len(log.Data) >= 64 {
		secondValue = common.BytesToHash(log.Data[32:64]).Big()
	}

	// 获取区块时间戳（这里使用BSCScan API获取）
	timestamp := time.Now() // 临时使用当前时间，后续可以优化为获取实际区块时间

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(userAddress.Hex()),
		EventType:       models.EventTypeStaked,
		Amount:          models.NewBigInt(amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       timestamp,
	}

	// 保存事件
	if err := h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 更新用户质押信息（注意：BSCScan监听器暂时跳过合约调用，由同步服务处理）
	h.listener.logger.Debug().
		Str("user", userAddress.Hex()).
		Msg("User stake update will be handled by sync service")

	h.listener.logger.Info().
		Str("user", userAddress.Hex()).
		Str("amount", amount.String()).
		Str("second_value", secondValue.String()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Staked event processed")

	return nil
}

// BSCScanWithdrawnEventHandler BSCScan提取事件处理器
type BSCScanWithdrawnEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理提取事件
func (h *BSCScanWithdrawnEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析Withdrawn事件的参数
	if len(log.Topics) < 2 {
		return fmt.Errorf("invalid Withdrawn event: insufficient topics")
	}

	// 解析用户地址（indexed参数）
	userAddress := common.HexToAddress(log.Topics[1].Hex())

	// 解析Data字段中的金额
	if len(log.Data) < 32 {
		return fmt.Errorf("invalid Withdrawn event: insufficient data")
	}

	amount := common.BytesToHash(log.Data[0:32]).Big()
	timestamp := time.Now() // 临时使用当前时间

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(userAddress.Hex()),
		EventType:       models.EventTypeWithdrawn,
		Amount:          models.NewBigInt(amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       timestamp,
	}

	// 保存事件
	if err := h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	h.listener.logger.Info().
		Str("user", userAddress.Hex()).
		Str("amount", amount.String()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Withdrawn event processed")

	return nil
}

// BSCScanRewardClaimedEventHandler BSCScan奖励领取事件处理器
type BSCScanRewardClaimedEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理奖励领取事件
func (h *BSCScanRewardClaimedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析RewardClaimed事件的参数
	if len(log.Topics) < 2 {
		return fmt.Errorf("invalid RewardClaimed event: insufficient topics")
	}

	// 解析用户地址（indexed参数）
	userAddress := common.HexToAddress(log.Topics[1].Hex())

	// 解析Data字段中的金额
	if len(log.Data) < 32 {
		return fmt.Errorf("invalid RewardClaimed event: insufficient data")
	}

	amount := common.BytesToHash(log.Data[0:32]).Big()
	timestamp := time.Now() // 临时使用当前时间

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(userAddress.Hex()),
		EventType:       models.EventTypeRewardClaimed,
		Amount:          models.NewBigInt(amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       timestamp,
	}

	// 保存事件
	if err := h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	h.listener.logger.Info().
		Str("user", userAddress.Hex()).
		Str("amount", amount.String()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("RewardClaimed event processed")

	return nil
}

// ===== BSCScan治理事件处理器 =====

// BSCScanProposalCreatedEventHandler BSCScan提案创建事件处理器
type BSCScanProposalCreatedEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理提案创建事件
func (h *BSCScanProposalCreatedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 直接调用处理逻辑
	return h.handleProposalCreatedEvent(ctx, log)
}

// handleProposalCreatedEvent 处理提案创建事件的具体逻辑
func (h *BSCScanProposalCreatedEventHandler) handleProposalCreatedEvent(ctx context.Context, log types.Log) error {
	// 解析事件参数
	if len(log.Topics) < 3 {
		return fmt.Errorf("invalid ProposalCreated event: insufficient topics")
	}

	// 从Topics解析indexed参数
	proposalId := new(big.Int).SetBytes(log.Topics[1][:])
	proposer := common.HexToAddress(log.Topics[2].Hex())

	// 从Data解析非indexed参数
	if len(log.Data) < 32*5 { // 至少需要5个32字节的参数
		return fmt.Errorf("invalid ProposalCreated event: insufficient data")
	}

	// 解析Data字段中的参数
	proposalType := uint8(common.BytesToHash(log.Data[0:32]).Big().Uint64())
	// startTime := common.BytesToHash(log.Data[32:64]).Big()
	// endTime := common.BytesToHash(log.Data[64:96]).Big()

	// 字符串参数需要特殊处理（ABI编码）
	// 这里简化处理，使用默认值
	title := fmt.Sprintf("Proposal #%d", proposalId.Uint64())
	description := "Proposal created via BSCScan event listener"

	// 获取区块时间戳（使用缓存）
	blockTimestamp, err := h.listener.GetCachedBlockTimestamp(ctx, log.BlockNumber)
	if err != nil {
		h.listener.logger.Warn().
			Err(err).
			Uint64("block_number", log.BlockNumber).
			Msg("Failed to get block timestamp, using current time")
		blockTimestamp = time.Now()
	}

	// 检查提案是否已存在
	existingProposal, err := h.listener.governanceRepo.GetProposal(ctx, proposalId.Uint64())
	if err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to check existing proposal")
	}

	if existingProposal != nil {
		// 提案已存在，跳过
		h.listener.logger.Debug().
			Uint64("proposal_id", proposalId.Uint64()).
			Msg("Proposal already exists, skipping")
		return nil
	}

	// 创建新提案记录
	proposal := &models.Proposal{
		ProposalID:       proposalId.Uint64(),
		Proposer:         strings.ToLower(proposer.Hex()),
		Title:            title,
		Description:      description,
		Category:         h.getProposalCategory(proposalType),
		Status:           "active",
		ForVotes:         "0",
		AgainstVotes:     "0",
		AbstainVotes:     "0",
		TotalVotes:       "0",
		QuorumRequired:   "1000000", // 默认值
		MajorityRequired: 0.5,       // 默认值
		StartBlock:       uint64(log.BlockNumber),
		EndBlock:         uint64(log.BlockNumber) + 40320, // 假设7天
		CreatedAt:        blockTimestamp,
		UpdatedAt:        blockTimestamp,
	}

	// 保存提案
	if err := h.listener.governanceRepo.CreateProposal(ctx, proposal); err != nil {
		return fmt.Errorf("failed to create proposal: %w", err)
	}

	h.listener.logger.Info().
		Uint64("proposal_id", proposal.ProposalID).
		Str("title", proposal.Title).
		Str("proposer", proposal.Proposer).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Proposal created event processed successfully")

	return nil
}

// getBlockTimestamp 获取区块的真实时间戳
func (h *BSCScanProposalCreatedEventHandler) getBlockTimestamp(ctx context.Context, blockNumber uint64) (time.Time, error) {
	blockInfo, err := h.listener.bscClient.GetBlockByNumber(ctx, blockNumber)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get block info: %w", err)
	}

	if blockInfo.Timestamp == "" {
		return time.Time{}, fmt.Errorf("empty timestamp in block info")
	}

	timestampHex := strings.TrimPrefix(blockInfo.Timestamp, "0x")
	timestampInt, err := strconv.ParseInt(timestampHex, 16, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	return time.Unix(timestampInt, 0), nil
}

// getProposalCategory 根据提案类型获取分类
func (h *BSCScanProposalCreatedEventHandler) getProposalCategory(proposalType uint8) string {
	switch proposalType {
	case 0:
		return "parameter"
	case 1:
		return "upgrade"
	case 2:
		return "treasury"
	default:
		return "other"
	}
}

// BSCScanVoteCastEventHandler BSCScan投票事件处理器
type BSCScanVoteCastEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理投票事件
func (h *BSCScanVoteCastEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析事件参数
	if len(log.Topics) < 3 {
		return fmt.Errorf("invalid VoteCast event: insufficient topics")
	}

	// 从Topics解析indexed参数
	proposalId := new(big.Int).SetBytes(log.Topics[1][:])
	voter := common.HexToAddress(log.Topics[2].Hex())

	// 从Data解析非indexed参数
	if len(log.Data) < 32*3 { // 至少需要3个32字节的参数
		return fmt.Errorf("invalid VoteCast event: insufficient data")
	}

	support := uint8(common.BytesToHash(log.Data[0:32]).Big().Uint64())
	votingPower := common.BytesToHash(log.Data[32:64]).Big()
	// reason字符串参数暂时跳过，使用空字符串

	// 获取区块时间戳（使用缓存）
	blockTimestamp, err := h.listener.GetCachedBlockTimestamp(ctx, log.BlockNumber)
	if err != nil {
		h.listener.logger.Warn().
			Err(err).
			Uint64("block_number", log.BlockNumber).
			Msg("Failed to get block timestamp, using current time")
		blockTimestamp = time.Now()
	}

	// 检查投票是否已存在（通过提案ID和投票者地址）
	existingVote, err := h.listener.governanceRepo.GetVote(ctx, proposalId.Uint64(), strings.ToLower(voter.Hex()))
	if err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to check existing vote")
	}

	if existingVote != nil {
		// 投票已存在，跳过
		h.listener.logger.Debug().
			Uint64("proposal_id", proposalId.Uint64()).
			Str("voter", voter.Hex()).
			Msg("Vote already exists, skipping")
		return nil
	}

	// 创建新投票记录
	vote := &models.Vote{
		ProposalID:      proposalId.Uint64(),
		Voter:           strings.ToLower(voter.Hex()),
		Support:         int(support),
		VotingPower:     votingPower.String(),
		Reason:          "", // 暂时使用空字符串
		BlockNumber:     uint64(log.BlockNumber),
		TransactionHash: log.TxHash.Hex(),
		Timestamp:       blockTimestamp,
		CreatedAt:       blockTimestamp,
	}

	// 保存投票
	if err := h.listener.governanceRepo.CreateVote(ctx, vote); err != nil {
		return fmt.Errorf("failed to create vote: %w", err)
	}

	h.listener.logger.Info().
		Uint64("proposal_id", vote.ProposalID).
		Str("voter", vote.Voter).
		Int("support", vote.Support).
		Str("voting_power", vote.VotingPower).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Vote cast event processed successfully")

	return nil
}

// getBlockTimestamp 获取区块的真实时间戳
func (h *BSCScanVoteCastEventHandler) getBlockTimestamp(ctx context.Context, blockNumber uint64) (time.Time, error) {
	blockInfo, err := h.listener.bscClient.GetBlockByNumber(ctx, blockNumber)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get block info: %w", err)
	}

	if blockInfo.Timestamp == "" {
		return time.Time{}, fmt.Errorf("empty timestamp in block info")
	}

	timestampHex := strings.TrimPrefix(blockInfo.Timestamp, "0x")
	timestampInt, err := strconv.ParseInt(timestampHex, 16, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	return time.Unix(timestampInt, 0), nil
}

// BSCScanProposalExecutedEventHandler BSCScan提案执行事件处理器
type BSCScanProposalExecutedEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理提案执行事件
func (h *BSCScanProposalExecutedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 解析事件参数
	if len(log.Topics) < 2 {
		return fmt.Errorf("invalid ProposalExecuted event: insufficient topics")
	}

	// 从Topics解析indexed参数
	proposalId := new(big.Int).SetBytes(log.Topics[1][:])

	// 获取提案并更新状态
	proposal, err := h.listener.governanceRepo.GetProposal(ctx, proposalId.Uint64())
	if err != nil {
		return fmt.Errorf("failed to get proposal: %w", err)
	}

	if proposal == nil {
		h.listener.logger.Warn().
			Uint64("proposal_id", proposalId.Uint64()).
			Msg("Proposal not found for execution event")
		return nil
	}

	// 更新提案状态为已执行
	proposal.Status = "executed"
	proposal.UpdatedAt = time.Now()

	if err := h.listener.governanceRepo.UpdateProposal(ctx, proposal); err != nil {
		return fmt.Errorf("failed to update proposal status: %w", err)
	}

	h.listener.logger.Info().
		Uint64("proposal_id", proposal.ProposalID).
		Str("title", proposal.Title).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Proposal executed event processed successfully")

	return nil
}

// ===== BSCScan内容注册事件处理器 =====

// BSCScanContentRegisteredEventHandler BSCScan内容注册事件处理器
type BSCScanContentRegisteredEventHandler struct {
	listener *BSCScanEventListener
}

// Handle 处理内容注册事件
func (h *BSCScanContentRegisteredEventHandler) Handle(ctx context.Context, log types.Log) error {
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Uint64("block_number", log.BlockNumber).
		Msg("Processing ContentRegistered event")

	// 解析事件数据
	// ContentRegistered(uint256 indexed contentId, address indexed creator, string contentType, string ipfsHash, uint256 patFee)
	if len(log.Topics) < 3 {
		return fmt.Errorf("insufficient topics for ContentRegistered event")
	}

	// 解析 contentId (第一个 indexed 参数)
	contentID := new(big.Int).SetBytes(log.Topics[1].Bytes())

	// 解析 creator (第二个 indexed 参数)
	creator := common.HexToAddress(log.Topics[2].Hex())

	h.listener.logger.Info().
		Str("content_id", contentID.String()).
		Str("creator", creator.Hex()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("ContentRegistered event parsed successfully")

	// 查找对应的内容记录并更新状态
	if err := h.updateContentStatus(ctx, log.TxHash.Hex(), log.BlockNumber); err != nil {
		h.listener.logger.Error().
			Err(err).
			Str("tx_hash", log.TxHash.Hex()).
			Msg("Failed to update content status")
		return fmt.Errorf("failed to update content status: %w", err)
	}

	return nil
}

// updateContentStatus 更新内容状态为已确认
func (h *BSCScanContentRegisteredEventHandler) updateContentStatus(ctx context.Context, txHash string, blockNumber uint64) error {
	// 通过交易哈希查找内容记录
	db := h.listener.tokenRepo.GetDB()

	var record models.TokenContentRecord
	err := db.Where("transaction_hash = ?", txHash).First(&record).Error
	if err != nil {
		return fmt.Errorf("failed to find content record with tx hash %s: %w", txHash, err)
	}

	// 更新状态为已确认
	err = db.Model(&record).Updates(map[string]interface{}{
		"status":       "confirmed",
		"block_number": blockNumber,
	}).Error
	if err != nil {
		return fmt.Errorf("failed to update content record status: %w", err)
	}

	h.listener.logger.Info().
		Str("content_id", record.ContentID).
		Str("tx_hash", txHash).
		Uint64("block_number", blockNumber).
		Msg("Content status updated to confirmed")

	return nil
}
