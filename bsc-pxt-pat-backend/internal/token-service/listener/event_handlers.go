package listener

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"

	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/models"
)

// ===== 质押相关事件处理器 =====

// StakedEventHandler 质押事件处理器
type StakedEventHandler struct {
	listener *MultiEventListener
}

func (h *StakedEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.StakedEvent
	err := contracts.ABIs.StakingPool.UnpackIntoInterface(&event, "Staked", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack Staked event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}
	event.Raw = log

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(event.User.Hex()),
		EventType:       models.EventTypeStaked,
		Amount:          models.NewBigInt(event.Amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存事件
	if err := h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent); err != nil {
		return fmt.Errorf("failed to create staking event: %w", err)
	}

	// 更新用户质押信息
	return h.updateUserStake(ctx, event.User, log.BlockNumber)
}

func (h *StakedEventHandler) updateUserStake(ctx context.Context, userAddress common.Address, blockNumber uint64) error {
	contracts := h.listener.client.GetContracts()

	// 获取用户质押信息
	userInfo, err := h.listener.client.GetUserStakeInfo(ctx, contracts.Addresses.StakingPool, userAddress)
	if err != nil {
		h.listener.logger.Warn().Err(err).
			Str("user", userAddress.Hex()).
			Msg("Failed to get user stake info from contract")
		return nil // 不阻断事件处理，只记录警告
	}

	// 创建或更新用户质押记录
	userStake := &models.UserStake{
		UserAddress:         strings.ToLower(userAddress.Hex()),
		Amount:              models.NewBigInt(userInfo.Amount),
		StakingLevel:        models.StakingLevel(userInfo.Level),
		MiningPower:         models.NewBigInt(userInfo.MiningPower),
		TotalRewardsClaimed: models.NewBigInt(userInfo.TotalRewardsClaimed),
		StartTime:           userInfo.StartTime,
		LastUpdateTime:      userInfo.LastUpdateTime,
		IsActive:            userInfo.Amount.Cmp(big.NewInt(0)) > 0,
	}

	// 保存到数据库
	if err := h.listener.tokenRepo.CreateOrUpdateUserStake(ctx, userStake); err != nil {
		return fmt.Errorf("failed to create or update user stake: %w", err)
	}

	h.listener.logger.Info().
		Str("user", userAddress.Hex()).
		Str("amount", userInfo.Amount.String()).
		Uint8("level", uint8(userInfo.Level)).
		Uint64("block", blockNumber).
		Msg("User stake updated")

	return nil
}

// WithdrawnEventHandler 提取事件处理器
type WithdrawnEventHandler struct {
	listener *MultiEventListener
}

func (h *WithdrawnEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.WithdrawnEvent
	err := contracts.ABIs.StakingPool.UnpackIntoInterface(&event, "Withdrawn", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack Withdrawn event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}
	event.Raw = log

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(event.User.Hex()),
		EventType:       models.EventTypeWithdrawn,
		Amount:          models.NewBigInt(event.Amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存事件
	return h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent)
}

// RewardClaimedEventHandler 奖励领取事件处理器
type RewardClaimedEventHandler struct {
	listener *MultiEventListener
}

func (h *RewardClaimedEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.RewardClaimedEvent
	err := contracts.ABIs.StakingPool.UnpackIntoInterface(&event, "RewardClaimed", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack RewardClaimed event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}
	event.Raw = log

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(event.User.Hex()),
		EventType:       models.EventTypeRewardClaimed,
		Amount:          models.NewBigInt(event.Amount),
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存事件
	return h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent)
}

// StakingLevelUpdatedEventHandler 质押等级更新事件处理器
type StakingLevelUpdatedEventHandler struct {
	listener *MultiEventListener
}

func (h *StakingLevelUpdatedEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.StakingLevelUpdatedEvent
	err := contracts.ABIs.StakingPool.UnpackIntoInterface(&event, "StakingLevelUpdated", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack StakingLevelUpdated event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}
	event.Raw = log

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建质押事件记录
	oldLevel := models.StakingLevel(event.OldLevel)
	newLevel := models.StakingLevel(event.NewLevel)
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(event.User.Hex()),
		EventType:       models.EventTypeStakingLevelUpdated,
		OldLevel:        &oldLevel,
		NewLevel:        &newLevel,
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存事件
	return h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent)
}

// MiningPowerUpdatedEventHandler 挖矿算力更新事件处理器
type MiningPowerUpdatedEventHandler struct {
	listener *MultiEventListener
}

func (h *MiningPowerUpdatedEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.MiningPowerUpdatedEvent
	err := contracts.ABIs.StakingPool.UnpackIntoInterface(&event, "MiningPowerUpdated", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack MiningPowerUpdated event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.User = common.HexToAddress(log.Topics[1].Hex())
	}
	event.Raw = log

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建质押事件记录
	stakingEvent := &models.StakingEvent{
		UserAddress:     strings.ToLower(event.User.Hex()),
		EventType:       models.EventTypeMiningPowerUpdated,
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint(log.Index),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存事件
	return h.listener.eventRepo.CreateStakingEvent(ctx, stakingEvent)
}

// ===== 辅助函数 =====

// getProposalCategory 根据提案类型返回分类
func getProposalCategory(proposalType uint8) string {
	switch proposalType {
	case 0:
		return "parameter"
	case 1:
		return "funding"
	case 2:
		return "upgrade"
	case 3:
		return "membership"
	case 4:
		return "emergency"
	default:
		return "other"
	}
}

// ===== 代币相关事件处理器 =====

// TransferEventHandler 转账事件处理器
type TransferEventHandler struct {
	listener *MultiEventListener
}

func (h *TransferEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件
	var event blockchain.TransferEvent
	err := contracts.ABIs.ERC20Token.UnpackIntoInterface(&event, "Transfer", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack Transfer event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 3 {
		event.From = common.HexToAddress(log.Topics[1].Hex())
		event.To = common.HexToAddress(log.Topics[2].Hex())
	}
	event.Raw = log

	// 确定代币类型
	tokenType := h.getTokenType(log.Address)
	if tokenType == "" {
		return nil // 不是我们关心的代币
	}

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 创建转账记录
	transfer := &models.TokenTransfer{
		TransactionHash: log.TxHash.Hex(),
		BlockNumber:     log.BlockNumber,
		LogIndex:        uint64(log.Index),
		TokenType:       tokenType,
		FromAddress:     strings.ToLower(event.From.Hex()),
		ToAddress:       strings.ToLower(event.To.Hex()),
		Amount:          event.Value.String(),
		Timestamp:       time.Unix(int64(block.Time()), 0),
	}

	// 保存转账记录
	if err := h.listener.tokenRepo.CreateTransfer(ctx, transfer); err != nil {
		return fmt.Errorf("failed to create transfer record: %w", err)
	}

	// 更新相关地址的余额
	if err := h.updateBalances(ctx, event.From, event.To, tokenType); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to update balances")
	}

	// 检查是否涉及池子地址，如果是则进行特殊处理
	if err := h.handlePoolTransferIfNeeded(ctx, transfer, log); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to handle pool transfer")
	}

	return nil
}

func (h *TransferEventHandler) getTokenType(address common.Address) string {
	contracts := h.listener.client.GetContracts()

	if address == contracts.Addresses.PXTToken {
		return "PXT"
	} else if address == contracts.Addresses.PATToken {
		return "PAT"
	}

	return ""
}

func (h *TransferEventHandler) updateBalances(ctx context.Context, from, to common.Address, tokenType string) error {
	contracts := h.listener.client.GetContracts()
	var tokenAddress common.Address

	// 获取代币合约地址
	if tokenType == "PXT" {
		tokenAddress = contracts.Addresses.PXTToken
	} else if tokenType == "PAT" {
		tokenAddress = contracts.Addresses.PATToken
	} else {
		return fmt.Errorf("unknown token type: %s", tokenType)
	}

	// 更新发送方余额（如果不是零地址）
	zeroAddress := common.HexToAddress("0x0000000000000000000000000000000000000000")
	if from != zeroAddress {
		if err := h.updateAddressBalance(ctx, from, tokenAddress, tokenType); err != nil {
			h.listener.logger.Warn().Err(err).Str("address", from.Hex()).Msg("Failed to update sender balance")
		}
	}

	// 更新接收方余额
	if err := h.updateAddressBalance(ctx, to, tokenAddress, tokenType); err != nil {
		h.listener.logger.Warn().Err(err).Str("address", to.Hex()).Msg("Failed to update receiver balance")
	}

	h.listener.logger.Debug().
		Str("from", from.Hex()).
		Str("to", to.Hex()).
		Str("token", tokenType).
		Msg("Balances updated")

	return nil
}

func (h *TransferEventHandler) updateAddressBalance(ctx context.Context, address common.Address, tokenAddress common.Address, tokenType string) error {
	// 获取当前余额
	balance, err := h.listener.client.GetTokenBalance(ctx, tokenAddress, address)
	if err != nil {
		return fmt.Errorf("failed to get balance: %w", err)
	}

	// 获取锁定余额（仅对PXT代币）
	var lockedAmount string = "0"
	if tokenType == "PXT" {
		if locked, err := h.listener.client.GetTokenLockedBalance(ctx, tokenAddress, address); err == nil {
			lockedAmount = locked.String()
		}
	}

	// 创建或更新余额记录
	tokenBalance := &models.TokenBalance{
		UserAddress:  strings.ToLower(address.Hex()),
		TokenType:    tokenType,
		Balance:      balance.String(),
		LockedAmount: lockedAmount,
	}

	return h.listener.tokenRepo.CreateOrUpdateBalance(ctx, tokenBalance)
}

// ApprovalEventHandler 授权事件处理器
type ApprovalEventHandler struct {
	listener *MultiEventListener
}

func (h *ApprovalEventHandler) Handle(ctx context.Context, log types.Log) error {
	// 授权事件通常不需要特殊处理，只记录日志
	h.listener.logger.Debug().
		Str("contract", log.Address.Hex()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Approval event processed")

	return nil
}

// ===== 池子转账处理方法 =====

// handlePoolTransferIfNeeded 检查并处理池子转账
func (h *TransferEventHandler) handlePoolTransferIfNeeded(ctx context.Context, transfer *models.TokenTransfer, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	chinaPoolAddr := strings.ToLower(contracts.Addresses.ChinaMainlandPool.Hex())
	globalPoolAddr := strings.ToLower(contracts.Addresses.GlobalPool.Hex())

	// 检查是否涉及池子地址
	isPoolTransfer := transfer.FromAddress == chinaPoolAddr || transfer.FromAddress == globalPoolAddr ||
		transfer.ToAddress == chinaPoolAddr || transfer.ToAddress == globalPoolAddr

	if !isPoolTransfer {
		return nil // 不涉及池子，跳过
	}

	// 确定池子类型和转账方向
	var poolType, direction string
	var poolAddress string

	if transfer.FromAddress == chinaPoolAddr {
		poolType = "china_mainland"
		poolAddress = chinaPoolAddr
		direction = "out"
	} else if transfer.FromAddress == globalPoolAddr {
		poolType = "global"
		poolAddress = globalPoolAddr
		direction = "out"
	} else if transfer.ToAddress == chinaPoolAddr {
		poolType = "china_mainland"
		poolAddress = chinaPoolAddr
		direction = "in"
	} else if transfer.ToAddress == globalPoolAddr {
		poolType = "global"
		poolAddress = globalPoolAddr
		direction = "in"
	}

	// 创建池子活动记录
	poolActivity := &models.PoolActivity{
		PoolAddress:     poolAddress,
		PoolType:        poolType,
		TransactionHash: transfer.TransactionHash,
		TokenType:       transfer.TokenType,
		Direction:       direction,
		FromAddress:     transfer.FromAddress,
		ToAddress:       transfer.ToAddress,
		Amount:          transfer.Amount,
		Purpose:         h.determinePoolPurpose(transfer, direction),
		BlockNumber:     transfer.BlockNumber,
		Timestamp:       transfer.Timestamp,
	}

	// 保存池子活动记录
	if err := h.listener.tokenRepo.CreatePoolActivity(ctx, poolActivity); err != nil {
		return fmt.Errorf("failed to create pool activity: %w", err)
	}

	// 更新池子余额
	if err := h.updatePoolBalance(ctx, poolAddress, transfer.TokenType); err != nil {
		h.listener.logger.Warn().Err(err).Str("pool", poolAddress).Msg("Failed to update pool balance")
	}

	// 发送池子通知
	h.sendPoolNotification(ctx, poolActivity)

	h.listener.logger.Info().
		Str("pool_address", poolAddress).
		Str("pool_type", poolType).
		Str("direction", direction).
		Str("amount", transfer.Amount).
		Str("token", transfer.TokenType).
		Msg("Pool transfer processed")

	return nil
}

// determinePoolPurpose 确定池子转账用途
func (h *TransferEventHandler) determinePoolPurpose(transfer *models.TokenTransfer, direction string) string {
	// 根据转账金额、地址等信息判断用途
	amount, ok := new(big.Int).SetString(transfer.Amount, 10)
	if !ok {
		return "unknown"
	}

	// 大额转账可能是奖励分发
	rewardThreshold := new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil) // 1 token
	rewardThreshold.Mul(rewardThreshold, big.NewInt(1000))                   // 1000 tokens

	if amount.Cmp(rewardThreshold) >= 0 {
		if direction == "out" {
			return "reward_distribution" // 奖励分发
		} else {
			return "large_deposit" // 大额存入
		}
	}

	// 中等金额可能是普通奖励
	normalRewardThreshold := new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil) // 1 token
	normalRewardThreshold.Mul(normalRewardThreshold, big.NewInt(10))               // 10 tokens

	if amount.Cmp(normalRewardThreshold) >= 0 {
		if direction == "out" {
			return "reward" // 普通奖励
		} else {
			return "deposit" // 存入
		}
	}

	// 小额转账
	if direction == "out" {
		return "small_reward" // 小额奖励
	} else {
		return "small_deposit" // 小额存入
	}
}

// updatePoolBalance 更新池子余额
func (h *TransferEventHandler) updatePoolBalance(ctx context.Context, poolAddress, tokenType string) error {
	contracts := h.listener.client.GetContracts()
	var tokenAddress common.Address

	// 获取代币合约地址
	if tokenType == "PXT" {
		tokenAddress = contracts.Addresses.PXTToken
	} else if tokenType == "PAT" {
		tokenAddress = contracts.Addresses.PATToken
	} else {
		return fmt.Errorf("unknown token type: %s", tokenType)
	}

	poolAddr := common.HexToAddress(poolAddress)

	// 获取当前余额
	balance, err := h.listener.client.GetTokenBalance(ctx, tokenAddress, poolAddr)
	if err != nil {
		return fmt.Errorf("failed to get pool balance: %w", err)
	}

	// 创建或更新余额记录
	poolBalance := &models.PoolBalance{
		PoolAddress: strings.ToLower(poolAddress),
		TokenType:   tokenType,
		Balance:     balance.String(),
		UpdatedAt:   time.Now(),
	}

	// 保存余额记录
	if err := h.listener.tokenRepo.CreateOrUpdatePoolBalance(ctx, poolBalance); err != nil {
		return fmt.Errorf("failed to update pool balance: %w", err)
	}

	return nil
}

// sendPoolNotification 发送池子通知
func (h *TransferEventHandler) sendPoolNotification(ctx context.Context, activity *models.PoolActivity) {
	// 记录池子活动日志
	h.listener.logger.Info().
		Str("pool_address", activity.PoolAddress).
		Str("pool_type", activity.PoolType).
		Str("direction", activity.Direction).
		Str("amount", activity.Amount).
		Str("token", activity.TokenType).
		Str("purpose", activity.Purpose).
		Msg("Pool activity notification")

	// TODO: 在这里可以添加WebSocket通知或其他通知机制
	// 当前先记录日志，后续可以集成通知服务
}

// ProposalCreatedEventHandler 提案创建事件处理器
type ProposalCreatedEventHandler struct {
	listener *MultiEventListener
}

func (h *ProposalCreatedEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件数据
	event := struct {
		ProposalId   *big.Int
		Proposer     common.Address
		ProposalType uint8
		StartTime    *big.Int
		EndTime      *big.Int
		Title        string
		Description  string
	}{}

	err := contracts.ABIs.ProposalManager.UnpackIntoInterface(&event, "ProposalCreated", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack ProposalCreated event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.ProposalId = new(big.Int).SetBytes(log.Topics[1][:])
	}
	if len(log.Topics) >= 3 {
		event.Proposer = common.HexToAddress(log.Topics[2].Hex())
	}

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 检查提案是否已存在
	existingProposal, err := h.listener.governanceRepo.GetProposal(ctx, event.ProposalId.Uint64())
	if err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to check existing proposal")
	}

	if existingProposal != nil {
		// 提案已存在，跳过
		h.listener.logger.Debug().
			Uint64("proposal_id", event.ProposalId.Uint64()).
			Msg("Proposal already exists, skipping")
		return nil
	}

	// 创建新提案记录
	proposal := &models.Proposal{
		ProposalID:       event.ProposalId.Uint64(),
		Proposer:         strings.ToLower(event.Proposer.Hex()),
		Title:            event.Title,
		Description:      event.Description,
		Category:         getProposalCategory(event.ProposalType),
		Status:           "active",
		ForVotes:         "0",
		AgainstVotes:     "0",
		AbstainVotes:     "0",
		TotalVotes:       "0",
		QuorumRequired:   "1000000", // 默认值
		MajorityRequired: 0.5,       // 默认值
		StartBlock:       uint64(log.BlockNumber),
		EndBlock:         uint64(log.BlockNumber) + 40320, // 假设7天
		CreatedAt:        time.Unix(int64(block.Time()), 0),
		UpdatedAt:        time.Unix(int64(block.Time()), 0),
	}

	// 保存提案
	if err := h.listener.governanceRepo.CreateProposal(ctx, proposal); err != nil {
		return fmt.Errorf("failed to create proposal: %w", err)
	}

	h.listener.logger.Info().
		Uint64("proposal_id", proposal.ProposalID).
		Str("title", proposal.Title).
		Str("proposer", proposal.Proposer).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Proposal created event processed successfully")

	return nil
}

// VoteCastEventHandler 投票事件处理器
type VoteCastEventHandler struct {
	listener *MultiEventListener
}

func (h *VoteCastEventHandler) Handle(ctx context.Context, log types.Log) error {
	contracts := h.listener.client.GetContracts()

	// 解析事件数据
	event := struct {
		ProposalId  *big.Int
		Voter       common.Address
		Support     uint8
		VotingPower *big.Int
		Reason      string
	}{}

	err := contracts.ABIs.Voting.UnpackIntoInterface(&event, "VoteCast", log.Data)
	if err != nil {
		return fmt.Errorf("failed to unpack VoteCast event: %w", err)
	}

	// 解析indexed参数
	if len(log.Topics) >= 2 {
		event.Voter = common.HexToAddress(log.Topics[1].Hex())
	}
	if len(log.Topics) >= 3 {
		event.ProposalId = new(big.Int).SetBytes(log.Topics[2][:])
	}

	// 获取区块时间戳
	block, err := h.listener.client.GetBlockByNumber(ctx, log.BlockNumber)
	if err != nil {
		return fmt.Errorf("failed to get block: %w", err)
	}

	// 检查投票是否已存在
	existingVote, err := h.listener.governanceRepo.GetVote(ctx, event.ProposalId.Uint64(), event.Voter.Hex())
	if err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to check existing vote")
	}

	if existingVote != nil {
		// 投票已存在，跳过
		h.listener.logger.Debug().
			Uint64("proposal_id", event.ProposalId.Uint64()).
			Str("voter", event.Voter.Hex()).
			Msg("Vote already exists, skipping")
		return nil
	}

	// 创建新投票记录
	vote := &models.Vote{
		ProposalID:      event.ProposalId.Uint64(),
		Voter:           strings.ToLower(event.Voter.Hex()),
		Support:         int(event.Support),
		VotingPower:     event.VotingPower.String(),
		Reason:          event.Reason,
		BlockNumber:     uint64(log.BlockNumber),
		TransactionHash: log.TxHash.Hex(),
		Timestamp:       time.Unix(int64(block.Time()), 0),
		CreatedAt:       time.Unix(int64(block.Time()), 0),
	}

	// 保存投票
	if err := h.listener.governanceRepo.CreateVote(ctx, vote); err != nil {
		return fmt.Errorf("failed to create vote: %w", err)
	}

	// 更新提案的投票统计
	if err := h.updateProposalVoteStats(ctx, event.ProposalId.Uint64()); err != nil {
		h.listener.logger.Warn().Err(err).Msg("Failed to update proposal vote stats")
	}

	h.listener.logger.Info().
		Uint64("proposal_id", vote.ProposalID).
		Str("voter", vote.Voter).
		Int("support", vote.Support).
		Str("voting_power", vote.VotingPower).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Vote cast event processed successfully")

	return nil
}

// updateProposalVoteStats 更新提案投票统计
func (h *VoteCastEventHandler) updateProposalVoteStats(ctx context.Context, proposalID uint64) error {
	// 获取提案的所有投票
	votes, _, err := h.listener.governanceRepo.GetProposalVotes(ctx, proposalID, 1, 10000)
	if err != nil {
		return fmt.Errorf("获取提案投票失败: %v", err)
	}

	// 计算投票统计
	var forVotes, againstVotes, abstainVotes big.Int
	for _, vote := range votes {
		votingPower, ok := new(big.Int).SetString(vote.VotingPower, 10)
		if !ok {
			continue
		}

		switch vote.Support {
		case 1: // 支持
			forVotes.Add(&forVotes, votingPower)
		case 0: // 反对
			againstVotes.Add(&againstVotes, votingPower)
		case 2: // 弃权
			abstainVotes.Add(&abstainVotes, votingPower)
		}
	}

	// 计算总投票数
	totalVotes := new(big.Int)
	totalVotes.Add(&forVotes, &againstVotes)
	totalVotes.Add(totalVotes, &abstainVotes)

	// 获取提案并更新统计
	proposal, err := h.listener.governanceRepo.GetProposal(ctx, proposalID)
	if err != nil {
		return fmt.Errorf("获取提案失败: %v", err)
	}

	if proposal != nil {
		proposal.ForVotes = forVotes.String()
		proposal.AgainstVotes = againstVotes.String()
		proposal.AbstainVotes = abstainVotes.String()
		proposal.TotalVotes = totalVotes.String()
		proposal.UpdatedAt = time.Now()

		if err := h.listener.governanceRepo.UpdateProposal(ctx, proposal); err != nil {
			return fmt.Errorf("更新提案投票统计失败: %v", err)
		}
	}

	return nil
}

// ProposalExecutedEventHandler 提案执行事件处理器
type ProposalExecutedEventHandler struct {
	listener *MultiEventListener
}

func (h *ProposalExecutedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// TODO: 实现提案执行事件处理
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Proposal executed event processed")

	return nil
}

// ProposalCancelledEventHandler 提案取消事件处理器
type ProposalCancelledEventHandler struct {
	listener *MultiEventListener
}

func (h *ProposalCancelledEventHandler) Handle(ctx context.Context, log types.Log) error {
	// TODO: 实现提案取消事件处理
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Proposal cancelled event processed")

	return nil
}

// ===== 奖励分配事件处理器 =====

// RewardDistributedEventHandler 奖励分配事件处理器
type RewardDistributedEventHandler struct {
	listener *MultiEventListener
}

func (h *RewardDistributedEventHandler) Handle(ctx context.Context, log types.Log) error {
	// TODO: 实现奖励分配事件处理
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Reward distributed event processed")

	return nil
}

// ===== 国库事件处理器 =====

// TreasuryTransferEventHandler 国库转账事件处理器
type TreasuryTransferEventHandler struct {
	listener *MultiEventListener
}

func (h *TreasuryTransferEventHandler) Handle(ctx context.Context, log types.Log) error {
	// TODO: 实现国库转账事件处理
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Msg("Treasury transfer event processed")

	return nil
}

// ===== 内容注册事件处理器 =====

// ContentRegisteredEventHandler 内容注册事件处理器
type ContentRegisteredEventHandler struct {
	listener *MultiEventListener
}

func (h *ContentRegisteredEventHandler) Handle(ctx context.Context, log types.Log) error {
	h.listener.logger.Info().
		Str("tx_hash", log.TxHash.Hex()).
		Uint64("block_number", log.BlockNumber).
		Msg("Processing ContentRegistered event")

	// 解析事件数据
	// ContentRegistered(uint256 indexed contentId, address indexed creator, string contentType, string ipfsHash, uint256 patFee)
	if len(log.Topics) < 3 {
		return fmt.Errorf("insufficient topics for ContentRegistered event")
	}

	// 解析 contentId (第一个 indexed 参数)
	contentID := new(big.Int).SetBytes(log.Topics[1].Bytes())

	// 解析 creator (第二个 indexed 参数)
	creator := common.HexToAddress(log.Topics[2].Hex())

	h.listener.logger.Info().
		Str("content_id", contentID.String()).
		Str("creator", creator.Hex()).
		Str("tx_hash", log.TxHash.Hex()).
		Msg("ContentRegistered event parsed successfully")

	// 查找对应的内容记录并更新状态
	if err := h.updateContentStatus(ctx, log.TxHash.Hex(), log.BlockNumber); err != nil {
		h.listener.logger.Error().
			Err(err).
			Str("tx_hash", log.TxHash.Hex()).
			Msg("Failed to update content status")
		return fmt.Errorf("failed to update content status: %w", err)
	}

	return nil
}

// updateContentStatus 更新内容状态为已确认
func (h *ContentRegisteredEventHandler) updateContentStatus(ctx context.Context, txHash string, blockNumber uint64) error {
	// 通过交易哈希查找内容记录
	db := h.listener.tokenRepo.GetDB()

	var record models.TokenContentRecord
	err := db.Where("transaction_hash = ?", txHash).First(&record).Error
	if err != nil {
		return fmt.Errorf("failed to find content record with tx hash %s: %w", txHash, err)
	}

	// 更新状态为已确认
	err = db.Model(&record).Updates(map[string]interface{}{
		"status":       "confirmed",
		"block_number": blockNumber,
	}).Error
	if err != nil {
		return fmt.Errorf("failed to update content record status: %w", err)
	}

	h.listener.logger.Info().
		Uint("record_id", record.ID).
		Str("content_id", record.ContentID).
		Str("tx_hash", txHash).
		Uint64("block_number", blockNumber).
		Msg("Content status updated to confirmed")

	return nil
}
