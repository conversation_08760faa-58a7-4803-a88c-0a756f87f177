package utils

import (
	"fmt"
	"time"

	"github.com/fatih/color"
	"github.com/rs/zerolog"
)

// SyncLogger 同步日志工具，提供彩色和格式化的日志输出
type SyncLogger struct {
	logger zerolog.Logger
	config SyncLoggerConfig
}

// SyncLoggerConfig 同步日志配置
type SyncLoggerConfig struct {
	EnableColorfulLogs      bool
	ShowBlockProgress       bool
	LogSyncProgressInterval int
	LogTransferDetails      bool
	LogBalanceUpdates       bool
}

// NewSyncLogger 创建新的同步日志器
func NewSyncLogger(logger zerolog.Logger, config SyncLoggerConfig) *SyncLogger {
	return &SyncLogger{
		logger: logger,
		config: config,
	}
}

// 定义颜色
var (
	// 区块相关颜色
	blockColor     = color.New(color.FgCyan, color.Bold)
	progressColor  = color.New(color.FgGreen, color.Bold)
	warningColor   = color.New(color.FgYellow, color.Bold)
	errorColor     = color.New(color.FgRed, color.Bold)
	successColor   = color.New(color.FgGreen, color.Bold)
	
	// 数据相关颜色
	transferColor  = color.New(color.FgBlue)
	balanceColor   = color.New(color.FgMagenta)
	tokenColor     = color.New(color.FgYellow)
	addressColor   = color.New(color.FgCyan)
)

// LogBlockSync 记录区块同步日志
func (sl *SyncLogger) LogBlockSync(fromBlock, toBlock uint64, transferCount, newTransferCount int) {
	if !sl.config.ShowBlockProgress {
		return
	}

	if sl.config.EnableColorfulLogs {
		// 彩色输出
		if newTransferCount > 0 {
			fmt.Printf("🔄 同步区块 %s 到 %s，发现 %s 笔交易，已同步 %s 笔新交易\n",
				blockColor.Sprintf("%d", fromBlock),
				blockColor.Sprintf("%d", toBlock),
				transferColor.Sprintf("%d", transferCount),
				successColor.Sprintf("%d", newTransferCount),
			)
		} else {
			fmt.Printf("✅ 同步区块 %s 到 %s，发现 %s 笔交易，无新交易需要同步\n",
				blockColor.Sprintf("%d", fromBlock),
				blockColor.Sprintf("%d", toBlock),
				transferColor.Sprintf("%d", transferCount),
			)
		}
	} else {
		// 普通日志输出
		if newTransferCount > 0 {
			sl.logger.Info().
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Int("total_transfers", transferCount).
				Int("new_transfers", newTransferCount).
				Msg("区块同步完成 - 发现新交易")
		} else {
			sl.logger.Info().
				Uint64("from_block", fromBlock).
				Uint64("to_block", toBlock).
				Int("total_transfers", transferCount).
				Msg("区块同步完成 - 无新交易")
		}
	}
}

// LogSyncProgress 记录同步进度
func (sl *SyncLogger) LogSyncProgress(currentBlock, latestBlock uint64, progress float64) {
	if !sl.config.ShowBlockProgress {
		return
	}

	// 只在指定间隔记录进度
	if currentBlock%uint64(sl.config.LogSyncProgressInterval) != 0 {
		return
	}

	if sl.config.EnableColorfulLogs {
		fmt.Printf("📊 同步进度: %s/%s (%.2f%%) - 剩余 %s 个区块\n",
			progressColor.Sprintf("%d", currentBlock),
			blockColor.Sprintf("%d", latestBlock),
			progress,
			warningColor.Sprintf("%d", latestBlock-currentBlock),
		)
	} else {
		sl.logger.Info().
			Uint64("current_block", currentBlock).
			Uint64("latest_block", latestBlock).
			Float64("progress_percent", progress).
			Uint64("remaining_blocks", latestBlock-currentBlock).
			Msg("同步进度更新")
	}
}

// LogTransferSync 记录转账同步日志
func (sl *SyncLogger) LogTransferSync(tokenType string, transferCount int, duration time.Duration) {
	if !sl.config.LogTransferDetails && transferCount == 0 {
		return
	}

	if sl.config.EnableColorfulLogs {
		if transferCount > 0 {
			fmt.Printf("💰 %s 转账同步完成: %s 笔交易，耗时 %s\n",
				tokenColor.Sprintf(tokenType),
				transferColor.Sprintf("%d", transferCount),
				duration.Round(time.Millisecond),
			)
		}
	} else {
		sl.logger.Info().
			Str("token_type", tokenType).
			Int("transfer_count", transferCount).
			Dur("duration", duration).
			Msg("转账数据同步完成")
	}
}

// LogBalanceSync 记录余额同步日志
func (sl *SyncLogger) LogBalanceSync(tokenType string, userCount int, successCount int, duration time.Duration) {
	if !sl.config.LogBalanceUpdates && successCount == userCount {
		return
	}

	if sl.config.EnableColorfulLogs {
		if successCount == userCount {
			fmt.Printf("💳 %s 余额同步完成: %s 个用户，全部成功，耗时 %s\n",
				tokenColor.Sprintf(tokenType),
				successColor.Sprintf("%d", userCount),
				duration.Round(time.Millisecond),
			)
		} else {
			fmt.Printf("⚠️  %s 余额同步完成: %s/%s 个用户成功，耗时 %s\n",
				tokenColor.Sprintf(tokenType),
				warningColor.Sprintf("%d", successCount),
				transferColor.Sprintf("%d", userCount),
				duration.Round(time.Millisecond),
			)
		}
	} else {
		sl.logger.Info().
			Str("token_type", tokenType).
			Int("total_users", userCount).
			Int("success_count", successCount).
			Dur("duration", duration).
			Msg("用户余额同步完成")
	}
}

// LogSyncStart 记录同步开始
func (sl *SyncLogger) LogSyncStart(syncType string) {
	if sl.config.EnableColorfulLogs {
		fmt.Printf("🚀 开始 %s 同步...\n", successColor.Sprintf(syncType))
	} else {
		sl.logger.Info().Str("sync_type", syncType).Msg("开始同步")
	}
}

// LogSyncComplete 记录同步完成
func (sl *SyncLogger) LogSyncComplete(syncType string, duration time.Duration) {
	if sl.config.EnableColorfulLogs {
		fmt.Printf("✅ %s 同步完成，总耗时: %s\n",
			successColor.Sprintf(syncType),
			progressColor.Sprintf("%v", duration.Round(time.Millisecond)),
		)
	} else {
		sl.logger.Info().
			Str("sync_type", syncType).
			Dur("total_duration", duration).
			Msg("同步完成")
	}
}

// LogSyncError 记录同步错误
func (sl *SyncLogger) LogSyncError(syncType string, err error) {
	if sl.config.EnableColorfulLogs {
		fmt.Printf("❌ %s 同步失败: %s\n",
			errorColor.Sprintf(syncType),
			errorColor.Sprintf("%v", err),
		)
	} else {
		sl.logger.Error().
			Str("sync_type", syncType).
			Err(err).
			Msg("同步失败")
	}
}

// LogTokenSupplyUpdate 记录代币供应量更新
func (sl *SyncLogger) LogTokenSupplyUpdate(tokenType, totalSupply, circulatingSupply string) {
	if sl.config.EnableColorfulLogs {
		fmt.Printf("📈 %s 供应量更新: 总量 %s，流通量 %s\n",
			tokenColor.Sprintf(tokenType),
			balanceColor.Sprintf(totalSupply),
			balanceColor.Sprintf(circulatingSupply),
		)
	} else {
		sl.logger.Info().
			Str("token_type", tokenType).
			Str("total_supply", totalSupply).
			Str("circulating_supply", circulatingSupply).
			Msg("代币供应量更新")
	}
}

// LogUserBalanceUpdate 记录用户余额更新（仅在启用详细日志时）
func (sl *SyncLogger) LogUserBalanceUpdate(userAddress, tokenType, balance string) {
	if !sl.config.LogBalanceUpdates {
		return
	}

	if sl.config.EnableColorfulLogs {
		fmt.Printf("💰 用户余额更新: %s %s = %s\n",
			addressColor.Sprintf(userAddress[:10]+"..."),
			tokenColor.Sprintf(tokenType),
			balanceColor.Sprintf(balance),
		)
	} else {
		sl.logger.Debug().
			Str("user_address", userAddress).
			Str("token_type", tokenType).
			Str("balance", balance).
			Msg("用户余额更新")
	}
}

// LogAPICall 记录API调用（用于调试）
func (sl *SyncLogger) LogAPICall(method string, duration time.Duration, success bool) {
	if !sl.config.LogTransferDetails {
		return
	}

	if sl.config.EnableColorfulLogs {
		if success {
			fmt.Printf("🌐 API调用: %s，耗时 %s\n",
				method,
				duration.Round(time.Millisecond),
			)
		} else {
			fmt.Printf("🔴 API调用失败: %s，耗时 %s\n",
				errorColor.Sprintf(method),
				duration.Round(time.Millisecond),
			)
		}
	} else {
		sl.logger.Debug().
			Str("method", method).
			Dur("duration", duration).
			Bool("success", success).
			Msg("API调用")
	}
}
