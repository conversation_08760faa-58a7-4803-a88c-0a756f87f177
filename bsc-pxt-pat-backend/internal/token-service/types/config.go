package types

import (
	"pxpat-backend/pkg/types"
)

// Config 代币服务配置
type Config struct {
	Server           types.GlobalServerConfig   `mapstructure:"server"`
	Database         types.GlobalDatabaseConfig `mapstructure:"database"`
	Redis            types.GlobalRedisConfig    `mapstructure:"redis"`
	Log              types.GlobalLogConfig      `mapstructure:"log"`
	RabbitMQ         types.GlobalRabbitMQConfig `mapstructure:"rabbitmq"`
	Storage          types.GlobalStorageConfig  `mapstructure:"storage"`
	JWT              types.GlobalJWTConfig      `mapstructure:"jwt"`
	ExternalServices ExternalServicesConfig     `mapstructure:"external_services"`
	Blockchain       BlockchainConfig           `mapstructure:"blockchain"`
	Transfer         TransferConfig             `mapstructure:"transfer"`
	Security         SecurityConfig             `mapstructure:"security"`
	Sync             SyncConfig                 `mapstructure:"sync"`
	IPFS             IPFSConfig                 `mapstructure:"ipfs"`
}

// ExternalServicesConfig 外部服务配置
type ExternalServicesConfig struct {
	UserService    ServiceConfig `mapstructure:"user_service"`
	ContentService ServiceConfig `mapstructure:"content_service"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Host    string `mapstructure:"host"`
	Port    int    `mapstructure:"port"`
	Timeout int    `mapstructure:"timeout"`
}

// BlockchainConfig 区块链配置
type BlockchainConfig struct {
	Network             string          `mapstructure:"network"`
	RPCURL              string          `mapstructure:"rpc_url"`
	BackupRPCURLs       []string        `mapstructure:"backup_rpc_urls"`
	ChainID             int64           `mapstructure:"chain_id"`
	StartBlock          uint64          `mapstructure:"start_block"`
	ConfirmBlocks       uint64          `mapstructure:"confirm_blocks"`
	BatchSize           int             `mapstructure:"batch_size"`
	BSCScanAPIKey       string          `mapstructure:"bscscan_api_key"`
	UseBSCScanForEvents bool            `mapstructure:"use_bscscan_for_events"`
	Contracts           ContractsConfig `mapstructure:"contracts"`
	Sync                SyncConfig      `mapstructure:"sync"`
}

// ContractsConfig 合约配置
type ContractsConfig struct {
	PXTToken          string `mapstructure:"pxt_token"`
	PATToken          string `mapstructure:"pat_token"`
	StakingPool       string `mapstructure:"staking_pool"`
	DAO               string `mapstructure:"dao"`
	RewardDistributor string `mapstructure:"reward_distributor"`
	Voting            string `mapstructure:"voting"`
	ProposalManager   string `mapstructure:"proposal_manager"`
	Treasury          string `mapstructure:"treasury"`
	ContentRegistry   string `mapstructure:"content_registry"` // 内容注册合约
	ContentMint       string `mapstructure:"content_mint"`     // 内容铸造合约
}

// TransferConfig 转账配置
type TransferConfig struct {
	SystemPrivateKey string `mapstructure:"system_private_key"`
	SystemAddress    string `mapstructure:"system_address"`
	GasLimit         uint64 `mapstructure:"gas_limit"`
	GasPrice         int64  `mapstructure:"gas_price"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	// 基础同步配置
	Enabled           bool `mapstructure:"enabled"`
	SyncInterval      int  `mapstructure:"sync_interval_seconds"`
	MaxRetries        int  `mapstructure:"max_retries"`
	RetryDelay        int  `mapstructure:"retry_delay_seconds"`
	ConcurrentWorkers int  `mapstructure:"concurrent_workers"`

	// 🆕 每日完整同步配置
	EnableDailyFullSync bool `mapstructure:"enable_daily_full_sync"`
	DailyFullSyncHour   int  `mapstructure:"daily_full_sync_hour"`
	DailyFullSyncMinute int  `mapstructure:"daily_full_sync_minute"`

	// 🆕 同步间隔配置（秒）
	TransferSyncIntervalSeconds int `mapstructure:"transfer_sync_interval_seconds"`
	UserSyncIntervalSeconds     int `mapstructure:"user_sync_interval_seconds"`

	// 🆕 批次大小配置
	TransferBatchSize int `mapstructure:"transfer_batch_size"`
	ContentBatchSize  int `mapstructure:"content_batch_size"`
	BurnBatchSize     int `mapstructure:"burn_batch_size"`

	// 🆕 性能配置
	MaxSyncDurationSeconds int  `mapstructure:"max_sync_duration_seconds"`
	EnableMetrics          bool `mapstructure:"enable_metrics"`

	// 🆕 数据保留配置
	MetricsRetentionDays int `mapstructure:"metrics_retention_days"`
	LogRetentionDays     int `mapstructure:"log_retention_days"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	CORS types.GlobalCORSConfig `mapstructure:"cors"`
}

// IPFSConfig IPFS配置 - 学习xLog的IPFS存储方案
type IPFSConfig struct {
	// 基础配置
	Provider   string `mapstructure:"provider"`    // IPFS服务提供商: pinata, infura, local
	APIUrl     string `mapstructure:"api_url"`     // IPFS API地址
	APIKey     string `mapstructure:"api_key"`     // API密钥
	APISecret  string `mapstructure:"api_secret"`  // API密钥
	GatewayURL string `mapstructure:"gateway_url"` // IPFS网关地址

	// 上传配置
	MaxFileSizeMB    int      `mapstructure:"max_file_size_mb"`   // 最大文件大小(MB)
	AllowedFileTypes []string `mapstructure:"allowed_file_types"` // 允许的文件类型
	AutoPin          bool     `mapstructure:"auto_pin"`           // 自动固定文件

	// 缓存配置
	CacheEnabled   bool `mapstructure:"cache_enabled"`     // 启用缓存
	CacheTTLHours  int  `mapstructure:"cache_ttl_hours"`   // 缓存过期时间(小时)
	CacheMaxSizeMB int  `mapstructure:"cache_max_size_mb"` // 最大缓存大小(MB)
}

func (c Config) IsConfig() {}
