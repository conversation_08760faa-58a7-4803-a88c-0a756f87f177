package analytics

import (
	"github.com/gin-gonic/gin"
)

// RegisterAnalyticsInternalRoutes 注册统计分析相关的内部服务路由
func RegisterAnalyticsInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/analytics")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.GET("/dashboard/internal", handler.GetDashboardStatsForService)
	// serviceGroup.GET("/rankings/internal", handler.GetRankingsForService)
	// serviceGroup.POST("/refresh/internal", handler.RefreshAnalyticsForService)
}
