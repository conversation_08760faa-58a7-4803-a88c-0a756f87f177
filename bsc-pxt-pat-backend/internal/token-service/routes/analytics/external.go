package analytics

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterAnalyticsExternalRoutes 注册统计分析相关的外部API路由
func RegisterAnalyticsExternalRoutes(r *gin.RouterGroup, analyticsHandler *handler.AnalyticsHandler) {
	// 统计分析路由
	analytics := r.Group("/analytics")
	{
		analytics.GET("/dashboard", analyticsHandler.GetDashboardStats)
		analytics.GET("/tokens/:token/trends", analyticsHandler.GetTokenTrends)
		analytics.GET("/staking/trends", analyticsHandler.GetStakingTrends)
		analytics.GET("/overview", analyticsHandler.GetOverview)
		analytics.GET("/websocket/stats", analyticsHandler.GetWebSocketStats)
	}
}
