package bridge

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterBridgeRouter 注册跨链桥路由
func RegisterBridgeRouter(router *gin.RouterGroup, bridgeHandler *handler.BridgeHandler) {
	bridgeGroup := router.Group("/bridge")
	{
		// 跨链概览
		bridgeGroup.GET("/overview", bridgeHandler.GetBridgeOverview)
		
		// 跨链请求管理
		bridgeGroup.GET("/requests", bridgeHandler.GetBridgeRequests)
		bridgeGroup.GET("/requests/:request_id", bridgeHandler.GetBridgeRequestByID)
		
		// 跨链统计
		bridgeGroup.GET("/stats", bridgeHandler.GetBridgeStats)
		
		// 验证者管理
		bridgeGroup.GET("/validators", bridgeHandler.GetBridgeValidators)
		
		// 健康检查
		bridgeGroup.GET("/health", bridgeHandler.GetBridgeHealth)
	}
}
