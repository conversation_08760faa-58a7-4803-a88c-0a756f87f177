package token

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterTokenExternalRoutes 注册代币相关的外部API路由
func RegisterTokenExternalRoutes(r *gin.RouterGroup, tokenHandler *handler.TokenHandler) {
	tokens := r.Group("/tokens")
	{
		// 代币概览
		tokens.GET("/overview", tokenHandler.GetOverview)

		// 代币基础信息
		tokens.GET("/pxt/info", tokenHandler.GetPXTInfo)
		tokens.GET("/pat/info", tokenHandler.GetPATInfo)
		tokens.GET("/:token/info", tokenHandler.GetTokenInfo)

		// 代币供应量
		tokens.GET("/supply", tokenHandler.GetTokenSupply)

		// 用户余额
		tokens.GET("/balances/:address", tokenHandler.GetUserBalances)
		tokens.POST("/balances/:address/refresh", tokenHandler.RefreshUserBalance)

		// 转账历史
		tokens.GET("/transfers", tokenHandler.GetRecentTransfers)
		tokens.GET("/transfers/:address", tokenHandler.GetTransferHistory)

		// 持币者排行榜
		tokens.GET("/holders", tokenHandler.GetTopHolders)
		tokens.GET("/:token/holders", tokenHandler.GetTokenHolders)
	}
}
