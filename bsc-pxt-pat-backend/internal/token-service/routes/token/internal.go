package token

import (
	"github.com/gin-gonic/gin"
)

// RegisterTokenInternalRoutes 注册代币相关的内部服务路由
func RegisterTokenInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/tokens")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.GET("/balances/:address/internal", handler.GetUserBalancesForService)
	// serviceGroup.POST("/sync", handler.SyncTokenDataForService)
	// serviceGroup.GET("/supply/internal", handler.GetTokenSupplyForService)
}
