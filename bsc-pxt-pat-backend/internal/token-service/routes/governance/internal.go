package governance

import (
	"github.com/gin-gonic/gin"
)

// RegisterGovernanceInternalRoutes 注册治理相关的内部服务路由
func RegisterGovernanceInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/governance")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.GET("/proposals/:id/internal", handler.GetProposalDetailForService)
	// serviceGroup.POST("/votes/sync", handler.SyncVotesForService)
	// serviceGroup.GET("/stats/internal", handler.GetGovernanceStatsForService)
}
