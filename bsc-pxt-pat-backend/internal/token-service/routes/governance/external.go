package governance

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterGovernanceExternalRoutes 注册治理相关的外部API路由
func RegisterGovernanceExternalRoutes(r *gin.RouterGroup, governanceHandler *handler.GovernanceHandler) {
	governance := r.Group("/governance")
	{
		// 提案相关
		governance.GET("/proposals", governanceHandler.GetProposals)
		governance.GET("/proposals/:id", governanceHandler.GetProposalDetail)
		governance.GET("/proposals/:id/votes", governanceHandler.GetProposalVotes)

		// 用户相关
		governance.GET("/power/:address", governanceHandler.GetUserVotingPower)
		governance.GET("/votes/:address", governanceHandler.GetUserVoteHistory)
		governance.GET("/delegates/:address", governanceHandler.GetUserDelegations)

		// 统计数据
		governance.GET("/stats", governanceHandler.GetGovernanceStats)
	}
}
