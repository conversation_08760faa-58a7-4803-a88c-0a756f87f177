package staking

import (
	"github.com/gin-gonic/gin"
)

// RegisterStakingInternalRoutes 注册质押相关的内部服务路由
func RegisterStakingInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/staking")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.GET("/user/:address/stats", handler.GetUserStakingStatsForService)
	// serviceGroup.POST("/sync", handler.SyncStakingDataForService)
	// serviceGroup.GET("/leaderboard/internal", handler.GetLeaderboardForService)
}
