package staking

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterStakingExternalRoutes 注册质押相关的外部API路由
func RegisterStakingExternalRoutes(r *gin.RouterGroup, httpHandler *handler.HTTPHandler) {
	// 质押相关路由
	staking := r.Group("/staking")
	{
		// 排行榜路由 - 使用分页中间件
		staking.GET("/leaderboard", httpHandler.GetLeaderboard)
		staking.GET("/overview", httpHandler.GetStakingOverview)
		staking.GET("/level-distribution", httpHandler.GetLevelDistribution)

		// 用户相关路由 - 使用地址验证中间件
		staking.GET("/user/:address", httpHandler.GetUserStakingStats)
		staking.GET("/user/:address/ranking", httpHandler.GetUserRanking)
		staking.POST("/user/:address/sync", httpHandler.SyncUserStakingData)
		staking.GET("/user/:address/freshness", httpHandler.CheckDataFreshness)
	}

	// 管理相关路由
	admin := r.Group("/admin")
	{
		admin.POST("/cache/refresh", httpHandler.RefreshCache)
	}
}
