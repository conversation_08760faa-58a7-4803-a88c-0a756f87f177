package ranking

import (
	"github.com/gin-gonic/gin"
)

// RegisterRankingInternalRoutes 注册排行榜相关的内部服务路由
func RegisterRankingInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/rankings")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.GET("/staking/internal", handler.GetStakingRankingForService)
	// serviceGroup.GET("/holders/internal", handler.GetTokenHolderRankingForService)
	// serviceGroup.POST("/refresh/internal", handler.RefreshRankingsForService)
}
