package ranking

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterRankingExternalRoutes 注册排行榜相关的外部API路由
func RegisterRankingExternalRoutes(r *gin.RouterGroup, rankingHandler *handler.RankingHandler) {
	// 排行榜路由
	rankings := r.Group("/rankings")
	{
		rankings.GET("/staking", rankingHandler.GetStakingRanking)
		rankings.GET("/holders/:token", rankingHandler.GetTokenHolderRanking)
		rankings.GET("/voting-power", rankingHandler.GetVotingPowerRanking)
		rankings.GET("/user/:address", rankingHandler.GetUserRanking)
		rankings.POST("/refresh", rankingHandler.RefreshRankings)
	}
}
