package routes

import (
	"pxpat-backend/internal/token-service/handler"
	"pxpat-backend/internal/token-service/routes/analytics"
	"pxpat-backend/internal/token-service/routes/bridge"
	"pxpat-backend/internal/token-service/routes/governance"
	"pxpat-backend/internal/token-service/routes/ranking"
	"pxpat-backend/internal/token-service/routes/staking"
	"pxpat-backend/internal/token-service/routes/token"
	"pxpat-backend/internal/token-service/routes/transaction"
	"pxpat-backend/internal/token-service/service"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(
	router *gin.Engine,
	stakingService *service.StakingService,
	leaderboardService *service.LeaderboardService,
	tokenService *service.TokenService,
	governanceService *service.GovernanceService,
	transactionService *service.TransactionService,
	analyticsService *service.AnalyticsService,
	rankingService *service.RankingService,
	websocketService *service.WebSocketService,
	syncService *service.SyncService,
	contentService *service.ContentService,
	bridgeService *service.BridgeService,
	logger zerolog.Logger,
) {
	// 创建所有Handler
	httpHandler := handler.NewHTTPHandler(
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		nil, // analyticsHandler 将在下面创建
		websocketService,
		syncService,
		logger,
	)

	var tokenHandler *handler.TokenHandler
	if tokenService != nil {
		tokenHandler = handler.NewTokenHandler(tokenService, logger)
	}

	var governanceHandler *handler.GovernanceHandler
	if governanceService != nil {
		governanceHandler = handler.NewGovernanceHandler(governanceService, logger)
	}

	var transactionHandler *handler.TransactionHandler
	if transactionService != nil {
		transactionHandler = handler.NewTransactionHandler(transactionService, logger)
	}

	var analyticsHandler *handler.AnalyticsHandler
	if analyticsService != nil {
		analyticsHandler = handler.NewAnalyticsHandler(analyticsService, websocketService, logger)
	}

	var rankingHandler *handler.RankingHandler
	if rankingService != nil {
		rankingHandler = handler.NewRankingHandler(rankingService, logger)
	}

	var contentHandler *handler.ContentHandler
	if contentService != nil {
		contentHandler = handler.NewContentHandler(contentService, logger)
	}

	var bridgeHandler *handler.BridgeHandler
	if bridgeService != nil {
		bridgeHandler = handler.NewBridgeHandler(bridgeService, logger)
	}

	// 健康检查
	router.GET("/health", httpHandler.HealthCheck)
	router.GET("/metrics", httpHandler.Metrics)
	logger.Info().Msg("✅ Health check routes registered:")
	logger.Info().Msg("   GET  /health")
	logger.Info().Msg("   GET  /metrics")

	// API v1 路由组
	v1 := router.Group("/api/v1")

	// 注册各功能域路由
	staking.RegisterStakingRouter(v1, httpHandler)
	logger.Info().Msg("✅ Staking routes registered:")
	logger.Info().Msg("   GET  /api/v1/staking/overview")
	logger.Info().Msg("   GET  /api/v1/staking/stats")
	logger.Info().Msg("   GET  /api/v1/staking/events")

	if tokenHandler != nil {
		token.RegisterTokenRouter(v1, tokenHandler)
		logger.Info().Msg("✅ Token routes registered:")
		logger.Info().Msg("   GET  /api/v1/tokens/overview")
		logger.Info().Msg("   GET  /api/v1/tokens/transfers")
		logger.Info().Msg("   GET  /api/v1/tokens/balances")
		logger.Info().Msg("   GET  /api/v1/tokens/supply")
		logger.Info().Msg("   GET  /api/v1/tokens/holders")
	}

	if contentHandler != nil {
		handler.RegisterContentRoutes(v1, contentHandler)
		handler.RegisterContentIPFSRoutes(v1, contentHandler)
		logger.Info().Msg("✅ Content routes registered:")
		logger.Info().Msg("   POST /api/v1/content/publish")
		logger.Info().Msg("   POST /api/v1/content/publish-ipfs")
		logger.Info().Msg("   GET  /api/v1/content/records")
		logger.Info().Msg("   GET  /api/v1/content/stats")
		logger.Info().Msg("   GET  /api/v1/content/types")
		logger.Info().Msg("   GET  /api/v1/content/fees")
	}

	if governanceHandler != nil {
		governance.RegisterGovernanceRouter(v1, governanceHandler)
		logger.Info().Msg("✅ Governance routes registered:")
		logger.Info().Msg("   GET  /api/v1/governance/overview")
		logger.Info().Msg("   GET  /api/v1/governance/proposals")
	}

	// 跨链桥相关路由 - 新增
	if bridgeHandler != nil {
		bridge.RegisterBridgeRouter(v1, bridgeHandler)
		logger.Info().Msg("✅ Bridge routes registered:")
		logger.Info().Msg("   GET  /api/v1/bridge/overview")
		logger.Info().Msg("   GET  /api/v1/bridge/requests")
		logger.Info().Msg("   GET  /api/v1/bridge/stats")
		logger.Info().Msg("   GET  /api/v1/bridge/validators")
		logger.Info().Msg("   GET  /api/v1/bridge/health")
		logger.Info().Msg("   GET  /api/v1/governance/votes")
	}

	if transactionHandler != nil {
		transaction.RegisterTransactionRouter(v1, transactionHandler)
		logger.Info().Msg("✅ Transaction routes registered:")
		logger.Info().Msg("   GET  /api/v1/transactions/network/info")
		logger.Info().Msg("   GET  /api/v1/transactions/recent")
	}

	if analyticsHandler != nil {
		analytics.RegisterAnalyticsRouter(v1, analyticsHandler)
		logger.Info().Msg("✅ Analytics routes registered:")
		logger.Info().Msg("   GET  /api/v1/analytics/overview")
		logger.Info().Msg("   GET  /api/v1/analytics/metrics")
	}

	if rankingHandler != nil {
		ranking.RegisterRankingRouter(v1, rankingHandler)
		logger.Info().Msg("✅ Ranking routes registered:")
		logger.Info().Msg("   GET  /api/v1/rankings/holders")
		logger.Info().Msg("   GET  /api/v1/rankings/stakers")
	}

	// 注册WebSocket处理器
	if websocketService != nil {
		v1.GET("/ws", websocketService.HandleWebSocket)
		logger.Info().Msg("✅ WebSocket route registered:")
		logger.Info().Msg("   GET  /api/v1/ws")
	}

	// 注册Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	logger.Info().Msg("✅ Swagger documentation route registered:")
	logger.Info().Msg("   GET  /swagger/index.html")

	logger.Info().Msg("🚀 All API routes registered successfully")
}
