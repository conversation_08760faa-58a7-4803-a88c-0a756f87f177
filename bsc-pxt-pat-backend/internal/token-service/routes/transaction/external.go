package transaction

import (
	"pxpat-backend/internal/token-service/handler"

	"github.com/gin-gonic/gin"
)

// RegisterTransactionExternalRoutes 注册交易相关的外部API路由
func RegisterTransactionExternalRoutes(r *gin.RouterGroup, transactionHandler *handler.TransactionHandler) {
	transactions := r.Group("/transactions")
	{
		// 交易准备和广播
		transactions.POST("/prepare", transactionHandler.PrepareTransaction)
		transactions.POST("/broadcast", transactionHandler.BroadcastTransaction)

		// 交易状态查询
		transactions.GET("/:hash/status", transactionHandler.GetTransactionStatus)

		// 网络信息
		transactions.GET("/network/info", transactionHandler.GetNetworkInfo)
		transactions.GET("/network/gas-price", transactionHandler.GetGasPrice)
	}

	// 用户相关
	users := r.Group("/user")
	{
		users.GET("/:address/nonce", transactionHandler.GetUserNonce)
	}

	// 质押相关交易
	staking := r.Group("/staking")
	{
		staking.POST("/prepare-stake", transactionHandler.PrepareStakeTransaction)
		staking.POST("/prepare-unstake", transactionHandler.PrepareUnstakeTransaction)
		staking.POST("/estimate", transactionHandler.EstimateStakingRewards)
	}

	// 治理相关交易
	governance := r.Group("/governance")
	{
		governance.POST("/prepare-vote", transactionHandler.PrepareVoteTransaction)
		governance.POST("/prepare-delegate", transactionHandler.PrepareDelegateTransaction)
	}
}
