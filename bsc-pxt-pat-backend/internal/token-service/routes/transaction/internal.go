package transaction

import (
	"github.com/gin-gonic/gin"
)

// RegisterTransactionInternalRoutes 注册交易相关的内部服务路由
func RegisterTransactionInternalRoutes(r *gin.RouterGroup) {
	_ = r.Group("/intra/transactions")
	// 微服务内部接口
	// TODO: 添加内部服务接口，如：
	// serviceGroup.POST("/prepare/internal", handler.PrepareTransactionForService)
	// serviceGroup.GET("/:hash/status/internal", handler.GetTransactionStatusForService)
	// serviceGroup.GET("/network/info/internal", handler.GetNetworkInfoForService)
}
