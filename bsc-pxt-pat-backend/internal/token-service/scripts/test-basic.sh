#!/bin/bash

# Token Service 基础测试脚本

echo "🚀 Token Service 基础架构测试"
echo "================================"

# 设置工作目录
cd "$(dirname "$0")/.."

# 检查Go版本
echo "📋 检查Go版本..."
go version

# 检查依赖
echo "📦 检查依赖..."
go mod tidy

# 编译检查
echo "🔨 编译检查..."
go build -o /tmp/token-service ./cmd/main.go

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
else
    echo "❌ 编译失败！"
    exit 1
fi

# 运行测试
echo "🧪 运行测试..."
go test ./...

if [ $? -eq 0 ]; then
    echo "✅ 测试通过！"
else
    echo "⚠️  测试有问题，但继续..."
fi

echo ""
echo "🎉 基础架构测试完成！"
echo ""
echo "下一步："
echo "1. 配置数据库连接"
echo "2. 运行数据库迁移"
echo "3. 启动服务测试API"
echo ""
echo "启动命令："
echo "cd backend/internal/token-service"
echo "go run cmd/main.go"
