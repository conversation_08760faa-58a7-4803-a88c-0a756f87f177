#!/bin/bash

# Token Service GORM迁移测试脚本

echo "🗄️  Token Service GORM迁移测试"
echo "================================"

# 设置工作目录
cd "$(dirname "$0")/.."

# 检查配置文件
CONFIG_FILE="../../../../configs/token-service.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "⚠️  配置文件不存在，创建示例配置..."
    mkdir -p "../../../../configs/finance-cluster"
    cat > "$CONFIG_FILE" << EOF
# Token Service 配置文件

# 服务器配置
server:
  http_port: 14001
  grpc_port: 24001
  mode: debug

# 数据库配置
database:
  host: localhost
  port: 5432
  user: postgres
  password: ""
  dbname: pxpat_finance_test
  sslmode: disable

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 1

# 区块链配置
blockchain:
  network: bsc_testnet
  rpc_url: "https://data-seed-prebsc-1-s1.binance.org:8545/"
  staking_pool_address: "0x6fD63476E4F2cb0b4841500e13eE851679A869f1"
  start_block: 54313108
  confirm_blocks: 12
  batch_size: 1000

# 日志配置
log:
  level: info
  format: json
EOF
    echo "✅ 创建了示例配置文件: $CONFIG_FILE"
fi

# 检查Go模块
echo "📦 检查Go模块..."
go mod tidy

# 编译检查
echo "🔨 编译检查..."
go build -o /tmp/token-service ./cmd/main.go
if [ $? -eq 0 ]; then
    echo "✅ 主程序编译成功！"
else
    echo "❌ 主程序编译失败！"
    exit 1
fi

go build -o /tmp/migrate ./cmd/migrate/main.go
if [ $? -eq 0 ]; then
    echo "✅ 迁移工具编译成功！"
else
    echo "❌ 迁移工具编译失败！"
    exit 1
fi

echo ""
echo "🎉 GORM迁移准备完成！"
echo ""
echo "下一步操作："
echo "1. 确保PostgreSQL数据库运行"
echo "2. 创建数据库: createdb pxpat_finance_test"
echo "3. 运行迁移: make db-migrate"
echo "4. 启动服务: make dev"
echo ""
echo "迁移命令："
echo "  make db-migrate    # 运行迁移"
echo "  make db-reset      # 重置数据库"
echo "  make db-rollback   # 回滚迁移"
echo ""
echo "测试API："
echo "  curl http://localhost:14001/health"
echo "  curl http://localhost:14001/api/v1/staking/overview"
