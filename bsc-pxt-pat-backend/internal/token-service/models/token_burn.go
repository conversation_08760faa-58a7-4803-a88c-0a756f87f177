package models

import (
	"time"
)

// TokenBurn 代币销毁记录
type TokenBurn struct {
	ID               uint      `gorm:"primaryKey" json:"id"`
	TransactionHash  string    `gorm:"type:varchar(66);not null;uniqueIndex" json:"transaction_hash"`
	BlockNumber      uint64    `gorm:"not null;index" json:"block_number"`
	TokenType        string    `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	Amount           string    `gorm:"type:numeric(78,18);not null" json:"amount"`
	BurnReason       string    `gorm:"type:varchar(50);not null" json:"burn_reason"` // fee, penalty, buyback
	InitiatorAddress string    `gorm:"type:varchar(42);not null;index" json:"initiator_address"`
	Timestamp        time.Time `gorm:"not null;index" json:"timestamp"`
	CreatedAt        time.Time `json:"created_at"`
}

// TableName 指定表名
func (TokenBurn) TableName() string {
	return "token_burns"
}
