package models

import (
	"time"
)

// TokenHolder 代币持有者统计
type TokenHolder struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	TokenType   string    `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	UserAddress string    `gorm:"type:varchar(42);not null;index" json:"user_address"`
	Balance     string    `gorm:"type:numeric(78,18);not null" json:"balance"`
	Percentage  float64   `gorm:"type:decimal(10,6);not null" json:"percentage"`
	Rank        int       `gorm:"not null;index" json:"rank"`
	LastUpdated time.Time `gorm:"not null" json:"last_updated"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TokenHolder) TableName() string {
	return "token_holders"
}
