package models

import (
	"time"
)

// StakingStats 质押统计表（用于缓存统计数据）
type StakingStats struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TotalStaked string    `gorm:"type:numeric(78,18);not null" json:"total_staked"`
	TotalUsers  int64     `gorm:"not null" json:"total_users"`
	ActiveUsers int64     `gorm:"not null" json:"active_users"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (StakingStats) TableName() string {
	return "token_staking_stats"
}
