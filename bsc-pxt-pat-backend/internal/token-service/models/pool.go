package models

import (
	"time"
)

// PoolBalance 池子余额模型
type PoolBalance struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	PoolAddress string    `gorm:"index;not null" json:"pool_address"` // 池子地址
	TokenType   string    `gorm:"not null" json:"token_type"`         // 代币类型 (PXT, PAT)
	Balance     string    `gorm:"not null" json:"balance"`            // 余额
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`   // 更新时间
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`   // 创建时间
}

// TableName 指定表名
func (PoolBalance) TableName() string {
	return "token_pool_balances"
}

// PoolActivity 池子活动记录模型
type PoolActivity struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	PoolAddress     string    `gorm:"index;not null" json:"pool_address"`     // 池子地址
	PoolType        string    `gorm:"index;not null" json:"pool_type"`        // 池子类型 (china_mainland, global)
	TransactionHash string    `gorm:"uniqueIndex;not null" json:"transaction_hash"` // 交易哈希
	TokenType       string    `gorm:"index;not null" json:"token_type"`       // 代币类型 (PXT, PAT)
	Direction       string    `gorm:"index;not null" json:"direction"`        // 方向 (in, out)
	FromAddress     string    `gorm:"index;not null" json:"from_address"`     // 发送地址
	ToAddress       string    `gorm:"index;not null" json:"to_address"`       // 接收地址
	Amount          string    `gorm:"not null" json:"amount"`                 // 金额
	Purpose         string    `gorm:"index" json:"purpose"`                   // 用途 (reward, deposit, transfer, etc.)
	BlockNumber     uint64    `gorm:"index;not null" json:"block_number"`     // 区块号
	Timestamp       time.Time `gorm:"index;not null" json:"timestamp"`       // 时间戳
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`       // 创建时间
}

// TableName 指定表名
func (PoolActivity) TableName() string {
	return "token_pool_activities"
}

// PoolStats 池子统计模型
type PoolStats struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	PoolAddress     string    `gorm:"uniqueIndex;not null" json:"pool_address"` // 池子地址
	PoolType        string    `gorm:"not null" json:"pool_type"`                // 池子类型
	TotalTransfers  int64     `gorm:"default:0" json:"total_transfers"`         // 总转账次数
	TotalInAmount   string    `gorm:"default:'0'" json:"total_in_amount"`       // 总转入金额
	TotalOutAmount  string    `gorm:"default:'0'" json:"total_out_amount"`      // 总转出金额
	LastActivity    time.Time `json:"last_activity"`                           // 最后活动时间
	DailyTransfers  int64     `gorm:"default:0" json:"daily_transfers"`         // 日转账次数
	WeeklyTransfers int64     `gorm:"default:0" json:"weekly_transfers"`        // 周转账次数
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`         // 更新时间
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`         // 创建时间
}

// TableName 指定表名
func (PoolStats) TableName() string {
	return "token_pool_stats"
}
