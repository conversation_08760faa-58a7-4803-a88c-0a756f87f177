package models

import (
	"database/sql/driver"
	"fmt"
	"math/big"
	"strings"
)

// BigInt 自定义big.Int类型，实现GORM序列化
type BigInt struct {
	*big.Int
}

// NewBigInt 创建新的BigInt
func NewBigInt(i *big.Int) *BigInt {
	if i == nil {
		return &BigInt{big.NewInt(0)}
	}
	return &BigInt{i}
}

// Value 实现driver.Valuer接口，用于数据库存储
func (b *BigInt) Value() (driver.Value, error) {
	if b == nil || b.Int == nil {
		return "0", nil
	}
	return b.Int.String(), nil
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (b *BigInt) Scan(value interface{}) error {
	if value == nil {
		b.Int = big.NewInt(0)
		return nil
	}

	switch v := value.(type) {
	case string:
		if b.Int == nil {
			b.Int = new(big.Int)
		}
		// 处理decimal格式，移除小数部分
		if dotIndex := strings.Index(v, "."); dotIndex != -1 {
			v = v[:dotIndex]
		}
		_, ok := b.Int.SetString(v, 10)
		if !ok {
			return fmt.Errorf("cannot parse %s as big.Int", v)
		}
	case []byte:
		if b.Int == nil {
			b.Int = new(big.Int)
		}
		str := string(v)
		// 处理decimal格式，移除小数部分
		if dotIndex := strings.Index(str, "."); dotIndex != -1 {
			str = str[:dotIndex]
		}
		_, ok := b.Int.SetString(str, 10)
		if !ok {
			return fmt.Errorf("cannot parse %s as big.Int", str)
		}
	default:
		return fmt.Errorf("cannot scan %T into BigInt", value)
	}

	return nil
}

// MarshalJSON 实现json.Marshaler接口，用于JSON序列化
func (b *BigInt) MarshalJSON() ([]byte, error) {
	if b == nil || b.Int == nil {
		return []byte("\"0\""), nil
	}
	return []byte("\"" + b.Int.String() + "\""), nil
}

// UnmarshalJSON 实现json.Unmarshaler接口，用于JSON反序列化
func (b *BigInt) UnmarshalJSON(data []byte) error {
	if b.Int == nil {
		b.Int = new(big.Int)
	}

	// 移除引号
	str := strings.Trim(string(data), "\"")
	_, ok := b.Int.SetString(str, 10)
	if !ok {
		return fmt.Errorf("cannot parse %s as big.Int", str)
	}
	return nil
}

// StakingLevel 质押等级枚举
type StakingLevel int

const (
	DingJi StakingLevel = iota
	ChengJi
	YiJi
	JiaJi
	ShiJue
	ShuangShiJue
	ZhiZun
)

// String 返回质押等级的字符串表示
func (s StakingLevel) String() string {
	switch s {
	case DingJi:
		return "丁级"
	case ChengJi:
		return "丙级"
	case YiJi:
		return "乙级"
	case JiaJi:
		return "甲级"
	case ShiJue:
		return "十绝"
	case ShuangShiJue:
		return "双十绝"
	case ZhiZun:
		return "至尊"
	default:
		return "未知"
	}
}

// Value 实现driver.Valuer接口，用于数据库存储
func (s StakingLevel) Value() (driver.Value, error) {
	return int64(s), nil
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (s *StakingLevel) Scan(value interface{}) error {
	if value == nil {
		*s = DingJi
		return nil
	}

	switch v := value.(type) {
	case int64:
		*s = StakingLevel(v)
	case int:
		*s = StakingLevel(v)
	default:
		return fmt.Errorf("cannot scan %T into StakingLevel", value)
	}

	return nil
}

// EventType 事件类型枚举
type EventType string

const (
	EventTypeStaked              EventType = "STAKED"
	EventTypeWithdrawn           EventType = "WITHDRAWN"
	EventTypeRewardClaimed       EventType = "REWARD_CLAIMED"
	EventTypeStakingLevelUpdated EventType = "STAKING_LEVEL_UPDATED"
	EventTypeMiningPowerUpdated  EventType = "MINING_POWER_UPDATED"
)
