package models

import (
	"time"
)

// TokenTransfer 代币转账记录
type TokenTransfer struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	TransactionHash string    `gorm:"type:varchar(66);not null;uniqueIndex" json:"transaction_hash"`
	BlockNumber     uint64    `gorm:"not null;index" json:"block_number"`
	LogIndex        uint64    `gorm:"not null" json:"log_index"`
	TokenType       string    `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	FromAddress     string    `gorm:"type:varchar(42);not null;index" json:"from_address"`
	ToAddress       string    `gorm:"type:varchar(42);not null;index" json:"to_address"`
	Amount          string    `gorm:"type:numeric(78,18);not null" json:"amount"`
	Timestamp       time.Time `gorm:"not null;index" json:"timestamp"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (TokenTransfer) TableName() string {
	return "token_transfers"
}
