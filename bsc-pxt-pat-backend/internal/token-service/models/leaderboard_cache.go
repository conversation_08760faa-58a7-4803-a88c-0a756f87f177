package models

import (
	"time"
)

// LeaderboardCache 排行榜缓存
type LeaderboardCache struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	Cache<PERSON>ey  string    `gorm:"type:varchar(100);not null;uniqueIndex" json:"cache_key"`
	CacheData string    `gorm:"type:text;not null" json:"cache_data"`
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (LeaderboardCache) TableName() string {
	return "token_leaderboard_cache"
}

// IsExpired 检查缓存是否过期
func (l *LeaderboardCache) IsExpired() bool {
	return time.Now().After(l.ExpiresAt)
}
