package models

import (
	"time"
)

// Delegation 投票权委托记录
type Delegation struct {
	ID              uint       `gorm:"primaryKey" json:"id"`
	Delegator       string     `gorm:"type:varchar(42);not null;index" json:"delegator"`
	Delegate        string     `gorm:"type:varchar(42);not null;index" json:"delegate"`
	Amount          string     `gorm:"type:numeric(78,18);not null" json:"amount"`
	IsActive        bool       `gorm:"default:true;index" json:"is_active"`
	StartTime       time.Time  `gorm:"not null" json:"start_time"`
	EndTime         *time.Time `json:"end_time"`
	TransactionHash string     `gorm:"type:varchar(66);not null" json:"transaction_hash"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (Delegation) TableName() string {
	return "token_delegations"
}
