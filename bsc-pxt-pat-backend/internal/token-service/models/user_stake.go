package models

import (
	"time"
)

// UserStake 用户质押信息
type UserStake struct {
	ID                  uint64       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserAddress         string       `gorm:"type:varchar(42);not null;index" json:"user_address"`
	Amount              *BigInt      `gorm:"type:numeric(78,18);not null" json:"amount"`
	StakingLevel        StakingLevel `gorm:"type:smallint;not null;index" json:"staking_level"`
	StartTime           time.Time    `gorm:"not null" json:"start_time"`
	LastUpdateTime      time.Time    `gorm:"default:CURRENT_TIMESTAMP" json:"last_update_time"`
	IsActive            bool         `gorm:"default:true;index" json:"is_active"`
	TotalRewardsClaimed *BigInt      `gorm:"type:numeric(78,18);default:0" json:"total_rewards_claimed"`
	MiningPower         *BigInt      `gorm:"type:numeric(78,18);default:0" json:"mining_power"`
	LastRewardTime      *time.Time   `json:"last_reward_time"`
	PendingRewards      *BigInt      `gorm:"type:numeric(78,18);default:0" json:"pending_rewards"`
	CreatedAt           time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt           time.Time    `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (UserStake) TableName() string {
	return "token_user_stakes"
}
