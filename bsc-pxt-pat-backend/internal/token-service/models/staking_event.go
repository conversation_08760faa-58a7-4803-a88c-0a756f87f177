package models

import (
	"time"
)

// StakingEvent 质押事件记录
type StakingEvent struct {
	ID              uint64        `gorm:"primaryKey;autoIncrement" json:"id"`
	UserAddress     string        `gorm:"type:varchar(42);not null;index" json:"user_address"`
	EventType       EventType     `gorm:"type:varchar(20);not null;index" json:"event_type"`
	Amount          *BigInt       `gorm:"type:numeric(78,18)" json:"amount"`
	OldLevel        *StakingLevel `gorm:"type:smallint" json:"old_level"`
	NewLevel        *StakingLevel `gorm:"type:smallint" json:"new_level"`
	TransactionHash string        `gorm:"type:varchar(66);not null;uniqueIndex" json:"transaction_hash"`
	BlockNumber     uint64        `gorm:"not null;index" json:"block_number"`
	LogIndex        uint          `gorm:"not null" json:"log_index"`
	Timestamp       time.Time     `gorm:"not null;index" json:"timestamp"`
	CreatedAt       time.Time     `gorm:"autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (StakingEvent) TableName() string {
	return "token_staking_events"
}
