package models

import (
	"time"
)

// TreasuryOperation 国库操作记录
type TreasuryOperation struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	ProposalID      uint64    `gorm:"not null;index" json:"proposal_id"`
	OperationType   string    `gorm:"type:varchar(50);not null;index" json:"operation_type"` // transfer, burn, mint
	TokenType       string    `gorm:"type:varchar(10);not null" json:"token_type"`           // PXT, PAT
	Amount          string    `gorm:"type:numeric(78,18);not null" json:"amount"`
	Recipient       string    `gorm:"type:varchar(42)" json:"recipient"`
	Purpose         string    `gorm:"type:varchar(200);not null" json:"purpose"`
	TransactionHash string    `gorm:"type:varchar(66);not null;uniqueIndex" json:"transaction_hash"`
	BlockNumber     uint64    `gorm:"not null" json:"block_number"`
	ExecutedAt      time.Time `gorm:"not null" json:"executed_at"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (TreasuryOperation) TableName() string {
	return "token_treasury_operations"
}
