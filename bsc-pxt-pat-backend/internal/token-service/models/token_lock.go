package models

import (
	"time"
)

// TokenLock 代币锁定记录
type TokenLock struct {
	ID           uint       `gorm:"primaryKey" json:"id"`
	UserAddress  string     `gorm:"type:varchar(42);not null;index" json:"user_address"`
	TokenType    string     `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	Amount       string     `gorm:"type:numeric(78,18);not null" json:"amount"`
	LockType     string     `gorm:"type:varchar(20);not null;index" json:"lock_type"` // staking, governance, vesting
	LockDuration int        `gorm:"not null" json:"lock_duration"`                    // 锁定天数
	StartTime    time.Time  `gorm:"not null" json:"start_time"`
	EndTime      time.Time  `gorm:"not null;index" json:"end_time"`
	IsUnlocked   bool       `gorm:"default:false;index" json:"is_unlocked"`
	UnlockTime   *time.Time `json:"unlock_time"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (TokenLock) TableName() string {
	return "token_locks"
}
