package models

import (
	"time"
)

// TokenContentRecord 内容上链记录
type TokenContentRecord struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	ContentID      string    `gorm:"type:varchar(64);not null;index" json:"content_id"`         // 内容ID
	OnChainID      string    `gorm:"type:varchar(128);not null;uniqueIndex" json:"on_chain_id"` // 上链ID
	Title          string    `gorm:"type:varchar(500);not null" json:"title"`                   // 标题
	OriginalTitle  string    `gorm:"type:varchar(500)" json:"original_title"`                   // 原标题
	Description    string    `gorm:"type:text" json:"description"`                              // 描述
	ContentType    string    `gorm:"type:varchar(50);not null;index" json:"content_type"`       // 内容类型
	LicenseNumber  string    `gorm:"type:varchar(100)" json:"license_number"`                   // 许可证号
	Cast           string    `gorm:"type:text" json:"cast"`                                     // 演员列表(JSON)
	Director       string    `gorm:"type:varchar(200)" json:"director"`                         // 导演
	Publisher      string    `gorm:"type:varchar(200)" json:"publisher"`                        // 发布者
	PublisherKSUID string    `gorm:"type:varchar(64);not null;index" json:"publisher_ksuid"`    // 发布者KSUID
	ReleaseDate    time.Time `json:"release_date"`                                              // 发布日期
	OnChainDate    time.Time `gorm:"not null;index" json:"on_chain_date"`                       // 上链日期
	Reviewers      string    `gorm:"type:text;not null" json:"reviewers"`                       // 审核员列表(JSON)
	ContentHash    string    `gorm:"type:varchar(66);not null" json:"content_hash"`             // 内容哈希
	FileKSUID      string    `gorm:"type:varchar(64);index" json:"file_ksuid"`                  // 文件KSUID
	FileMD5        string    `gorm:"type:varchar(64);index" json:"file_md5"`                    // 文件MD5哈希

	// IPFS相关字段 - 学习xLog的IPFS存储方案
	IPFSHash     string `gorm:"type:varchar(100);index" json:"ipfs_hash"`     // 内容IPFS哈希
	MetadataIPFS string `gorm:"type:varchar(100);index" json:"metadata_ipfs"` // 元数据IPFS哈希

	// 区块链相关字段
	TransactionHash string    `gorm:"type:varchar(66);index" json:"transaction_hash"`         // 交易哈希
	BlockNumber     uint64    `gorm:"index" json:"block_number"`                              // 区块号
	PATFee          string    `gorm:"type:numeric(18,8);not null" json:"pat_fee"`             // PAT费用
	Status          string    `gorm:"type:varchar(20);default:'pending';index" json:"status"` // 状态: pending, confirmed, failed
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TokenContentRecord) TableName() string {
	return "token_content_records"
}

// ContentRecord 内容记录类型别名 (为了兼容性)
type ContentRecord = TokenContentRecord
