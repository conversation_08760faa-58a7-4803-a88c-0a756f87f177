package models

import (
	"time"
)

// BlockSyncStatus 区块同步状态
type BlockSyncStatus struct {
	ID              uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	LastSyncedBlock uint64    `gorm:"not null" json:"last_synced_block"`
	LastSyncedTime  time.Time `gorm:"not null" json:"last_synced_time"`
	SyncStatus      string    `gorm:"type:varchar(20);default:'syncing'" json:"sync_status"` // syncing, synced, error
	ErrorMessage    string    `gorm:"type:text" json:"error_message"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (BlockSyncStatus) TableName() string {
	return "token_block_sync_status"
}
