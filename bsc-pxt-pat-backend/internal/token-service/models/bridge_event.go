package models

import (
	"time"

	"gorm.io/gorm"
)

// BridgeRequest 跨链请求记录
type BridgeRequest struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	RequestID         string    `json:"request_id" gorm:"uniqueIndex;not null;comment:跨链请求ID"`
	FromChain         uint64    `json:"from_chain" gorm:"not null;comment:源链ID"`
	ToChain           uint64    `json:"to_chain" gorm:"not null;comment:目标链ID"`
	TokenAddress      string    `json:"token_address" gorm:"not null;comment:代币合约地址"`
	Sender            string    `json:"sender" gorm:"not null;comment:发送者地址"`
	Receiver          string    `json:"receiver" gorm:"not null;comment:接收者地址"`
	Amount            string    `json:"amount" gorm:"not null;comment:跨链数量"`
	Fee               string    `json:"fee" gorm:"not null;comment:跨链手续费"`
	Status            string    `json:"status" gorm:"not null;default:pending;comment:状态:pending,confirmed,failed"`
	TxHash            string    `json:"tx_hash" gorm:"not null;comment:交易哈希"`
	BlockNumber       uint64    `json:"block_number" gorm:"not null;comment:区块号"`
	ConfirmationCount uint      `json:"confirmation_count" gorm:"default:0;comment:确认数量"`
	RequiredConfirms  uint      `json:"required_confirms" gorm:"not null;comment:需要确认数"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// BridgeConfirmation 跨链确认记录
type BridgeConfirmation struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	RequestID string    `json:"request_id" gorm:"not null;comment:跨链请求ID"`
	Validator string    `json:"validator" gorm:"not null;comment:验证者地址"`
	TxHash    string    `json:"tx_hash" gorm:"not null;comment:确认交易哈希"`
	CreatedAt time.Time `json:"created_at"`
}

// BridgeStats 跨链统计
type BridgeStats struct {
	ID                  uint      `json:"id" gorm:"primaryKey"`
	Date                time.Time `json:"date" gorm:"uniqueIndex;comment:统计日期"`
	TotalRequests       uint64    `json:"total_requests" gorm:"comment:总请求数"`
	SuccessfulRequests  uint64    `json:"successful_requests" gorm:"comment:成功请求数"`
	FailedRequests      uint64    `json:"failed_requests" gorm:"comment:失败请求数"`
	TotalVolume         string    `json:"total_volume" gorm:"comment:总跨链量"`
	TotalFees           string    `json:"total_fees" gorm:"comment:总手续费"`
	AverageConfirmTime  uint64    `json:"average_confirm_time" gorm:"comment:平均确认时间(秒)"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// BridgeValidator 跨链验证者信息
type BridgeValidator struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	Address         string    `json:"address" gorm:"uniqueIndex;not null;comment:验证者地址"`
	IsActive        bool      `json:"is_active" gorm:"default:true;comment:是否活跃"`
	TotalConfirms   uint64    `json:"total_confirms" gorm:"default:0;comment:总确认次数"`
	LastConfirmTime time.Time `json:"last_confirm_time" gorm:"comment:最后确认时间"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 设置表名
func (BridgeRequest) TableName() string {
	return "bridge_requests"
}

func (BridgeConfirmation) TableName() string {
	return "bridge_confirmations"
}

func (BridgeStats) TableName() string {
	return "bridge_stats"
}

func (BridgeValidator) TableName() string {
	return "bridge_validators"
}

// BeforeCreate 创建前钩子
func (br *BridgeRequest) BeforeCreate(tx *gorm.DB) error {
	if br.CreatedAt.IsZero() {
		br.CreatedAt = time.Now()
	}
	if br.UpdatedAt.IsZero() {
		br.UpdatedAt = time.Now()
	}
	return nil
}

func (bc *BridgeConfirmation) BeforeCreate(tx *gorm.DB) error {
	if bc.CreatedAt.IsZero() {
		bc.CreatedAt = time.Now()
	}
	return nil
}

func (bs *BridgeStats) BeforeCreate(tx *gorm.DB) error {
	if bs.CreatedAt.IsZero() {
		bs.CreatedAt = time.Now()
	}
	if bs.UpdatedAt.IsZero() {
		bs.UpdatedAt = time.Now()
	}
	return nil
}

func (bv *BridgeValidator) BeforeCreate(tx *gorm.DB) error {
	if bv.CreatedAt.IsZero() {
		bv.CreatedAt = time.Now()
	}
	if bv.UpdatedAt.IsZero() {
		bv.UpdatedAt = time.Now()
	}
	return nil
}

// BeforeUpdate 更新前钩子
func (br *BridgeRequest) BeforeUpdate(tx *gorm.DB) error {
	br.UpdatedAt = time.Now()
	return nil
}

func (bs *BridgeStats) BeforeUpdate(tx *gorm.DB) error {
	bs.UpdatedAt = time.Now()
	return nil
}

func (bv *BridgeValidator) BeforeUpdate(tx *gorm.DB) error {
	bv.UpdatedAt = time.Now()
	return nil
}

// BridgeOverviewDTO 跨链概览DTO
type BridgeOverviewDTO struct {
	TotalRequests      uint64 `json:"total_requests"`
	SuccessfulRequests uint64 `json:"successful_requests"`
	FailedRequests     uint64 `json:"failed_requests"`
	PendingRequests    uint64 `json:"pending_requests"`
	TotalVolume        string `json:"total_volume"`
	TotalFees          string `json:"total_fees"`
	SuccessRate        string `json:"success_rate"`
	ActiveValidators   uint64 `json:"active_validators"`
}

// BridgeRequestDTO 跨链请求DTO
type BridgeRequestDTO struct {
	RequestID         string    `json:"request_id"`
	FromChain         uint64    `json:"from_chain"`
	ToChain           uint64    `json:"to_chain"`
	TokenSymbol       string    `json:"token_symbol"`
	Sender            string    `json:"sender"`
	Receiver          string    `json:"receiver"`
	Amount            string    `json:"amount"`
	Fee               string    `json:"fee"`
	Status            string    `json:"status"`
	TxHash            string    `json:"tx_hash"`
	ConfirmationCount uint      `json:"confirmation_count"`
	RequiredConfirms  uint      `json:"required_confirms"`
	Progress          string    `json:"progress"`
	CreatedAt         time.Time `json:"created_at"`
}

// BridgeStatsDTO 跨链统计DTO
type BridgeStatsDTO struct {
	Date               string `json:"date"`
	TotalRequests      uint64 `json:"total_requests"`
	SuccessfulRequests uint64 `json:"successful_requests"`
	FailedRequests     uint64 `json:"failed_requests"`
	TotalVolume        string `json:"total_volume"`
	TotalFees          string `json:"total_fees"`
	AverageConfirmTime uint64 `json:"average_confirm_time"`
}

// BridgeValidatorDTO 验证者DTO
type BridgeValidatorDTO struct {
	Address         string    `json:"address"`
	IsActive        bool      `json:"is_active"`
	TotalConfirms   uint64    `json:"total_confirms"`
	LastConfirmTime time.Time `json:"last_confirm_time"`
}
