package models

import (
	"time"
)

// ProposalParameter 提案参数变更记录
type ProposalParameter struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	ProposalID uint64    `gorm:"not null;index" json:"proposal_id"`
	ParamName  string    `gorm:"type:varchar(100);not null" json:"param_name"`
	OldValue   string    `gorm:"type:text" json:"old_value"`
	NewValue   string    `gorm:"type:text;not null" json:"new_value"`
	ParamType  string    `gorm:"type:varchar(50);not null" json:"param_type"` // uint256, string, bool, address
	CreatedAt  time.Time `json:"created_at"`
}

// TableName 指定表名
func (ProposalParameter) TableName() string {
	return "token_proposal_parameters"
}
