package models

import (
	"time"
)

// Vote 投票记录
type Vote struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	ProposalID      uint64    `gorm:"not null;index" json:"proposal_id"`
	Voter           string    `gorm:"type:varchar(42);not null;index" json:"voter"`
	Support         int       `gorm:"not null" json:"support"` // 0=against, 1=for, 2=abstain
	VotingPower     string    `gorm:"type:numeric(78,18);not null" json:"voting_power"`
	Reason          string    `gorm:"type:text" json:"reason"`
	BlockNumber     uint64    `gorm:"not null" json:"block_number"`
	TransactionHash string    `gorm:"type:varchar(66);not null;uniqueIndex" json:"transaction_hash"`
	Timestamp       time.Time `gorm:"not null;index" json:"timestamp"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (Vote) TableName() string {
	return "token_votes"
}
