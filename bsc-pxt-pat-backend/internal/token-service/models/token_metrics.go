package models

import (
	"time"
)

// TokenMetrics 代币经济指标
type TokenMetrics struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	TokenType       string    `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	Price           string    `gorm:"type:numeric(18,8)" json:"price"`
	MarketCap       string    `gorm:"type:numeric(78,18)" json:"market_cap"`
	Volume24h       string    `gorm:"type:numeric(78,18)" json:"volume_24h"`
	Transactions24h int       `gorm:"not null" json:"transactions_24h"`
	ActiveHolders   int       `gorm:"not null" json:"active_holders"`
	VelocityRatio   float64   `gorm:"type:decimal(10,6)" json:"velocity_ratio"`
	InflationRate   float64   `gorm:"type:decimal(8,4)" json:"inflation_rate"`
	StakingRatio    float64   `gorm:"type:decimal(8,4)" json:"staking_ratio"`
	Date            time.Time `gorm:"type:date;not null;index" json:"date"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TokenMetrics) TableName() string {
	return "token_metrics"
}
