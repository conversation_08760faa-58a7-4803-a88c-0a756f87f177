package models

import (
	"time"
)

// TokenSupply 代币供应量信息
type TokenSupply struct {
	ID                uint      `gorm:"primaryKey" json:"id"`
	TokenType         string    `gorm:"type:varchar(10);not null;uniqueIndex" json:"token_type"` // PXT, PAT
	TotalSupply       string    `gorm:"type:numeric(78,18);not null" json:"total_supply"`
	CirculatingSupply string    `gorm:"type:numeric(78,18);not null" json:"circulating_supply"`
	LockedSupply      string    `gorm:"type:numeric(78,18);not null" json:"locked_supply"`
	BurnedSupply      string    `gorm:"type:numeric(78,18);default:0" json:"burned_supply"`
	LastUpdated       time.Time `gorm:"not null" json:"last_updated"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TokenSupply) TableName() string {
	return "token_supply"
}
