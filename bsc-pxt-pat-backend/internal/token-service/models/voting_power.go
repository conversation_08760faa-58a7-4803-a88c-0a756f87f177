package models

import (
	"time"
)

// VotingPower 投票权重记录
type VotingPower struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	UserAddress     string    `gorm:"type:varchar(42);not null;index" json:"user_address"`
	StakedAmount    string    `gorm:"type:numeric(78,18);not null" json:"staked_amount"`
	StakingLevel    int       `gorm:"not null" json:"staking_level"`
	LevelMultiplier float64   `gorm:"type:decimal(5,2);not null" json:"level_multiplier"`
	DelegatedPower  string    `gorm:"type:numeric(78,18);default:0" json:"delegated_power"`
	TotalPower      string    `gorm:"type:numeric(78,18);not null" json:"total_power"`
	LastUpdated     time.Time `gorm:"not null" json:"last_updated"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (VotingPower) TableName() string {
	return "token_voting_power"
}
