package models

import (
	"time"
)

// TokenBalance 代币余额信息
type TokenBalance struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	UserAddress  string    `gorm:"type:varchar(42);not null;index" json:"user_address"`
	TokenType    string    `gorm:"type:varchar(10);not null;index" json:"token_type"` // PXT, PAT
	Balance      string    `gorm:"type:numeric(78,18);not null" json:"balance"`
	LockedAmount string    `gorm:"type:numeric(78,18);default:0" json:"locked_amount"`
	LastUpdated  time.Time `gorm:"not null" json:"last_updated"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (TokenBalance) TableName() string {
	return "token_balances"
}
