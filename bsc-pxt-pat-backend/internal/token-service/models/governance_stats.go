package models

import (
	"time"
)

// GovernanceStats 治理统计数据
type GovernanceStats struct {
	ID                uint      `gorm:"primaryKey" json:"id"`
	TotalProposals    int       `gorm:"not null" json:"total_proposals"`
	ActiveProposals   int       `gorm:"not null" json:"active_proposals"`
	ExecutedProposals int       `gorm:"not null" json:"executed_proposals"`
	TotalVoters       int       `gorm:"not null" json:"total_voters"`
	ActiveVoters      int       `gorm:"not null" json:"active_voters"`
	TotalVotingPower  string    `gorm:"type:numeric(78,18);not null" json:"total_voting_power"`
	ParticipationRate float64   `gorm:"type:decimal(8,4);not null" json:"participation_rate"`
	AverageQuorum     float64   `gorm:"type:decimal(8,4);not null" json:"average_quorum"`
	Date              time.Time `gorm:"type:date;not null;uniqueIndex" json:"date"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// TableName 指定表名
func (GovernanceStats) TableName() string {
	return "token_governance_stats"
}
