package models

import (
	"time"
)

// Proposal 治理提案
type Proposal struct {
	ID               uint       `gorm:"primaryKey" json:"id"`
	ProposalID       uint64     `gorm:"not null;uniqueIndex" json:"proposal_id"`
	Proposer         string     `gorm:"type:varchar(42);not null;index" json:"proposer"`
	Title            string     `gorm:"type:varchar(200);not null" json:"title"`
	Description      string     `gorm:"type:text;not null" json:"description"`
	Category         string     `gorm:"type:varchar(50);not null;index" json:"category"` // parameter, upgrade, treasury
	Status           string     `gorm:"type:varchar(20);not null;index" json:"status"`   // pending, active, succeeded, defeated, executed, cancelled
	StartBlock       uint64     `gorm:"not null" json:"start_block"`
	EndBlock         uint64     `gorm:"not null" json:"end_block"`
	ExecutionHash    string     `gorm:"type:varchar(66)" json:"execution_hash"`
	ForVotes         string     `gorm:"type:numeric(78,18);default:0" json:"for_votes"`
	AgainstVotes     string     `gorm:"type:numeric(78,18);default:0" json:"against_votes"`
	AbstainVotes     string     `gorm:"type:numeric(78,18);default:0" json:"abstain_votes"`
	TotalVotes       string     `gorm:"type:numeric(78,18);default:0" json:"total_votes"`
	QuorumRequired   string     `gorm:"type:numeric(78,18);not null" json:"quorum_required"`
	MajorityRequired float64    `gorm:"type:decimal(5,4);not null" json:"majority_required"`
	ExecutedAt       *time.Time `json:"executed_at"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (Proposal) TableName() string {
	return "token_proposals"
}
