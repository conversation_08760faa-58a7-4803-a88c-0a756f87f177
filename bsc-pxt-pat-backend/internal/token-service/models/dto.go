package models

import (
	"time"
)

// LeaderboardEntry 排行榜条目
type LeaderboardEntry struct {
	UserAddress         string       `json:"user_address"`
	Amount              *BigInt      `json:"amount"`
	StakingLevel        StakingLevel `json:"staking_level"`
	MiningPower         *BigInt      `json:"mining_power"`
	TotalRewardsClaimed *BigInt      `json:"total_rewards_claimed"`
	StartTime           time.Time    `json:"start_time"`
	Rank                int          `json:"rank"`
}

// UserStakingStats 用户质押统计
type UserStakingStats struct {
	UserAddress         string       `json:"user_address"`
	Amount              *BigInt      `json:"amount"`
	StakingLevel        StakingLevel `json:"staking_level"`
	MiningPower         *BigInt      `json:"mining_power"`
	TotalRewardsClaimed *BigInt      `json:"total_rewards_claimed"`
	PendingRewards      *BigInt      `json:"pending_rewards"`
	StartTime           time.Time    `json:"start_time"`
	LastUpdateTime      time.Time    `json:"last_update_time"`
	RankByAmount        int          `json:"rank_by_amount"`
	RankByMiningPower   int          `json:"rank_by_mining_power"`
	StakingDays         int          `json:"staking_days"`
	IsActive            bool         `json:"is_active"`
}

// StakingOverview 质押总览统计
type StakingOverview struct {
	TotalStaked        *BigInt   `json:"total_staked"`
	TotalUsers         int64     `json:"total_users"`
	ActiveUsers        int64     `json:"active_users"`
	TotalRewardsClaimd *BigInt   `json:"total_rewards_claimed"`
	TotalMiningPower   *BigInt   `json:"total_mining_power"`
	AverageStakeAmount *BigInt   `json:"average_stake_amount"`
	LastUpdateTime     time.Time `json:"last_update_time"`
}

// LevelDistribution 等级分布统计
type LevelDistribution struct {
	Level       StakingLevel `json:"level"`
	Count       int64        `json:"count"`
	TotalAmount *BigInt      `json:"total_amount"`
	Percentage  float64      `json:"percentage"`
}

// UserRanking 用户排名
type UserRanking struct {
	UserAddress string `json:"user_address"`
	Rank        int64  `json:"rank"`
}
