# 📚 PXT-PAT 内容上链指南

## 🚀 概述

PXT-PAT Token服务提供了完整的内容上链功能，支持普通内容发布和IPFS去中心化存储。本指南将帮助你了解如何使用内容上链脚本。

## 📋 脚本列表

| 脚本文件 | 功能 | 用途 |
|---------|------|------|
| `scripts/publish-content.sh` | 普通内容上链 | 发布内容到区块链 |
| `scripts/publish-content-ipfs.sh` | IPFS内容上链 | 发布内容到IPFS和区块链 |
| `scripts/content-manager.sh` | 内容管理 | 综合内容管理工具 |

## 🔧 使用前准备

### 1. 启动Token服务
```bash
cd pxpat-backend
go run cmd/token-service/main.go
```

### 2. 验证服务状态
```bash
curl http://localhost:15004/health
```

### 3. 查看API文档
访问: http://localhost:15004/swagger/index.html

## 📖 使用指南

### 1. 内容管理脚本 (推荐)

这是最全面的管理工具，提供所有内容相关操作：

```bash
# 查看帮助
./scripts/content-manager.sh help

# 检查服务健康状态
./scripts/content-manager.sh health

# 列出支持的内容类型
./scripts/content-manager.sh list-types

# 获取内容统计信息
./scripts/content-manager.sh get-stats

# 列出内容记录
./scripts/content-manager.sh list-records 1

# 发布普通内容
./scripts/content-manager.sh publish video "我的视频" "测试视频" user_001

# 发布IPFS内容
./scripts/content-manager.sh publish-ipfs audio "音乐作品" "原创音乐" user_002

# 查询内容
./scripts/content-manager.sh query video_1234567890_abcd

# 查询IPFS内容
./scripts/content-manager.sh query-ipfs audio_ipfs_1234567890_abcd
```

### 2. 普通内容上链脚本

用于发布普通内容到区块链：

```bash
# 基本用法
./scripts/publish-content.sh [内容类型] [标题] [描述] [发布者KSUID]

# 示例
./scripts/publish-content.sh video "我的第一个视频" "这是一个测试视频" user_test_001
./scripts/publish-content.sh short_video "短视频测试" "TikTok风格短视频" user_creator_123
./scripts/publish-content.sh audio "音乐作品" "原创音乐分享" user_musician_456
```

### 3. IPFS内容上链脚本

用于发布内容到IPFS和区块链：

```bash
# 基本用法
./scripts/publish-content-ipfs.sh [内容类型] [标题] [描述] [发布者KSUID] [IPFS哈希(可选)]

# 使用预设IPFS哈希
./scripts/publish-content-ipfs.sh video "我的视频" "测试视频" user_001

# 使用自定义IPFS哈希
./scripts/publish-content-ipfs.sh audio "音乐作品" "原创音乐" user_002 QmYourCustomIPFSHash...
```

## 📊 支持的内容类型

| 内容类型 | 说明 | 预设IPFS哈希 |
|---------|------|-------------|
| `video` | 视频内容 | QmYjtig7VJQ6XsnUjqqJvj7QaMcCAwtrgNdahSiFofrE7o |
| `short_video` | 短视频 | QmYjtig7VJQ6XsnUjqqJvj7QaMcCAwtrgNdahSiFofrE7o |
| `audio` | 音频内容 | QmZ4tDuvesekSs4qM5ZBKpXiZGun7S2CYtEZRB3DYXkjGx |
| `music` | 音乐作品 | QmZ4tDuvesekSs4qM5ZBKpXiZGun7S2CYtEZRB3DYXkjGx |
| `image` | 图片内容 | QmSsYRx3LpDAb1GZQm7zZ1AuHZjfbPkD6J9s9r1CNEkxDv |
| `text` | 文本内容 | QmNLei78zWmzUdbeRB3CiUfAizWUrbeeZh5K1rhAQKCh51 |
| `live_stream` | 直播内容 | QmYjtig7VJQ6XsnUjqqJvj7QaMcCAwtrgNdahSiFofrE7o |
| `podcast` | 播客 | QmZ4tDuvesekSs4qM5ZBKpXiZGun7S2CYtEZRB3DYXkjGx |
| `ebook` | 电子书 | QmNLei78zWmzUdbeRB3CiUfAizWUrbeeZh5K1rhAQKCh51 |

## 🔍 输出说明

### 成功响应示例
```json
{
  "success": true,
  "data": {
    "id": 123,
    "content_id": "content_video_1234567890_abcd",
    "on_chain_id": "video_1234567890_abcd",
    "title": "我的视频",
    "transaction_hash": "0x1234567890abcdef...",
    "block_number": 12345678,
    "status": "confirmed"
  }
}
```

### IPFS响应示例
```json
{
  "success": true,
  "data": {
    "record": {
      "id": 124,
      "content_id": "ipfs_content_audio_1234567890_abcd",
      "ipfs_hash": "QmYourIPFSHash...",
      "metadata_ipfs": "QmMetadataHash...",
      "transaction_hash": "0x1234567890abcdef...",
      "title": "音乐作品"
    }
  }
}
```

## 📁 临时文件

脚本会在 `/tmp/` 目录下创建临时文件：

- `/tmp/last_content_id.txt` - 最后发布的内容ID
- `/tmp/last_ipfs_content_id.txt` - 最后发布的IPFS内容ID
- `/tmp/content_publish_*.json` - 完整的发布响应
- `/tmp/ipfs_content_publish_*.json` - 完整的IPFS发布响应

## 🌐 IPFS访问链接

发布到IPFS的内容可以通过以下网关访问：

- **Pinata网关**: `https://gateway.pinata.cloud/ipfs/{hash}`
- **IPFS官方网关**: `https://ipfs.io/ipfs/{hash}`
- **Cloudflare网关**: `https://cloudflare-ipfs.com/ipfs/{hash}`

## 🔧 故障排除

### 1. 服务连接失败
```bash
# 检查服务是否运行
curl http://localhost:15004/health

# 检查端口是否被占用
lsof -i :15004
```

### 2. IPFS哈希格式错误
- 确保IPFS哈希以 `Qm` 开头，长度为46字符
- 或者以 `baf` 开头的新格式哈希

### 3. 权限问题
```bash
# 确保脚本有执行权限
chmod +x scripts/*.sh
```

### 4. 依赖检查
```bash
# 检查必需的工具
which curl jq openssl
```

## 📚 API文档

- **Swagger UI**: http://localhost:15004/swagger/index.html
- **内容发布API**: `POST /api/v1/content/publish`
- **IPFS发布API**: `POST /api/v1/content/publish-ipfs`
- **内容查询API**: `GET /api/v1/content/{on_chain_id}`
- **IPFS查询API**: `GET /api/v1/content/{on_chain_id}/ipfs`

## 🎯 最佳实践

1. **测试环境**: 先在测试环境验证脚本功能
2. **IPFS哈希**: 使用真实的IPFS哈希获得最佳效果
3. **错误处理**: 检查脚本输出，处理可能的错误
4. **日志记录**: 保存重要的上链记录和交易哈希
5. **备份数据**: 定期备份临时文件中的重要信息

## 🔗 相关链接

- [Token Service API文档](http://localhost:15004/swagger/index.html)
- [IPFS官方文档](https://docs.ipfs.io/)
- [Pinata IPFS服务](https://pinata.cloud/)
- [以太坊开发文档](https://ethereum.org/developers/)
