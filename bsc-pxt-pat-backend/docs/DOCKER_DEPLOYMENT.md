# PXPAT Backend Docker 部署指南

## 📋 概述

本指南将帮助你使用 Docker 部署 PXPAT Backend 服务，包括 Token Service 和 Wallet Service。

## 🏗️ 架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │  Token Service  │    │ Wallet Service  │
│   (Port 80)     │    │   (Port 15004)  │    │  (Port 15002)   │
│                 │    │                 │    │                 │
│  反向代理        │◄──►│   API 服务      │    │   API 服务      │
│  负载均衡        │    │   区块链同步     │    │   钱包管理      │
│  SSL 终止       │    │   缓存管理       │    │   交易处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   外部服务       │
                    │                 │
                    │ • NEON PostgreSQL│
                    │ • Redis.io      │
                    │ • BSCScan API   │
                    └─────────────────┘
```

## 🚀 快速开始

### 1. 准备环境

确保你的系统已安装：
- Docker (>= 20.10)
- Docker Compose (>= 2.0)

### 2. 克隆项目

```bash
cd /Users/<USER>/Desktop/pxpat-backend
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.docker .env

# 根据需要编辑环境变量
vim .env
```

### 4. 构建和启动

```bash
# 构建 Docker 镜像
./docker-build.sh

# 启动所有服务
./docker-start.sh
```

### 5. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 测试 API
curl http://localhost/health
curl http://localhost/api/v1/tokens/supply
curl http://localhost/api/v1/wallet/health
```

## 📁 文件结构

```
pxpat-backend/
├── Dockerfile                 # 多阶段构建文件
├── docker-compose.yml         # 服务编排配置
├── .dockerignore             # Docker 忽略文件
├── .env.docker               # 环境变量模板
├── docker-build.sh           # 构建脚本
├── docker-start.sh           # 启动脚本
├── docker-stop.sh            # 停止脚本
├── configs/
│   ├── docker.yaml           # Docker 专用配置
│   ├── global.yaml           # 全局配置
│   ├── token-service/        # Token 服务配置
│   └── wallet-service/       # Wallet 服务配置
├── nginx/
│   └── conf.d/
│       └── default.conf      # Nginx 配置
└── logs/                     # 日志目录
    ├── service/              # 服务日志
    └── postgres/             # 数据库日志
```

## 🔧 配置说明

### 环境变量

主要环境变量说明：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `TOKEN_SERVICE_PORT` | 15004 | Token 服务端口 |
| `WALLET_SERVICE_PORT` | 15002 | Wallet 服务端口 |
| `NGINX_HTTP_PORT` | 80 | Nginx HTTP 端口 |
| `GIN_MODE` | release | Gin 运行模式 |
| `LOG_LEVEL` | info | 日志级别 |

### 数据库配置

使用外部 NEON PostgreSQL：
- 主机：`ep-still-bonus-a4nkvv0i-pooler.us-east-1.aws.neon.tech`
- 端口：`5432`
- 数据库：`pxpat`
- SSL：`require`

### Redis 配置

使用外部 Redis.io：
- 主机：`redis-11086.c294.ap-northeast-1-2.ec2.redns.redis-cloud.com`
- 端口：`11086`
- 数据库：`0`

## 🌐 API 访问

### 通过 Nginx (推荐)

- **基础地址**：`http://localhost`
- **Token API**：`http://localhost/api/v1/tokens/*`
- **Wallet API**：`http://localhost/api/v1/wallet/*`
- **健康检查**：`http://localhost/health`

### 直接访问服务

- **Token Service**：`http://localhost:15004`
- **Wallet Service**：`http://localhost:15002`

## 🛠️ 常用命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart token-service

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f token-service
docker-compose logs -f wallet-service
docker-compose logs -f nginx
```

### 调试和维护

```bash
# 进入容器
docker exec -it pxpat-token-service sh
docker exec -it pxpat-wallet-service sh

# 查看容器资源使用
docker stats

# 清理未使用的资源
docker system prune -f

# 重新构建镜像
docker-compose build --no-cache
```

## 📊 监控和日志

### 日志位置

- **服务日志**：`logs/service/`
- **数据库日志**：`logs/postgres/`
- **Nginx 日志**：容器内 `/var/log/nginx/`

### 健康检查

所有服务都配置了健康检查：
- **检查间隔**：30 秒
- **超时时间**：10 秒
- **重试次数**：3 次
- **启动等待**：40 秒

## 🔒 安全配置

### Nginx 安全头

- `X-Frame-Options: SAMEORIGIN`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### 容器安全

- 使用非 root 用户运行
- 最小化镜像大小
- 只暴露必要端口

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs service-name
   
   # 检查配置文件
   docker-compose config
   ```

2. **数据库连接失败**
   ```bash
   # 检查网络连接
   docker exec -it container-name ping database-host
   
   # 验证配置
   cat configs/global.yaml
   ```

3. **Redis 连接失败**
   ```bash
   # 检查 Redis 配置
   docker-compose logs | grep -i redis
   ```

### 性能优化

1. **调整资源限制**
   ```yaml
   # 在 docker-compose.yml 中添加
   deploy:
     resources:
       limits:
         memory: 512M
         cpus: '0.5'
   ```

2. **优化日志配置**
   ```yaml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## 📈 扩展部署

### 水平扩展

```bash
# 扩展 Token Service 到 3 个实例
docker-compose up -d --scale token-service=3

# 扩展 Wallet Service 到 2 个实例
docker-compose up -d --scale wallet-service=2
```

### 生产环境部署

1. 使用 Docker Swarm 或 Kubernetes
2. 配置 SSL 证书
3. 设置监控和告警
4. 配置自动备份
5. 实施 CI/CD 流水线

## 📞 支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 验证网络连接
4. 联系开发团队
