# PXPAT Backend 数据库优化方案

## 🌐 当前环境分析

### 现状
- **数据库**: NEON PostgreSQL (日本机房)
- **应用**: Docker 本地部署
- **网络延迟**: 跨国网络导致的自然延迟
- **慢查询阈值**: 200ms (考虑到网络延迟，这个阈值偏低)

## 🚀 优化方案

### 1. 数据库连接优化

#### 连接池配置优化
```yaml
# configs/docker.yaml 数据库配置优化
database:
  host: "ep-still-bonus-a4nkvv0i-pooler.us-east-1.aws.neon.tech"
  port: 5432
  database: "pxpat"
  username: "pxpat_owner"
  password: "npg_QDfc5v8uMbFS"
  
  # 连接池优化
  max_open_conns: 25        # 增加最大连接数
  max_idle_conns: 10        # 保持空闲连接
  conn_max_lifetime: "2h"   # 延长连接生命周期
  
  # 超时配置
  connect_timeout: "30s"    # 连接超时
  read_timeout: "60s"       # 读取超时
  write_timeout: "60s"      # 写入超时
  
  # SSL 配置
  sslmode: "require"
  
  # 性能参数
  statement_timeout: "30s"  # SQL 语句超时
  idle_in_transaction_timeout: "60s"
```

### 2. 慢查询阈值调整

#### 调整日志阈值
```go
// 考虑到跨国网络延迟，调整慢查询阈值
slowQueryThreshold := 500 * time.Millisecond  // 从 200ms 调整到 500ms
```

### 3. 批量操作优化

#### 批量插入优化
```go
// 使用批量插入减少网络往返
func (r *Repository) BatchInsertTransfers(transfers []TokenTransfer) error {
    const batchSize = 100
    
    for i := 0; i < len(transfers); i += batchSize {
        end := i + batchSize
        if end > len(transfers) {
            end = len(transfers)
        }
        
        batch := transfers[i:end]
        if err := r.db.CreateInBatches(batch, batchSize).Error; err != nil {
            return err
        }
    }
    return nil
}
```

### 4. 查询优化

#### 索引优化建议
```sql
-- 创建复合索引优化常用查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_token_transfers_composite 
ON token_transfers(token_type, block_number DESC, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_token_balances_user_token_updated 
ON token_balances(user_address, token_type, last_updated DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_token_holders_type_rank 
ON token_holders(token_type, rank ASC);

-- 分区表优化 (如果数据量大)
CREATE TABLE token_transfers_partitioned (
    LIKE token_transfers INCLUDING ALL
) PARTITION BY RANGE (block_number);

-- 创建分区
CREATE TABLE token_transfers_2024 PARTITION OF token_transfers_partitioned
FOR VALUES FROM (54000000) TO (56000000);
```

### 5. 缓存策略

#### Redis 缓存配置
```yaml
# 添加 Redis 缓存配置
cache:
  redis:
    host: "redis-12345.c1.asia-northeast1-1.gce.cloud.redislabs.com"
    port: 12345
    username: "default"
    password: "your_redis_password"
    db: 0
    
    # 缓存策略
    token_supply_ttl: "5m"      # Token 供应量缓存 5 分钟
    token_holders_ttl: "10m"    # 持有者列表缓存 10 分钟
    token_balances_ttl: "2m"    # 余额缓存 2 分钟
    
    # 连接池
    pool_size: 10
    min_idle_conns: 5
```

#### 缓存实现示例
```go
// 缓存热点数据
func (s *TokenService) GetTokenSupplyWithCache(tokenType string) (*TokenSupply, error) {
    cacheKey := fmt.Sprintf("token_supply:%s", tokenType)
    
    // 尝试从缓存获取
    if cached, err := s.cache.Get(cacheKey); err == nil {
        var supply TokenSupply
        if err := json.Unmarshal([]byte(cached), &supply); err == nil {
            return &supply, nil
        }
    }
    
    // 缓存未命中，从数据库获取
    supply, err := s.repo.GetTokenSupply(tokenType)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(supply); err == nil {
        s.cache.Set(cacheKey, string(data), 5*time.Minute)
    }
    
    return supply, nil
}
```

### 6. 数据库迁移优化

#### 分区表迁移
```sql
-- 创建分区表迁移脚本
BEGIN;

-- 1. 创建新的分区表
CREATE TABLE token_transfers_new (
    LIKE token_transfers INCLUDING ALL
) PARTITION BY RANGE (block_number);

-- 2. 创建分区
CREATE TABLE token_transfers_54m_55m PARTITION OF token_transfers_new
FOR VALUES FROM (54000000) TO (55000000);

CREATE TABLE token_transfers_55m_56m PARTITION OF token_transfers_new
FOR VALUES FROM (55000000) TO (56000000);

CREATE TABLE token_transfers_56m_57m PARTITION OF token_transfers_new
FOR VALUES FROM (56000000) TO (57000000);

-- 3. 迁移数据 (分批进行)
INSERT INTO token_transfers_new 
SELECT * FROM token_transfers 
WHERE block_number >= 54000000 AND block_number < 55000000;

-- 4. 重命名表
ALTER TABLE token_transfers RENAME TO token_transfers_old;
ALTER TABLE token_transfers_new RENAME TO token_transfers;

COMMIT;
```

### 7. 监控和告警

#### 性能监控脚本
```bash
#!/bin/bash
# database-monitor.sh

echo "🔍 数据库性能监控"

# 检查连接数
echo "当前连接数:"
curl -s "http://localhost:15004/metrics" | grep "db_connections" || echo "需要添加监控端点"

# 检查慢查询
echo "最近慢查询 (>500ms):"
docker-compose logs token-service | grep "Slow SQL" | grep -E "(5[0-9][0-9]|[6-9][0-9][0-9]|[0-9]{4,})" | tail -5

# 检查数据库大小
echo "数据库统计:"
curl -s "http://localhost/api/v1/tokens/transfers?limit=1" | jq '.data.total'

# 检查最新同步状态
echo "同步状态:"
docker-compose logs token-service | grep "last_synced_block" | tail -1
```

### 8. 配置文件优化

#### 生产环境配置
```yaml
# configs/production.yaml
app:
  environment: "production"
  log_level: "info"  # 减少日志输出
  
database:
  # 针对 NEON 优化的配置
  max_open_conns: 20
  max_idle_conns: 8
  conn_max_lifetime: "3h"
  slow_query_threshold: "500ms"  # 调整慢查询阈值
  
  # 批量操作优化
  batch_size: 50
  bulk_insert_size: 100
  
blockchain:
  # 同步优化
  batch_size: 300           # 适当减小批次大小
  sync_interval: 3          # 增加同步间隔
  confirm_blocks: 12        # 确认区块数
  
  # 重试配置
  max_retries: 5
  retry_delay: "2s"
  
cache:
  enabled: true
  default_ttl: "5m"
  
logging:
  # 减少日志噪音
  slow_query_log: true
  slow_query_threshold: "500ms"
  access_log: false
```

## 🎯 实施建议

### 优先级 1 (立即实施)
1. **调整慢查询阈值** 到 500ms
2. **优化连接池配置**
3. **添加关键索引**

### 优先级 2 (短期实施)
1. **实施 Redis 缓存**
2. **批量操作优化**
3. **监控脚本部署**

### 优先级 3 (长期规划)
1. **分区表迁移**
2. **读写分离**
3. **数据归档策略**

## 📊 预期效果

### 性能提升
- **查询响应时间**: 减少 30-50%
- **并发处理能力**: 提升 2-3 倍
- **缓存命中率**: 80%+ 的热点数据

### 稳定性提升
- **减少网络超时**
- **更好的错误恢复**
- **更稳定的同步性能**

## 🔧 实施脚本

创建优化脚本来自动应用这些改进。
