version: '3.8'

services:
  # Token Service
  token-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pxpat-token-service
    command: ["./token-service"]
    ports:
      - "${TOKEN_SERVICE_PORT:-15004}:15004"
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - GIN_MODE=${GIN_MODE:-release}
      - APP_ENV=${APP_ENV:-docker}
    env_file:
      - .env.docker
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:15004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pxpat-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Wallet Service
  wallet-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pxpat-wallet-service
    command: ["./wallet-service"]
    ports:
      - "${WALLET_SERVICE_PORT:-15002}:15002"
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - GIN_MODE=${GIN_MODE:-release}
      - APP_ENV=${APP_ENV:-docker}
    env_file:
      - .env.docker
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:15002/api/v1/wallet/airdrops"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pxpat-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: pxpat-nginx
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - token-service
      - wallet-service
    restart: unless-stopped
    networks:
      - pxpat-network
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

networks:
  pxpat-network:
    driver: bridge
    name: pxpat-network

volumes:
  logs:
    driver: local
