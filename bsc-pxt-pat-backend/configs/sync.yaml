# 数据同步服务配置
sync:
  # 基础同步配置
  enabled: true                           # 是否启用同步服务
  sync_interval_seconds: 300              # 基础同步间隔（5分钟）
  max_retries: 3                          # 最大重试次数
  retry_delay_seconds: 30                 # 重试延迟（秒）
  concurrent_workers: 8                   # 并发工作者数量

  # 每日完整同步配置
  enable_daily_full_sync: true            # 是否启用每日完整同步
  daily_full_sync_hour: 3                 # 每日完整同步的小时（0-23）
  daily_full_sync_minute: 0               # 每日完整同步的分钟（0-59）

  # 同步间隔配置（秒）
  transfer_sync_interval_seconds: 300     # 转账数据同步间隔（5分钟）
  user_sync_interval_seconds: 600         # 用户数据同步间隔（10分钟）

  # 同步批次大小配置
  transfer_batch_size: 1000               # 转账数据批次大小
  content_batch_size: 100                 # 内容数据批次大小
  burn_batch_size: 500                    # 销毁数据批次大小

  # 性能优化配置
  max_sync_duration_seconds: 1800         # 最大同步时长（30分钟）
  enable_metrics: true                    # 是否启用性能指标

  # 数据保留配置
  metrics_retention_days: 90              # 指标数据保留天数
  log_retention_days: 30                  # 日志保留天数

# 同步策略说明
# 1. 轻量级同步（每5-10分钟）：
#    - 转账数据同步
#    - 质押数据同步
#    - 活跃用户同步
#
# 2. 重量级同步（每日凌晨3点）：
#    - 完整数据同步
#    - 代币经济指标计算
#    - 代币销毁数据同步
#    - 内容上链数据同步
#    - 治理统计数据同步
#    - 缓存刷新
#
# 3. 凌晨3点的优势：
#    - 用户活跃度最低
#    - 网络负载最小
#    - 数据库压力最小
#    - 不影响用户体验
#    - 有充足时间完成重量级计算
