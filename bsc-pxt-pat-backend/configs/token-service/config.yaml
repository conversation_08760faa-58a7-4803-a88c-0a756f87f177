# Token Service 生产环境配置文件

# 服务器配置
server:
  port: 15004
  mode: release  # 改为 release 模式，减少调试日志

# 日志配置
logging:
  level: info     # 设置日志级别为 info，减少 debug 日志
  format: console # 使用彩色控制台格式，更清爽
  enable_sql_logs: false  # 关闭详细的SQL日志

# 数据库配置会从global.yaml继承，这里可以覆盖特定设置
database:
  max_open_conns: 20
  max_idle_conns: 10
  slow_query_threshold: 5000  # 慢查询阈值提高到5秒，减少警告日志
  log_level: silent  # 关闭GORM的详细日志

# Redis配置 (继承global.yaml格式)
redis:
  db: 0

# 区块链配置
blockchain:
  network: bsc_localhost  # 修改为本地网络
  rpc_url: "http://127.0.0.1:18485/"  # 本地BSC链RPC地址
  backup_rpc_urls:
    - "http://127.0.0.1:18485/"  # 本地备用RPC
  chain_id: 97
  start_block: 1  # 从创世块开始监听，确保包含所有部署和转账
  confirm_blocks: 1  # 本地链确认块数减少
  batch_size: 100  # 本地链批次大小

  # 网络连接配置
  request_timeout: 30s      # 请求超时时间
  max_retries: 5           # 最大重试次数
  retry_delay: 2s          # 重试延迟
  health_check_interval: 30s # 健康检查间隔

  # BSCScan API配置
  bscscan_api_key: "**********************************"
  use_bscscan_for_events: true   # 使用 BSCScan API，更稳定
  bscscan_request_timeout: 30s   # BSCScan请求超时
  bscscan_max_retries: 3         # BSCScan最大重试次数
  bscscan_retry_delay: 5s        # BSCScan重试延迟

  # 合约地址配置 - 更新为最新部署地址 (2025-07-23)
  contracts:
    # 核心代币合约 - 最新部署地址
    pxt_token: "0x833a05A4aFD3D04a5a57A40d40476a7e45a73efe"          # PXT 治理代币 (最新)
    pat_token: "0x920b936D5e650e54F7940D171B54F6495d8a6711"          # PAT 功能代币 (最新)

    # 质押系统合约 - 最新部署地址
    staking_pool: "0x194B366D2FEc7Ed312821a7194F1d4cD7CcC5717"       # 质押池合约 (最新)
    staking_factory: "0x36a17B3c36C17fcdf194D144A8b185Cc4952e4b1"   # 质押工厂合约 (最新)
    reward_distributor: "0xF3B8a6b5ef37F52d8fa73A6e72e746e6c50a5e00" # 奖励分配器 (最新)

    # 治理系统合约 - 最新部署地址
    dao: "0x4D57da86cb0f221F7574083CEDcCdE86dE15ee2e"              # DAO 主合约 (最新)
    voting: "0xD9dFc16C82053a88958404B8774da6572c62B88b"           # 投票合约 (最新)
    proposal_manager: "0x3B1628F56F3d211f8C1a1cCc41ccE906deB2cD15"  # 提案管理器 (最新)
    treasury: "0xfeA3ea1aE025CF2897C28BC1915b79EB10e30082"         # 国库合约 (最新)

    # 其他系统合约 - 最新部署地址
    token_registry: "0x4DBEd95b531239Ccfb092f630a866eD180C776De"    # 代币注册表 (最新)

    # 跨链桥系统合约 - 新增
    token_bridge: "0x88C7981862D33A384FD00a477497E350bf5bd215"      # TokenBridge合约 (最新)

    # 池子地址配置 - 重要：用于监听转账和奖励分发 (最新地址)
    china_mainland_pool: "0x1d3D8bfB93Ac1eF24c4Fdb8A1B0325e7b3359006" # 中国大陆池子 (最新)
    global_pool: "0x7e701BA3D1200934F30eCA5B2b8eaCf22d1D5E09"        # 国际池子 (最新)
    staking_pool_address: "0x472dBe81a6552A9EC7b3cD7AA74176b0171Bb22c" # 质押池地址 (最新)
    cross_chain_pool: "0xa28e20bB2b6495FbE0Eb2173Ea323f7aF3D378C9"   # 跨链池地址 (最新)

    # 内容上链系统合约地址 - 最新部署地址 (2025-07-23)
    content_registry: "0x851Aa4E0b45Cb5eC93A52454F3EbDA5E380d9328"  # ContentRegistry合约 (最新)
    content_character: "0x96b4854eBc97fBEd7b1896A0a9cDD98647d51cCC" # ContentCharacter合约 (最新)
    content_mint: "0x0ca82E64436a08cbf3C6EF0ceadcCdD6Dfb288Cc"      # ContentMint合约 (最新)

# IPFS配置 - 学习xLog的IPFS存储方案
ipfs:
  # IPFS服务提供商配置 (推荐使用Pinata)
  provider: "pinata"                                    # 支持: pinata, infura, local
  api_url: "https://api.pinata.cloud"                  # Pinata API地址
  api_key: "121a274e13e83493e1d1"                      # Pinata API Key
  api_secret: "****************************************************************"  # Pinata API Secret
  gateway_url: "https://gateway.pinata.cloud"          # IPFS网关地址

  # 上传配置
  max_file_size_mb: 100                                # 最大文件大小 (MB)
  allowed_file_types: ["json", "txt", "md", "pdf", "jpg", "png", "mp4", "mp3"] # 允许的文件类型
  auto_pin: true                                       # 自动固定上传的文件

  # 缓存配置
  cache_enabled: true                                  # 启用本地缓存
  cache_ttl_hours: 24                                  # 缓存过期时间 (小时)
  cache_max_size_mb: 1000                             # 最大缓存大小 (MB)

# 转账配置 - 更新为最新地址和私钥
transfer:
  system_private_key: "0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba"  # 中国大陆池子私钥 (最新)
  system_address: "0x1d3D8bfB93Ac1eF24c4Fdb8A1B0325e7b3359006"  # 中国大陆池子地址 (最新)
  gas_limit: 500000
  gas_price: 100000000

# 跨链池配置 - 新增
cross_chain:
  pool_private_key: "0xb3cab8de9692f7c6c740734f854da35c407f72f0e41848f065b5dd8059b3ec61"  # 跨链池私钥
  pool_address: "0xa28e20bB2b6495FbE0Eb2173Ea323f7aF3D378C9"  # 跨链池地址

# 质押池配置 - 新增
staking:
  pool_private_key: "0xf8f5374b4d3668a8a4d6edc1dee0b25eca09a8f61e1f4d65f1f1c3123092cc1f"  # 质押池私钥
  pool_address: "0x472dBe81a6552A9EC7b3cD7AA74176b0171Bb22c"  # 质押池地址

# 同步配置
sync:
  enabled: true
  sync_interval_seconds: 60  # 60秒同步一次，减少网络压力
  max_retries: 5             # 增加重试次数
  retry_delay_seconds: 10    # 增加重试延迟，避免网络拥堵
  concurrent_workers: 1      # 减少并发，避免 API 限制
  enable_daily_full_sync: true
  daily_full_sync_hour: 3
  daily_full_sync_minute: 0
  transfer_sync_interval_seconds: 60   # 60秒同步一次转账
  user_sync_interval_seconds: 60      # 60秒同步一次用户数据
  transfer_batch_size: 100             # 与区块批次大小保持一致
  content_batch_size: 100
  burn_batch_size: 500
  max_sync_duration_seconds: 1800
  enable_metrics: true
  metrics_retention_days: 30
  log_retention_days: 7
  # 日志优化配置
  log_sync_progress_interval: 50     # 每50个区块记录一次同步进度
  log_transfer_details: false        # 不记录每个转账的详细信息
  log_balance_updates: false         # 不记录余额更新详情
  enable_colorful_logs: true         # 启用彩色日志
  show_block_progress: true          # 显示区块同步进度

# 日志配置
log:
  level: info
  format: console  # 使用彩色控制台格式

# 缓存配置
cache:
  leaderboard_ttl: 300
  user_stats_ttl: 60
  overview_ttl: 15
  default_ttl: 300

# API配置
api:
  default_page_size: 50
  max_page_size: 200
  timeout: 30

# 监控配置
monitoring:
  health_check_interval: 30
  metrics_interval: 60

# 安全配置
security:
  cors:
    enabled: true
    allowed_origins: "*"
    allowed_methods: "GET, POST, PUT, DELETE, OPTIONS"
    allowed_headers: "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With"
    allowed_credentials: "true"
