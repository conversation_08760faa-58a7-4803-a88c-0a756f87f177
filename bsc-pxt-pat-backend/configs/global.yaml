# 线上生产环境全局配置
# 使用线上数据库和Redis服务

database:
  host: ep-still-bonus-a4nkvv0i-pooler.us-east-1.aws.neon.tech
  port: 5432
  user: pxpat_owner
  password: npg_QDfc5v8uMbFS
  dbname: pxpat
  ssl_mode: require
  max_open_conns: 25
  max_idle_conns: 12
  conn_max_lifetime_minutes: 120m
  auto_migrate: true
  time_zone: "Asia/Shanghai"
  log_level: "info"
  # 针对跨国网络延迟优化
  slow_query_threshold: "500ms"
  connect_timeout: "30s"
  read_timeout: "60s"
  write_timeout: "60s"

redis:
  host: redis-11086.c294.ap-northeast-1-2.ec2.redns.redis-cloud.com
  port: 11086
  password: "4CcW8bv5pwKYxXGWpC0cDVNhuvE9iyjq"
  username: "default"
  db: 0
  pool_size: 20
  min_idle_conns: 10
  dial_timeout_seconds: 5s
  read_timeout_seconds: 3s
  write_timeout_seconds: 3s
  pool_timeout_seconds: 4s

log:
  level: "info"
  format: "json"
  output: "console"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true
  service_file_path: "logs/service/{{serviceName}}-service.log"
  postgres_file_path: "logs/postgres/{{serviceName}}-postgres.log"
