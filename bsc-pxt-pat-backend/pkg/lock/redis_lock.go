package lock

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"github.com/redis/go-redis/v9"
	"time"

	"github.com/rs/zerolog/log"
)

// 错误定义
var (
	ErrLockNotObtained = errors.New("lock not obtained")
	ErrLockNotOwned    = errors.New("lock not owned")
)

// RedisLock 实现了基于Redis的分布式锁
type RedisLock struct {
	client   *redis.Client
	key      string
	value    string
	ttl      time.Duration
	obtained bool
}

// Config Redis锁配置
type Config struct {
	Addr     string
	Password string
	DB       int
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg Config) *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})
}

// NewRedisLock 创建新的Redis锁
func NewRedisLock(client *redis.Client, key string, ttl time.Duration) *RedisLock {
	// 生成随机锁值
	value, err := generateRandomValue(16)
	if err != nil {
		value = time.Now().String()
	}

	return &RedisLock{
		client: client,
		key:    key,
		value:  value,
		ttl:    ttl,
	}
}

// Obtain 获取锁
func (l *RedisLock) Obtain(ctx context.Context) error {
	log.Debug().Str("key", l.key).Str("value", l.value).Dur("ttl", l.ttl).Msg("Trying to obtain lock")

	// 使用SET NX命令尝试设置锁
	ok, err := l.client.SetNX(ctx, l.key, l.value, l.ttl).Result()
	if err != nil {
		log.Error().Err(err).Str("key", l.key).Msg("Failed to obtain lock")
		return err
	}

	if !ok {
		return ErrLockNotObtained
	}

	l.obtained = true
	log.Debug().Str("key", l.key).Msg("Lock obtained successfully")
	return nil
}

// Release 释放锁
func (l *RedisLock) Release(ctx context.Context) error {
	if !l.obtained {
		return nil
	}

	// 使用Lua脚本确保只删除自己的锁
	script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	res, err := l.client.Eval(ctx, script, []string{l.key}, l.value).Result()
	if err != nil {
		log.Error().Err(err).Str("key", l.key).Msg("Failed to release lock")
		return err
	}

	if res.(int64) != 1 {
		log.Warn().Str("key", l.key).Msg("Lock was not owned by us")
		return ErrLockNotOwned
	}

	l.obtained = false
	log.Debug().Str("key", l.key).Msg("Lock released successfully")
	return nil
}

// Refresh 刷新锁的TTL
func (l *RedisLock) Refresh(ctx context.Context) error {
	if !l.obtained {
		return ErrLockNotObtained
	}

	// 使用Lua脚本确保只刷新自己的锁
	script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("PEXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	res, err := l.client.Eval(ctx, script, []string{l.key}, l.value, int64(l.ttl/time.Millisecond)).Result()
	if err != nil {
		log.Error().Err(err).Str("key", l.key).Msg("Failed to refresh lock")
		return err
	}

	if res.(int64) != 1 {
		l.obtained = false
		log.Warn().Str("key", l.key).Msg("Lock was not owned by us during refresh")
		return ErrLockNotOwned
	}

	log.Debug().Str("key", l.key).Dur("ttl", l.ttl).Msg("Lock refreshed successfully")
	return nil
}

// IsLocked 检查是否已获取锁
func (l *RedisLock) IsLocked(ctx context.Context) (bool, error) {
	if !l.obtained {
		return false, nil
	}

	value, err := l.client.Get(ctx, l.key).Result()
	if err != nil {
		if err == redis.Nil {
			l.obtained = false
			return false, nil
		}
		log.Error().Err(err).Str("key", l.key).Msg("Failed to check lock status")
		return false, err
	}

	isOwned := value == l.value
	if !isOwned {
		l.obtained = false
	}

	return isOwned, nil
}

// WithLock 使用锁执行函数
func WithLock(ctx context.Context, lock *RedisLock, fn func() error) error {
	if err := lock.Obtain(ctx); err != nil {
		return err
	}

	defer func() {
		if err := lock.Release(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to release lock after execution")
		}
	}()

	return fn()
}

// generateRandomValue 生成随机字符串
func generateRandomValue(length int) (string, error) {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
