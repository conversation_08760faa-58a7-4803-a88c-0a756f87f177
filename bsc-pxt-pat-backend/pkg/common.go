package pkg

import (
	"strings"
)

func ReverseString(str string) string {
	runes := []rune(str)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func RReplace(s, old, new string, n int) string {
	if old == "" || n <= 0 {
		return s
	}

	sReversed := ReverseString(s)
	oldReversed := ReverseString(old)
	newReversed := ReverseString(new)

	replaced := strings.Replace(sReversed, oldReversed, newReversed, n)

	return ReverseString(replaced)
}

func Contains[T comparable](slice []T, target T) bool {
	for _, v := range slice {
		if v == target {
			return true
		}
	}
	return false
}
