package validation

import (
	"regexp"
)

// IsValidUsername 验证用户名是否合法
// 用户名只能包含字母、数字、下划线，长度3-30个字符
func IsValidUsername(username string) bool {
	// 长度检查
	if len(username) < 3 || len(username) > 30 {
		return false
	}

	// 使用正则表达式验证用户名格式
	validUsernameRegex := regexp.MustCompile("^[a-zA-Z0-9_]+$")
	return validUsernameRegex.MatchString(username)
}

// IsValidEmail 检查邮箱格式是否有效
// 简单验证，仅检查是否包含@和.
func IsValidEmail(email string) bool {
	return regexp.MustCompile(`^[^@]+@[^@]+\.[^@]+$`).MatchString(email)
}
