package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 标准响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSO<PERSON>(http.StatusOK, Response{
		Code:    0,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, err error) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    1,
		Message: err.Error(),
	})
}

// ErrorWithCode 带状态码的错误响应
func ErrorWithCode(c *gin.Context, code int, err error) {
	c.<PERSON>(code, Response{
		Code:    1,
		Message: err.Error(),
	})
}

// ErrorWithMessage 带消息的错误响应
func ErrorWithMessage(c *gin.Context, message string) {
	c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, Response{
		Code:    1,
		Message: message,
	})
}

// SuccessWithData 带数据的成功响应
func SuccessWithData(c *gin.Context, data interface{}) {
	c.JSO<PERSON>(http.StatusOK, Response{
		Code:    0,
		Message: "success",
		Data:    data,
	})
}
