package cors

import (
	"github.com/gin-gonic/gin"
	globalTypes "pxpat-backend/pkg/types"
)

// Middleware CORS中间件
func Middleware(origin, credentials, headers, methods string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>.Header().Set("Access-Control-Allow-Origin", origin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", credentials)
		c.Writer.Header().Set("Access-Control-Allow-Headers", headers)
		c.Writer.Header().Set("Access-Control-Allow-Methods", methods)

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// CORSMiddleware CORS中间件别名
func CORSMiddleware(cors globalTypes.GlobalCORSConfig) gin.HandlerFunc {
	return Middleware(cors.AllowedOrigins, cors.AllowedCredentials, cors.AllowedHeaders, cors.AllowedMethods)
}
