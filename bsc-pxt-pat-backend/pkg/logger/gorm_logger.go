package logger

import (
	"context"
	"errors"
	"time"

	"github.com/rs/zerolog"
	"gorm.io/gorm/logger"
)

// GormLogger 实现了 gorm.logger.Interface 接口的 zerolog 适配器
type GormLogger struct {
	logger                    zerolog.Logger
	LogLevel                  logger.LogLevel
	SlowThreshold             time.Duration
	SkipCallerLookup          bool
	IgnoreRecordNotFoundError bool
}

// NewGormLogger 创建一个新的 GORM logger 实例
func NewGormLogger() *GormLogger {
	config := DefaultGormLoggerConfig()

	return &GormLogger{
		logger:                    ZeroGormLogger, // 使用专用的 GORM 日志器
		LogLevel:                  config.LogLevel,
		SlowThreshold:             config.SlowThreshold,
		SkipCallerLookup:          config.SkipCallerLookup,
		IgnoreRecordNotFoundError: config.IgnoreRecordNotFoundError,
	}
}

// NewGormLoggerWithCustomLogger 使用自定义日志器创建 GORM logger 实例
func NewGormLoggerWithCustomLogger(config GormLoggerConfig) *GormLogger {
	return &GormLogger{
		logger:                    ZeroGormLogger,
		LogLevel:                  config.LogLevel,
		SlowThreshold:             config.SlowThreshold,
		SkipCallerLookup:          config.SkipCallerLookup,
		IgnoreRecordNotFoundError: config.IgnoreRecordNotFoundError,
	}
}

// GormLoggerConfig GORM logger 配置
type GormLoggerConfig struct {
	LogLevel                  logger.LogLevel
	SlowThreshold             time.Duration
	SkipCallerLookup          bool
	IgnoreRecordNotFoundError bool
}

// DefaultGormLoggerConfig 返回默认的 GORM logger 配置
func DefaultGormLoggerConfig() GormLoggerConfig {
	return GormLoggerConfig{
		LogLevel:                  logger.Silent,           // 关闭SQL日志
		SlowThreshold:             5000 * time.Millisecond, // 提高到5秒，减少慢查询警告
		SkipCallerLookup:          false,
		IgnoreRecordNotFoundError: true,
	}
}

// LogMode 设置日志级别
func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info 记录信息日志
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		l.logger.Info().Msgf(msg, data...)
	}
}

// Warn 记录警告日志
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		l.logger.Warn().Msgf(msg, data...)
	}
}

// Error 记录错误日志
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		l.logger.Error().Msgf(msg, data...)
	}
}

// Trace 记录 SQL 执行日志
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	event := l.logger.With().
		Str("sql", sql).
		Dur("elapsed", elapsed).
		Int64("rows", rows).
		Logger()

	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, logger.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		event.Error().Err(err).Msg("SQL execution error")
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		event.Warn().Msgf("Slow SQL query detected (threshold: %v)", l.SlowThreshold)
	case l.LogLevel == logger.Info:
		event.Info().Msg("SQL executed")
	}
}
