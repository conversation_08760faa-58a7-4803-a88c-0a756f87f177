package logger

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"pxpat-backend/pkg/types"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gopkg.in/natefinch/lumberjack.v2"
)

var ZeroGormLogger zerolog.Logger

// InitLogger 初始化日志系统
func InitLogger(config types.GlobalLogConfig, serviceName string) error {
	// 设置时间格式
	zerolog.TimeFieldFormat = time.RFC3339

	// 设置日志级别
	level, err := zerolog.ParseLevel(config.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	var writers []io.Writer

	// 创建基础的目录
	config.ServiceFilePath = strings.Replace(config.ServiceFilePath, "{{serviceName}}", serviceName, -1)
	config.PostgresFilePath = strings.Replace(config.PostgresFilePath, "{{serviceName}}", serviceName, -1)

	err = createEssentialDir(config.ServiceFilePath, config.PostgresFilePath)
	if err != nil {
		return fmt.Errorf("create essential dir failed: %w", err)
	}

	// 根据输出配置设置写入器
	switch strings.ToLower(config.Output) {
	case "file":
		// 配置服务日志轮转
		serviceFileWriter := &lumberjack.Logger{
			Filename:   config.ServiceFilePath,
			MaxSize:    config.MaxSize, // MB
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge, // days
			Compress:   config.Compress,
		}
		writers = append(writers, serviceFileWriter)

		// 创建 GORM 专用日志器
		if err := createZeroGormLogger(config); err != nil {
			return fmt.Errorf("failed to create gorm logger: %w", err)
		}

	case "both":
		// 对于both模式，我们需要创建一个自定义的多写入器
		// 文件写入器 - JSON格式
		serviceFileWriter := &lumberjack.Logger{
			Filename:   config.ServiceFilePath,
			MaxSize:    config.MaxSize, // MB
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge, // days
			Compress:   config.Compress,
		}

		// 控制台写入器 - 彩色格式
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.Kitchen, // 使用 3:04PM 格式
		}

		// 创建自定义的多写入器，文件用JSON，控制台用彩色
		writer := &DualFormatWriter{
			fileWriter:    serviceFileWriter,
			consoleWriter: consoleWriter,
		}
		writers = append(writers, writer)

		// 创建 GORM 专用日志器
		if err := createZeroGormLogger(config); err != nil {
			return fmt.Errorf("failed to create gorm logger: %w", err)
		}

	default: // "stdout" 或其他
		// 控制台输出
		if strings.ToLower(config.Format) == "json" {
			writers = append(writers, os.Stdout)
			ZeroGormLogger = zerolog.New(os.Stdout).With().Timestamp().Logger()
		} else {
			writers = append(writers, zerolog.ConsoleWriter{
				Out:        os.Stdout,
				TimeFormat: time.Kitchen, // 使用 3:04PM 格式
			})
			ZeroGormLogger = zerolog.New(zerolog.ConsoleWriter{
				Out:        os.Stdout,
				TimeFormat: time.Kitchen,
			}).With().Timestamp().Logger()
		}
	}

	// 创建多写入器
	var writer io.Writer
	if len(writers) == 1 {
		writer = writers[0]
	} else {
		writer = io.MultiWriter(writers...)
	}

	// 设置全局日志器
	log.Logger = zerolog.New(writer).With().Timestamp().Logger()

	return nil
}

// createZeroGormLogger 创建 GORM 专用日志器
func createZeroGormLogger(config types.GlobalLogConfig) error {
	// 配置 PostgreSQL 日志轮转
	postgresFileWriter := &lumberjack.Logger{
		Filename:   config.PostgresFilePath,
		MaxSize:    config.MaxSize, // MB
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge, // days
		Compress:   config.Compress,
	}

	// 创建 GORM 专用日志器
	ZeroGormLogger = zerolog.New(postgresFileWriter).With().Timestamp().Logger()

	return nil
}

func createEssentialDir(serviceFilePath, postgresFilePath string) error {
	if serviceFilePath == "" {
		return fmt.Errorf("service file path is required when output is set to file")
	}
	if postgresFilePath == "" {
		return fmt.Errorf("postgres file path is required when output is set to file")
	}

	// 确保服务日志目录存在
	serviceLogDir := filepath.Dir(serviceFilePath)
	if err := os.MkdirAll(serviceLogDir, 0755); err != nil {
		return fmt.Errorf("failed to create intra log directory: %w", err)
	}

	// 确保 PostgreSQL 日志目录存在
	postgresLogDir := filepath.Dir(postgresFilePath)
	if err := os.MkdirAll(postgresLogDir, 0755); err != nil {
		return fmt.Errorf("failed to create postgres log directory: %w", err)
	}
	return nil
}

// DualFormatWriter 实现双格式写入器
// 文件写入JSON格式，控制台写入彩色格式
type DualFormatWriter struct {
	fileWriter    io.Writer
	consoleWriter io.Writer
}

// Write 实现io.Writer接口
func (w *DualFormatWriter) Write(p []byte) (n int, err error) {
	// 解析JSON日志
	var logEntry map[string]interface{}
	if err := json.Unmarshal(p, &logEntry); err != nil {
		// 如果不是JSON格式，直接写入两个输出
		w.fileWriter.Write(p)
		w.consoleWriter.Write(p)
		return len(p), nil
	}

	// 写入文件 - 保持JSON格式
	w.fileWriter.Write(p)

	// 写入控制台 - 转换为彩色格式
	// 重新创建一个zerolog事件并写入控制台
	consoleLogger := zerolog.New(w.consoleWriter).With().Timestamp().Logger()

	// 提取日志级别
	levelStr, ok := logEntry["level"].(string)
	if !ok {
		levelStr = "info"
	}

	// 提取消息
	message, ok := logEntry["message"].(string)
	if !ok {
		message = ""
	}

	// 创建对应级别的日志事件
	var event *zerolog.Event
	switch levelStr {
	case "debug":
		event = consoleLogger.Debug()
	case "info":
		event = consoleLogger.Info()
	case "warn":
		event = consoleLogger.Warn()
	case "error":
		event = consoleLogger.Error()
	case "fatal":
		event = consoleLogger.Fatal()
	case "panic":
		event = consoleLogger.Panic()
	default:
		event = consoleLogger.Info()
	}

	// 添加其他字段（除了level, message, time）
	for key, value := range logEntry {
		if key != "level" && key != "message" && key != "time" {
			event = event.Interface(key, value)
		}
	}

	// 发送消息（这会触发控制台写入器的格式化）
	event.Msg(message)

	return len(p), nil
}
