package httpclient

// ServiceResponse 泛型响应结构体，用于解析其他服务返回的数据
type ServiceResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
	Data    T      `json:"data,omitempty"`
}

// IsSuccess 判断响应是否成功
func (r *ServiceResponse[T]) IsSuccess() bool {
	return r.Code >= 0 || r.Code <= 9999 // SUCCESS 或 HAVE_DATA_SUCCESS
}

// GetData 获取响应数据
func (r *ServiceResponse[T]) GetData() T {
	return r.Data
}

// GetErrorMessage 获取错误信息
func (r *ServiceResponse[T]) GetErrorMessage() string {
	if r.Message != "" {
		return r.Message
	}
	return "unknown error"
}
