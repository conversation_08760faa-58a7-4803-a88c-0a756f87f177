package httpclient

import (
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// HTTPClient 封装的HTTP客户端
type HTTPClient struct {
	client  *resty.Client
	baseURL string
}

// ClientConfig HTTP客户端配置
type ClientConfig struct {
	BaseURL          string
	Timeout          time.Duration
	RetryCount       int
	RetryWaitTime    time.Duration
	RetryMaxWaitTime time.Duration
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(config ClientConfig) *HTTPClient {
	client := resty.New()

	// 设置基础URL
	if config.BaseURL != "" {
		client.SetBaseURL(config.BaseURL)
	}

	// 设置超时
	if config.Timeout > 0 {
		client.SetTimeout(config.Timeout)
	} else {
		client.SetTimeout(30 * time.Second) // 默认30秒
	}

	// 设置重试
	if config.RetryCount > 0 {
		client.SetRetryCount(config.RetryCount)
	}
	if config.RetryWaitTime > 0 {
		client.SetRetryWaitTime(config.RetryWaitTime)
	}
	if config.RetryMaxWaitTime > 0 {
		client.SetRetryMaxWaitTime(config.RetryMaxWaitTime)
	}

	// 设置通用头部
	client.SetHeader("Content-Type", "application/json")
	client.SetHeader("Accept", "application/json")

	return &HTTPClient{
		client:  client,
		baseURL: config.BaseURL,
	}
}

// Get 发送GET请求
func (c *HTTPClient) Get(url string, result interface{}, headers ...map[string]string) error {
	req := c.client.R().SetResult(result)

	// 设置额外头部
	for _, header := range headers {
		for k, v := range header {
			req.SetHeader(k, v)
		}
	}

	resp, err := req.Get(url)
	if err != nil {
		return fmt.Errorf("GET request failed: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("GET request returned error status: %d", resp.StatusCode())
	}

	return nil
}

// Post 发送POST请求
func (c *HTTPClient) Post(url string, body interface{}, result interface{}, headers ...map[string]string) error {
	req := c.client.R().SetBody(body).SetResult(result)

	// 设置额外头部
	for _, header := range headers {
		for k, v := range header {
			req.SetHeader(k, v)
		}
	}

	resp, err := req.Post(url)
	if err != nil {
		return fmt.Errorf("POST request failed: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("POST request returned error status: %d", resp.StatusCode())
	}

	return nil
}

// Put 发送PUT请求
func (c *HTTPClient) Put(url string, body interface{}, result interface{}, headers ...map[string]string) error {
	req := c.client.R().SetBody(body).SetResult(result)

	// 设置额外头部
	for _, header := range headers {
		for k, v := range header {
			req.SetHeader(k, v)
		}
	}

	resp, err := req.Put(url)
	if err != nil {
		return fmt.Errorf("PUT request failed: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("PUT request returned error status: %d", resp.StatusCode())
	}

	return nil
}

// Delete 发送DELETE请求
func (c *HTTPClient) Delete(url string, result interface{}, headers ...map[string]string) error {
	req := c.client.R().SetResult(result)

	// 设置额外头部
	for _, header := range headers {
		for k, v := range header {
			req.SetHeader(k, v)
		}
	}

	resp, err := req.Delete(url)
	if err != nil {
		return fmt.Errorf("DELETE request failed: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("DELETE request returned error status: %d", resp.StatusCode())
	}

	return nil
}

// SetAuthToken 设置认证令牌
func (c *HTTPClient) SetAuthToken(token string) {
	c.client.SetAuthToken(token)
}

// SetHeader 设置请求头
func (c *HTTPClient) SetHeader(key, value string) {
	c.client.SetHeader(key, value)
}
