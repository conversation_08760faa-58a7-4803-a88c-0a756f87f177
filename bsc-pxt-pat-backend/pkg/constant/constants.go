package constant

import "time"

// 系统常量
const (
	// 应用信息
	AppName    = "PXPat Backend"
	AppVersion = "1.0.0"
	
	// 默认配置
	DefaultPageSize     = 20
	MaxPageSize         = 100
	DefaultTimeout      = 30 * time.Second
	DefaultCacheTimeout = 1 * time.Hour
	
	// JWT相关
	DefaultJWTExpiration = 24 * time.Hour
	JWTIssuer           = "pxpat-backend"
	
	// 缓存键前缀
	CacheKeyPrefix = "pxpat"
	
	// 用户相关
	DefaultUserLevel = "C"
	AdminUserLevel   = "A"
	GuestUserLevel   = "D"
	
	// 区域代码
	RegionGlobal = "global"
	RegionCN     = "cn"
	RegionUS     = "us"
	RegionEU     = "eu"
	RegionJP     = "jp"
	
	// 文件上传
	MaxFileSize        = 100 * 1024 * 1024 // 100MB
	MaxImageSize       = 10 * 1024 * 1024  // 10MB
	MaxVideoSize       = 500 * 1024 * 1024 // 500MB
	DefaultPartSize    = 5 * 1024 * 1024   // 5MB
	
	// 密码相关
	MinPasswordLength = 8
	MaxPasswordLength = 128
	
	// 验证码
	VerificationCodeLength = 6
	VerificationCodeExpiry = 10 * time.Minute
	
	// 邮件相关
	EmailVerificationExpiry = 24 * time.Hour
	PasswordResetExpiry     = 1 * time.Hour
	
	// 限流相关
	DefaultRateLimit = 100 // 每分钟请求数
	LoginRateLimit   = 10  // 登录每分钟请求数
	
	// 数据库相关
	DefaultDBTimeout     = 30 * time.Second
	DefaultMaxIdleConns  = 10
	DefaultMaxOpenConns  = 100
	DefaultConnMaxLife   = 1 * time.Hour
	
	// Redis相关
	DefaultRedisDB       = 0
	DefaultRedisPoolSize = 10
	
	// 角色相关
	RoleTypeCreator    = "creator"
	RoleTypeSharer     = "sharer"
	RoleTypeFamous     = "famous"
	RoleTypeOmnipotent = "omnipotent"
	
	// 角色等级
	RoleLevelD = "D"
	RoleLevelC = "C"
	RoleLevelB = "B"
	RoleLevelA = "A"
	RoleLevelS = "S"
	
	// HTTP状态相关
	StatusSuccess = "success"
	StatusError   = "error"
	StatusFail    = "fail"
)

// 错误代码
const (
	// 通用错误
	ErrCodeInternalError     = 10001
	ErrCodeInvalidParams     = 10002
	ErrCodeUnauthorized      = 10003
	ErrCodeForbidden         = 10004
	ErrCodeNotFound          = 10005
	ErrCodeConflict          = 10006
	ErrCodeTooManyRequests   = 10007
	
	// 用户相关错误
	ErrCodeUserNotFound      = 20001
	ErrCodeUserExists        = 20002
	ErrCodeInvalidPassword   = 20003
	ErrCodeUserDisabled      = 20004
	ErrCodeEmailNotVerified  = 20005
	
	// 认证相关错误
	ErrCodeInvalidToken      = 30001
	ErrCodeTokenExpired      = 30002
	ErrCodeInvalidCredentials = 30003
	
	// 角色相关错误
	ErrCodeRoleNotFound      = 40001
	ErrCodeInsufficientRole  = 40002
	ErrCodeRoleUpgradeFailed = 40003
	
	// 文件相关错误
	ErrCodeFileNotFound      = 50001
	ErrCodeFileTooLarge      = 50002
	ErrCodeInvalidFileType   = 50003
	ErrCodeUploadFailed      = 50004
)

// 缓存键模板
const (
	CacheKeyUser           = "user:%s"
	CacheKeyUserToken      = "user:token:%s"
	CacheKeyUserSession    = "user:session:%s"
	CacheKeyUserRole       = "user:role:%s"
	CacheKeyVerifyCode     = "verify:code:%s"
	CacheKeyPasswordReset  = "password:reset:%s"
	CacheKeyEmailVerify    = "email:verify:%s"
	CacheKeyRateLimit      = "rate:limit:%s"
	CacheKeyFileUpload     = "file:upload:%s"
)

// 队列名称
const (
	QueueEmailSend         = "email:send"
	QueueImageProcess      = "image:process"
	QueueVideoProcess      = "video:process"
	QueueNotificationSend  = "notification:send"
	QueueRoleUpgrade       = "role:upgrade"
	QueueContentStorage    = "content:storage:process" // 内容存储处理队列
)

// 事件类型
const (
	EventUserRegistered    = "user.registered"
	EventUserLoggedIn      = "user.logged_in"
	EventUserLoggedOut     = "user.logged_out"
	EventUserUpdated       = "user.updated"
	EventRoleApplied       = "role.applied"
	EventRoleUpgraded      = "role.upgraded"
	EventFileUploaded      = "file.uploaded"
	EventFileDeleted       = "file.deleted"
)

// 权限定义
const (
	PermissionCreateContent    = "create_content"
	PermissionShareContent     = "share_content"
	PermissionModerateContent  = "moderate_content"
	PermissionManageUsers      = "manage_users"
	PermissionManageRoles      = "manage_roles"
	PermissionViewAnalytics    = "view_analytics"
	PermissionSystemAdmin      = "system_admin"
)

// 内容类型
const (
	ContentTypeText  = "text"
	ContentTypeImage = "image"
	ContentTypeVideo = "video"
	ContentTypeAudio = "audio"
	ContentTypeFile  = "file"
)

// 文件类型
const (
	FileTypeImage = "image"
	FileTypeVideo = "video"
	FileTypeAudio = "audio"
	FileTypeDoc   = "document"
	FileTypeOther = "other"
)

// 支持的图片格式
var SupportedImageTypes = []string{
	"image/jpeg",
	"image/jpg", 
	"image/png",
	"image/gif",
	"image/webp",
}

// 支持的视频格式
var SupportedVideoTypes = []string{
	"video/mp4",
	"video/avi",
	"video/mov",
	"video/wmv",
	"video/flv",
	"video/webm",
}

// 支持的音频格式
var SupportedAudioTypes = []string{
	"audio/mp3",
	"audio/wav",
	"audio/flac",
	"audio/aac",
	"audio/ogg",
}

// 默认头像列表
var DefaultAvatars = []string{
	"avatar_01.png",
	"avatar_02.png", 
	"avatar_03.png",
	"avatar_04.png",
	"avatar_05.png",
	"avatar_06.png",
	"avatar_07.png",
	"avatar_08.png",
	"avatar_09.png",
	"avatar_10.png",
} 