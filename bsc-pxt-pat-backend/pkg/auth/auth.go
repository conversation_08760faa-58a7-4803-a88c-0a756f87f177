package auth

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// Manager 认证管理器接口
type Manager interface {
	// JWT相关方法
	GenerateToken(userID, email, region string, isAdmin bool) (string, error)
	VerifyToken(tokenString string) (jwt.MapClaims, error)
}

// JWTManager JWT认证管理器实现
type JWTManager struct {
	secretKey     string
	tokenDuration time.Duration
	issuer        string
}

// NewJWTManager 创建JWT认证管理器
func NewJWTManager(secretKey string, tokenDuration time.Duration, issuer string) Manager {
	return &JWTManager{
		secretKey:     secretKey,
		tokenDuration: tokenDuration,
		issuer:        issuer,
	}
}

// GenerateToken 生成用户JWT令牌
func (m *JWTManager) GenerateToken(userKSUID, email, region string, isAdmin bool) (string, error) {
	expirationTime := time.Now().Add(m.tokenDuration)

	claims := jwt.MapClaims{
		"user_ksuid":  userKSUID,
		"email":       email,
		"region_code": region,
		"user_level":  "B",
		"is_admin":    isAdmin,
		"exp":         expirationTime.Unix(),
		"iat":         time.Now().Unix(),
		"iss":         m.issuer,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(m.secretKey))
}

// VerifyToken 验证JWT令牌
func (m *JWTManager) VerifyToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(m.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}
