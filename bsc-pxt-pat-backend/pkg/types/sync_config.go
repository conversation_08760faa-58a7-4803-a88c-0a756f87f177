package types

// SyncServiceConfig 同步服务配置结构
type SyncServiceConfig struct {
	Sync SyncConfig `mapstructure:"sync"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	// 基础同步配置
	Enabled           bool `mapstructure:"enabled"`
	SyncInterval      int  `mapstructure:"sync_interval_seconds"`
	MaxRetries        int  `mapstructure:"max_retries"`
	RetryDelay        int  `mapstructure:"retry_delay_seconds"`
	ConcurrentWorkers int  `mapstructure:"concurrent_workers"`

	// 每日完整同步配置
	EnableDailyFullSync bool `mapstructure:"enable_daily_full_sync"`
	DailyFullSyncHour   int  `mapstructure:"daily_full_sync_hour"`
	DailyFullSyncMinute int  `mapstructure:"daily_full_sync_minute"`

	// 同步间隔配置（秒）
	TransferSyncInterval int `mapstructure:"transfer_sync_interval_seconds"`
	UserSyncInterval     int `mapstructure:"user_sync_interval_seconds"`

	// 同步批次大小配置
	TransferBatchSize int `mapstructure:"transfer_batch_size"`
	ContentBatchSize  int `mapstructure:"content_batch_size"`
	BurnBatchSize     int `mapstructure:"burn_batch_size"`

	// 性能优化配置
	MaxSyncDuration int  `mapstructure:"max_sync_duration_seconds"`
	EnableMetrics   bool `mapstructure:"enable_metrics"`

	// 数据保留配置
	MetricsRetentionDays int `mapstructure:"metrics_retention_days"`
	LogRetentionDays     int `mapstructure:"log_retention_days"`
}

// 确保 SyncServiceConfig 实现 RestrictServiceConfig 接口
func (c *SyncServiceConfig) IsServiceConfig() bool {
	return true
}
