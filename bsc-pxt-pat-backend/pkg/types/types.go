package types

import (
	"time"
)

// 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Success bool        `json:"success"`
}

// 分页请求参数
type PaginationRequest struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// 分页响应结构
type PaginationResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// 用户基础信息
type UserInfo struct {
	ID         string    `json:"id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	Nickname   string    `json:"nickname"`
	Avatar     string    `json:"avatar"`
	UserLevel  string    `json:"user_level"`
	RegionCode string    `json:"region_code"`
	IsAdmin    bool      `json:"is_admin"`
	IsGuest    bool      `json:"is_guest"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// 角色信息
type RoleInfo struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	RoleType  string    `json:"role_type"`
	Level     string    `json:"level"`
	Region    string    `json:"region"`
	Status    string    `json:"status"`
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// 文件信息
type FileInfo struct {
	ID          string    `json:"id"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	FileType    string    `json:"file_type"`
	ContentType string    `json:"content_type"`
	URL         string    `json:"url"`
	StorageKey  string    `json:"storage_key"`
	UploadedBy  string    `json:"uploaded_by"`
	CreatedAt   time.Time `json:"created_at"`
}

// 上传文件请求
type UploadRequest struct {
	FileName    string `json:"file_name" binding:"required"`
	FileSize    int64  `json:"file_size" binding:"required,min=1"`
	ContentType string `json:"content_type" binding:"required"`
	FileType    string `json:"file_type"`
}

// 上传文件响应
type UploadResponse struct {
	FileID      string `json:"file_id"`
	UploadURL   string `json:"upload_url,omitempty"`
	UploadID    string `json:"upload_id,omitempty"`
	PartSize    int64  `json:"part_size,omitempty"`
	IsMultipart bool   `json:"is_multipart"`
}

// 多部分上传部分信息
type UploadPart struct {
	PartNumber int32  `json:"part_number"`
	ETag       string `json:"etag"`
}

// 完成多部分上传请求
type CompleteUploadRequest struct {
	UploadID string       `json:"upload_id" binding:"required"`
	Parts    []UploadPart `json:"parts" binding:"required"`
}

// 缓存项
type CacheItem struct {
	Key        string        `json:"key"`
	Value      interface{}   `json:"value"`
	Expiration time.Duration `json:"expiration"`
}

// 邮件信息
type EmailInfo struct {
	To      []string `json:"to" binding:"required"`
	Subject string   `json:"subject" binding:"required"`
	Body    string   `json:"body" binding:"required"`
	IsHTML  bool     `json:"is_html"`
}

// 验证码信息
type VerificationCode struct {
	Code      string    `json:"code"`
	Email     string    `json:"email"`
	Type      string    `json:"type"`
	ExpiresAt time.Time `json:"expires_at"`
}

// 区域信息
type RegionInfo struct {
	Code         string   `json:"code"`
	Name         string   `json:"name"`
	CountryCodes []string `json:"country_codes"`
	Domains      []string `json:"domains"`
	IsDefault    bool     `json:"is_default"`
}

// 权限信息
type PermissionInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
}

// 角色权限映射
type RolePermissions struct {
	RoleType    string   `json:"role_type"`
	Level       string   `json:"level"`
	Permissions []string `json:"permissions"`
}

// 系统配置
type SystemConfig struct {
	Key         string      `json:"key"`
	Value       interface{} `json:"value"`
	Type        string      `json:"type"`
	Description string      `json:"description"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// 错误详情
type ErrorDetail struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Field   string `json:"field,omitempty"`
}

// 批量操作请求
type BatchRequest struct {
	IDs    []string `json:"ids" binding:"required"`
	Action string   `json:"action" binding:"required"`
}

// 批量操作响应
type BatchResponse struct {
	Success []string      `json:"success"`
	Failed  []ErrorDetail `json:"failed"`
	Total   int           `json:"total"`
}

// 统计信息
type Statistics struct {
	TotalUsers    int64     `json:"total_users"`
	ActiveUsers   int64     `json:"active_users"`
	TotalContent  int64     `json:"total_content"`
	TotalFiles    int64     `json:"total_files"`
	StorageUsed   int64     `json:"storage_used"`
	BandwidthUsed int64     `json:"bandwidth_used"`
	LastUpdated   time.Time `json:"last_updated"`
}

// 健康检查响应
type HealthCheck struct {
	Status    string            `json:"status"`
	Version   string            `json:"version"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}

// 日志条目
type LogEntry struct {
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id,omitempty"`
	RequestID string                 `json:"request_id,omitempty"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// 事件数据
type EventData struct {
	Type      string                 `json:"type"`
	UserID    string                 `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// 队列消息
type QueueMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Payload   map[string]interface{} `json:"payload"`
	Retry     int                    `json:"retry"`
	CreatedAt time.Time              `json:"created_at"`
}

// 限流信息
type RateLimitInfo struct {
	Key       string        `json:"key"`
	Limit     int           `json:"limit"`
	Remaining int           `json:"remaining"`
	ResetTime time.Time     `json:"reset_time"`
	Window    time.Duration `json:"window"`
}

// JWT配置
type JWTConfig struct {
	SecretKey  string        `json:"secret_key"`
	Expiration time.Duration `json:"expiration"`
	Issuer     string        `json:"issuer"`
}

// 邮件配置
type MailConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	UseTLS   bool   `json:"use_tls"`
}
