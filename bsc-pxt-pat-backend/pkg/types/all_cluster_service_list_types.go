package types

type GlobalAllServicesList struct {
	UserService serviceConfig `mapstructure:"user_service"`
}

type GlobalAllClusterList struct {
	UserCluster   serviceConfig `mapstructure:"user_cluster"`
	NotifyCluster serviceConfig `mapstructure:"notify_cluster"`
}

type serviceConfig struct {
	Host  string `mapstructure:"host"`
	Port  int    `mapstructure:"port"`
	Token string `mapstructure:"token"`
}
