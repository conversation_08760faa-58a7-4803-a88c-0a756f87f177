package types

import "time"

type GlobalServerConfig struct {
	Host                   string                `mapstructure:"host"`
	Port                   int                   `mapstructure:"port"`
	Mode                   string                `mapstructure:"mode"`
	Name                   string                `mapstructure:"name"`
	ReadTimeoutSeconds     time.Duration         `mapstructure:"read_timeout_seconds"`
	WriteTimeoutSeconds    time.Duration         `mapstructure:"write_timeout_seconds"`
	IdleTimeoutSeconds     time.Duration         `mapstructure:"idle_timeout_seconds"`
	ShutdownTimeoutSeconds time.Duration         `mapstructure:"shutdown_timeout_seconds"`
	AllServiceList         GlobalAllServicesList `mapstructure:"all_service_list"`
	AllClusterList         GlobalAllClusterList  `mapstructure:"all_cluster_list"`
}

type GlobalDatabaseConfig struct {
	Type                   string        `mapstructure:"types"`
	Host                   string        `mapstructure:"host"`
	Port                   int           `mapstructure:"port"`
	User                   string        `mapstructure:"user"`
	Password               string        `mapstructure:"password"`
	DBName                 string        `mapstructure:"dbname"`
	MaxIdleConns           int           `mapstructure:"max_idle_conns"`
	MaxOpenConns           int           `mapstructure:"max_open_conns"`
	AutoMigrate            bool          `mapstructure:"auto_migrate"`
	SSLMode                string        `mapstructure:"ssl_mode"`
	TimeZone               string        `mapstructure:"time_zone"`
	ConnMaxLifetimeMinutes time.Duration `mapstructure:"conn_max_lifetime_minutes"`
	LogLevel               string        `mapstructure:"log_level"`
}

type GlobalRedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Username     string        `mapstructure:"username"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	PoolTimeout  time.Duration `mapstructure:"pool_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

type GlobalExternalServiceConfig struct {
	Host    string        `yaml:"host" mapstructure:"host"`
	Port    int           `yaml:"port" mapstructure:"port"`
	Timeout time.Duration `yaml:"timeout" mapstructure:"timeout" default:"10s"`
}

// GlobalJWTConfig 配置
type GlobalJWTConfig struct {
	SecretKey  string        `json:"secret_key" mapstructure:"secret"`
	Expiration time.Duration `json:"expiration" mapstructure:"expiration"`
	Issuer     string        `json:"issuer" mapstructure:"issuer"`
}

// GlobalLogConfig 日志配置
type GlobalLogConfig struct {
	Level            string `mapstructure:"level"`
	Format           string `mapstructure:"format"`
	Output           string `mapstructure:"output"`
	MaxSize          int    `mapstructure:"max_size"`
	MaxBackups       int    `mapstructure:"max_backups"`
	MaxAge           int    `mapstructure:"max_age"`
	Compress         bool   `mapstructure:"compress"`
	ServiceFilePath  string `mapstructure:"service_file_path"`
	PostgresFilePath string `mapstructure:"postgres_file_path"`
}

// GlobalRateLimitConfig 限流配置
type GlobalRateLimitConfig struct {
	Enabled           bool `yaml:"enabled"`
	RequestsPerMinute int  `yaml:"requests_per_minute"`
	Burst             int  `yaml:"burst"`
}

// GlobalCORSConfig 跨域配置
type GlobalCORSConfig struct {
	Enabled            bool   `mapstructure:"enabled"`
	AllowedOrigins     string `mapstructure:"allowed_origins"`
	AllowedMethods     string `mapstructure:"allowed_methods"`
	AllowedHeaders     string `mapstructure:"allowed_headers"`
	AllowedCredentials string `mapstructure:"allowed_credentials"`
}

type GlobalRabbitMQConfig struct {
	URL string `mapstructure:"url"`
}

// GlobalStorageConfig 通用存储配置
type GlobalStorageConfig struct {
	Provider        string   `mapstructure:"provider" json:"provider"`                   // 存储提供者: minio, r2, s3
	Endpoint        string   `mapstructure:"endpoint" json:"endpoint"`                   // 存储端点
	Region          string   `mapstructure:"region" json:"region"`                       // 存储区域
	Bucket          string   `mapstructure:"bucket" json:"bucket"`                       // 存储桶名称
	Buckets         []string `mapstructure:"buckets" json:"buckets"`                     // 多存储桶选项
	AccessKeyID     string   `mapstructure:"access_key_id" json:"access_key_id"`         // 访问密钥ID
	SecretAccessKey string   `mapstructure:"secret_access_key" json:"secret_access_key"` // 秘密访问密钥
	UseSSL          bool     `mapstructure:"use_ssl" json:"use_ssl"`                     // 是否使用SSL
	IsMultiBucket   bool     `mapstructure:"is_multi_bucket" json:"is_multi_bucket"`     // 是否是多个bucket

	TempSavePath string `mapstructure:"temp_save_path" json:"temp_save_path"`

	// R2 specific
	AccountID string `mapstructure:"account_id" json:"account_id"` // R2 账户ID
	PublicURL string `mapstructure:"public_url" json:"public_url"` // R2 公共URL

	// Minio specific
	UploadExpireTime time.Duration `mapstructure:"upload_expire_time"` // 上传凭证过期时间
	UrlExpiry        time.Duration `mapstructure:"url_expiry"`         // 生成的预签名URL过期时间

	// 其他配置
	MaxFileSize    int64         `mapstructure:"max_file_size"`                          // 最大文件大小
	MaxRetries     int           `mapstructure:"max_retries" json:"max_retries"`         // 最大重试次数
	RequestTimeout time.Duration `mapstructure:"request_timeout" json:"request_timeout"` // 请求超时时间
	PartSize       int64         `mapstructure:"part_size" json:"part_size"`             // 分片大小
	AllowedTypes   []string      `mapstructure:"allowed_types"`                          // 允许的文件类型
}

type RestrictServiceConfig interface {
	IsConfig()
}
