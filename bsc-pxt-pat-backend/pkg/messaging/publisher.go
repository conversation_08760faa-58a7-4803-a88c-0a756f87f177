package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	amqp091 "github.com/rabbitmq/amqp091-go"
)

// Publisher 通用消息发布器
type Publisher struct {
	conn   *Connection
	config *PublisherConfig
}

// NewPublisher 创建新的发布器
func NewPublisher(config *PublisherConfig) (*Publisher, error) {
	if config == nil {
		return nil, fmt.Errorf("publisher config is required")
	}

	if config.Connection == nil {
		config.Connection = DefaultConnectionConfig()
	}

	// 创建连接
	conn, err := NewConnection(config.Connection)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	publisher := &Publisher{
		conn:   conn,
		config: config,
	}

	// 声明交换机
	if err := publisher.setupExchanges(); err != nil {
		publisher.Close()
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to setup exchanges: %w", err)
	}

	return publisher, nil
}

// setupExchanges 设置交换机
func (p *Publisher) setupExchanges() error {
	for _, exchange := range p.config.Exchanges {
		if err := p.conn.DeclareExchange(exchange); err != nil {
			return fmt.Errorf("failed to declare exchange %s: %w", exchange.Name, err)
		}
		log.Printf("Exchange declared: %s", exchange.Name)
	}
	return nil
}

// Publish 发布消息
func (p *Publisher) Publish(ctx context.Context, config PublishConfig, message interface{}) error {
	if !p.conn.IsConnected() {
		return fmt.Errorf("publisher is not connected")
	}

	channel, err := p.conn.GetChannel()
	if err != nil {
		return fmt.Errorf("failed to get channel: %w", err)
	}

	// 序列化消息
	var body []byte
	switch msg := message.(type) {
	case []byte:
		body = msg
	case string:
		body = []byte(msg)
	default:
		body, err = json.Marshal(message)
		if err != nil {
			return fmt.Errorf("failed to marshal message: %w", err)
		}
	}

	// 发布消息
	err = channel.PublishWithContext(ctx,
		config.Exchange,
		config.RoutingKey,
		config.Mandatory,
		config.Immediate,
		amqp091.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp091.Persistent, // 持久化消息
			Timestamp:    time.Now(),
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	log.Printf("Message published to exchange: %s, routing key: %s", config.Exchange, config.RoutingKey)
	return nil
}

// PublishJSON 发布JSON消息（便捷方法）
func (p *Publisher) PublishJSON(ctx context.Context, exchange, routingKey string, message interface{}) error {
	config := PublishConfig{
		Exchange:   exchange,
		RoutingKey: routingKey,
		Mandatory:  false,
		Immediate:  false,
	}
	return p.Publish(ctx, config, message)
}

// IsConnected 检查连接状态
func (p *Publisher) IsConnected() bool {
	return p.conn.IsConnected()
}

// Close 关闭发布器
func (p *Publisher) Close() error {
	if p.conn != nil {
		return p.conn.Close()
	}
	return nil
}
