package messaging

// 审核服务
const (
	AUDIT_EXCHANGE_TYPE = "topic"
	AUDIT_EXCHANGE      = "audit.events"
	AUDIT_QUEUE         = "audit.events.queue"
)

// 内容服务
const (
	// 交换机
	CONTENT_EXCHANGE_TYPE = "topic"
	CONTENT_EXCHANGE      = "content.events"

	// 队列
	// 监听转码完成的队列
	CONTENT_TRANSCODING_RESULTS_QUEUE = "content.transcoding.results"

	// 路由键
	CONTENT_TRANSCODING_COMPLETED_ROUTING_KEY = "transcoding.completed"
	CONTENT_TRANSCODING_FAILED_ROUTING_KEY    = "transcoding.failed"
)

// 媒体处理服务
const (
	MEDIA_PROCESSING_EXCHANGE = "media.processing"
	// 转码
	MEDIA_PROCESSING_TRANSCODE_QUEUE = "media.processing.transcode"
	// 路由键
	MEDIA_PROCESSING_TRANSCODE_REQUEST_ROUTING_KEY = "transcode.request"
	// 内容服务发起的转码请求
	MEDIA_PROCESSING_CONTENT_TRANSCODE_REQUEST_ROUTING_KEY = "transcode.content.request"
	// 审核服务发起的转码请求
	MEDIA_PROCESSING_AUDIT_TRANSCODE_REQUEST_ROUTING_KEY = "transcode.audit.request"
)

// 用户服务
const (
	// 交换机
	USER_EVENTS_EXCHANGE_TYPE = "topic"
	USER_EVENTS_EXCHANGE      = "user.events"

	// 队列
	// 积分服务监听用户事件的队列
	POINTS_SERVICE_USER_EVENTS_QUEUE = "points.service.user.events"
	// 钱包服务监听用户注册事件的队列
	WALLET_SERVICE_USER_REGISTERED_QUEUE = "wallet.service.user.registered"

	// 路由键
	USER_REGISTERED_ROUTING_KEY = "user.registered"
)
