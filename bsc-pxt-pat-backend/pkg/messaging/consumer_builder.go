package messaging

import (
	"time"
)

// ConsumerBuilder 消费者构建器
type ConsumerBuilder struct {
	config *ConsumerConfig
}

// NewConsumerBuilder 创建消费者构建器
func NewConsumerBuilder(url string) *ConsumerBuilder {
	return &ConsumerBuilder{
		config: &ConsumerConfig{
			Connection: &ConnectionConfig{
				URL:               url,
				ReconnectInterval: 5 * time.Second,
				MaxReconnectTries: 10,
				HeartbeatInterval: 10 * time.Second,
				ConnectionTimeout: 30 * time.Second,
			},
			Exchanges: []ExchangeConfig{},
			Queues:    []QueueConfig{},
			Bindings:  []BindingConfig{},
			QoS:       DefaultQoSConfig(),
			Consume:   ConsumeConfig{},
		},
	}
}

// WithExchange 添加交换机
func (b *ConsumerBuilder) WithExchange(name, exchangeType string, durable bool) *ConsumerBuilder {
	b.config.Exchanges = append(b.config.Exchanges, ExchangeConfig{
		Name:       name,
		Type:       exchangeType,
		Durable:    durable,
		AutoDelete: false,
		Internal:   false,
		NoWait:     false,
		Args:       nil,
	})
	return b
}

// WithTopicExchange 添加Topic交换机
func (b *ConsumerBuilder) WithTopicExchange(name string) *ConsumerBuilder {
	return b.WithExchange(name, "topic", true)
}

// WithQueue 添加队列
func (b *ConsumerBuilder) WithQueue(name string, durable bool) *ConsumerBuilder {
	b.config.Queues = append(b.config.Queues, QueueConfig{
		Name:       name,
		Durable:    durable,
		AutoDelete: false,
		Exclusive:  false,
		NoWait:     false,
		Args:       nil,
	})
	return b
}

// WithBinding 添加绑定
func (b *ConsumerBuilder) WithBinding(queueName, exchangeName, routingKey string) *ConsumerBuilder {
	b.config.Bindings = append(b.config.Bindings, BindingConfig{
		QueueName:    queueName,
		ExchangeName: exchangeName,
		RoutingKey:   routingKey,
		NoWait:       false,
		Args:         nil,
	})
	return b
}

// WithQoS 设置QoS
func (b *ConsumerBuilder) WithQoS(prefetchCount, prefetchSize int, global bool) *ConsumerBuilder {
	b.config.QoS = &QoSConfig{
		PrefetchCount: prefetchCount,
		PrefetchSize:  prefetchSize,
		Global:        global,
	}
	return b
}

// WithConsume 设置消费配置
func (b *ConsumerBuilder) WithConsume(queueName, consumer string, autoAck bool) *ConsumerBuilder {
	b.config.Consume = ConsumeConfig{
		QueueName: queueName,
		Consumer:  consumer,
		AutoAck:   autoAck,
		Exclusive: false,
		NoLocal:   false,
		NoWait:    false,
		Args:      nil,
	}
	return b
}

// WithReconnectConfig 设置重连配置
func (b *ConsumerBuilder) WithReconnectConfig(interval time.Duration, maxTries int) *ConsumerBuilder {
	b.config.Connection.ReconnectInterval = interval
	b.config.Connection.MaxReconnectTries = maxTries
	return b
}

// Build 构建消费者
func (b *ConsumerBuilder) Build(handler MessageHandler) (*Consumer, error) {
	return NewConsumer(b.config, handler)
}
