package messaging

import (
	"time"
)

// PublisherBuilder 发布器构建器
type PublisherBuilder struct {
	config *PublisherConfig
}

// NewPublisherBuilder 创建发布器构建器
func NewPublisherBuilder(url string) *PublisherBuilder {
	return &PublisherBuilder{
		config: &PublisherConfig{
			Connection: &ConnectionConfig{
				URL:               url,
				ReconnectInterval: 5 * time.Second,
				MaxReconnectTries: 10,
				HeartbeatInterval: 10 * time.Second,
				ConnectionTimeout: 30 * time.Second,
			},
			Exchanges: []ExchangeConfig{},
		},
	}
}

// WithExchange 添加交换机
func (b *PublisherBuilder) WithExchange(name, exchangeType string, durable bool) *PublisherBuilder {
	b.config.Exchanges = append(b.config.Exchanges, ExchangeConfig{
		Name:       name,
		Type:       exchangeType,
		Durable:    durable,
		AutoDelete: false,
		Internal:   false,
		NoWait:     false,
		Args:       nil,
	})
	return b
}

// WithTopicExchange 添加Topic交换机
func (b *PublisherBuilder) WithTopicExchange(name string) *PublisherBuilder {
	return b.WithExchange(name, "topic", true)
}

// WithDirectExchange 添加Direct交换机
func (b *PublisherBuilder) WithDirectExchange(name string) *PublisherBuilder {
	return b.WithExchange(name, "direct", true)
}

// WithReconnectConfig 设置重连配置
func (b *PublisherBuilder) WithReconnectConfig(interval time.Duration, maxTries int) *PublisherBuilder {
	b.config.Connection.ReconnectInterval = interval
	b.config.Connection.MaxReconnectTries = maxTries
	return b
}

// Build 构建发布器
func (b *PublisherBuilder) Build() (*Publisher, error) {
	return NewPublisher(b.config)
}
