package messaging

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	amqp091 "github.com/rabbitmq/amqp091-go"
)

// Consumer 通用消息消费者
type Consumer struct {
	conn          *Connection
	config        *ConsumerConfig
	handler       MessageHandler
	closed        bool
	mutex         sync.RWMutex
	processedIDs  map[string]time.Time // 防重复处理
	cleanupTicker *time.Ticker
}

// NewConsumer 创建新的消费者
func NewConsumer(config *ConsumerConfig, handler MessageHandler) (*Consumer, error) {
	if config == nil {
		return nil, fmt.Errorf("consumer config is required")
	}

	if handler == nil {
		return nil, fmt.Errorf("message handler is required")
	}

	if config.Connection == nil {
		config.Connection = DefaultConnectionConfig()
	}

	if config.QoS == nil {
		config.QoS = DefaultQoSConfig()
	}

	// 创建连接
	conn, err := NewConnection(config.Connection)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	consumer := &Consumer{
		conn:          conn,
		config:        config,
		handler:       handler,
		closed:        false,
		processedIDs:  make(map[string]time.Time),
		cleanupTicker: time.NewTicker(1 * time.Hour), // 每小时清理一次过期的消息ID
	}

	// 设置队列和绑定
	if err := consumer.setup(); err != nil {
		consumer.Close()
		return nil, fmt.Errorf("failed to setup consumer: %w", err)
	}

	// 启动清理例程
	go consumer.startCleanupRoutine()

	return consumer, nil
}

// setup 设置交换机、队列和绑定
func (c *Consumer) setup() error {
	// 声明交换机
	for _, exchange := range c.config.Exchanges {
		if err := c.conn.DeclareExchange(exchange); err != nil {
			return fmt.Errorf("failed to declare exchange %s: %w", exchange.Name, err)
		}
		log.Printf("Exchange declared: %s", exchange.Name)
	}

	// 声明队列
	for _, queue := range c.config.Queues {
		_, err := c.conn.DeclareQueue(queue)
		if err != nil {
			return fmt.Errorf("failed to declare queue %s: %w", queue.Name, err)
		}
		log.Printf("Queue declared: %s", queue.Name)
	}

	// 绑定队列
	for _, binding := range c.config.Bindings {
		if err := c.conn.BindQueue(binding); err != nil {
			return fmt.Errorf("failed to bind queue %s to exchange %s: %w",
				binding.QueueName, binding.ExchangeName, err)
		}
		log.Printf("Queue bound: %s -> %s (routing key: %s)",
			binding.QueueName, binding.ExchangeName, binding.RoutingKey)
	}

	// 设置QoS
	if err := c.conn.SetQoS(*c.config.QoS); err != nil {
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	return nil
}

// StartConsuming 开始消费消息
func (c *Consumer) StartConsuming(ctx context.Context) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if c.closed {
		return fmt.Errorf("consumer is closed")
	}

	if !c.conn.IsConnected() {
		return fmt.Errorf("consumer is not connected")
	}

	channel, err := c.conn.GetChannel()
	if err != nil {
		return fmt.Errorf("failed to get channel: %w", err)
	}

	// 开始消费消息
	msgs, err := channel.Consume(
		c.config.Consume.QueueName,
		c.config.Consume.Consumer,
		c.config.Consume.AutoAck,
		c.config.Consume.Exclusive,
		c.config.Consume.NoLocal,
		c.config.Consume.NoWait,
		c.config.Consume.Args,
	)
	if err != nil {
		return fmt.Errorf("failed to start consuming: %w", err)
	}

	log.Printf("Started consuming messages from queue: %s", c.config.Consume.QueueName)

	// 处理消息
	go func() {
		for {
			select {
			case <-ctx.Done():
				log.Println("Consumer context cancelled, stopping message consumption")
				return
			case msg, ok := <-msgs:
				if !ok {
					log.Println("Message channel closed")
					return
				}
				c.handleMessage(ctx, msg)
			}
		}
	}()

	return nil
}

// handleMessage 处理单个消息
func (c *Consumer) handleMessage(ctx context.Context, delivery amqp091.Delivery) {
	// 手动ACK
	if !c.config.Consume.AutoAck {
		defer func() {
			if err := c.handler.HandleMessage(ctx, delivery); err != nil {
				log.Printf("Message handling failed: %v", err)
				// 拒绝消息并重新排队
				if nackErr := delivery.Nack(false, true); nackErr != nil {
					log.Printf("Failed to nack message: %v", nackErr)
				}
			} else {
				// 确认消息
				if ackErr := delivery.Ack(false); ackErr != nil {
					log.Printf("Failed to ack message: %v", ackErr)
				}
			}
		}()
	} else {
		// 自动ACK
		if err := c.handler.HandleMessage(ctx, delivery); err != nil {
			log.Printf("Message handling error: %v", err)
		}
	}

}

// IsMessageProcessed 检查消息是否已处理（防重复处理）
func (c *Consumer) IsMessageProcessed(messageID string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	_, exists := c.processedIDs[messageID]
	return exists
}

// MarkMessageProcessed 标记消息为已处理
func (c *Consumer) MarkMessageProcessed(messageID string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.processedIDs[messageID] = time.Now()
}

// startCleanupRoutine 启动清理例程
func (c *Consumer) startCleanupRoutine() {
	for {
		select {
		case <-c.cleanupTicker.C:
			c.cleanupExpiredMessages()
		}
	}
}

// cleanupExpiredMessages 清理过期的已处理消息ID
func (c *Consumer) cleanupExpiredMessages() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	for messageID, processedAt := range c.processedIDs {
		// 清理24小时前的记录
		if now.Sub(processedAt) > 24*time.Hour {
			delete(c.processedIDs, messageID)
		}
	}
	log.Printf("Cleaned up expired message IDs, current count: %d", len(c.processedIDs))
}

// IsConnected 检查连接状态
func (c *Consumer) IsConnected() bool {
	return c.conn.IsConnected()
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true

	if c.cleanupTicker != nil {
		c.cleanupTicker.Stop()
	}

	if c.conn != nil {
		return c.conn.Close()
	}

	log.Println("Consumer closed")
	return nil
}
