package messaging

import (
	"context"
	"time"

	amqp091 "github.com/rabbitmq/amqp091-go"
)

// ExchangeConfig 交换机配置
type ExchangeConfig struct {
	Name       string // 交换机名称
	Type       string // 交换机类型 (topic, direct, fanout, headers)
	Durable    bool   // 是否持久化
	AutoDelete bool   // 是否自动删除
	Internal   bool   // 是否内部使用
	NoWait     bool   // 是否等待
	Args       amqp091.Table
}

// QueueConfig 队列配置
type QueueConfig struct {
	Name       string // 队列名称
	Durable    bool   // 是否持久化
	AutoDelete bool   // 是否自动删除
	Exclusive  bool   // 是否独占
	NoWait     bool   // 是否等待
	Args       amqp091.Table
}

// BindingConfig 绑定配置
type BindingConfig struct {
	QueueName    string // 队列名称
	ExchangeName string // 交换机名称
	RoutingKey   string // 路由键
	NoWait       bool   // 是否等待
	Args         amqp091.Table
}

// QoSConfig QoS配置
type QoSConfig struct {
	PrefetchCount int  // 预取计数
	PrefetchSize  int  // 预取大小
	Global        bool // 是否全局
}

// PublishConfig 发布配置
type PublishConfig struct {
	Exchange   string // 交换机名称
	RoutingKey string // 路由键
	Mandatory  bool   // 是否强制
	Immediate  bool   // 是否立即
}

// ConsumeConfig 消费配置
type ConsumeConfig struct {
	QueueName string // 队列名称
	Consumer  string // 消费者标签
	AutoAck   bool   // 是否自动ACK
	Exclusive bool   // 是否独占
	NoLocal   bool   // 是否本地
	NoWait    bool   // 是否等待
	Args      amqp091.Table
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(ctx context.Context, delivery amqp091.Delivery) error
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	URL               string        // RabbitMQ连接URL
	ReconnectInterval time.Duration // 重连间隔
	MaxReconnectTries int           // 最大重连次数
	HeartbeatInterval time.Duration // 心跳间隔
	ConnectionTimeout time.Duration // 连接超时
}

// DefaultConnectionConfig 默认连接配置
func DefaultConnectionConfig() *ConnectionConfig {
	return &ConnectionConfig{
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 10,
		HeartbeatInterval: 10 * time.Second,
		ConnectionTimeout: 30 * time.Second,
	}
}

// PublisherConfig 发布器配置
type PublisherConfig struct {
	Connection *ConnectionConfig
	Exchanges  []ExchangeConfig
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	Connection *ConnectionConfig
	Exchanges  []ExchangeConfig
	Queues     []QueueConfig
	Bindings   []BindingConfig
	QoS        *QoSConfig
	Consume    ConsumeConfig
}

// DefaultQoSConfig 默认QoS配置
func DefaultQoSConfig() *QoSConfig {
	return &QoSConfig{
		PrefetchCount: 1,
		PrefetchSize:  0,
		Global:        false,
	}
}
