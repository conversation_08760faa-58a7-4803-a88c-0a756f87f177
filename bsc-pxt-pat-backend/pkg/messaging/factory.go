package messaging

// 预定义的常用配置

// CreateAuditEventsPublisher 创建审核事件发布器
func CreateAuditEventsPublisher(url string) (*Publisher, error) {
	return NewPublisherBuilder(url).
		WithTopicExchange(AUDIT_EXCHANGE).
		Build()
}

// CreateWalletEventsPublisher 创建钱包事件发布器
func CreateWalletEventsPublisher(url string) (*Publisher, error) {
	return NewPublisherBuilder(url).
		WithTopicExchange("wallet.events").
		WithTopicExchange("user.events").
		Build()
}

// CreateUserEventsPublisher 创建用户事件发布器
func CreateUserEventsPublisher(url string) (*Publisher, error) {
	return NewPublisherBuilder(url).
		WithTopicExchange(USER_EVENTS_EXCHANGE).
		Build()
}

// CreateMediaProcessingPublisher 创建转码事件发布器
func CreateMediaProcessingPublisher(url string) (*Publisher, error) {
	return NewPublisherBuilder(url).
		WithTopicExchange(MEDIA_PROCESSING_EXCHANGE).
		Build()
}

// CreateMediaProcessingConsumer 创建媒体处理消费者
func CreateMediaProcessingConsumer(url string, handler MessageHandler) (*Consumer, error) {
	return NewConsumerBuilder(url).
		WithTopicExchange(MEDIA_PROCESSING_EXCHANGE).
		WithQueue(MEDIA_PROCESSING_TRANSCODE_QUEUE, true).
		WithBinding(MEDIA_PROCESSING_TRANSCODE_QUEUE, MEDIA_PROCESSING_EXCHANGE, MEDIA_PROCESSING_TRANSCODE_REQUEST_ROUTING_KEY).
		WithBinding(MEDIA_PROCESSING_TRANSCODE_QUEUE, MEDIA_PROCESSING_EXCHANGE, MEDIA_PROCESSING_CONTENT_TRANSCODE_REQUEST_ROUTING_KEY).
		WithBinding(MEDIA_PROCESSING_TRANSCODE_QUEUE, MEDIA_PROCESSING_EXCHANGE, MEDIA_PROCESSING_AUDIT_TRANSCODE_REQUEST_ROUTING_KEY).
		WithQoS(1, 0, false).
		WithConsume(MEDIA_PROCESSING_TRANSCODE_QUEUE, "", false).
		Build(handler)
}

func CreateContentTranscodingResultConsumer(url string, handler MessageHandler) (*Consumer, error) {
	return NewConsumerBuilder(url).
		WithTopicExchange(CONTENT_EXCHANGE).
		WithQueue(CONTENT_TRANSCODING_RESULTS_QUEUE, true).
		WithBinding(CONTENT_TRANSCODING_RESULTS_QUEUE, CONTENT_EXCHANGE, CONTENT_TRANSCODING_COMPLETED_ROUTING_KEY).
		WithBinding(CONTENT_TRANSCODING_RESULTS_QUEUE, CONTENT_EXCHANGE, CONTENT_TRANSCODING_FAILED_ROUTING_KEY).
		WithQoS(1, 0, false).
		WithConsume(CONTENT_TRANSCODING_RESULTS_QUEUE, "", false).
		Build(handler)
}

// CreatePointsServiceUserEventsConsumer 创建积分服务用户事件消费者
func CreatePointsServiceUserEventsConsumer(url string, handler MessageHandler) (*Consumer, error) {
	return NewConsumerBuilder(url).
		WithTopicExchange(USER_EVENTS_EXCHANGE).
		WithQueue(POINTS_SERVICE_USER_EVENTS_QUEUE, true).
		WithBinding(POINTS_SERVICE_USER_EVENTS_QUEUE, USER_EVENTS_EXCHANGE, USER_REGISTERED_ROUTING_KEY).
		WithQoS(1, 0, false).
		WithConsume(POINTS_SERVICE_USER_EVENTS_QUEUE, "", false).
		Build(handler)
}

// CreateWalletServiceUserRegisteredConsumer 创建钱包服务用户注册事件消费者
func CreateWalletServiceUserRegisteredConsumer(url string, handler MessageHandler) (*Consumer, error) {
	return NewConsumerBuilder(url).
		WithTopicExchange(USER_EVENTS_EXCHANGE).
		WithQueue(WALLET_SERVICE_USER_REGISTERED_QUEUE, true).
		WithBinding(WALLET_SERVICE_USER_REGISTERED_QUEUE, USER_EVENTS_EXCHANGE, USER_REGISTERED_ROUTING_KEY).
		WithQoS(1, 0, false).
		WithConsume(WALLET_SERVICE_USER_REGISTERED_QUEUE, "", false).
		Build(handler)
}
