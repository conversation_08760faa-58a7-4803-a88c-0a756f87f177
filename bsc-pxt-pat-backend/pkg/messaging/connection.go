package messaging

import (
	"fmt"
	"log"
	"sync"
	"time"

	amqp091 "github.com/rabbitmq/amqp091-go"
)

// Connection RabbitMQ连接管理器
type Connection struct {
	config  *ConnectionConfig
	conn    *amqp091.Connection
	channel *amqp091.Channel
	mutex   sync.RWMutex
	closed  bool
	closeCh chan struct{}
	errorCh chan *amqp091.Error
}

// NewConnection 创建新的连接管理器
func NewConnection(config *ConnectionConfig) (*Connection, error) {
	if config == nil {
		config = DefaultConnectionConfig()
	}

	c := &Connection{
		config:  config,
		closed:  false,
		closeCh: make(chan struct{}),
		errorCh: make(chan *amqp091.Error),
	}

	if err := c.connect(); err != nil {
		return nil, err
	}

	// 启动连接监控
	go c.monitorConnection()

	return c, nil
}

// connect 建立连接
func (c *Connection) connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return fmt.Errorf("connection is closed")
	}

	// 建立连接
	conn, err := amqp091.Dial(c.config.URL)
	if err != nil {
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	// 创建通道
	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return fmt.Errorf("failed to create channel: %w", err)
	}

	c.conn = conn
	c.channel = channel

	// 监听连接错误
	c.errorCh = make(chan *amqp091.Error)
	c.conn.NotifyClose(c.errorCh)

	log.Printf("RabbitMQ connection established: %s", c.config.URL)
	return nil
}

// monitorConnection 监控连接状态
func (c *Connection) monitorConnection() {
	for {
		select {
		case <-c.closeCh:
			return
		case err := <-c.errorCh:
			if err != nil {
				log.Printf("RabbitMQ connection error: %v", err)
				c.reconnect()
			}
		}
	}
}

// reconnect 重新连接
func (c *Connection) reconnect() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return
	}

	// 关闭现有连接
	if c.channel != nil {
		c.channel.Close()
	}
	if c.conn != nil {
		c.conn.Close()
	}

	// 重连逻辑
	for i := 0; i < c.config.MaxReconnectTries; i++ {
		log.Printf("Attempting to reconnect to RabbitMQ (attempt %d/%d)", i+1, c.config.MaxReconnectTries)

		if err := c.connect(); err != nil {
			log.Printf("Reconnection attempt %d failed: %v", i+1, err)
			time.Sleep(c.config.ReconnectInterval)
			continue
		}

		log.Println("RabbitMQ reconnection successful")
		return
	}

	log.Printf("Failed to reconnect to RabbitMQ after %d attempts", c.config.MaxReconnectTries)
}

// GetChannel 获取通道
func (c *Connection) GetChannel() (*amqp091.Channel, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if c.closed {
		return nil, fmt.Errorf("connection is closed")
	}

	if c.conn == nil || c.conn.IsClosed() || c.channel == nil {
		return nil, fmt.Errorf("connection is not ready")
	}

	return c.channel, nil
}

// DeclareExchange 声明交换机
func (c *Connection) DeclareExchange(config ExchangeConfig) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.ExchangeDeclare(
		config.Name,
		config.Type,
		config.Durable,
		config.AutoDelete,
		config.Internal,
		config.NoWait,
		config.Args,
	)
}

// DeclareQueue 声明队列
func (c *Connection) DeclareQueue(config QueueConfig) (amqp091.Queue, error) {
	channel, err := c.GetChannel()
	if err != nil {
		return amqp091.Queue{}, err
	}

	return channel.QueueDeclare(
		config.Name,
		config.Durable,
		config.AutoDelete,
		config.Exclusive,
		config.NoWait,
		config.Args,
	)
}

// BindQueue 绑定队列
func (c *Connection) BindQueue(config BindingConfig) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.QueueBind(
		config.QueueName,
		config.RoutingKey,
		config.ExchangeName,
		config.NoWait,
		config.Args,
	)
}

// SetQoS 设置QoS
func (c *Connection) SetQoS(config QoSConfig) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.Qos(
		config.PrefetchCount,
		config.PrefetchSize,
		config.Global,
	)
}

// IsConnected 检查连接状态
func (c *Connection) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return !c.closed && c.conn != nil && !c.conn.IsClosed() && c.channel != nil
}

// Close 关闭连接
func (c *Connection) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true
	close(c.closeCh)

	if c.channel != nil {
		if err := c.channel.Close(); err != nil {
			log.Printf("Failed to close channel: %v", err)
		}
	}

	if c.conn != nil {
		if err := c.conn.Close(); err != nil {
			log.Printf("Failed to close connection: %v", err)
		}
	}

	log.Println("RabbitMQ connection closed")
	return nil
}
