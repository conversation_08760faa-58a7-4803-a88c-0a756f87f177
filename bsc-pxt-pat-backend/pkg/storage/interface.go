package storage

import (
	"context"
	"fmt"
	"io"
	GlobalTypes "pxpat-backend/pkg/types"
	"time"

	"github.com/minio/minio-go/v7"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

type ConstantProviderString string

const (
	CONSTANT_PROVIDER_MINIO = ConstantProviderString("minio")
	CONSTANT_PROVIDER_R2    = ConstantProviderString("r2")
	CONSTANT_PROVIDER_S3    = ConstantProviderString("s3")
)

// StorageClient 定义存储操作的统一接口
type StorageClient interface {
	GetClient() interface{}
	GetProvider() ConstantProviderString
	GetBucket() string

	// 基础文件操作
	Upload(ctx context.Context, key string, reader io.Reader, size int64, contentType string) error
	Download(ctx context.Context, key string) (io.ReadCloser, error)
	DownloadToPath(ctx context.Context, key string, localPath string) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)

	// 分片上传操作
	CreateMultipartUpload(ctx context.Context, key string, contentType string) (string, error)
	UploadPart(ctx context.Context, key string, uploadID string, partNumber int32, reader io.Reader) (*types.CompletedPart, error)
	CompleteMultipartUpload(ctx context.Context, key string, uploadID string, parts []types.CompletedPart) (string, error)
	AbortMultipartUpload(ctx context.Context, key string, uploadID string) error
	ListParts(ctx context.Context, key string, uploadID string) ([]types.CompletedPart, error)

	// 分片上传预签名URL操作
	GenerateMultipartUploadPresignedURLs(ctx context.Context, key string, uploadID string, partNumbers []int32, expiry time.Duration) (map[int32]string, error)

	// URL 和列表操作
	GeneratePresignedURL(ctx context.Context, key string, operation string, expiry time.Duration) (string, error)
	GeneratePublicURL(ctx context.Context, key string) (string, error)
	ListObjectsWithPrefix(ctx context.Context, prefix string) ([]*storageFileObject, error)

	// MakeBucket 创建存储桶
	MakeBucket(ctx context.Context, bucket string) error
	BucketExists(ctx context.Context, bucket string) (bool, error)
}

// NewStorageClient 根据配置创建存储客户端
func NewStorageClient(config GlobalTypes.GlobalStorageConfig) (StorageClient, error) {
	switch {
	case ConstantProviderString(config.Provider) == CONSTANT_PROVIDER_MINIO:
		return NewMinioClient(MinioConfig{
			Endpoint:        config.Endpoint,
			Region:          config.Region,
			AccessKeyID:     config.AccessKeyID,
			SecretAccessKey: config.SecretAccessKey,
			UseSSL:          config.UseSSL,
			Bucket:          config.Bucket,
		})
	case ConstantProviderString(config.Provider) == CONSTANT_PROVIDER_R2:
		return NewR2Client(R2Config{
			AccountID:       config.AccountID,
			AccessKeyID:     config.AccessKeyID,
			AccessKeySecret: config.SecretAccessKey,
			Bucket:          config.Bucket,
			Region:          config.Region,
			PublicURL:       config.PublicURL,
		})
	case ConstantProviderString(config.Provider) == CONSTANT_PROVIDER_S3:
		return NewS3Client(S3Config{
			Region:          config.Region,
			AccessKeyID:     config.AccessKeyID,
			SecretAccessKey: config.SecretAccessKey,
			Bucket:          config.Bucket,
		})
	default:
		return nil, fmt.Errorf("unsupported storage provider: %s", config.Provider)
	}
}

type storageFileObject struct {
	FileName     string
	Size         int64
	LastModified time.Time
}

func ListObjectTransformToStorageFileObject(provide ConstantProviderString, object interface{}) (*storageFileObject, error) {
	var storageFileObjects *storageFileObject
	switch provide {
	case CONSTANT_PROVIDER_MINIO:
		value, ok := object.(minio.ObjectInfo)
		if !ok {
			return nil, fmt.Errorf("object is not minio.ObjectInfo")
		}
		storageFileObjects = &storageFileObject{
			FileName:     value.Key,
			Size:         value.Size,
			LastModified: value.LastModified,
		}
		break
	case CONSTANT_PROVIDER_R2:

		break
	case CONSTANT_PROVIDER_S3:
		break
	}
	return storageFileObjects, nil
}
