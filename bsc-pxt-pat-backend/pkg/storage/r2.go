package storage

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// R2Client represents a Cloudflare R2 storage client
type R2Client struct {
	client *s3.Client
	config R2Config
}

// R2Config holds the configuration for R2 storage
type R2Config struct {
	AccountID       string
	AccessKeyID     string
	AccessKeySecret string
	Bucket          string
	Region          string
	PublicURL       string
}

func (c *R2Client) GetClient() interface{} {
	return c.client
}

func (c *R2Client) GetBucket() string {
	return c.config.Bucket
}

func (c *R2Client) GetProvider() ConstantProviderString {
	return CONSTANT_PROVIDER_R2
}

// NewR2Client creates a new R2 storage client
func NewR2Client(cfg R2Config) (*R2Client, error) {
	customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
		return aws.Endpoint{
			URL:           cfg.PublicURL,
			SigningRegion: cfg.Region,
		}, nil
	})

	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(cfg.AccessKeyID, cfg.AccessKeySecret, "")),
		config.WithEndpointResolverWithOptions(customResolver),
		config.WithRegion(cfg.Region),
	)
	if err != nil {
		return nil, fmt.Errorf("unable to load AWS SDK types: %w", err)
	}

	client := s3.NewFromConfig(awsCfg)

	return &R2Client{
		client: client,
		config: cfg,
	}, nil
}

// Upload uploads a file to R2 storage
func (c *R2Client) Upload(ctx context.Context, key string, reader io.Reader, size int64, contentType string) error {
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:        aws.String(c.config.Bucket),
		Key:           aws.String(key),
		Body:          reader,
		ContentLength: aws.Int64(size),
		ContentType:   aws.String(contentType),
	})
	if err != nil {
		return fmt.Errorf("failed to upload file to R2: %w", err)
	}
	return nil
}

// Download downloads a file from R2 storage
func (c *R2Client) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	result, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to download file from R2: %w", err)
	}
	return result.Body, nil
}

// DownloadToPath downloads a file from R2 storage to specified local path
func (c *R2Client) DownloadToPath(ctx context.Context, key string, localPath string) error {
	// 从R2获取对象
	result, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("failed to get object from R2: %w", err)
	}
	defer result.Body.Close()

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()

	// 复制文件内容
	bytesWritten, err := io.Copy(localFile, result.Body)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	// 验证文件大小
	if bytesWritten == 0 {
		return fmt.Errorf("downloaded file is empty")
	}

	return nil
}

// Delete deletes a file from R2 storage
func (c *R2Client) Delete(ctx context.Context, key string) error {
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("failed to delete file from R2: %w", err)
	}
	return nil
}

// Exists checks if a file exists in R2 storage
func (c *R2Client) Exists(ctx context.Context, key string) (bool, error) {
	_, err := c.client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		// 检查是否是文件不存在的错误
		var noSuchKey *types.NoSuchKey
		if errors.As(err, &noSuchKey) {
			return false, nil
		}
		// 检查是否是404错误
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "404") {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if file exists in R2: %w", err)
	}
	return true, nil
}

// CreateMultipartUpload initiates a multipart upload
func (c *R2Client) CreateMultipartUpload(ctx context.Context, key string, contentType string) (string, error) {
	result, err := c.client.CreateMultipartUpload(ctx, &s3.CreateMultipartUploadInput{
		Bucket:      aws.String(c.config.Bucket),
		Key:         aws.String(key),
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", fmt.Errorf("failed to create multipart upload: %w", err)
	}
	return *result.UploadId, nil
}

// UploadPart uploads a part in a multipart upload
func (c *R2Client) UploadPart(ctx context.Context, key string, uploadID string, partNumber int32, reader io.Reader) (*types.CompletedPart, error) {
	result, err := c.client.UploadPart(ctx, &s3.UploadPartInput{
		Bucket:     aws.String(c.config.Bucket),
		Key:        aws.String(key),
		UploadId:   aws.String(uploadID),
		PartNumber: aws.Int32(partNumber),
		Body:       reader,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to upload part: %w", err)
	}

	return &types.CompletedPart{
		PartNumber: aws.Int32(partNumber),
		ETag:       result.ETag,
	}, nil
}

// CompleteMultipartUpload completes a multipart upload
func (c *R2Client) CompleteMultipartUpload(ctx context.Context, key string, uploadID string, parts []types.CompletedPart) (string, error) {
	result, err := c.client.CompleteMultipartUpload(ctx, &s3.CompleteMultipartUploadInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
		MultipartUpload: &types.CompletedMultipartUpload{
			Parts: parts,
		},
	})
	if err != nil {
		return "", fmt.Errorf("failed to complete multipart upload: %w", err)
	}

	return *result.ETag, nil
}

// AbortMultipartUpload aborts a multipart upload
func (c *R2Client) AbortMultipartUpload(ctx context.Context, key string, uploadID string) error {
	_, err := c.client.AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
	})
	if err != nil {
		return fmt.Errorf("failed to abort multipart upload: %w", err)
	}
	return nil
}

// ListParts lists uploaded parts for a multipart upload
func (c *R2Client) ListParts(ctx context.Context, key string, uploadID string) ([]types.CompletedPart, error) {
	result, err := c.client.ListParts(ctx, &s3.ListPartsInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list R2 parts: %w", err)
	}

	var parts []types.CompletedPart
	for _, part := range result.Parts {
		parts = append(parts, types.CompletedPart{
			PartNumber: part.PartNumber,
			ETag:       part.ETag,
		})
	}

	return parts, nil
}

// GeneratePresignedURL generates a presigned URL for object operations
func (c *R2Client) GeneratePresignedURL(ctx context.Context, key string, operation string, expiry time.Duration) (string, error) {
	presignClient := s3.NewPresignClient(c.client)

	var presignedURL string

	switch operation {
	case "get":
		req, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
			Bucket: aws.String(c.config.Bucket),
			Key:    aws.String(key),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return "", fmt.Errorf("failed to generate presigned URL for get: %w", err)
		}
		presignedURL = req.URL
	case "put":
		req, err := presignClient.PresignPutObject(ctx, &s3.PutObjectInput{
			Bucket: aws.String(c.config.Bucket),
			Key:    aws.String(key),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return "", fmt.Errorf("failed to generate presigned URL for put: %w", err)
		}
		presignedURL = req.URL
	default:
		return "", fmt.Errorf("unsupported operation: %s", operation)
	}

	return presignedURL, nil
}

// GeneratePublicURL 生成公开访问URL（适用于公开存储桶）
func (c *R2Client) GeneratePublicURL(ctx context.Context, key string) (string, error) {
	// 使用R2的公开URL格式：PublicURL/key
	publicURL := fmt.Sprintf("%s/%s", strings.TrimSuffix(c.config.PublicURL, "/"), key)
	return publicURL, nil
}

// ListObjectsWithPrefix lists all objects with the given prefix
func (c *R2Client) ListObjectsWithPrefix(ctx context.Context, prefix string) ([]*storageFileObject, error) {
	var returnObjects []*storageFileObject
	var continuationToken *string

	for {
		// 创建列举对象的请求
		input := &s3.ListObjectsV2Input{
			Bucket: aws.String(c.config.Bucket),
			Prefix: aws.String(prefix),
		}

		// 如果有续传令牌，添加到请求中
		if continuationToken != nil {
			input.ContinuationToken = continuationToken
		}

		// 调用ListObjectsV2 API
		result, err := c.client.ListObjectsV2(ctx, input)
		if err != nil {
			return nil, fmt.Errorf("failed to list objects with prefix %s: %w", prefix, err)
		}

		// 将结果添加到keys切片
		for _, object := range result.Contents {
			fileObject, err := ListObjectTransformToStorageFileObject(CONSTANT_PROVIDER_R2, object)
			if err != nil {
				return nil, err
			}
			returnObjects = append(returnObjects, fileObject)
		}

		// 检查是否需要继续获取下一页结果
		if result.IsTruncated != nil && *result.IsTruncated {
			continuationToken = result.NextContinuationToken
		} else {
			break
		}
	}

	return returnObjects, nil
}

// BucketExists 检查存储桶是否存在
func (c *R2Client) BucketExists(ctx context.Context, bucket string) (bool, error) {
	_, err := c.client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		// 如果是 NoSuchBucket 错误，返回 false
		return false, nil
	}
	return true, nil
}

// MakeBucket 创建存储桶
func (c *R2Client) MakeBucket(ctx context.Context, bucket string) error {
	_, err := c.client.CreateBucket(ctx, &s3.CreateBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		return fmt.Errorf("创建R2存储桶失败: %w", err)
	}
	return nil
}

// GenerateMultipartUploadPresignedURLs 生成分片上传的预签名URL
func (c *R2Client) GenerateMultipartUploadPresignedURLs(ctx context.Context, key string, uploadID string, partNumbers []int32, expiry time.Duration) (map[int32]string, error) {
	presignClient := s3.NewPresignClient(c.client)
	urls := make(map[int32]string)

	for _, partNumber := range partNumbers {
		req, err := presignClient.PresignUploadPart(ctx, &s3.UploadPartInput{
			Bucket:     aws.String(c.config.Bucket),
			Key:        aws.String(key),
			UploadId:   aws.String(uploadID),
			PartNumber: aws.Int32(partNumber),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return nil, fmt.Errorf("生成R2分片 %d 预签名URL失败: %w", partNumber, err)
		}
		urls[partNumber] = req.URL
	}

	return urls, nil
}
