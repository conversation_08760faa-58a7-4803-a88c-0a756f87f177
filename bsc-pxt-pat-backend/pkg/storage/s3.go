package storage

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// S3Config AWS S3配置结构体
type S3Config struct {
	Region          string // AWS区域
	AccessKeyID     string // 访问密钥ID
	SecretAccessKey string // 秘密访问密钥
	Bucket          string // 默认存储桶
}

// S3Client AWS S3客户端实现
type S3Client struct {
	client *s3.Client
	config S3Config
}

func (c *S3Client) GetClient() interface{} {
	return c.client
}

func (c *S3Client) GetBucket() string {
	return c.config.Bucket
}

func (c *S3Client) GetProvider() ConstantProviderString {
	return CONSTANT_PROVIDER_S3
}

// NewS3Client 创建AWS S3客户端
func NewS3Client(cfg S3Config) (*S3Client, error) {
	// 创建AWS配置
	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			cfg.AccessKeyID,
			cfg.SecretAccessKey,
			"",
		)),
		config.WithRegion(cfg.Region),
	)
	if err != nil {
		return nil, fmt.Errorf("创建AWS配置失败: %w", err)
	}

	// 创建S3客户端
	client := s3.NewFromConfig(awsCfg)

	return &S3Client{
		client: client,
		config: cfg,
	}, nil
}

// Upload 上传文件到S3
func (c *S3Client) Upload(ctx context.Context, key string, reader io.Reader, size int64, contentType string) error {
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:        aws.String(c.config.Bucket),
		Key:           aws.String(key),
		Body:          reader,
		ContentLength: aws.Int64(size),
		ContentType:   aws.String(contentType),
	})
	if err != nil {
		return fmt.Errorf("上传文件到S3失败: %w", err)
	}
	return nil
}

// Download 从S3下载文件
func (c *S3Client) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	result, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("从S3下载文件失败: %w", err)
	}
	return result.Body, nil
}

// DownloadToPath 从S3下载文件到指定本地路径
func (c *S3Client) DownloadToPath(ctx context.Context, key string, localPath string) error {
	// 从S3获取对象
	result, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("从S3获取对象失败: %w", err)
	}
	defer result.Body.Close()

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer localFile.Close()

	// 复制文件内容
	bytesWritten, err := io.Copy(localFile, result.Body)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	// 验证文件大小
	if bytesWritten == 0 {
		return fmt.Errorf("下载的文件为空")
	}

	return nil
}

// Delete 从S3删除文件
func (c *S3Client) Delete(ctx context.Context, key string) error {
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("从S3删除文件失败: %w", err)
	}
	return nil
}

// Exists 检查文件是否存在于S3
func (c *S3Client) Exists(ctx context.Context, key string) (bool, error) {
	_, err := c.client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		// 检查是否是文件不存在的错误
		var noSuchKey *types.NoSuchKey
		if errors.As(err, &noSuchKey) {
			return false, nil
		}
		// 检查是否是404错误
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "404") {
			return false, nil
		}
		return false, fmt.Errorf("检查S3文件是否存在失败: %w", err)
	}
	return true, nil
}

// CreateMultipartUpload 创建分片上传
func (c *S3Client) CreateMultipartUpload(ctx context.Context, key string, contentType string) (string, error) {
	result, err := c.client.CreateMultipartUpload(ctx, &s3.CreateMultipartUploadInput{
		Bucket:      aws.String(c.config.Bucket),
		Key:         aws.String(key),
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", fmt.Errorf("创建S3分片上传失败: %w", err)
	}
	return *result.UploadId, nil
}

// UploadPart 上传分片
func (c *S3Client) UploadPart(ctx context.Context, key string, uploadID string, partNumber int32, reader io.Reader) (*types.CompletedPart, error) {
	result, err := c.client.UploadPart(ctx, &s3.UploadPartInput{
		Bucket:     aws.String(c.config.Bucket),
		Key:        aws.String(key),
		UploadId:   aws.String(uploadID),
		PartNumber: aws.Int32(partNumber),
		Body:       reader,
	})
	if err != nil {
		return nil, fmt.Errorf("上传S3分片失败: %w", err)
	}

	return &types.CompletedPart{
		PartNumber: aws.Int32(partNumber),
		ETag:       result.ETag,
	}, nil
}

// CompleteMultipartUpload 完成分片上传
func (c *S3Client) CompleteMultipartUpload(ctx context.Context, key string, uploadID string, parts []types.CompletedPart) (string, error) {
	result, err := c.client.CompleteMultipartUpload(ctx, &s3.CompleteMultipartUploadInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
		MultipartUpload: &types.CompletedMultipartUpload{
			Parts: parts,
		},
	})
	if err != nil {
		return "", fmt.Errorf("完成S3分片上传失败: %w", err)
	}

	return *result.ETag, nil
}

// AbortMultipartUpload 取消分片上传
func (c *S3Client) AbortMultipartUpload(ctx context.Context, key string, uploadID string) error {
	_, err := c.client.AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
	})
	if err != nil {
		return fmt.Errorf("取消S3分片上传失败: %w", err)
	}
	return nil
}

// ListParts 列出已上传的分片
func (c *S3Client) ListParts(ctx context.Context, key string, uploadID string) ([]types.CompletedPart, error) {
	result, err := c.client.ListParts(ctx, &s3.ListPartsInput{
		Bucket:   aws.String(c.config.Bucket),
		Key:      aws.String(key),
		UploadId: aws.String(uploadID),
	})
	if err != nil {
		return nil, fmt.Errorf("列出S3分片失败: %w", err)
	}

	var parts []types.CompletedPart
	for _, part := range result.Parts {
		parts = append(parts, types.CompletedPart{
			PartNumber: part.PartNumber,
			ETag:       part.ETag,
		})
	}

	return parts, nil
}

// GeneratePresignedURL 生成预签名URL
func (c *S3Client) GeneratePresignedURL(ctx context.Context, key string, operation string, expiry time.Duration) (string, error) {
	presignClient := s3.NewPresignClient(c.client)

	switch operation {
	case "get":
		req, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
			Bucket: aws.String(c.config.Bucket),
			Key:    aws.String(key),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return "", fmt.Errorf("生成S3 GET预签名URL失败: %w", err)
		}
		return req.URL, nil
	case "put":
		req, err := presignClient.PresignPutObject(ctx, &s3.PutObjectInput{
			Bucket: aws.String(c.config.Bucket),
			Key:    aws.String(key),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return "", fmt.Errorf("生成S3 PUT预签名URL失败: %w", err)
		}
		return req.URL, nil
	default:
		return "", fmt.Errorf("不支持的操作类型: %s", operation)
	}
}

// GeneratePublicURL 生成公开访问URL（适用于公开存储桶）
func (c *S3Client) GeneratePublicURL(ctx context.Context, key string) (string, error) {
	// 构建S3公开访问URL格式：https://bucket.s3.region.amazonaws.com/key
	publicURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", c.config.Bucket, c.config.Region, key)
	return publicURL, nil
}

// ListObjectsWithPrefix 列举指定前缀的所有对象
func (c *S3Client) ListObjectsWithPrefix(ctx context.Context, prefix string) ([]*storageFileObject, error) {
	var returnObjects []*storageFileObject
	var continuationToken *string

	for {
		input := &s3.ListObjectsV2Input{
			Bucket: aws.String(c.config.Bucket),
			Prefix: aws.String(prefix),
		}

		if continuationToken != nil {
			input.ContinuationToken = continuationToken
		}

		result, err := c.client.ListObjectsV2(ctx, input)
		if err != nil {
			return nil, fmt.Errorf("列举S3对象失败: %w", err)
		}

		for _, object := range result.Contents {
			fileObject, err := ListObjectTransformToStorageFileObject(CONSTANT_PROVIDER_S3, object)
			if err != nil {
				return nil, err
			}
			returnObjects = append(returnObjects, fileObject)
		}

		if result.IsTruncated != nil && *result.IsTruncated {
			continuationToken = result.NextContinuationToken
		} else {
			break
		}
	}

	return returnObjects, nil
}

// BucketExists 检查存储桶是否存在
func (c *S3Client) BucketExists(ctx context.Context, bucket string) (bool, error) {
	_, err := c.client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		// 如果是 NoSuchBucket 错误，返回 false
		return false, nil
	}
	return true, nil
}

// MakeBucket 创建存储桶
func (c *S3Client) MakeBucket(ctx context.Context, bucket string) error {
	_, err := c.client.CreateBucket(ctx, &s3.CreateBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		return fmt.Errorf("创建S3存储桶失败: %w", err)
	}
	return nil
}

// GenerateMultipartUploadPresignedURLs 生成分片上传的预签名URL
func (c *S3Client) GenerateMultipartUploadPresignedURLs(ctx context.Context, key string, uploadID string, partNumbers []int32, expiry time.Duration) (map[int32]string, error) {
	presignClient := s3.NewPresignClient(c.client)
	urls := make(map[int32]string)

	for _, partNumber := range partNumbers {
		req, err := presignClient.PresignUploadPart(ctx, &s3.UploadPartInput{
			Bucket:     aws.String(c.config.Bucket),
			Key:        aws.String(key),
			UploadId:   aws.String(uploadID),
			PartNumber: aws.Int32(partNumber),
		}, s3.WithPresignExpires(expiry))
		if err != nil {
			return nil, fmt.Errorf("生成S3分片 %d 预签名URL失败: %w", partNumber, err)
		}
		urls[partNumber] = req.URL
	}

	return urls, nil
}
