package storage

import (
	"context"
	"fmt"
	"io"
	"net/http"
	url2 "net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinioConfig MinIO配置结构体
type MinioConfig struct {
	Endpoint        string // MinIO服务端点
	Region          string // 存储区域
	AccessKeyID     string // 访问密钥ID
	SecretAccessKey string // 秘密访问密钥
	UseSSL          bool   // 是否使用SSL
	Bucket          string // 默认存储桶
}

// MinioClient MinIO客户端实现，同时实现 StorageClient、StorageProvider 接口
type MinioClient struct {
	client *minio.Client
	config MinioConfig
}

func (c *MinioClient) GetClient() interface{} {
	return c.client
}

func (c *MinioClient) GetBucket() string {
	return c.config.Bucket
}

func (c *MinioClient) GetProvider() ConstantProviderString {
	return CONSTANT_PROVIDER_MINIO
}

// NewMinioClient 创建MinIO客户端
func NewMinioClient(config MinioConfig) (*MinioClient, error) {
	// 创建MinIO客户端
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
		Region: config.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("创建MinIO客户端失败: %w", err)
	}

	// 确保存储桶存在
	exists, err := client.BucketExists(context.Background(), config.Bucket)
	if err != nil {
		return nil, fmt.Errorf("检查存储桶失败: %w", err)
	}

	if !exists {
		err = client.MakeBucket(context.Background(), config.Bucket, minio.MakeBucketOptions{
			Region: config.Region,
		})
		if err != nil {
			return nil, fmt.Errorf("创建存储桶失败: %w", err)
		}
	}

	return &MinioClient{
		client: client,
		config: config,
	}, nil
}

// ========== StorageClient 接口实现 ==========

// Upload 上传文件到默认存储桶
func (c *MinioClient) Upload(ctx context.Context, key string, reader io.Reader, size int64, contentType string) error {
	_, err := c.client.PutObject(ctx, c.config.Bucket, key, reader, size, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return fmt.Errorf("上传文件失败: %w", err)
	}
	return nil
}

// Download 从默认存储桶下载文件
func (c *MinioClient) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	obj, err := c.client.GetObject(ctx, c.config.Bucket, key, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("下载文件失败: %w", err)
	}
	return obj, nil
}

// DownloadToPath 从默认存储桶下载文件到指定本地路径
func (c *MinioClient) DownloadToPath(ctx context.Context, key string, localPath string) error {
	// 从MinIO获取对象
	obj, err := c.client.GetObject(ctx, c.config.Bucket, key, minio.GetObjectOptions{})
	if err != nil {
		return fmt.Errorf("获取MinIO对象失败: %w", err)
	}
	defer obj.Close()

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer localFile.Close()

	// 复制文件内容
	bytesWritten, err := io.Copy(localFile, obj)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	// 验证文件大小
	if bytesWritten == 0 {
		return fmt.Errorf("下载的文件为空")
	}

	return nil
}

// Delete 从默认存储桶删除文件
func (c *MinioClient) Delete(ctx context.Context, key string) error {
	err := c.client.RemoveObject(ctx, c.config.Bucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}
	return nil
}

// Exists 检查文件是否存在
func (c *MinioClient) Exists(ctx context.Context, key string) (bool, error) {
	_, err := c.client.StatObject(ctx, c.config.Bucket, key, minio.StatObjectOptions{})
	if err != nil {
		// 检查是否是文件不存在的错误
		errResponse := minio.ToErrorResponse(err)
		if errResponse.Code == "NoSuchKey" {
			return false, nil
		}
		return false, fmt.Errorf("检查文件是否存在失败: %w", err)
	}
	return true, nil
}

// CreateMultipartUpload 创建分片上传
func (c *MinioClient) CreateMultipartUpload(ctx context.Context, key string, contentType string) (string, error) {
	// MinIO使用S3兼容的API，通过Core API访问分片上传功能
	core := minio.Core{Client: c.client}

	uploadID, err := core.NewMultipartUpload(ctx, c.config.Bucket, key, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return "", fmt.Errorf("创建MinIO分片上传失败: %w", err)
	}
	return uploadID, nil
}

// UploadPart 上传分片
func (c *MinioClient) UploadPart(ctx context.Context, key string, uploadID string, partNumber int32, reader io.Reader) (*types.CompletedPart, error) {
	// MinIO使用S3兼容的API，通过Core API访问分片上传功能
	core := minio.Core{Client: c.client}

	// 需要获取reader的大小
	// 如果reader是*bytes.Reader或*strings.Reader，可以获取大小
	// 否则需要先读取到内存中
	var size int64
	var body io.Reader = reader

	// 尝试获取大小
	if seeker, ok := reader.(io.Seeker); ok {
		currentPos, err := seeker.Seek(0, io.SeekCurrent)
		if err == nil {
			endPos, err := seeker.Seek(0, io.SeekEnd)
			if err == nil {
				size = endPos - currentPos
				seeker.Seek(currentPos, io.SeekStart) // 恢复原始位置
			}
		}
	}

	// 如果无法获取大小，读取到内存中
	if size == 0 {
		data, err := io.ReadAll(reader)
		if err != nil {
			return nil, fmt.Errorf("读取分片数据失败: %w", err)
		}
		size = int64(len(data))
		body = strings.NewReader(string(data))
	}

	part, err := core.PutObjectPart(ctx, c.config.Bucket, key, uploadID, int(partNumber), body, size, minio.PutObjectPartOptions{})
	if err != nil {
		return nil, fmt.Errorf("上传MinIO分片失败: %w", err)
	}

	return &types.CompletedPart{
		PartNumber: &partNumber,
		ETag:       &part.ETag,
	}, nil
}

// CompleteMultipartUpload 完成分片上传
func (c *MinioClient) CompleteMultipartUpload(ctx context.Context, key string, uploadID string, parts []types.CompletedPart) (string, error) {
	// MinIO使用S3兼容的API，通过Core API访问分片上传功能
	core := minio.Core{Client: c.client}

	// 转换parts格式
	var minioParts []minio.CompletePart
	for _, part := range parts {
		minioParts = append(minioParts, minio.CompletePart{
			PartNumber: int(*part.PartNumber),
			ETag:       *part.ETag,
		})
	}

	result, err := core.CompleteMultipartUpload(ctx, c.config.Bucket, key, uploadID, minioParts, minio.PutObjectOptions{})
	if err != nil {
		return "", fmt.Errorf("完成MinIO分片上传失败: %w", err)
	}

	return result.ETag, nil
}

// AbortMultipartUpload 取消分片上传
func (c *MinioClient) AbortMultipartUpload(ctx context.Context, key string, uploadID string) error {
	// MinIO使用S3兼容的API，通过Core API访问分片上传功能
	core := minio.Core{Client: c.client}

	err := core.AbortMultipartUpload(ctx, c.config.Bucket, key, uploadID)
	if err != nil {
		return fmt.Errorf("取消MinIO分片上传失败: %w", err)
	}
	return nil
}

// ListParts 列出已上传的分片
func (c *MinioClient) ListParts(ctx context.Context, key string, uploadID string) ([]types.CompletedPart, error) {
	// MinIO使用S3兼容的API，通过Core API访问分片上传功能
	core := minio.Core{Client: c.client}

	result, err := core.ListObjectParts(ctx, c.config.Bucket, key, uploadID, 0, 1000)
	if err != nil {
		return nil, fmt.Errorf("列出MinIO分片失败: %w", err)
	}

	var parts []types.CompletedPart
	for _, part := range result.ObjectParts {
		partNumber := int32(part.PartNumber)
		parts = append(parts, types.CompletedPart{
			PartNumber: &partNumber,
			ETag:       &part.ETag,
		})
	}

	return parts, nil
}

// GeneratePresignedURL 生成预签名URL
func (c *MinioClient) GeneratePresignedURL(ctx context.Context, key string, operation string, expiry time.Duration) (string, error) {
	switch operation {
	case "get":
		url, err := c.client.PresignedGetObject(ctx, c.config.Bucket, key, expiry, nil)
		if err != nil {
			return "", fmt.Errorf("生成GET预签名URL失败: %w", err)
		}
		return url.String(), nil
	case "put":
		url, err := c.client.PresignedPutObject(ctx, c.config.Bucket, key, expiry)
		if err != nil {
			return "", fmt.Errorf("生成PUT预签名URL失败: %w", err)
		}
		return url.String(), nil
	default:
		return "", fmt.Errorf("不支持的操作类型: %s", operation)
	}
}

// GeneratePublicURL 生成公开访问URL（适用于公开存储桶）
func (c *MinioClient) GeneratePublicURL(ctx context.Context, key string) (string, error) {
	// 构建公开访问URL格式：http(s)://endpoint/bucket/key
	scheme := "http"
	if c.config.UseSSL {
		scheme = "https"
	}

	publicURL := fmt.Sprintf("%s://%s/%s/%s", scheme, c.config.Endpoint, c.config.Bucket, key)
	return publicURL, nil
}

// GenerateMultipartUploadPresignedURLs 生成分片上传的预签名URL
func (c *MinioClient) GenerateMultipartUploadPresignedURLs(ctx context.Context, key string, uploadID string, partNumbers []int32, expiry time.Duration) (map[int32]string, error) {
	urls := make(map[int32]string)

	for _, partNumber := range partNumbers {
		req := url2.Values{}
		req.Set("uploadId", uploadID)
		req.Set("partNumber", strconv.Itoa(int(partNumber)))
		// 使用标准的预签名URL方法，然后手动添加查询参数
		url, err := c.client.Presign(ctx, http.MethodPut, c.config.Bucket, key, expiry, req)
		if err != nil {
			return nil, fmt.Errorf("生成分片 %d 预签名URL失败: %w", partNumber, err)
		}

		urls[partNumber] = url.String()
	}

	return urls, nil
}

// ListObjectsWithPrefix 列举指定前缀的所有对象
func (c *MinioClient) ListObjectsWithPrefix(ctx context.Context, prefix string) ([]*storageFileObject, error) {
	var returnObjects []*storageFileObject

	objectCh := c.client.ListObjects(ctx, c.config.Bucket, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("列举对象失败: %w", object.Err)
		}
		fileObject, err := ListObjectTransformToStorageFileObject(CONSTANT_PROVIDER_MINIO, object)
		if err != nil {
			return nil, err
		}
		returnObjects = append(returnObjects, fileObject)
	}

	return returnObjects, nil
}

// BucketExists 检查存储桶是否存在
func (c *MinioClient) BucketExists(ctx context.Context, bucket string) (bool, error) {
	return c.client.BucketExists(ctx, bucket)
}

// MakeBucket 创建存储桶
func (c *MinioClient) MakeBucket(ctx context.Context, bucket string) error {
	return c.client.MakeBucket(ctx, bucket, minio.MakeBucketOptions{
		Region: c.config.Region,
	})
}
