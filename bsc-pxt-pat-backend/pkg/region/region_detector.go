package region

import (
	"net"
	"strings"
)

// 区域常量
const (
	RegionCN     = "CN"     // 中国大陆
	RegionGlobal = "GLOBAL" // 国际
)

// RegionConfig 区域配置
type RegionConfig struct {
	CNDomains       []string `mapstructure:"cn_domains"`
	CNIPRanges      []string `mapstructure:"cn_ip_ranges"`
	CNPhonePrefixes []string `mapstructure:"cn_phone_prefixes"`
	GlobalDomains   []string `mapstructure:"global_domains"`
	DefaultRegion   string   `mapstructure:"default_region"`
}

// RegionDetector 区域检测器接口
type RegionDetector interface {
	// 根据域名判断区域
	DetectRegionByDomain(domain string) string
	// 根据IP地址判断区域
	DetectRegionByIP(ip string) string
	// 根据手机号判断区域
	DetectRegionByPhone(phoneNumber string) string
	// 整合多源信息判断区域
	DetectRegion(domain, ip, phoneNumber string) string
}

// regionDetector 区域检测器实现
type regionDetector struct {
	config          *RegionConfig
	cnDomains       []string
	cnIPRanges      []string
	cnPhonePrefixes []string
}

// NewRegionDetector 创建新的区域检测器
func NewRegionDetector(config *RegionConfig) RegionDetector {
	if config == nil {
		// 默认配置
		return &regionDetector{
			config: &RegionConfig{
				DefaultRegion: RegionGlobal,
				GlobalDomains: []string{".com", ".org", ".net"},
			},
			cnDomains:       []string{".cn", "localhost"},
			cnIPRanges:      []string{"192.168.", "127.0.0."},
			cnPhonePrefixes: []string{"+86", "86"},
		}
	}

	return &regionDetector{
		config:          config,
		cnDomains:       config.CNDomains,
		cnIPRanges:      config.CNIPRanges,
		cnPhonePrefixes: config.CNPhonePrefixes,
	}
}

// DetectRegionByDomain 根据域名判断区域
func (d *regionDetector) DetectRegionByDomain(domain string) string {
	if domain == "" {
		return d.config.DefaultRegion
	}

	// 清理域名，去除协议前缀和路径
	domain = cleanDomain(domain)

	// 检查是否匹配中国大陆域名
	for _, cnDomain := range d.cnDomains {
		if strings.HasSuffix(domain, cnDomain) {
			return RegionCN
		}
	}

	// 检查是否匹配国际域名
	for _, globalDomain := range d.config.GlobalDomains {
		if strings.HasSuffix(domain, globalDomain) {
			return RegionGlobal
		}
	}

	// 默认返回配置的默认区域
	return d.config.DefaultRegion
}

// DetectRegionByIP 根据IP地址判断区域
func (d *regionDetector) DetectRegionByIP(ip string) string {
	if ip == "" {
		return d.config.DefaultRegion
	}

	// 这里可以集成第三方IP地理位置服务
	// 简单实现：检查是否为私有IP或本地IP
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return d.config.DefaultRegion
	}

	// 检查是否为私有IP或本地IP
	if isPrivateIP(parsedIP) || isLoopbackIP(parsedIP) {
		return d.config.DefaultRegion
	}

	// 根据配置文件中的cn_ip_ranges，遍历IP段判断是否为中国大陆IP
	for _, ipCIDR := range d.cnIPRanges {
		_, parsedCIDR, _ := net.ParseCIDR(ipCIDR)
		if parsedCIDR.Contains(net.ParseIP(ip)) {
			return RegionCN
		}
	}
	return d.config.DefaultRegion
}

// DetectRegionByPhone 根据手机号判断区域
func (d *regionDetector) DetectRegionByPhone(phoneNumber string) string {
	if phoneNumber == "" {
		return d.config.DefaultRegion
	}

	// 简单实现：如果手机号以+86开头或长度为11位且以1开头，判断为中国大陆手机号
	if strings.HasPrefix(phoneNumber, "+86") || (len(phoneNumber) == 11 && strings.HasPrefix(phoneNumber, "1")) {
		return RegionCN
	}

	// 其他情况判断为国际手机号
	return RegionGlobal
}

// DetectRegion 整合多源信息判断区域
func (d *regionDetector) DetectRegion(domain, ip, phoneNumber string) string {
	// 根据手机号码判断
	if phoneNumber != "" {
		for _, prefix := range d.cnPhonePrefixes {
			if strings.HasPrefix(phoneNumber, prefix) {
				return RegionCN
			}
		}
	}

	// 根据域名判断
	if domain != "" {
		domain = strings.ToLower(domain)
		for _, cnDomain := range d.cnDomains {
			if strings.Contains(domain, cnDomain) {
				return RegionCN
			}
		}
	}

	// 根据IP地址判断
	if ip != "" {
		for _, ipCIDR := range d.cnIPRanges {
			_, parsedCIDR, _ := net.ParseCIDR(ipCIDR)
			if parsedCIDR.Contains(net.ParseIP(ip)) {
				return RegionCN
			}
		}
	}

	// 默认返回全球区域
	return RegionGlobal
}

// cleanDomain 清理域名，去除协议前缀和路径
func cleanDomain(domain string) string {
	// 去除http://或https://前缀
	if strings.HasPrefix(domain, "http://") {
		domain = domain[7:]
	} else if strings.HasPrefix(domain, "https://") {
		domain = domain[8:]
	}

	// 去除路径和查询参数
	if index := strings.Index(domain, "/"); index != -1 {
		domain = domain[:index]
	}

	// 去除端口号
	if index := strings.Index(domain, ":"); index != -1 {
		domain = domain[:index]
	}

	return domain
}

// isPrivateIP 判断IP是否为私有IP
func isPrivateIP(ip net.IP) bool {
	// IPv4私有IP段检查
	if ip4 := ip.To4(); ip4 != nil {
		// 10.0.0.0/8
		if ip4[0] == 10 {
			return true
		}
		// **********/12
		if ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31 {
			return true
		}
		// ***********/16
		if ip4[0] == 192 && ip4[1] == 168 {
			return true
		}
	}

	// IPv6私有IP段检查（简化版）
	return false
}

// isLoopbackIP 判断IP是否为回环IP
func isLoopbackIP(ip net.IP) bool {
	return ip.IsLoopback()
}
