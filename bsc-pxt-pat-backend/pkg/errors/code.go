package errors

import "errors"

var NoneError = errors.New("user error,don't need log ")

// 系统错误  -1 ~ ∞ 不展示给用户
const (
	SYSTEM_ERROR                 = -1
	MAKE_DIR_ERROR               = -2
	CREATE_FILE_ERROR            = -3
	WRITE_FILE_ERROR             = -4
	OPEN_PART_ERROR              = -5
	DATABASE_WRITE_ERROR         = -6
	READ_UPLOAD_ERROR            = -7
	CREATE_RECORD_ERROR          = -8
	GET_RECORD_ERROR             = -9
	SEND_INTERNAL_HTTP_ERROR     = -10
	INTERNAL_HTTP_RESPONSE_ERROR = -11
	JSON_UNMARSHAL_ERROR         = -12
	JSON_MARSHAL_ERROR           = -12
	GET_TAG_ERROR                = -13
	UPDATE_RECORD_ERROR          = -14
)

// 成功占用CODE 0-9999
const (
	SUCCESS           = 0
	HAVE_DATA_SUCCESS = 1
)

// 可展示给用户的错误 10000 ~ 无穷
const (
	INVALID_PARAMETER            = 10000
	INVALID_TOKEN                = 10001
	INVALID_USER                 = 10002
	INVALID_PASSWORD             = 10003
	OVERSIZE_FILE                = 10004
	INVALID_FILETYPE             = 10005
	FILE_NOT_FOUND               = 10006
	PERMISSION_DENIED            = 10007
	PART_ALREADY_EXISTS          = 10009
	UPLOAD_HAVE_OMIT             = 10010
	CATEGORY_ERROR               = 10011
	TITLE_ERROR                  = 10012
	TYPE_ERROR                   = 10013
	VIDEO_ID_ERROR               = 10014
	FILE_ID_ERROR                = 10015
	COVER_ERROR                  = 10016
	ACTOR_ERROR                  = 10017
	PUBLISHER_ERROR              = 10018
	DIRECTOR_ERROR               = 10019
	FILE_NOT_UPLOADED            = 10020
	NOT_USER_FILE                = 10021
	NOT_VALID_CATEGORY           = 10022
	VIDEO_ID_EXISTS              = 10023
	GET_TAG_NOT_EXISTS_ERROR     = 10024
	ORIGINAL_TAGS_ERROR          = 10082
	DATA_NOT_FOUND               = 10025
	CONTENT_STATUS_ERROR         = 10026
	NO_AVAILABLE_TASK            = 10027
	ALREADY_VOTED                = 10028
	PUBLISH_AT_ERROR             = 10029
	DO_NOT_HAVE_THIS_TRANSLATION = 10030
	SERVICE_UNAVAILABLE          = 10031

	// 媒体处理服务相关错误
	NOT_FOUND              = 10032
	INTERNAL_SERVER_ERROR  = 10033
	MEDIA_PROCESSING_ERROR = 10034
	TASK_NOT_FOUND         = 10035
	INVALID_MEDIA_FORMAT   = 10036
	PROCESSING_TIMEOUT     = 10037
	STORAGE_ERROR          = 10038

	CREATED_AT_TIME_LAYOUT_ERROR         = 10039
	OUT_OF_MAX_PARTS_NUM                 = 10040
	CREATE_UPLOAD_BUCKET_PARTS_ERROR     = 10041
	CANCEL_UPLOAD_BUCKET_PARTS_ERROR     = 10042
	CREATE_UPLOAD_BUCKET_PARTS_URL_ERROR = 10043
	UPLOAD_PART_PRESIGN_URL_NOT_FOUND    = 10044
	INVALID_FILE_STATUS                  = 10045
	AUDIT_TASK_STATUS_NOT_IS_PENDING     = 10046
	AVATAR_FILE_NOT_FOUND                = 10047
	UPLOAD_AVATAR_DISABLED               = 10048
	AVATAR_FILE_SIZE_EXCEEDED            = 10049
	DECODE_IMAGE_ERROR                   = 10050
	AVATAR_DIMENSION_TOO_SMALL           = 10051
	UPLOAD_AVATAR_FAILED                 = 10052
	READUPLOAD_ERROR                     = 10053

	// Banner相关错误
	UPLOAD_BANNER_DISABLED               = 10054
	UPLOAD_BANNER_FAILED                 = 10055
	BANNER_FILE_NOT_FOUND                = 10056
	BANNER_FILE_SIZE_EXCEEDED            = 10057
	BANNER_DIMENSION_TOO_SMALL           = 10058
)
