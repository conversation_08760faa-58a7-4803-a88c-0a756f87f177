package errors

import (
	errors2 "github.com/pkg/errors"
)

type Errors struct {
	Code         int `json:"code"`
	InternalCode int
	Err          error
}

func NewGlobalErrors(code int, internalCode int, err error) *Errors {
	return &Errors{
		Code:         code,
		InternalCode: internalCode,
		Err:          err,
	}
}

// 实现原生error接口
func (e *Errors) Error() string {
	if e.Err != nil {
		return e.Err.Error()
	} else {
		return "调用为空 error 请检查代码"
	}
}

func JudgeIsSuccessCode(code int) bool {
	if code > 9999 || code < 0 {
		return true
	}
	return false
}

func Is(err, target error) bool {
	return errors2.Is(err, target)
}
