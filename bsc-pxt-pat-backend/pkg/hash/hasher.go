package hash

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"hash"
	"io"
	"strings"

	"github.com/zeebo/blake3"
)

// Hasher 哈希计算器接口
type Hasher interface {
	// Name 返回哈希算法名称
	Name() string

	// Hash 计算数据的哈希值，返回十六进制编码的字符串
	Hash(data []byte) (string, error)

	// HashReader 计算Reader中数据的哈希值，返回十六进制编码的字符串
	HashReader(reader io.Reader) (string, error)

	// HashFile 计算文件的哈希值，返回十六进制编码的字符串
	HashFile(filePath string) (string, error)
}

// HashResult 哈希计算结果
type HashResult struct {
	Algorithm string `json:"algorithm"` // 算法名称
	Hash      string `json:"hash"`      // 哈希值（十六进制）
}

// SupportedAlgorithms 支持的哈希算法列表
var SupportedAlgorithms = []string{"SHA256", "BLAKE3"}

// NewHasher 根据算法名称创建哈希计算器
func NewHasher(algorithm string) (Hasher, error) {
	switch strings.ToUpper(algorithm) {
	case "SHA256":
		return NewSHA256Hasher(), nil
	case "BLAKE3":
		return NewBLAKE3Hasher(), nil
	default:
		return nil, fmt.Errorf("不支持的哈希算法: %s", algorithm)
	}
}

// HashFileWithAlgorithms 使用指定算法计算文件哈希值
func HashFileWithAlgorithms(filePath string, algorithms ...string) ([]HashResult, error) {
	if len(algorithms) == 0 {
		algorithms = SupportedAlgorithms
	}

	hashers := make([]Hasher, 0, len(algorithms))
	for _, algorithm := range algorithms {
		hasher, err := NewHasher(algorithm)
		if err != nil {
			return nil, err
		}
		hashers = append(hashers, hasher)
	}

	multiHasher := NewMultiHasher(hashers...)
	return multiHasher.HashFile(filePath)
}

// HashDataWithAlgorithms 使用指定算法计算数据哈希值
func HashDataWithAlgorithms(data []byte, algorithms ...string) ([]HashResult, error) {
	if len(algorithms) == 0 {
		algorithms = SupportedAlgorithms
	}

	hashers := make([]Hasher, 0, len(algorithms))
	for _, algorithm := range algorithms {
		hasher, err := NewHasher(algorithm)
		if err != nil {
			return nil, err
		}
		hashers = append(hashers, hasher)
	}

	multiHasher := NewMultiHasher(hashers...)
	return multiHasher.Hash(data)
}

// MultiHasher 多重哈希计算器，可以同时使用多个哈希算法
type MultiHasher struct {
	hashers []Hasher
}

// NewMultiHasher 创建多重哈希计算器
func NewMultiHasher(hashers ...Hasher) *MultiHasher {
	return &MultiHasher{
		hashers: hashers,
	}
}

// NewDefaultMultiHasher 创建默认的多重哈希计算器（包含SHA256和BLAKE3）
func NewDefaultMultiHasher() *MultiHasher {
	return NewMultiHasher(
		NewSHA256Hasher(),
		NewBLAKE3Hasher(),
	)
}

// HashFile 使用所有哈希算法计算文件哈希值
func (m *MultiHasher) HashFile(filePath string) ([]HashResult, error) {
	results := make([]HashResult, 0, len(m.hashers))

	for _, hasher := range m.hashers {
		hash, err := hasher.HashFile(filePath)
		if err != nil {
			return nil, err
		}

		results = append(results, HashResult{
			Algorithm: hasher.Name(),
			Hash:      hash,
		})
	}

	return results, nil
}

// HashReader 使用所有哈希算法计算Reader中数据的哈希值
// 使用流式处理，避免将大文件完全加载到内存中
func (m *MultiHasher) HashReader(reader io.Reader) ([]HashResult, error) {
	// 创建所有哈希器的写入器
	writers := make([]io.Writer, 0, len(m.hashers))
	hashers := make([]interface{}, 0, len(m.hashers)) // 存储实际的哈希器实例

	for _, hasher := range m.hashers {
		switch hasher.(type) {
		case *SHA256Hasher:
			sha256Hasher := sha256.New()
			writers = append(writers, sha256Hasher)
			hashers = append(hashers, sha256Hasher)
		case *BLAKE3Hasher:
			blake3Hasher := blake3.New()
			writers = append(writers, blake3Hasher)
			hashers = append(hashers, blake3Hasher)
		default:
			// 对于不支持流式处理的哈希器，回退到原方法
			data, err := io.ReadAll(reader)
			if err != nil {
				return nil, err
			}
			return m.Hash(data)
		}
	}

	// 使用MultiWriter同时写入所有哈希器
	multiWriter := io.MultiWriter(writers...)

	// 流式读取并计算哈希
	_, err := io.CopyBuffer(multiWriter, reader, make([]byte, 1*1024*1024))
	if err != nil {
		return nil, fmt.Errorf("流式哈希计算失败: %w", err)
	}

	// 收集结果
	results := make([]HashResult, 0, len(m.hashers))
	for i, hasher := range m.hashers {
		var hashBytes []byte
		var algorithm string

		switch hashers[i].(type) {
		case hash.Hash: // SHA256
			sha256Hasher := hashers[i].(hash.Hash)
			hashBytes = sha256Hasher.Sum(nil)
			algorithm = hasher.Name()
		case *blake3.Hasher: // BLAKE3
			blake3Hasher := hashers[i].(*blake3.Hasher)
			hashBytes = blake3Hasher.Sum(nil)
			algorithm = hasher.Name()
		}

		results = append(results, HashResult{
			Algorithm: algorithm,
			Hash:      hex.EncodeToString(hashBytes),
		})
	}

	return results, nil
}

// Hash 使用所有哈希算法计算数据哈希值
func (m *MultiHasher) Hash(data []byte) ([]HashResult, error) {
	results := make([]HashResult, 0, len(m.hashers))

	for _, hasher := range m.hashers {
		hash, err := hasher.Hash(data)
		if err != nil {
			return nil, err
		}

		results = append(results, HashResult{
			Algorithm: hasher.Name(),
			Hash:      hash,
		})
	}

	return results, nil
}
