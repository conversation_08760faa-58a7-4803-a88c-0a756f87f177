package hash

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
)

// SHA256Hasher SHA-256哈希计算器
type SHA256Hasher struct{}

// NewSHA256Hasher 创建SHA-256哈希计算器
func NewSHA256Hasher() *SHA256Hasher {
	return &SHA256Hasher{}
}

// Name 返回哈希算法名称
func (h *SHA256Hasher) Name() string {
	return "SHA256"
}

// Hash 计算数据的SHA-256哈希值
func (h *SHA256Hasher) Hash(data []byte) (string, error) {
	hasher := sha256.New()
	_, err := hasher.Write(data)
	if err != nil {
		return "", fmt.Errorf("SHA256哈希计算失败: %w", err)
	}
	
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes), nil
}

// HashReader 计算Reader中数据的SHA-256哈希值
func (h *SHA256Hasher) HashReader(reader io.Reader) (string, error) {
	hasher := sha256.New()
	_, err := io.Copy(hasher, reader)
	if err != nil {
		return "", fmt.Errorf("SHA256哈希计算失败: %w", err)
	}
	
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes), nil
}

// HashFile 计算文件的SHA-256哈希值
func (h *SHA256Hasher) HashFile(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()
	
	return h.HashReader(file)
}
