package hash

import (
	"encoding/hex"
	"fmt"
	"io"
	"os"

	"github.com/zeebo/blake3"
)

// BLAKE3Hasher BLAKE3哈希计算器
type BLAKE3Hasher struct{}

// NewBLAKE3Hasher 创建BLAKE3哈希计算器
func NewBLAKE3Hasher() *BLAKE3Hasher {
	return &BLAKE3Hasher{}
}

// Name 返回哈希算法名称
func (h *BLAKE3Hasher) Name() string {
	return "BLAKE3"
}

// Hash 计算数据的BLAKE3哈希值
func (h *BLAKE3Hasher) Hash(data []byte) (string, error) {
	hasher := blake3.New()
	_, err := hasher.Write(data)
	if err != nil {
		return "", fmt.Errorf("BLAKE3哈希计算失败: %w", err)
	}
	
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes), nil
}

// HashReader 计算Reader中数据的BLAKE3哈希值
func (h *BLAKE3Hasher) HashReader(reader io.Reader) (string, error) {
	hasher := blake3.New()
	_, err := io.Copy(hasher, reader)
	if err != nil {
		return "", fmt.Errorf("BLAKE3哈希计算失败: %w", err)
	}
	
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes), nil
}

// HashFile 计算文件的BLAKE3哈希值
func (h *BLAKE3Hasher) HashFile(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()
	
	return h.HashReader(file)
}
