package config

import (
	"fmt"
	"os"
	"path/filepath"
	"pxpat-backend/pkg/types"
	"reflect"
	"strings"

	"github.com/spf13/viper"
)

type TypeName string

const (
	Service TypeName = "intra"
	Gateway TypeName = "gateway"
	Sync    TypeName = "sync"
)

type LoaderConfig struct {
	TypeName       TypeName
	ClusterName    string
	ServiceName    string
	GatewayName    string
	UseRedis       bool
	IsMultiGateway bool
}

func Load[T types.RestrictServiceConfig](loaderConfig LoaderConfig) *T {
	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		panic(fmt.Errorf("%s组下%s服务，获取当前运行路径失败", loaderConfig.ClusterName, loaderConfig.ServiceName))
	}
	// 基础路径 - 直接使用configs目录，不区分环境
	configBasePath := filepath.Join(cwd, "configs")
	globalConfigPath := filepath.Join(configBasePath, "global.yaml")

	var config T

	switch loaderConfig.TypeName {
	case Service:
		// 设置环境变量前缀
		viper.SetEnvPrefix(strings.ToUpper(loaderConfig.ServiceName))
		viper.AutomaticEnv()
		// 读取全局通用配置文件
		viper.SetConfigFile(globalConfigPath)
		if err := viper.ReadInConfig(); err != nil {
			panic(fmt.Errorf("%s组下%s服务读取global配置失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, loaderConfig.ServiceName, globalConfigPath, err))
		}
		// 读取私有配置文件 - 直接使用服务名目录
		configPath := filepath.Join(configBasePath, loaderConfig.ServiceName+"-service", "config.yaml")
		// 如果配置文件不存在就panic
		if _, err := os.Stat(configPath); err != nil {
			panic(fmt.Errorf("%s组下%s服务配置文件不存在！\n路径：%s", loaderConfig.ClusterName, loaderConfig.ServiceName, configPath))
		}
		viper.SetConfigFile(configPath)
		if err := viper.MergeInConfig(); err != nil {
			panic(fmt.Errorf("%s组下%s服务配置文件读取失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, loaderConfig.ServiceName, configPath, err))
		}

		// 反序列化
		if err := viper.Unmarshal(&config); err != nil {
			panic(fmt.Errorf("%s组下%s服务配置文件反序列化失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, loaderConfig.ServiceName, configPath, err))
		}
	case Gateway:
		viper.SetConfigFile(globalConfigPath)
		if err := viper.ReadInConfig(); err != nil {
			panic(fmt.Errorf("%s组下%s服务读取global配置失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, loaderConfig.ServiceName, globalConfigPath, err))
		}
		// 设置私有路径 - 直接使用configs目录
		if loaderConfig.IsMultiGateway {
			configBasePath = filepath.Join(configBasePath, "gateway")
		}
		var gateway string
		if loaderConfig.GatewayName != "" {
			gateway = loaderConfig.GatewayName
		} else {
			gateway = "gateway"
		}

		// Gateway 配置文件路径
		configPath := filepath.Join(configBasePath, gateway+".yaml")
		// 如果配置文件不存在就panic
		if _, err := os.Stat(configPath); err != nil {
			panic(fmt.Errorf("%s组下%s文件不存在！\n路径：%s", loaderConfig.ClusterName, gateway, configPath))
		}
		viper.SetConfigFile(configPath)
		// 读取配置
		if err := viper.MergeInConfig(); err != nil {
			panic(fmt.Errorf("%s组下gateway文件读取失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, configPath, err))
		}

		// 反序列化
		if err := viper.Unmarshal(&config); err != nil {
			panic(fmt.Errorf("%s组下gateway文件反序列化失败\n路径：%s\n原因：%s", loaderConfig.ClusterName, configPath, err))
		}
	case Sync:
		// 读取 sync.yaml 配置文件
		syncConfigPath := filepath.Join(configBasePath, "sync.yaml")
		viper.SetConfigFile(syncConfigPath)
		if err := viper.ReadInConfig(); err != nil {
			panic(fmt.Errorf("读取sync配置失败\n路径：%s\n原因：%s", syncConfigPath, err))
		}

		// 反序列化
		if err := viper.Unmarshal(&config); err != nil {
			panic(fmt.Errorf("sync配置文件反序列化失败\n路径：%s\n原因：%s", syncConfigPath, err))
		}
	}

	// 如果使用Redis，判断是否设置DB
	if loaderConfig.UseRedis {
		globalConfigStruct := reflect.ValueOf(config)
		redisField, _ := globalConfigStruct.Type().FieldByName("Redis")
		redisStruct := globalConfigStruct.FieldByIndex(redisField.Index)
		dbField := redisStruct.FieldByName("DB")
		if !dbField.IsValid() {
			panic(fmt.Errorf("%s组下%s服务，Redis配置文件中缺少DB字段", loaderConfig.ClusterName, loaderConfig.ServiceName))
		}
		if dbField.Kind() == reflect.Int && dbField.Int() == -1 {
			panic(fmt.Errorf("%s组下%s服务，请设置Redis.DB的值", loaderConfig.ClusterName, loaderConfig.ServiceName))
		}
	}

	return &config
}
