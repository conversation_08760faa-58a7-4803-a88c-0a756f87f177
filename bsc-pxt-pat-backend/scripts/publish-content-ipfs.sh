#!/bin/bash

# PXT-PAT IPFS内容上链脚本
# 使用方法: ./scripts/publish-content-ipfs.sh [内容类型] [标题] [描述] [发布者KSUID] [IPFS哈希]
# ./scripts/publish-content-ipfs.sh video "我的测试视频" "这是一个测试视频内容" user_001

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
TOKEN_SERVICE_URL="http://localhost:15004"
API_BASE_URL="${TOKEN_SERVICE_URL}/api/v1"


# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

print_result() {
    echo -e "${CYAN}[RESULT]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "🌐 PXT-PAT IPFS内容上链脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [内容类型] [标题] [描述] [发布者KSUID] [IPFS哈希(可选)]"
    echo ""
    echo "参数说明:"
    echo "  内容类型     - 内容类型 (video, audio, image, text 等)"
    echo "  标题         - 内容标题"
    echo "  描述         - 内容描述"
    echo "  发布者KSUID  - 发布者的KSUID"
    echo "  IPFS哈希     - 可选，如果不提供将自动创建并上传真实内容"
    echo ""
    echo "示例:"
    echo "  $0 video \"我的视频\" \"测试视频\" user_001"
    echo "  $0 audio \"音乐作品\" \"原创音乐\" user_002 QmYourIPFSHash..."
    echo ""
    echo "注意："
    echo "  - 如果不提供IPFS哈希，脚本会自动创建相应类型的内容并上传到IPFS"
    echo "  - 创建的内容将包含标题、描述和时间戳等信息"
    echo "  - 上传成功后会获得真实的IPFS哈希用于上链"
    echo ""
}

# 检查服务状态
check_service() {
    print_step "检查Token服务状态..."
    
    if ! curl -s "${TOKEN_SERVICE_URL}/health" > /dev/null; then
        print_error "Token服务未运行或无法访问: ${TOKEN_SERVICE_URL}"
        print_info "请先启动Token服务: go run cmd/token-service/main.go"
        exit 1
    fi
    
    print_success "Token服务运行正常"
}

# 创建并上传真实内容到IPFS
create_and_upload_content() {
    local content_type=$1
    local title=$2
    local description=$3
    local timestamp=$(date +%s)
    
    print_step "创建真实内容并上传到IPFS..." >&2
    
    # 创建临时文件
    local temp_file="/tmp/real_content_${content_type}_${timestamp}"
    
    # 根据内容类型创建不同的内容
    case $content_type in
        video|short_video|live_stream)
            # 创建一个简单的文本文件模拟视频内容
            cat > "$temp_file.txt" << EOF
视频内容: $title
描述: $description
类型: $content_type
创建时间: $(date)
内容ID: content_${timestamp}
这是一个模拟的视频内容文件，用于测试IPFS上传功能。
实际应用中，这里应该是真实的视频文件。
EOF
            temp_file="${temp_file}.txt"
            ;;
        audio|podcast|music)
            # 创建一个简单的文本文件模拟音频内容
            cat > "$temp_file.txt" << EOF
音频内容: $title
描述: $description
类型: $content_type
创建时间: $(date)
内容ID: content_${timestamp}
这是一个模拟的音频内容文件，用于测试IPFS上传功能。
实际应用中，这里应该是真实的音频文件。
EOF
            temp_file="${temp_file}.txt"
            ;;
        image|photo|artwork)
            # 创建一个简单的文本文件模拟图片内容
            cat > "$temp_file.txt" << EOF
图片内容: $title
描述: $description
类型: $content_type
创建时间: $(date)
内容ID: content_${timestamp}
这是一个模拟的图片内容文件，用于测试IPFS上传功能。
实际应用中，这里应该是真实的图片文件。
EOF
            temp_file="${temp_file}.txt"
            ;;
        text|article|ebook)
            # 创建一个文本内容
            cat > "$temp_file.txt" << EOF
文本内容: $title
描述: $description
类型: $content_type
创建时间: $(date)
内容ID: content_${timestamp}

这是一个真实的文本内容，用于测试IPFS上传功能。

内容详情：
- 标题: $title
- 描述: $description
- 类型: $content_type
- 时间戳: $timestamp

这里可以包含更多的文本内容，例如文章正文、电子书内容等。
在实际应用中，这里应该是用户提交的真实文本内容。
EOF
            temp_file="${temp_file}.txt"
            ;;
        *)
            # 默认创建通用内容
            cat > "$temp_file.txt" << EOF
通用内容: $title
描述: $description
类型: $content_type
创建时间: $(date)
内容ID: content_${timestamp}
这是一个通用的内容文件，用于测试IPFS上传功能。
EOF
            temp_file="${temp_file}.txt"
            ;;
    esac
    
    print_info "创建的内容文件: $temp_file" >&2
    print_info "文件大小: $(wc -c < "$temp_file") 字节" >&2
    
    # 上传到IPFS
    print_step "上传内容到IPFS..." >&2
    local upload_response=$(curl -s -X POST "${TOKEN_SERVICE_URL}/api/v1/content/ipfs/upload" \
        -F "file=@${temp_file}" \
        -F "metadata={\"description\":\"${description}\",\"type\":\"${content_type}\"}")
    
    print_info "IPFS上传响应:" >&2
    (echo "$upload_response" | jq . 2>/dev/null || echo "$upload_response") >&2
    
    # 检查上传是否成功
    if echo "$upload_response" | jq -e '.code == 200' > /dev/null 2>&1; then
        local ipfs_hash=$(echo "$upload_response" | jq -r '.data.data.ipfs_hash // .data.ipfs_hash // .data.hash // "未知"')
        print_success "✅ 内容上传到IPFS成功！" >&2
        print_info "IPFS哈希: $ipfs_hash" >&2
        
        # 清理临时文件
        rm -f "$temp_file"
        
        # 只返回IPFS哈希（输出到stdout）
        echo "$ipfs_hash"
    else
        print_error "❌ 内容上传到IPFS失败" >&2
        rm -f "$temp_file"
        return 1
    fi
}

# 验证IPFS哈希格式
validate_ipfs_hash() {
    local hash=$1
    
    # 基本格式检查 (Qm开头，长度46字符)
    if [[ $hash =~ ^Qm[1-9A-HJ-NP-Za-km-z]{44}$ ]]; then
        return 0
    fi
    
    # 检查新格式 (baf开头)
    if [[ $hash =~ ^baf[a-z0-9]{56}$ ]]; then
        return 0
    fi
    
    return 1
}

# 生成唯一的上链ID
generate_on_chain_id() {
    local content_type=$1
    echo "${content_type}_ipfs_$(date +%s)_$(openssl rand -hex 4)"
}

# 发布IPFS内容到区块链
publish_ipfs_content() {
    local content_type=$1
    local title=$2
    local description=$3
    local publisher_ksuid=$4
    local ipfs_hash=$5
    
    print_step "准备发布IPFS内容到区块链..."
    
    # 如果没有提供IPFS哈希，创建并上传真实内容
    if [ -z "$ipfs_hash" ]; then
        print_info "没有提供IPFS哈希，将创建并上传真实内容..."
        ipfs_hash=$(create_and_upload_content "$content_type" "$title" "$description")
        
        # 检查上传是否成功
        if [ $? -ne 0 ] || [ -z "$ipfs_hash" ] || [ "$ipfs_hash" = "未知" ]; then
            print_error "❌ 无法获取IPFS哈希，上传失败"
            return 1
        fi
        
        print_success "✅ 使用新创建的IPFS哈希: $ipfs_hash"
    else
        print_info "使用提供的IPFS哈希: $ipfs_hash"
    fi
    
    # 验证IPFS哈希
    if ! validate_ipfs_hash "$ipfs_hash"; then
        print_warning "IPFS哈希格式可能不正确: $ipfs_hash"
        print_info "继续执行，让服务端验证..."
    fi
    
    # 生成唯一ID
    local on_chain_id=$(generate_on_chain_id "$content_type")
    local content_id="ipfs_content_${on_chain_id}"
    
    print_info "内容ID: $content_id"
    print_info "上链ID: $on_chain_id"
    print_info "内容类型: $content_type"
    print_info "标题: $title"
    print_info "发布者: $publisher_ksuid"
    print_info "IPFS哈希: $ipfs_hash"
    
    # 发布内容到区块链并消耗代币
    print_step "发布内容到区块链并消耗代币..."
    
    # 使用 /content/publish-ipfs 端点（带代币转账功能）
    local publish_response=$(curl -s -X POST "${TOKEN_SERVICE_URL}/api/v1/content/publish-ipfs" \
        -H "Content-Type: application/json" \
        -d "{
            \"id\": \"${on_chain_id}\",
            \"title\": \"${title}\",
            \"description\": \"${description}\",
            \"content_type\": \"${content_type}\",
            \"publisher_ksuid\": \"${publisher_ksuid}\",
            \"on_chain_id\": \"${on_chain_id}\",
            \"reviewers\": [\"system\"],
            \"ipfs_hash\": \"${ipfs_hash}\",
            \"content_data\": null
        }")
    
    print_info "发布响应:"
    (echo "$publish_response" | jq . 2>/dev/null || echo "$publish_response") >&2
    
    # 检查响应
    if echo "$publish_response" | grep -q '"code":200\|"message":"success"\|"status":"success"'; then
        print_success "✅ 内容发布成功！带代币转账功能已激活"
        print_info "响应详情: $publish_response"
        
        # 提取关键信息
        local record_id=$(echo "$publish_response" | jq -r '.data.record.id // .data.id // .id // empty' 2>/dev/null)
        local tx_hash=$(echo "$publish_response" | jq -r '.data.record.transaction_hash // .data.transaction_hash // .transaction_hash // empty' 2>/dev/null)
        local ipfs_url=$(echo "$publish_response" | jq -r '.data.record.ipfs_hash // .data.ipfs_hash // empty' 2>/dev/null)
        local metadata_ipfs=$(echo "$publish_response" | jq -r '.data.record.metadata_ipfs // .data.metadata_ipfs // empty' 2>/dev/null)
        
        echo ""
        print_result "🔗 关键信息:"
        [ -n "$record_id" ] && print_info "记录ID: $record_id"
        [ -n "$tx_hash" ] && print_info "交易哈希: $tx_hash"
        [ -n "$ipfs_url" ] && print_info "内容IPFS: $ipfs_url"
        [ -n "$metadata_ipfs" ] && print_info "元数据IPFS: $metadata_ipfs"
        print_info "上链ID: $on_chain_id"
        
        echo ""
        print_result "🌐 IPFS访问链接:"
        print_info "内容链接: https://gateway.pinata.cloud/ipfs/${ipfs_hash}"
        [ -n "$metadata_ipfs" ] && print_info "元数据链接: https://gateway.pinata.cloud/ipfs/${metadata_ipfs}"
        
        echo ""
        print_result "⛓️  区块链交易链接:"
        if [ -n "$tx_hash" ]; then
            print_info "BSC测试网交易: https://testnet.bscscan.com/tx/${tx_hash}"
            print_info "交易哈希: $tx_hash"
            print_warning "💰 此交易包含PAT代币转账记录（发布费用）"
        else
            print_warning "交易哈希为空"
        fi
        
        # 保存信息到文件
        echo "$on_chain_id" > /tmp/last_ipfs_content_id.txt
        echo "$publish_response" > "/tmp/ipfs_content_publish_${on_chain_id}.json"
        
        echo ""
        print_success "✅ 信息已保存到临时文件"
        print_info "上链ID文件: /tmp/last_ipfs_content_id.txt"
        print_info "完整响应: /tmp/ipfs_content_publish_${on_chain_id}.json"
        
        return 0
    else
        print_error "❌ 内容发布失败"
        print_info "响应详情: $publish_response"
        return 1
    fi
}

# 查询IPFS内容信息
query_ipfs_content() {
    local on_chain_id=$1
    
    print_step "查询IPFS内容信息: $on_chain_id"
    
    local response=$(curl -s "${API_BASE_URL}/content/${on_chain_id}/ipfs")
    
    if echo "$response" | grep -q "error"; then
        print_warning "IPFS内容查询失败或不存在"
        echo "$response"
        return 1
    fi
    
    print_success "IPFS内容查询成功"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# 主函数
main() {
    echo "🌐 PXT-PAT IPFS内容上链脚本"
    echo "============================="
    echo ""
    
    # 检查参数
    if [ $# -lt 4 ]; then
        show_help
        exit 1
    fi
    
    local content_type=$1
    local title=$2
    local description=$3
    local publisher_ksuid=$4
    local ipfs_hash=$5
    
    # 验证必需参数
    if [ -z "$content_type" ] || [ -z "$title" ] || [ -z "$description" ] || [ -z "$publisher_ksuid" ]; then
        print_error "前4个参数都是必需的"
        show_help
        exit 1
    fi
    
    # 执行流程
    check_service
    echo ""
    
    publish_ipfs_content "$content_type" "$title" "$description" "$publisher_ksuid" "$ipfs_hash"
    echo ""
    
    # 查询刚发布的内容
    if [ -f "/tmp/last_ipfs_content_id.txt" ]; then
        local last_id=$(cat /tmp/last_ipfs_content_id.txt)
        print_step "验证IPFS发布结果..."
        query_ipfs_content "$last_id"
    fi
    
    echo ""
    print_success "🎉 IPFS内容上链流程完成！"
    echo ""
    print_info "📚 相关文档:"
    print_info "- Swagger API: ${TOKEN_SERVICE_URL}/swagger/index.html"
    print_info "- IPFS内容API: ${API_BASE_URL}/content/publish-ipfs"
    echo ""
}

# 执行主函数
main "$@"
