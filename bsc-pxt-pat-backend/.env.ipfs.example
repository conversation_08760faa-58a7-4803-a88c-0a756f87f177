# IPFS配置示例文件
# 复制此文件为 .env.ipfs 并填入真实的API密钥

# ===== Pinata IPFS 服务配置 =====
# 从 https://pinata.cloud/ 获取以下密钥

# Pinata API密钥 (从API Keys页面获取)
PINATA_API_KEY=your_pinata_api_key_here
PINATA_SECRET_API_KEY=your_pinata_secret_key_here

# Pinata JWT (可选，新版API推荐使用)
PINATA_JWT=your_pinata_jwt_here

# IPFS网关配置
IPFS_GATEWAY=https://gateway.pinata.cloud/ipfs/
IPFS_UPLOAD_ENDPOINT=https://api.pinata.cloud/pinning/pinFileToIPFS
IPFS_JSON_ENDPOINT=https://api.pinata.cloud/pinning/pinJSONToIPFS

# ===== 其他IPFS服务配置 (可选) =====

# Web3.Storage (免费替代方案)
# WEB3_STORAGE_TOKEN=your_web3_storage_token

# Infura IPFS
# INFURA_PROJECT_ID=your_infura_project_id
# INFURA_PROJECT_SECRET=your_infura_project_secret

# ===== 使用说明 =====
# 1. 访问 https://pinata.cloud/ 注册账户
# 2. 进入 API Keys 页面创建新密钥
# 3. 设置权限：pinFileToIPFS, pinJSONToIPFS
# 4. 复制 API Key 和 Secret 到上面的配置中
# 5. 将此文件重命名为 .env.ipfs
# 6. 在主程序中加载这些环境变量
