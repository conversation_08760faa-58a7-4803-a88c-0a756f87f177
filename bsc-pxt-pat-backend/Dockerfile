# 多阶段构建 Dockerfile
# 第一阶段：构建阶段
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用程序
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o token-service ./cmd/token-service
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o wallet-service ./cmd/wallet-service

# 第二阶段：运行阶段
FROM alpine:latest

# 安装必要的包
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 appgroup && \
    adduser -D -u 1001 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/token-service .
COPY --from=builder /app/wallet-service .

# 复制配置文件
COPY --chown=appuser:appgroup configs/ ./configs/

# 创建日志目录
RUN mkdir -p logs/service logs/postgres && \
    chown -R appuser:appgroup logs

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 15002 15004

# 默认启动 token-service
CMD ["./token-service"]
