package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/token-service/blockchain"
	"pxpat-backend/internal/token-service/cache"
	"pxpat-backend/internal/token-service/config"
	"pxpat-backend/internal/token-service/database"
	"pxpat-backend/internal/token-service/handler"
	"pxpat-backend/internal/token-service/repository"
	"pxpat-backend/internal/token-service/routes"
	"pxpat-backend/internal/token-service/service"
	"pxpat-backend/internal/token-service/types"
	"pxpat-backend/pkg/auth"
	pkgcache "pxpat-backend/pkg/cache"
	configLoader "pxpat-backend/pkg/config"
	database2 "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/ethereum/go-ethereum/common"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// convertBlockchainConfig 转换区块链配置
func convertBlockchainConfig(typesConfig *types.BlockchainConfig, syncConfig *config.SyncConfig) *config.BlockchainConfig {
	return &config.BlockchainConfig{
		Network:             typesConfig.Network,
		RPCURL:              typesConfig.RPCURL,
		BackupRPCURLs:       typesConfig.BackupRPCURLs,
		ChainID:             typesConfig.ChainID,
		StartBlock:          typesConfig.StartBlock,
		ConfirmBlocks:       typesConfig.ConfirmBlocks,
		BatchSize:           typesConfig.BatchSize,
		BSCScanAPIKey:       typesConfig.BSCScanAPIKey,       // 正确传递BSCScan API Key
		UseBSCScanForEvents: typesConfig.UseBSCScanForEvents, // 正确传递BSCScan开关
		Sync:                *syncConfig,                     // 传递同步配置
		Contracts: config.ContractsConfig{
			PXTToken:        typesConfig.Contracts.PXTToken,
			PATToken:        typesConfig.Contracts.PATToken,
			StakingPool:     typesConfig.Contracts.StakingPool,
			DAO:             typesConfig.Contracts.DAO,
			ContentRegistry: typesConfig.Contracts.ContentRegistry,
		},
	}
}

// convertTransferConfig 转换转账配置
func convertTransferConfig(typesConfig *types.TransferConfig) config.TransferConfig {
	return config.TransferConfig{
		SystemPrivateKey: typesConfig.SystemPrivateKey,
		SystemAddress:    "",          // 需要从配置中获取
		GasLimit:         100000,      // 设置默认值
		GasPrice:         20000000000, // 设置默认值
	}
}

// convertSyncConfig 转换同步配置
func convertSyncConfig(typesConfig *types.SyncConfig) *config.SyncConfig {
	return &config.SyncConfig{
		Enabled:                     typesConfig.Enabled,
		SyncInterval:                typesConfig.SyncInterval,
		MaxRetries:                  typesConfig.MaxRetries,
		RetryDelay:                  typesConfig.RetryDelay,
		ConcurrentWorkers:           typesConfig.ConcurrentWorkers,
		EnableDailyFullSync:         typesConfig.EnableDailyFullSync,
		DailyFullSyncHour:           typesConfig.DailyFullSyncHour,
		DailyFullSyncMinute:         typesConfig.DailyFullSyncMinute,
		TransferSyncIntervalSeconds: typesConfig.TransferSyncIntervalSeconds,
		UserSyncIntervalSeconds:     typesConfig.UserSyncIntervalSeconds,
		TransferBatchSize:           typesConfig.TransferBatchSize,
		ContentBatchSize:            typesConfig.ContentBatchSize,
		BurnBatchSize:               typesConfig.BurnBatchSize,
		MaxSyncDurationSeconds:      typesConfig.MaxSyncDurationSeconds,
		EnableMetrics:               typesConfig.EnableMetrics,
		MetricsRetentionDays:        typesConfig.MetricsRetentionDays,
		LogRetentionDays:            typesConfig.LogRetentionDays,
	}
}

func main() {
	// 解析命令行参数
	var (
		migrate = flag.Bool("migrate", false, "Run database migration")
		action  = flag.String("action", "up", "Migration action: up, down, reset")
		force   = flag.Bool("force", false, "Force migration (for reset)")
	)
	flag.Parse()

	clusterName := "finance"
	serviceName := "token"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Token service starting...")

	// 如果是迁移模式，执行迁移后退出
	if *migrate {
		runMigration(cfg, *action, *force)
		return
	}

	// 初始化数据库
	db, err := database2.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 执行数据库迁移
	log.Info().Msg("Running database migrations...")
	if err := database.AutoMigrate(db); err != nil {
		log.Fatal().Err(err).Msg("Failed to run database migrations")
	}
	log.Info().Msg("Database migration completed")

	// 创建视图和函数
	log.Info().Msg("Creating database views and functions...")
	if err := database.CreateViews(db); err != nil {
		log.Warn().Err(err).Msg("Failed to create views (continuing anyway)")
	}
	if err := database.CreateFunctions(db); err != nil {
		log.Warn().Err(err).Msg("Failed to create functions (continuing anyway)")
	}

	// 初始化Redis缓存
	rdb, err := database2.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis")
	}
	log.Info().Msg("Redis connected successfully")

	_, err = pkgcache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
	}
	log.Info().Msg("Cache manager initialized")

	// 初始化token-service专用缓存管理器
	redisConfig := cache.RedisConfig{
		Host:                cfg.Redis.Host,
		Port:                cfg.Redis.Port,
		Username:            cfg.Redis.Username,
		Password:            cfg.Redis.Password,
		DB:                  cfg.Redis.DB,
		PoolSize:            cfg.Redis.PoolSize,
		MinIdleConns:        cfg.Redis.MinIdleConns,
		DialTimeoutSeconds:  cfg.Redis.DialTimeout.String(),
		ReadTimeoutSeconds:  cfg.Redis.ReadTimeout.String(),
		WriteTimeoutSeconds: cfg.Redis.WriteTimeout.String(),
		PoolTimeoutSeconds:  cfg.Redis.PoolTimeout.String(),
	}
	redisClient, err := cache.NewRedisClient(redisConfig, log.Logger)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to connect to Redis, continuing without cache")
		redisClient = nil
	}

	// 初始化缓存管理器
	var cacheManager *cache.CacheManager
	if redisClient != nil {
		cacheConfig := cache.CacheConfig{
			LeaderboardTTL: 5 * time.Second,  // 5秒缓存，极致实时性
			UserStatsTTL:   15 * time.Second, // 15秒缓存
			OverviewTTL:    3 * time.Second,  // 3秒缓存，极致实时性
			DefaultTTL:     30 * time.Second,
		}
		cacheManager = cache.NewCacheManager(redisClient, cacheConfig, log.Logger)
		log.Info().Msg("Redis cache initialized successfully")
	} else {
		log.Info().Msg("Running without Redis cache")
	}

	// 转换配置
	syncConfig := convertSyncConfig(&cfg.Sync)
	blockchainConfig := convertBlockchainConfig(&cfg.Blockchain, syncConfig)
	transferConfig := convertTransferConfig(&cfg.Transfer)

	// 初始化区块链客户端
	multiClient, err := blockchain.NewMultiContractClient(blockchainConfig, log.Logger)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create multi-contract client")
	}
	defer multiClient.Close()

	// 初始化Repository层
	stakingRepo := repository.NewStakingRepository(db)
	eventRepo := repository.NewEventRepository(db)
	tokenRepo := repository.NewTokenRepository(db)
	governanceRepo := repository.NewGovernanceRepository(db)
	contentRepo := repository.NewContentRepository(db)

	// 初始化转账服务
	transferService, err := service.NewTransferService(
		cfg.Blockchain.RPCURL,
		transferConfig,
		log.Logger,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize transfer service")
	}

	// 解析合约地址
	patTokenAddr := common.HexToAddress(cfg.Blockchain.Contracts.PATToken)
	_ = common.HexToAddress(cfg.Blockchain.Contracts.PXTToken) // pxtTokenAddr暂时不使用
	contentRegistryAddr := common.HexToAddress(cfg.Blockchain.Contracts.ContentRegistry)

	// 初始化IPFS服务 - 使用JWT token
	ipfsService := service.NewIPFSService(
		"https://api.pinata.cloud", // Pinata API URL
		"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qge86CAThtvlaOc5KoWfZ2uLIsF0tpbgBqTXTF-KE0k", // JWT Token
		"",                             // API Secret (不需要)
		"https://gateway.pinata.cloud", // Gateway URL
		log.Logger,
	)

	// 初始化Service层
	stakingService := service.NewStakingService(stakingRepo, eventRepo, cacheManager, log.Logger)
	leaderboardService := service.NewLeaderboardService(stakingRepo, cacheManager, log.Logger)
	tokenService := service.NewTokenService(tokenRepo, multiClient, cacheManager, log.Logger)
	governanceService := service.NewGovernanceService(governanceRepo, stakingRepo, multiClient, cacheManager, log.Logger)
	transactionService := service.NewTransactionService(multiClient, cacheManager, log.Logger)

	// 创建ContentRegistry服务
	contentRegistryService, err := service.CreateContentRegistryServiceFromConfig(
		multiClient.GetClient(),
		contentRegistryAddr.Hex(),
		patTokenAddr.Hex(),
		cfg.Transfer.SystemAddress,
		cfg.Transfer.SystemPrivateKey,
		cfg.Transfer.GasLimit,
		cfg.Transfer.GasPrice,
		log.Logger,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create ContentRegistry service")
	}
	log.Info().Str("address", contentRegistryAddr.Hex()).Msg("✅ ContentRegistry service created")

	// 创建ContentService (用于传统路由系统)
	contentService := service.NewContentService(
		contentRepo,
		multiClient,
		transferService,
		ipfsService,
		contentRegistryService,
		patTokenAddr,
		common.HexToAddress("0x0000000000000000000000000000000000000000"), // PXT暂时使用零地址
		contentRegistryAddr,
		common.HexToAddress("0x0000000000000000000000000000000000000000"), // ContentMint暂时使用零地址
		log.Logger,
	)
	log.Info().Msg("✅ ContentService created")

	// 注意：ContentMint合约地址暂时使用零地址，后续可以配置
	// contentService已移除，现在使用模块化的ContentRoutes
	syncService := service.NewSyncService(multiClient, blockchainConfig, tokenRepo, governanceRepo, stakingRepo, cacheManager, log.Logger, nil)

	// 初始化高级功能服务
	analyticsService := service.NewAnalyticsService(tokenRepo, stakingRepo, governanceRepo, cacheManager, log.Logger)
	rankingService := service.NewRankingService(stakingRepo, tokenRepo, governanceRepo, cacheManager, log.Logger)
	websocketService := service.NewWebSocketService(log.Logger)
	notificationService := service.NewNotificationService(websocketService, log.Logger)
	_ = service.NewEventNotifier(notificationService, rankingService, log.Logger) // 暂时不使用

	// 初始化跨链桥服务
	bridgeService := service.NewBridgeService(db, log.Logger)
	log.Info().Msg("✅ BridgeService created")

	// 初始化区块链管理器
	blockchainManager := service.NewBlockchainManager(
		multiClient,
		blockchainConfig,
		stakingRepo,
		eventRepo,
		tokenRepo,
		governanceRepo,
		cacheManager,
		log.Logger,
		syncConfig, // 🌙 传入同步配置
	)

	// 初始化高级功能处理器
	analyticsHandler := handler.NewAnalyticsHandler(analyticsService, websocketService, log.Logger)
	// 注意：contentHandler已移除，现在使用模块化的ContentRoutes

	// 初始化HTTP处理器 (暂时不使用，由routes处理)
	_ = handler.NewHTTPHandler(
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		analyticsHandler,
		websocketService,
		syncService,
		log.Logger,
	)

	// 初始化JWT管理器 (暂时不使用)
	_ = auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式 - 开发时使用debug模式显示详细路由
	if os.Getenv("SHOW_ROUTES") == "true" || cfg.Server.Mode == "debug" {
		gin.SetMode(gin.DebugMode)
		log.Info().Msg("🔧 Debug mode enabled - detailed routes will be shown")
	} else {
		gin.SetMode(cfg.Server.Mode)
	}
	log.Info().Str("mode", gin.Mode()).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(
		router,
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		analyticsService,
		rankingService,
		websocketService,
		syncService,
		contentService,
		bridgeService,
		log.Logger,
	)

	// 内容路由现在在 routes.RegisterRoutes 中统一注册

	// 启动区块链管理器
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		log.Info().Msg("Starting blockchain manager...")
		if err := blockchainManager.Start(ctx); err != nil {
			log.Error().Err(err).Msg("Blockchain manager failed")
		}
	}()

	// 注意：同步服务已经在区块链管理器中启动，这里不需要重复启动
	// 如果需要额外的同步逻辑，可以在这里添加

	// 启动服务器
	log.Info().Int("port", cfg.Server.Port).Msg("Starting token service server")

	// 显示服务启动完成信息
	log.Info().Msg("🎉 Token service is ready!")
	log.Info().Str("url", fmt.Sprintf("http://localhost:%d", cfg.Server.Port)).Msg("🌐 Server listening on")
	log.Info().Str("health", fmt.Sprintf("http://localhost:%d/health", cfg.Server.Port)).Msg("🏥 Health check available at")
	log.Info().Str("api", fmt.Sprintf("http://localhost:%d/api/v1", cfg.Server.Port)).Msg("🚀 API endpoints available at")

	// 在单独的goroutine中启动服务器，以便可以处理关闭信号
	go func() {
		cmd.GraceStartAndClose(cfg.Server, router)
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info().Msg("Shutting down server...")

	// 停止区块链管理器
	if err := blockchainManager.Stop(); err != nil {
		log.Error().Err(err).Msg("Failed to stop blockchain manager")
	}

	log.Info().Msg("Server exited")
}

// runMigration 执行数据库迁移
func runMigration(cfg *types.Config, action string, force bool) {
	// 连接数据库
	db, err := database2.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// 执行迁移操作
	switch action {
	case "up":
		log.Info().Msg("Running migrations...")
		if err := database.AutoMigrate(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to run migrations")
		}

		log.Info().Msg("Creating views and functions...")
		if err := database.CreateViews(db); err != nil {
			log.Error().Err(err).Msg("Failed to create views")
		}
		if err := database.CreateFunctions(db); err != nil {
			log.Error().Err(err).Msg("Failed to create functions")
		}

		log.Info().Msg("Migrations completed successfully!")

	case "down":
		if !force {
			log.Fatal().Msg("Down migration requires --force flag")
		}

		log.Warn().Msg("Dropping all tables...")
		if err := database.DropTables(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to drop tables")
		}

		log.Info().Msg("Tables dropped successfully!")

	case "reset":
		if !force {
			log.Fatal().Msg("Reset requires --force flag")
		}

		log.Warn().Msg("Resetting database...")

		// 先删除所有表
		if err := database.DropTables(db); err != nil {
			log.Error().Err(err).Msg("Failed to drop tables")
		}

		// 重新创建
		if err := database.AutoMigrate(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to run migrations")
		}

		if err := database.CreateViews(db); err != nil {
			log.Error().Err(err).Msg("Failed to create views")
		}
		if err := database.CreateFunctions(db); err != nil {
			log.Error().Err(err).Msg("Failed to create functions")
		}

		log.Info().Msg("Database reset completed!")

	default:
		log.Fatal().Str("action", action).Msg("Unknown migration action. Available actions: up, down, reset")
	}
}
