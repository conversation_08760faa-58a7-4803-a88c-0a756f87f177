package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/wallet-service/handler"
	"pxpat-backend/internal/wallet-service/messaging"
	"pxpat-backend/internal/wallet-service/model"
	"pxpat-backend/internal/wallet-service/repository/impl"
	"pxpat-backend/internal/wallet-service/routes"
	"pxpat-backend/internal/wallet-service/service"
	"pxpat-backend/internal/wallet-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	databaseLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func main() {
	clusterName := "finance"
	serviceName := "wallet"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Wallet service starting...")

	// 初始化数据库
	db, err := databaseLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 自动迁移数据库表结构
	if err := db.AutoMigrate(
		&model.PointAccount{},
		&model.CryptoWallet{},
		&model.TokenBalance{},
		&model.AirdropRecord{},
	); err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
	}
	log.Info().Msg("Database migration completed")

	// 初始化仓储层
	pointAccountRepo := impl.NewPointAccountRepository(db)
	walletRepo := impl.NewCryptoWalletRepository(db)
	tokenBalanceRepo := impl.NewTokenBalanceRepository(db)
	airdropRepo := impl.NewAirdropRecordRepository(db)

	// 初始化服务层
	walletService := service.NewWalletService(pointAccountRepo, walletRepo, tokenBalanceRepo, airdropRepo)
	airdropService := service.NewAirdropService(walletRepo, airdropRepo)

	// 初始化处理器
	walletHandler := handler.NewWalletHandler(walletService)
	airdropHandler := handler.NewAirdropHandler(airdropService)

	// 初始化RabbitMQ消息处理
	var consumer *messaging.Consumer

	// 初始化消息消费器
	if cfg.RabbitMQ.URL != "" {
		consumer, err = messaging.NewConsumer(cfg.RabbitMQ.URL, walletService)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ consumer")
			log.Info().Msg("Continuing without MQ message consumption...")
		} else {
			log.Info().Msg("MQ consumer initialized successfully")
			defer consumer.Close()

			// 创建上下文用于优雅关闭
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			// 启动消息消费
			go func() {
				if err := consumer.StartConsuming(ctx); err != nil {
					log.Error().Err(err).Msg("Error starting MQ consumer")
				}
			}()

			// 监听系统信号以优雅关闭消费者
			go func() {
				sigChan := make(chan os.Signal, 1)
				signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
				<-sigChan
				log.Info().Msg("Received shutdown signal, stopping MQ consumer...")
				cancel()
			}()
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
	}

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(router, jwtManager, walletHandler, airdropHandler)
	log.Info().Msg("Routes registered")

	// 启动服务器
	log.Info().Int("port", cfg.Server.Port).Msg("Starting wallet service server")
	cmd.GraceStartAndClose(cfg.Server, router)
}
