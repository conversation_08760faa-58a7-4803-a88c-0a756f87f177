package cmd

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"pxpat-backend/pkg/types"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func GraceStartAndClose(serverConfig types.GlobalServerConfig, router *gin.Engine) {
	gin.SetMode(serverConfig.Mode)

	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", serverConfig.Port),
		Handler:      router,
		ReadTimeout:  serverConfig.ReadTimeoutSeconds,
		WriteTimeout: serverConfig.WriteTimeoutSeconds,
	}

	go func() {
		log.Printf("服务启动完毕 端口 %d", serverConfig.Port)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatal().Err(err).Msg("服务器启动失败")
		}
	}()

	// 启动心跳服务
	heartbeatTicker := time.NewTicker(30 * time.Second)
	go func() {
		for {
			select {
			case <-heartbeatTicker.C:
				// 发送心跳包
				log.Info().Msg("Sending heartbeat...")
				// TODO: 实际环境中，这里应该调用服务注册中心的API
				// 临时实现：记录日志和服务状态
				status := map[string]interface{}{
					"intra":     serverConfig.Name,
					"status":    "healthy",
					"timestamp": time.Now().Unix(),
				}
				log.Info().Interface("heartbeat", status).Msg("Heartbeat sent")
			}
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止心跳服务
	heartbeatTicker.Stop()
	log.Info().Msg("Heartbeat intra stopped")
	// 清理资源
	log.Info().Msg("Server exiting")

	// 优雅关闭服务
	log.Info().Msg("Shutting down server...")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Error().Msg(fmt.Sprintf("Server Shutdown error: %v", err))
		os.Exit(1)
	}

	os.Exit(0)
}

func GraceStartAndCloseNoApi(serviceName string) {
	// 启动心跳服务
	heartbeatTicker := time.NewTicker(30 * time.Second)
	go func() {
		for {
			select {
			case <-heartbeatTicker.C:
				// 发送心跳包
				log.Info().Msg("Sending heartbeat...")
				// TODO: 实际环境中，这里应该调用服务注册中心的API
				// 临时实现：记录日志和服务状态
				status := map[string]interface{}{
					"intra":     serviceName,
					"status":    "healthy",
					"timestamp": time.Now().Unix(),
				}
				log.Info().Interface("heartbeat", status).Msg("Heartbeat sent")
			}
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止心跳服务
	heartbeatTicker.Stop()
	log.Info().Msg("Heartbeat intra stopped")
	// 清理资源
	log.Info().Msg("Server exiting")
}
