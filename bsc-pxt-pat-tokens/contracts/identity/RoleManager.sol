// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "./ArtistIdentity.sol";
import "../interfaces/TokenErrors.sol";

contract RoleManager is AccessControl, Pausable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using SafeMath for uint256;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PLATFORM_ROLE = keccak256("PLATFORM_ROLE");
    bytes32 public constant SUPERVISOR_ROLE = keccak256("SUPERVISOR_ROLE");

    constructor() {
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(PLATFORM_ROLE, msg.sender);
        _setupRole(SUPERVISOR_ROLE, msg.sender);

        _setupDefaultUpgradeRequirements();
    }

    function initialize(
        address platformAdmin,
        address _patoken,
        address _pxtoken,
        address _artistIdentity
    ) external onlyRole(ADMIN_ROLE) {
        require(address(patoken) == address(0), "Already initialized");

        if (platformAdmin == address(0)) revert TokenErrors.ZeroAddress();
        if (_patoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_pxtoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_artistIdentity == address(0)) revert TokenErrors.ZeroAddress();

        _setupRole(PLATFORM_ROLE, platformAdmin);

        patoken = IERC20(_patoken);
        pxtoken = IERC20(_pxtoken);
        artistIdentity = ArtistIdentity(_artistIdentity);
    }

    enum RoleType {
        USER,
        CREATOR,
        CURATOR,
        VALIDATOR,
        MODERATOR,
        DEVELOPER,
        PARTNER,
        ADMINISTRATOR
    }

    enum RoleLevel {
        BASIC,
        INTERMEDIATE,
        ADVANCED,
        EXPERT,
        MASTER
    }

    uint256 private constant MAX_PAGE_SIZE = 100;

    struct RoleInfo {
        uint8 roleType;
        uint8 roleLevel;
        uint256 assignedTimestamp;
        uint256 lastLevelUpgrade;
        uint256 activeDuration;
        bool isActive;
        uint256 reputation;
    }

    struct UpgradeRequirement {
        uint256 minActiveDuration;
        uint256 minReputation;
        uint256 patFee;
        uint256 pxtStakingAmount;
    }

    struct StakingRecord {
        uint256 amount;
        uint256 timestamp;
        RoleLevel forLevel;
    }

    IERC20 public patoken;
    IERC20 public pxtoken;

    ArtistIdentity public artistIdentity;

    mapping(uint8 => mapping(uint8 => UpgradeRequirement)) public upgradeRequirements;
    mapping(address => RoleInfo) public userRoles;
    mapping(uint8 => address[]) public roleAddresses;
    mapping(address => uint256) public roleIndices;
    mapping(address => mapping(bytes32 => bool)) private userPermissions;
    mapping(address => StakingRecord) public stakingRecords;

    event RoleAssigned(address indexed user, RoleType roleType, RoleLevel roleLevel);
    event RoleUpgraded(address indexed user, RoleType roleType, RoleLevel oldLevel, RoleLevel newLevel);
    event RoleDeactivated(address indexed user, RoleType roleType);
    event RoleReactivated(address indexed user, RoleType roleType);
    event PermissionGranted(address indexed user, bytes32 permission);
    event PermissionRevoked(address indexed user, bytes32 permission);
    event UpgradeRequirementUpdated(RoleType roleType, RoleLevel roleLevel);
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);
    event TokensWithdrawn(address indexed token, address indexed to, uint256 amount);
    event PXTStaked(address indexed user, uint256 amount, RoleLevel forLevel);

    function _setupDefaultUpgradeRequirements() internal {
        upgradeRequirements[uint8(RoleType.CREATOR)][uint8(RoleLevel.INTERMEDIATE)] = UpgradeRequirement({
            minActiveDuration: 30 days,
            minReputation: 2000,
            patFee: 200 * 10**18,
            pxtStakingAmount: 50 * 10**18
        });

        upgradeRequirements[uint8(RoleType.CREATOR)][uint8(RoleLevel.ADVANCED)] = UpgradeRequirement({
            minActiveDuration: 90 days,
            minReputation: 5000,
            patFee: 500 * 10**18,
            pxtStakingAmount: 150 * 10**18
        });

        upgradeRequirements[uint8(RoleType.CREATOR)][uint8(RoleLevel.EXPERT)] = UpgradeRequirement({
            minActiveDuration: 180 days,
            minReputation: 7500,
            patFee: 1000 * 10**18,
            pxtStakingAmount: 300 * 10**18
        });

        upgradeRequirements[uint8(RoleType.CREATOR)][uint8(RoleLevel.MASTER)] = UpgradeRequirement({
            minActiveDuration: 365 days,
            minReputation: 9000,
            patFee: 2000 * 10**18,
            pxtStakingAmount: 500 * 10**18
        });

        upgradeRequirements[uint8(RoleType.CURATOR)][uint8(RoleLevel.INTERMEDIATE)] = UpgradeRequirement({
            minActiveDuration: 30 days,
            minReputation: 2500,
            patFee: 250 * 10**18,
            pxtStakingAmount: 60 * 10**18
        });

        upgradeRequirements[uint8(RoleType.VALIDATOR)][uint8(RoleLevel.INTERMEDIATE)] = UpgradeRequirement({
            minActiveDuration: 30 days,
            minReputation: 3000,
            patFee: 300 * 10**18,
            pxtStakingAmount: 100 * 10**18
        });

        upgradeRequirements[uint8(RoleType.MODERATOR)][uint8(RoleLevel.INTERMEDIATE)] = UpgradeRequirement({
            minActiveDuration: 30 days,
            minReputation: 4000,
            patFee: 400 * 10**18,
            pxtStakingAmount: 120 * 10**18
        });
    }

    function assignRole(
        address user,
        RoleType roleType,
        bytes32[] memory initialPermissions
    ) external onlyRole(PLATFORM_ROLE) whenNotPaused nonReentrant {
        if (user == address(0)) revert TokenErrors.ZeroAddress();
        if (userRoles[user].assignedTimestamp != 0) revert TokenErrors.RoleAlreadyAssigned();

        RoleInfo memory newRole = RoleInfo({
            roleType: uint8(roleType),
            roleLevel: uint8(RoleLevel.BASIC),
            assignedTimestamp: block.timestamp,
            lastLevelUpgrade: block.timestamp,
            activeDuration: 0,
            isActive: true,
            reputation: 1000
        });

        userRoles[user] = newRole;

        roleIndices[user] = roleAddresses[uint8(roleType)].length;
        roleAddresses[uint8(roleType)].push(user);

        emit RoleAssigned(user, roleType, RoleLevel.BASIC);

        for (uint256 i = 0; i < initialPermissions.length;) {
            _grantPermission(user, initialPermissions[i]);
            unchecked { i++; }
        }
    }

    function upgradeRoleLevel(RoleLevel newLevel) external whenNotPaused nonReentrant {
        RoleInfo storage role = userRoles[msg.sender];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();
        if (!role.isActive) revert TokenErrors.RoleNotActive();
        if (uint256(newLevel) != uint256(role.roleLevel) + 1) revert TokenErrors.CannotUpgradeTwice();

        RoleType roleType = RoleType(role.roleType);
        RoleLevel currentLevel = RoleLevel(role.roleLevel);
        UpgradeRequirement memory req = upgradeRequirements[role.roleType][uint8(newLevel)];

        if (role.activeDuration < req.minActiveDuration) revert TokenErrors.InsufficientActiveDuration();
        if (role.reputation < req.minReputation) revert TokenErrors.InsufficientReputationScore();

        role.roleLevel = uint8(newLevel);
        role.lastLevelUpgrade = block.timestamp;

        if (req.patFee > 0) {
            patoken.safeTransferFrom(msg.sender, address(this), req.patFee);
        }

        if (req.pxtStakingAmount > 0) {
            uint256 pxtBalance = pxtoken.balanceOf(msg.sender);
            if (pxtBalance < req.pxtStakingAmount) revert TokenErrors.InsufficientPXTStaking();

            pxtoken.safeTransferFrom(msg.sender, address(this), req.pxtStakingAmount);

            stakingRecords[msg.sender] = StakingRecord({
                amount: req.pxtStakingAmount,
                timestamp: block.timestamp,
                forLevel: newLevel
            });

            emit PXTStaked(msg.sender, req.pxtStakingAmount, newLevel);
        }

        emit RoleUpgraded(msg.sender, roleType, currentLevel, newLevel);
    }

    function deactivateRole(address user) external onlyRole(SUPERVISOR_ROLE) nonReentrant {
        RoleInfo storage role = userRoles[user];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();
        if (!role.isActive) revert TokenErrors.RoleNotActive();

        role.isActive = false;

        emit RoleDeactivated(user, RoleType(role.roleType));
    }

    function reactivateRole(address user) external onlyRole(SUPERVISOR_ROLE) nonReentrant {
        RoleInfo storage role = userRoles[user];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();
        if (role.isActive) revert TokenErrors.RoleAlreadyActive();

        role.isActive = true;

        emit RoleReactivated(user, RoleType(role.roleType));
    }

    function _grantPermission(
        address user,
        bytes32 permission
    ) internal {
        userPermissions[user][permission] = true;
        emit PermissionGranted(user, permission);
    }

    function grantPermission(
        address user,
        bytes32 permission
    ) external onlyRole(PLATFORM_ROLE) nonReentrant {
        if (userRoles[user].assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();

        if (userPermissions[user][permission]) return;

        _grantPermission(user, permission);
    }

    function revokePermission(
        address user,
        bytes32 permission
    ) external onlyRole(PLATFORM_ROLE) nonReentrant {
        if (userRoles[user].assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();

        if (!userPermissions[user][permission]) revert TokenErrors.PermissionNotFound();

        userPermissions[user][permission] = false;

        emit PermissionRevoked(user, permission);
    }

    function updateActiveDuration(
        address user,
        uint256 additionalTime
    ) external onlyRole(PLATFORM_ROLE) whenNotPaused nonReentrant {
        RoleInfo storage role = userRoles[user];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();
        if (!role.isActive) revert TokenErrors.RoleNotActive();

        role.activeDuration += additionalTime;
    }

    function updateReputation(
        address user,
        uint256 newReputation
    ) external onlyRole(PLATFORM_ROLE) whenNotPaused nonReentrant {
        RoleInfo storage role = userRoles[user];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();

        if (newReputation > 10000) {
            newReputation = 10000;
        }

        role.reputation = newReputation;
    }

    function hasPermission(address user, bytes32 permission) public view returns (bool) {
        RoleInfo storage role = userRoles[user];
        if (!role.isActive) return false;

        return userPermissions[user][permission];
    }

    function hasUserRole(
        address user,
        RoleType roleType
    ) external view returns (bool) {
        RoleInfo storage role = userRoles[user];
        return role.assignedTimestamp > 0 && role.roleType == uint8(roleType) && role.isActive;
    }

    function getRoleInfo(
        address user
    ) external view returns (
        RoleType,
        RoleLevel,
        uint256,
        uint256,
        uint256,
        bool,
        uint256
    ) {
        RoleInfo storage role = userRoles[user];
        if (role.assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();

        return (
            RoleType(role.roleType),
            RoleLevel(role.roleLevel),
            role.assignedTimestamp,
            role.lastLevelUpgrade,
            role.activeDuration,
            role.isActive,
            role.reputation
        );
    }

    function getRoleAddresses(
        RoleType roleType
    ) external view returns (address[] memory) {
        return roleAddresses[uint8(roleType)];
    }

    function getUpgradeRequirement(
        RoleType roleType,
        RoleLevel roleLevel
    ) external view returns (
        uint256,
        uint256,
        uint256,
        uint256
    ) {
        UpgradeRequirement memory req = upgradeRequirements[uint8(roleType)][uint8(roleLevel)];

        return (
            req.minActiveDuration,
            req.minReputation,
            req.patFee,
            req.pxtStakingAmount
        );
    }

    function updateUpgradeRequirement(
        RoleType roleType,
        RoleLevel roleLevel,
        UpgradeRequirement memory req
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        upgradeRequirements[uint8(roleType)][uint8(roleLevel)] = req;

        emit UpgradeRequirementUpdated(roleType, roleLevel);
    }

    function getStakingRecord(address user) external view returns (
        uint256 amount,
        uint256 timestamp,
        RoleLevel forLevel
    ) {
        StakingRecord memory record = stakingRecords[user];
        return (record.amount, record.timestamp, record.forLevel);
    }

    function withdrawTokens(
        address token,
        address to,
        uint256 amount
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (to == address(0)) revert TokenErrors.ZeroAddress();
        IERC20(token).safeTransfer(to, amount);
        emit TokensWithdrawn(token, to, amount);
    }

    function setArtistIdentity(
        address _artistIdentity
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (_artistIdentity == address(0)) revert TokenErrors.ZeroAddress();

        artistIdentity = ArtistIdentity(_artistIdentity);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    function getRoleAddressesPaged(RoleType roleType, uint256 offset, uint256 limit) external view returns (address[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = roleAddresses[uint8(roleType)].length;
        if (offset >= total) {
            return new address[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        address[] memory result = new address[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = roleAddresses[uint8(roleType)][i];
            unchecked { i++; }
        }
        return result;
    }

    function getUserPermissions(address user) external view returns (bytes32[] memory) {
        if (userRoles[user].assignedTimestamp == 0) revert TokenErrors.RoleNotAssigned();

        uint256 permissionCount = 0;
        bytes32[] memory allPermissions = _getAllPossiblePermissions();

        for (uint256 i = 0; i < allPermissions.length;) {
            if (userPermissions[user][allPermissions[i]]) {
                permissionCount++;
            }
            unchecked { i++; }
        }

        bytes32[] memory result = new bytes32[](permissionCount);
        uint256 index = 0;

        for (uint256 i = 0; i < allPermissions.length;) {
            if (userPermissions[user][allPermissions[i]]) {
                result[index] = allPermissions[i];
                unchecked { index++; }
            }
            unchecked { i++; }
        }

        return result;
    }

    function _getAllPossiblePermissions() internal pure returns (bytes32[] memory) {
        bytes32[] memory permissions = new bytes32[](10);
        permissions[0] = keccak256("CREATE_CONTENT");
        permissions[1] = keccak256("EDIT_CONTENT");
        permissions[2] = keccak256("DELETE_CONTENT");
        permissions[3] = keccak256("MODERATE_CONTENT");
        permissions[4] = keccak256("CREATE_PROPOSAL");
        permissions[5] = keccak256("VOTE_PROPOSAL");
        permissions[6] = keccak256("EXECUTE_PROPOSAL");
        permissions[7] = keccak256("MINT_NFT");
        permissions[8] = keccak256("TRANSFER_FUNDS");
        permissions[9] = keccak256("VIEW_ANALYTICS");
        return permissions;
    }

    function hasUserRole(bytes32 role, address account) public view returns (bool) {
        return super.hasRole(role, account);
    }
}