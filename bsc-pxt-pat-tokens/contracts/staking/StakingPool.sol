// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";

contract StakingPool is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;

    constructor() {
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(OPERATOR_ROLE, msg.sender);
        _setupRole(EMERGENCY_ROLE, msg.sender);

        stakingLevelThresholds[uint8(StakingLevel.DingJi)] = 100 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.ChengJi)] = 1000 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.YiJi)] = 5000 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.JiaJi)] = 20000 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.ShiJue)] = 100000 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.ShuangShiJue)] = 250000 * 10**18;
        stakingLevelThresholds[uint8(StakingLevel.ZhiZun)] = 500000 * 10**18;

        dingJiThreshold = stakingLevelThresholds[uint8(StakingLevel.DingJi)];
        chengJiThreshold = stakingLevelThresholds[uint8(StakingLevel.ChengJi)];
        yiJiThreshold = stakingLevelThresholds[uint8(StakingLevel.YiJi)];
        jiaJiThreshold = stakingLevelThresholds[uint8(StakingLevel.JiaJi)];
        shiJueThreshold = stakingLevelThresholds[uint8(StakingLevel.ShiJue)];
        shuangShiJueThreshold = stakingLevelThresholds[uint8(StakingLevel.ShuangShiJue)];
        zhiZunThreshold = stakingLevelThresholds[uint8(StakingLevel.ZhiZun)];

        minUnlockPeriod = 27 days;
        maxUnlockPeriod = 1095 days;

        emergencyWithdrawalFee = 1000;

        maxEarlyUnlockPenalty = 2000;

        maxUnlockRequestsPerUser = 5;

        minStakeAmount = 1 * 10**18;

        _setDefaultRandomRewardRanges();
    }

    function initialize(
        address _pxtoken,
        address _rewardToken,
        uint256 _baseAPR
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(address(pxtoken) == address(0), "Already initialized");
        if (_pxtoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_rewardToken == address(0)) revert TokenErrors.ZeroAddress();

        pxtoken = IPXT(_pxtoken);
        rewardToken = IERC20(_rewardToken);
        baseAPR = _baseAPR;

        _setDefaultDurationMultipliers();
    }

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    enum StakingLevel { DingJi, ChengJi, YiJi, JiaJi, ShiJue, ShuangShiJue, ZhiZun }

    enum StakingDuration {
        OneMonth,
        ThreeMonths,
        SixMonths,
        OneYear,
        TwoYears,
        ThreeYears
    }

    struct LevelRewardRange {
        uint256 minMultiplier;
        uint256 maxMultiplier;
        bool isActive;
    }

    struct StakeInfo {
        uint256 amount;
        uint256 startTime;
        uint256 endTime;
        uint256 lastRewardTime;
        StakingLevel level;
        bool isUnlocking;
        uint256 unlockTime;
        uint256 requestedDuration;
        uint256 randomSeed;
    }

    struct UnlockRequest {
        uint256 amount;
        uint256 requestTime;
        uint256 unlockTime;
        bool processed;
    }

    struct DurationMultiplier {
        uint256 duration;
        uint256 multiplier;
    }

    IPXT public pxtoken;
    IERC20 public rewardToken;

    uint256 public baseAPR;
    uint256 public minStakeAmount;

    uint256 public minUnlockPeriod = 27 days;
    uint256 public maxUnlockPeriod = 1095 days;

    mapping(uint8 => uint256) public stakingLevelThresholds;

    uint256 public dingJiThreshold;
    uint256 public chengJiThreshold;
    uint256 public yiJiThreshold;
    uint256 public jiaJiThreshold;
    uint256 public shiJueThreshold;
    uint256 public shuangShiJueThreshold;
    uint256 public zhiZunThreshold;

    uint256 public emergencyWithdrawalFee;
    uint256 public maxEarlyUnlockPenalty;

    uint256 public totalStaked;

    uint256 public maxUnlockRequestsPerUser = 5;

    uint256 public promotionEndTime;

    mapping(StakingLevel => LevelRewardRange) public levelRewardRanges;

    bool public randomRewardEnabled = true;

    DurationMultiplier[] public durationMultipliers;
    uint256 public durationMultiplierCount;

    mapping(address => StakeInfo) public stakes;
    mapping(address => UnlockRequest[]) public unlockRequests;
    mapping(address => uint256) public pendingRewards;
    mapping(address => uint256) public totalRewardsClaimed;

    mapping(address => uint256[]) public stakingHistory;
    mapping(address => uint256[]) public rewardHistory;

    uint256 public totalRewardsDistributed;
    uint256 public totalParticipants;
    uint256 public totalPenaltiesCollected;

    address public penaltyPool;

    mapping(address => uint256) public userMiningPower;
    uint256 public totalMiningPower;
    address public miningPoolContract;

    event MiningPowerUpdated(address indexed user, uint256 oldPower, uint256 newPower);
    event MiningPoolContractUpdated(address indexed oldContract, address indexed newContract);

    event Staked(address indexed user, uint256 amount, StakingLevel level, uint256 duration);
    event UnlockRequested(address indexed user, uint256 amount, uint256 unlockTime);
    event UnlockCancelled(address indexed user, uint256 requestId);
    event Withdrawn(address indexed user, uint256 amount);
    event RewardClaimed(address indexed user, uint256 amount);
    event StakingLevelUpdated(address indexed user, StakingLevel oldLevel, StakingLevel newLevel);
    event EmergencyWithdrawal(address indexed user, uint256 amount, uint256 penalty);
    event APRUpdated(uint256 oldRate, uint256 newRate);
    event DurationMultiplierAdded(uint256 duration, uint256 multiplier);
    event DurationMultiplierUpdated(uint256 index, uint256 duration, uint256 multiplier);
    event TokenRecovered(address indexed token, uint256 amount);
    event PromotionEndTimeSet(uint256 endTime);
    event MaxUnlockRequestsUpdated(uint256 oldValue, uint256 newValue);
    event PenaltyPoolUpdated(address indexed oldPool, address indexed newPool);
    event ContractPaused(address indexed pauser);
    event ContractUnpaused(address indexed unpauser);
    event MinStakeAmountUpdated(uint256 oldValue, uint256 newValue);
    event RandomRewardRangeUpdated(StakingLevel indexed level, uint256 minMultiplier, uint256 maxMultiplier, bool isActive);
    event RandomRewardSystemToggled(bool enabled);
    event RandomRewardGenerated(address indexed user, StakingLevel level, uint256 multiplier, uint256 randomSeed);

    // 🔒 安全优化：添加安全相关事件
    event SecurityParameterChanged(string indexed parameter, uint256 oldValue, uint256 newValue, address indexed changer);
    event SuspiciousActivityDetected(address indexed user, string reason, uint256 timestamp);

    function _setDefaultDurationMultipliers() internal {
        durationMultipliers = new DurationMultiplier[](6);
        durationMultiplierCount = 0;

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 30 days,
            multiplier: 10500
        });

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 90 days,
            multiplier: 11000
        });

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 180 days,
            multiplier: 12000
        });

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 365 days,
            multiplier: 13500
        });

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 730 days,
            multiplier: 17000
        });

        durationMultipliers[durationMultiplierCount++] = DurationMultiplier({
            duration: 1095 days,
            multiplier: 20000
        });
    }
    

    
    function setPromotionEnd(uint256 _endTime) external {
        if (promotionEndTime != 0) revert TokenErrors.AlreadyInitialized();
        if (_endTime <= block.timestamp) revert TokenErrors.InvalidDates();

        promotionEndTime = _endTime;
        emit PromotionEndTimeSet(_endTime);
    }

    function stake(uint256 _amount) external nonReentrant whenNotPaused {
        if (_amount < minStakeAmount) revert TokenErrors.AmountTooLow(minStakeAmount, _amount);

        if (promotionEndTime != 0 && block.timestamp > promotionEndTime) {
            revert TokenErrors.MiningEnded();
        }

        StakeInfo storage userStake = stakes[msg.sender];

        if (userStake.amount > 0) {
            uint256 reward = calculateReward(msg.sender);
            pendingRewards[msg.sender] = pendingRewards[msg.sender] + reward;
            userStake.lastRewardTime = block.timestamp;
        } else {
            userStake.startTime = block.timestamp;
            userStake.lastRewardTime = block.timestamp;
            totalParticipants = totalParticipants + 1;

            // 🔒 安全优化：使用更安全的随机数生成
            userStake.randomSeed = uint256(keccak256(abi.encodePacked(
                block.timestamp,
                blockhash(block.number - 1),  // 使用前一个区块哈希
                msg.sender,
                totalParticipants,
                address(this)  // 添加合约地址增加熵
            )));
        }

        pxtoken.transferFrom(msg.sender, address(this), _amount);

        userStake.amount = userStake.amount + _amount;
        totalStaked = totalStaked + _amount;

        StakingLevel oldLevel = userStake.level;
        userStake.level = determineStakingLevel(userStake.amount);

        stakingHistory[msg.sender].push(block.timestamp);

        emit Staked(msg.sender, _amount, userStake.level, userStake.requestedDuration);

        if (oldLevel != userStake.level) {
            emit StakingLevelUpdated(msg.sender, oldLevel, userStake.level);
        }

        updateUserMiningPower(msg.sender);
    }
    

    function requestUnlock(uint256 _amount, uint256 _unlockPeriod) external nonReentrant whenNotPaused {
        StakeInfo storage userStake = stakes[msg.sender];
        if (userStake.amount < _amount) revert TokenErrors.InsufficientStakedAmount(userStake.amount, _amount);
        if (!(_unlockPeriod >= minUnlockPeriod && _unlockPeriod <= maxUnlockPeriod)) revert TokenErrors.InvalidUnlockPeriod(minUnlockPeriod, maxUnlockPeriod, _unlockPeriod);
        if (userStake.isUnlocking) revert TokenErrors.UnlockRequestActive();

        uint256 activeRequests = 0;
        for (uint i = 0; i < unlockRequests[msg.sender].length; i++) {
            if (!unlockRequests[msg.sender][i].processed) {
                activeRequests++;
            }
        }
        if (activeRequests >= maxUnlockRequestsPerUser) {
            revert TokenErrors.TooManyUnlockRequests(maxUnlockRequestsPerUser);
        }

        uint256 reward = calculateReward(msg.sender);
        pendingRewards[msg.sender] = pendingRewards[msg.sender] + reward;

        userStake.isUnlocking = true;
        userStake.unlockTime = block.timestamp + _unlockPeriod;
        userStake.lastRewardTime = block.timestamp;
        userStake.requestedDuration = _unlockPeriod;
        UnlockRequest memory request = UnlockRequest({
            amount: _amount,
            requestTime: block.timestamp,
            unlockTime: block.timestamp + _unlockPeriod,
            processed: false
        });

        unlockRequests[msg.sender].push(request);

        emit UnlockRequested(msg.sender, _amount, request.unlockTime);
    }


    function cancelUnlock(uint256 _requestId) external nonReentrant {
        if (_requestId >= unlockRequests[msg.sender].length) revert TokenErrors.InvalidRequestId(_requestId);

        UnlockRequest storage request = unlockRequests[msg.sender][_requestId];
        if (request.processed) revert TokenErrors.RequestAlreadyProcessed(_requestId);

        request.processed = true;


        stakes[msg.sender].isUnlocking = false;

        emit UnlockCancelled(msg.sender, _requestId);
    }
    

    function withdraw(uint256 _requestId) external nonReentrant {
        if (_requestId >= unlockRequests[msg.sender].length) revert TokenErrors.InvalidRequestId(_requestId);

        UnlockRequest storage request = unlockRequests[msg.sender][_requestId];
        if (request.processed) revert TokenErrors.RequestAlreadyProcessed(_requestId);

        StakeInfo storage userStake = stakes[msg.sender];
        if (!userStake.isUnlocking) revert TokenErrors.NoActiveUnlockRequest();

        uint256 withdrawAmount = request.amount;
        uint256 penalty = 0;

        if (block.timestamp < request.unlockTime) {
            uint256 totalLockDuration = request.unlockTime - request.requestTime;
            uint256 passedDuration = block.timestamp - request.requestTime;

            if (totalLockDuration > 0 && passedDuration < totalLockDuration) {
                uint256 remainingDuration = request.unlockTime - block.timestamp;
                uint256 penaltyRate = remainingDuration * maxEarlyUnlockPenalty / totalLockDuration;
                penalty = withdrawAmount * penaltyRate / 10000;
            }

            withdrawAmount = withdrawAmount - penalty;
            totalPenaltiesCollected = totalPenaltiesCollected + penalty;
        }


        request.processed = true;
        userStake.isUnlocking = false;


        userStake.amount = userStake.amount - request.amount;
        totalStaked = totalStaked - request.amount;


        StakingLevel oldLevel = userStake.level;
        userStake.level = determineStakingLevel(userStake.amount);

        if (oldLevel != userStake.level) {
            emit StakingLevelUpdated(msg.sender, oldLevel, userStake.level);
        }

        // 🔒 安全优化：先更新挖矿算力（在外部调用之前）
        updateUserMiningPower(msg.sender);

        // 转移代币
        pxtoken.transfer(msg.sender, withdrawAmount);

        // 转移惩罚到惩罚池
        if (penalty > 0 && penaltyPool != address(0)) {
            pxtoken.transfer(penaltyPool, penalty);
        }

        emit Withdrawn(msg.sender, withdrawAmount);
    }
    

    function emergencyWithdrawal(address _user) external nonReentrant {
        if (!hasRole(EMERGENCY_ROLE, msg.sender)) revert TokenErrors.MissingRole("EMERGENCY_ROLE");

        StakeInfo storage userStake = stakes[_user];
        if (userStake.amount == 0) revert TokenErrors.NoStakeFound();

        uint256 withdrawAmount = userStake.amount;
        uint256 penalty = withdrawAmount * emergencyWithdrawalFee / 10000;
        uint256 amountAfterPenalty = withdrawAmount - penalty;


        userStake.amount = 0;
        userStake.isUnlocking = false;
        totalStaked = totalStaked - withdrawAmount;


        totalPenaltiesCollected = totalPenaltiesCollected + penalty;


        pxtoken.transfer(_user, amountAfterPenalty);


        if (penalty > 0 && penaltyPool != address(0)) {
            pxtoken.transfer(penaltyPool, penalty);
        }

        emit EmergencyWithdrawal(_user, amountAfterPenalty, penalty);
    }


    function claimReward() external nonReentrant {
        uint256 reward = calculateReward(msg.sender);
        uint256 pending = pendingRewards[msg.sender];
        uint256 totalReward = reward + pending;

        if (totalReward == 0) revert TokenErrors.NoRewardToClaim();


        stakes[msg.sender].lastRewardTime = block.timestamp;
        pendingRewards[msg.sender] = 0;


        totalRewardsDistributed = totalRewardsDistributed + totalReward;
        totalRewardsClaimed[msg.sender] = totalRewardsClaimed[msg.sender] + totalReward;
        rewardHistory[msg.sender].push(totalReward);


        rewardToken.transfer(msg.sender, totalReward);

        emit RewardClaimed(msg.sender, totalReward);
    }
    

    function calculateReward(address _user) public view returns (uint256) {
        StakeInfo storage userStake = stakes[_user];

        if (userStake.amount == 0 || userStake.lastRewardTime == block.timestamp) {
            return 0;
        }

        // 计算质押时长
        uint256 stakingDuration = block.timestamp - userStake.lastRewardTime;

        // 基础APR (5% = 500基点)
        uint256 apr = baseAPR;

        // 获取期限倍数
        uint256 durationMultiplier = getDurationMultiplier(userStake.requestedDuration);

        // 计算基础APR：基础APR × 期限倍数
        apr = apr * durationMultiplier / 10000;

        // 计算基础奖励
        uint256 baseReward = userStake.amount * apr * stakingDuration / (365 days) / 10000;

        // 根据随机奖励是否启用，选择不同的等级倍数
        uint256 levelMultiplier;
        if (randomRewardEnabled) {
            // 随机模式：使用随机收益倍数范围
            levelMultiplier = _generateRandomLevelMultiplier(_user, userStake.level);
        } else {
            // 固定模式：使用固定收益倍数
            levelMultiplier = _getFixedLevelMultiplier(userStake.level);
        }

        // 应用等级倍数
        uint256 finalReward = baseReward * levelMultiplier / 10000;

        // 促销期额外奖励
        if (promotionEndTime != 0 && block.timestamp <= promotionEndTime) {
            finalReward = finalReward * 12000 / 10000; // 20%额外奖励
        }

        return finalReward;
    }


    function determineStakingLevel(uint256 _amount) public view returns (StakingLevel) {
        if (_amount >= zhiZunThreshold) {
            return StakingLevel.ZhiZun;
        } else if (_amount >= shuangShiJueThreshold) {
            return StakingLevel.ShuangShiJue;
        } else if (_amount >= shiJueThreshold) {
            return StakingLevel.ShiJue;
        } else if (_amount >= jiaJiThreshold) {
            return StakingLevel.JiaJi;
        } else if (_amount >= yiJiThreshold) {
            return StakingLevel.YiJi;
        } else if (_amount >= chengJiThreshold) {
            return StakingLevel.ChengJi;
        } else {
            return StakingLevel.DingJi;
        }
    }
    

    function getDurationMultiplier(uint256 _duration) public view returns (uint256) {
        if (_duration == 0) {
            return 10000;
        }

        if (durationMultiplierCount == 0) {
            return 10000;
        }

        if (_duration > maxUnlockPeriod && durationMultiplierCount > 0) {
            return durationMultipliers[durationMultiplierCount-1].multiplier;
        }

        uint256 multiplier = 10000;
        for (uint256 i = 0; i < durationMultiplierCount; i++) {

            if (_duration == durationMultipliers[i].duration) {
                return durationMultipliers[i].multiplier;
            }
            
            if (_duration >= durationMultipliers[i].duration) {
                multiplier = durationMultipliers[i].multiplier;
            } else if (i > 0) {

                uint256 lowerDuration = durationMultipliers[i-1].duration;
                uint256 upperDuration = durationMultipliers[i].duration;
                uint256 lowerMultiplier = durationMultipliers[i-1].multiplier;
                uint256 upperMultiplier = durationMultipliers[i].multiplier;
                

                multiplier = lowerMultiplier + 
                    (_duration - lowerDuration) * 
                    (upperMultiplier - lowerMultiplier) / 
                    (upperDuration - lowerDuration);
                break;
            } else {
                break;
            }
        }
        
        return multiplier;
    }
    

    function getUserStakingInfo(address _user) external view returns (
        uint256 amount,
        uint256 startTime,
        StakingLevel level,
        bool isUnlocking,
        uint256 unlockTime,
        uint256 requestedDuration,
        uint256 pendingReward,
        uint256 randomSeed,
        uint256 currentRandomMultiplier
    ) {
        StakeInfo storage userStake = stakes[_user];
        uint256 randomMultiplier = 0;
        if (userStake.amount > 0) {
            randomMultiplier = _generateRandomLevelMultiplier(_user, userStake.level);
        }
        
        return (
            userStake.amount,
            userStake.startTime,
            userStake.level,
            userStake.isUnlocking,
            userStake.unlockTime,
            userStake.requestedDuration,
            calculateReward(_user) + pendingRewards[_user],
            userStake.randomSeed,
            randomMultiplier
        );
    }
    

    function getUserUnlockRequests(address _user) external view returns (UnlockRequest[] memory) {
        return unlockRequests[_user];
    }
    

    function getStakingHistory(address _user) external view returns (uint256[] memory) {
        return stakingHistory[_user];
    }
    

    function getRewardHistory(address _user) external view returns (uint256[] memory) {
        return rewardHistory[_user];
    }
    

    function getTotalStaked() external view returns (uint256) {
        return totalStaked;
    }
    

    function getUserUnlockRequestsPaged(address _user, uint256 offset, uint256 limit) external view returns (UnlockRequest[] memory) {
        uint256 total = unlockRequests[_user].length;
        if (offset >= total) {
            return new UnlockRequest[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        UnlockRequest[] memory result = new UnlockRequest[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            result[i - offset] = unlockRequests[_user][i];
        }
        return result;
    }
    

    function setMaxUnlockRequests(uint256 _maxRequests) external onlyRole(ADMIN_ROLE) {
        if (_maxRequests < 1 || _maxRequests > 20) revert TokenErrors.InvalidMaxRequestsValue(1, 20, _maxRequests);
        
        uint256 oldValue = maxUnlockRequestsPerUser;
        maxUnlockRequestsPerUser = _maxRequests;
        
        emit MaxUnlockRequestsUpdated(oldValue, _maxRequests);
    }
    

    function setPenaltyPool(address _penaltyPool) external onlyRole(ADMIN_ROLE) {
        if (_penaltyPool == address(0)) revert TokenErrors.ZeroAddress();
        
        address oldPool = penaltyPool;
        penaltyPool = _penaltyPool;
        
        emit PenaltyPoolUpdated(oldPool, _penaltyPool);
    }
    

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
        emit ContractPaused(msg.sender);
    }
    

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }
    

    function setBaseAPR(uint256 _baseAPR) external onlyRole(ADMIN_ROLE) {
        if (_baseAPR == 0) revert TokenErrors.ZeroAmount();
        // 🔒 安全优化：限制APR范围，防止设置过高的利率
        require(_baseAPR <= 10000, "APR too high"); // 最大100%
        require(_baseAPR >= 100, "APR too low");    // 最小1%

        uint256 oldRate = baseAPR;
        baseAPR = _baseAPR;

        emit APRUpdated(oldRate, _baseAPR);
        emit SecurityParameterChanged("baseAPR", oldRate, _baseAPR, msg.sender);
    }
    

    function recoverToken(address _token, uint256 _amount) external onlyRole(ADMIN_ROLE) {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        
        if (_token == address(pxtoken)) {
            uint256 excessBalance = pxtoken.balanceOf(address(this)) - totalStaked;
            if (_amount > excessBalance) revert TokenErrors.InsufficientBalance();
        }

        if (_token == address(rewardToken) && rewardToken != pxtoken) {
        }
        
        IERC20(_token).transfer(msg.sender, _amount);
        
        emit TokenRecovered(_token, _amount);
    }
    

    function setMinStakeAmount(uint256 _minStakeAmount) external onlyRole(ADMIN_ROLE) {
        if (_minStakeAmount == 0) revert TokenErrors.ZeroAmount();
        
        uint256 oldValue = minStakeAmount;
        minStakeAmount = _minStakeAmount;
        
        emit MinStakeAmountUpdated(oldValue, _minStakeAmount);
    }
    

    function setRandomRewardEnabled(bool _enabled) external onlyRole(ADMIN_ROLE) {
        randomRewardEnabled = _enabled;
        emit RandomRewardSystemToggled(_enabled);
    }
    

    function setLevelRewardRange(
        StakingLevel _level,
        uint256 _minMultiplier,
        uint256 _maxMultiplier,
        bool _isActive
    ) external onlyRole(ADMIN_ROLE) {
        if (_minMultiplier > _maxMultiplier) revert TokenErrors.InvalidOperation();
        if (_maxMultiplier > 100000) revert TokenErrors.InvalidOperation();
        
        levelRewardRanges[_level] = LevelRewardRange({
            minMultiplier: _minMultiplier,
            maxMultiplier: _maxMultiplier,
            isActive: _isActive
        });
        
        emit RandomRewardRangeUpdated(_level, _minMultiplier, _maxMultiplier, _isActive);
    }
    

    function batchSetLevelRewardRanges(
        uint256[7] calldata _minMultipliers,
        uint256[7] calldata _maxMultipliers,
        bool[7] calldata _isActiveArray
    ) external onlyRole(ADMIN_ROLE) {
        StakingLevel[7] memory levels = [
            StakingLevel.DingJi,
            StakingLevel.ChengJi,
            StakingLevel.YiJi,
            StakingLevel.JiaJi,
            StakingLevel.ShiJue,
            StakingLevel.ShuangShiJue,
            StakingLevel.ZhiZun
        ];
        
        for (uint256 i = 0; i < 7; i++) {
            if (_minMultipliers[i] > _maxMultipliers[i]) revert TokenErrors.InvalidOperation();
            if (_maxMultipliers[i] > 100000) revert TokenErrors.InvalidOperation();
            
            levelRewardRanges[levels[i]] = LevelRewardRange({
                minMultiplier: _minMultipliers[i],
                maxMultiplier: _maxMultipliers[i],
                isActive: _isActiveArray[i]
            });
            
            emit RandomRewardRangeUpdated(levels[i], _minMultipliers[i], _maxMultipliers[i], _isActiveArray[i]);
        }
    }
    

    function getUserCurrentRandomMultiplier(address _user) external view returns (uint256) {
        StakeInfo storage userStake = stakes[_user];
        if (userStake.amount == 0) {
            return 0;
        }
        return _generateRandomLevelMultiplier(_user, userStake.level);
    }
    

    function getLevelRewardRange(StakingLevel _level) external view returns (
        uint256 minMultiplier,
        uint256 maxMultiplier,
        bool isActive
    ) {
        LevelRewardRange storage range = levelRewardRanges[_level];
        return (range.minMultiplier, range.maxMultiplier, range.isActive);
    }
    

    function _setDefaultRandomRewardRanges() internal {
        // 根据设计文档设置随机收益倍数范围
        // 这些是独立的随机收益倍数，替代固定倍数使用

        // 丁级 100+ PXT: 随机收益倍数范围 1.2x - 1.3x
        levelRewardRanges[StakingLevel.DingJi] = LevelRewardRange({
            minMultiplier: 12000,  // 1.2x
            maxMultiplier: 13000,  // 1.3x
            isActive: true
        });

        // 丙级 1,000 PXT: 随机收益倍数范围 1.3x - 1.4x
        levelRewardRanges[StakingLevel.ChengJi] = LevelRewardRange({
            minMultiplier: 13000,  // 1.3x
            maxMultiplier: 14000,  // 1.4x
            isActive: true
        });

        // 乙级 5,000 PXT: 随机收益倍数范围 1.5x - 1.6x
        levelRewardRanges[StakingLevel.YiJi] = LevelRewardRange({
            minMultiplier: 15000,  // 1.5x
            maxMultiplier: 16000,  // 1.6x
            isActive: true
        });

        // 甲级 20,000 PXT: 随机收益倍数范围 1.9x - 2.0x
        levelRewardRanges[StakingLevel.JiaJi] = LevelRewardRange({
            minMultiplier: 19000,  // 1.9x
            maxMultiplier: 20000,  // 2.0x
            isActive: true
        });

        // 十绝 100,000 PXT: 随机收益倍数范围 2.0x - 2.5x
        levelRewardRanges[StakingLevel.ShiJue] = LevelRewardRange({
            minMultiplier: 20000,  // 2.0x
            maxMultiplier: 25000,  // 2.5x
            isActive: true
        });

        // 双十绝 250,000 PXT: 随机收益倍数范围 3.0x - 4.0x
        levelRewardRanges[StakingLevel.ShuangShiJue] = LevelRewardRange({
            minMultiplier: 30000,  // 3.0x
            maxMultiplier: 40000,  // 4.0x
            isActive: true
        });

        // 至尊 500,000 PXT: 随机收益倍数范围 4.0x - 6.0x
        levelRewardRanges[StakingLevel.ZhiZun] = LevelRewardRange({
            minMultiplier: 40000,  // 4.0x
            maxMultiplier: 60000,  // 6.0x
            isActive: true
        });
    }
    

    function _generateRandomLevelMultiplier(address _user, StakingLevel _level) internal view returns (uint256) {
        LevelRewardRange storage range = levelRewardRanges[_level];

        if (!range.isActive || range.minMultiplier == 0 || range.maxMultiplier == 0) {
            return _getFixedLevelMultiplier(_level); // 返回固定倍数
        }

        uint256 userSeed = stakes[_user].randomSeed;
        if (userSeed == 0) {
            return _getFixedLevelMultiplier(_level); // 返回固定倍数
        }

        uint256 randomValue = uint256(keccak256(abi.encodePacked(
            userSeed,
            _level,
            block.timestamp / 1 hours
        )));

        uint256 range_size = range.maxMultiplier - range.minMultiplier;
        uint256 randomMultiplier = range.minMultiplier + (randomValue % (range_size + 1));

        return randomMultiplier;
    }

    function _generateRandomBonusMultiplier(address _user, StakingLevel _level) internal view returns (uint256) {
        if (!randomRewardEnabled) {
            return 10000;
        }

        LevelRewardRange storage range = levelRewardRanges[_level];

        if (!range.isActive || range.minMultiplier == 0 || range.maxMultiplier == 0) {
            return 10000;
        }

        uint256 userSeed = stakes[_user].randomSeed;
        if (userSeed == 0) {
            return 10000;
        }

        uint256 randomValue = uint256(keccak256(abi.encodePacked(
            userSeed,
            _level,
            block.timestamp / 1 hours
        )));

        uint256 range_size = range.maxMultiplier - range.minMultiplier;
        uint256 randomMultiplier = range.minMultiplier + (randomValue % (range_size + 1));

        return randomMultiplier;
    }
    

    function _getFixedLevelMultiplier(StakingLevel _level) internal pure returns (uint256) {
        // 根据设计文档的固定收益倍数设置
        if (_level == StakingLevel.DingJi) {
            return 13000;  // 1.3x (丁级)
        } else if (_level == StakingLevel.ChengJi) {
            return 14000;  // 1.4x (丙级)
        } else if (_level == StakingLevel.YiJi) {
            return 16000;  // 1.6x (乙级)
        } else if (_level == StakingLevel.JiaJi) {
            return 20000;  // 2.0x (甲级)
        } else if (_level == StakingLevel.ShiJue) {
            return 25000;  // 2.5x (十绝)
        } else if (_level == StakingLevel.ShuangShiJue) {
            return 30000;  // 3.0x (双十绝)
        } else if (_level == StakingLevel.ZhiZun) {
            return 50000;  // 5.0x (至尊)
        } else {
            return 10000;  // 1.0x (基础，100 PXT以下)
        }
    }
    

    function setMiningPoolContract(address _miningPoolContract) external onlyRole(ADMIN_ROLE) {
        address oldContract = miningPoolContract;
        miningPoolContract = _miningPoolContract;
        emit MiningPoolContractUpdated(oldContract, _miningPoolContract);
    }
    

    function calculateMiningPower(address _user) public view returns (uint256) {
        StakeInfo storage userStake = stakes[_user];
        if (userStake.amount == 0) {
            return 0;
        }
        

        uint256 basePower = userStake.amount / 10;
        

        uint256 levelMultiplier = _getFixedLevelMultiplier(userStake.level);
        

        // 🔒 安全优化：计算时间衰减系数，防止溢出
        uint256 stakingDays = (block.timestamp - userStake.startTime) / 1 days;
        // 简化计算，避免不必要的乘除法：stakingDays * 365 * 5000 / 365 = stakingDays * 5000
        uint256 timeDecayMultiplier = 10000 + Math.min(stakingDays * 5000, 10000);
        
        uint256 activityMultiplier = 10000;
        uint256 totalPower = basePower * levelMultiplier / 10000;
        totalPower = totalPower * timeDecayMultiplier / 10000;
        totalPower = totalPower * activityMultiplier / 10000;
        
        return totalPower;
    }
    

    function updateUserMiningPower(address _user) public {
        uint256 oldPower = userMiningPower[_user];
        uint256 newPower = calculateMiningPower(_user);
        

        totalMiningPower = totalMiningPower - oldPower + newPower;
        userMiningPower[_user] = newPower;
        
        emit MiningPowerUpdated(_user, oldPower, newPower);
    }
    

    function getUserMiningPower(address _user) external view returns (uint256) {
        return calculateMiningPower(_user);
    }
    

    function getTotalMiningPower() external view returns (uint256) {
        return totalMiningPower;
    }

    // ========== 治理函数 ==========
    // 这些函数只能通过治理提案调用

    /**
     * @dev 设置最小质押金额 - 治理函数
     * @param _minStakeAmount 新的最小质押金额
     */
    function setMinStakeAmountByGovernance(uint256 _minStakeAmount) external {
        require(_minStakeAmount > 0, "StakingPool: min stake amount must be greater than 0");
        require(_minStakeAmount <= 10000 * 10**18, "StakingPool: min stake amount too high");

        uint256 oldAmount = minStakeAmount;
        minStakeAmount = _minStakeAmount;

        emit MinStakeAmountUpdatedByGovernance(oldAmount, _minStakeAmount);
    }

    /**
     * @dev 设置质押等级阈值 - 治理函数
     * @param _level 质押等级
     * @param _threshold 新的阈值
     */
    function setStakingLevelThreshold(StakingLevel _level, uint256 _threshold) external {
        require(_threshold > 0, "StakingPool: threshold must be greater than 0");

        uint8 levelIndex = uint8(_level);
        uint256 oldThreshold = stakingLevelThresholds[levelIndex];
        stakingLevelThresholds[levelIndex] = _threshold;

        // 更新对应的阈值变量
        if (_level == StakingLevel.DingJi) {
            dingJiThreshold = _threshold;
        } else if (_level == StakingLevel.ChengJi) {
            chengJiThreshold = _threshold;
        } else if (_level == StakingLevel.YiJi) {
            yiJiThreshold = _threshold;
        } else if (_level == StakingLevel.JiaJi) {
            jiaJiThreshold = _threshold;
        } else if (_level == StakingLevel.ShiJue) {
            shiJueThreshold = _threshold;
        } else if (_level == StakingLevel.ShuangShiJue) {
            shuangShiJueThreshold = _threshold;
        } else if (_level == StakingLevel.ZhiZun) {
            zhiZunThreshold = _threshold;
        }

        emit StakingLevelThresholdUpdated(_level, oldThreshold, _threshold);
    }

    /**
     * @dev 设置解锁期间范围 - 治理函数
     * @param _minPeriod 最小解锁期间
     * @param _maxPeriod 最大解锁期间
     */
    function setUnlockPeriodRange(uint256 _minPeriod, uint256 _maxPeriod) external {
        require(_minPeriod > 0, "StakingPool: min period must be greater than 0");
        require(_maxPeriod > _minPeriod, "StakingPool: max period must be greater than min period");
        require(_maxPeriod <= 3650 days, "StakingPool: max period too long");

        uint256 oldMinPeriod = minUnlockPeriod;
        uint256 oldMaxPeriod = maxUnlockPeriod;

        minUnlockPeriod = _minPeriod;
        maxUnlockPeriod = _maxPeriod;

        emit UnlockPeriodRangeUpdated(oldMinPeriod, oldMaxPeriod, _minPeriod, _maxPeriod);
    }

    /**
     * @dev 设置紧急提取手续费 - 治理函数
     * @param _fee 新的手续费率（基点，10000 = 100%）
     */
    function setEmergencyWithdrawalFee(uint256 _fee) external {
        require(_fee <= 5000, "StakingPool: fee too high"); // 最高50%

        uint256 oldFee = emergencyWithdrawalFee;
        emergencyWithdrawalFee = _fee;

        emit EmergencyWithdrawalFeeUpdated(oldFee, _fee);
    }

    /**
     * @dev 设置最大早期解锁惩罚 - 治理函数
     * @param _penalty 新的惩罚率（基点，10000 = 100%）
     */
    function setMaxEarlyUnlockPenalty(uint256 _penalty) external {
        require(_penalty <= 5000, "StakingPool: penalty too high"); // 最高50%

        uint256 oldPenalty = maxEarlyUnlockPenalty;
        maxEarlyUnlockPenalty = _penalty;

        emit MaxEarlyUnlockPenaltyUpdated(oldPenalty, _penalty);
    }

    // ========== 事件定义 ==========

    event MinStakeAmountUpdatedByGovernance(uint256 oldAmount, uint256 newAmount);
    event StakingLevelThresholdUpdated(StakingLevel indexed level, uint256 oldThreshold, uint256 newThreshold);
    event UnlockPeriodRangeUpdated(uint256 oldMinPeriod, uint256 oldMaxPeriod, uint256 newMinPeriod, uint256 newMaxPeriod);
    event EmergencyWithdrawalFeeUpdated(uint256 oldFee, uint256 newFee);
    event MaxEarlyUnlockPenaltyUpdated(uint256 oldPenalty, uint256 newPenalty);
}