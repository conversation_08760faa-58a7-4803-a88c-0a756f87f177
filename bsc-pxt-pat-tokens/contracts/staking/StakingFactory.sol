// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/proxy/Clones.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import "./StakingPool.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

interface IRewardDistributor {
    function addRewardPool(address stakingPool, uint256 allocPoint) external;
}

contract StakingFactory is AccessControl, Pausable {
    using EnumerableSet for EnumerableSet.AddressSet;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant FACTORY_MANAGER_ROLE = keccak256("FACTORY_MANAGER_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    enum PoolType { Standard, Special, Promotional }

    struct PoolInfo {
        address poolAddress;
        string name;
        PoolType poolType;
        address rewardToken;
        uint256 creationTime;
        bool isActive;
    }

    address public pxtoken;
    address public patoken;
    address public stakingPoolImplementation;
    address public rewardDistributor;

    EnumerableSet.AddressSet private stakingPools;
    mapping(address => PoolInfo) public poolInfo;
    mapping(PoolType => EnumerableSet.AddressSet) private poolsByType;

    uint256 public poolCount;

    mapping(string => bool) private poolNameExists;

    uint256 public minPoolNameLength = 3;
    uint256 public maxPoolNameLength = 50;

    event PoolCreated(address indexed poolAddress, string indexed name, PoolType indexed poolType, address rewardToken);
    event PoolActivated(address indexed poolAddress, address indexed operator);
    event PoolDeactivated(address indexed poolAddress, address indexed operator);
    event RewardDistributorUpdated(address indexed newDistributor);
    event StandardImplementationUpdated(address indexed newImplementation);
    event TokenRecovered(address indexed token, uint256 amount);
    
    constructor(
        address _pxtoken,
        address _patoken
    ) {
        if (_pxtoken == address(0)) revert TokenErrors.InvalidAddress("PXT Token");
        if (_patoken == address(0)) revert TokenErrors.InvalidAddress("PAT Token");

        pxtoken = _pxtoken;
        patoken = _patoken;

        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(FACTORY_MANAGER_ROLE, msg.sender);
        _setupRole(EMERGENCY_ROLE, msg.sender);
    }

    function createPool(
        string memory _name,
        PoolType _poolType,
        address _rewardToken,
        uint256 _baseAPR,
        uint256 _duration
    )
        public
        onlyRole(FACTORY_MANAGER_ROLE)
        whenNotPaused
        returns (address)
    {
        validatePoolName(_name);

        address rewardTokenToUse = _rewardToken;
        if (_poolType == PoolType.Standard && _rewardToken == address(0)) {
            rewardTokenToUse = patoken;
        } else if (_poolType != PoolType.Standard && _rewardToken == address(0)) {
            revert TokenErrors.InvalidAddress("Reward Token");
        } else if (_rewardToken == address(0)) {
            rewardTokenToUse = patoken;
        }

        if (_poolType == PoolType.Promotional && _duration == 0) {
            revert TokenErrors.ZeroDuration();
        }

        address poolAddress = Clones.clone(stakingPoolImplementation);

        try IStakingPool(poolAddress).initialize(pxtoken, rewardTokenToUse, _baseAPR) {

            try AccessControl(poolAddress).grantRole(ADMIN_ROLE, address(this)) {
            } catch {
            }
        } catch {
            return address(0);
        }

        if (_poolType == PoolType.Promotional) {
            try IStakingPool(poolAddress).setPromotionEnd(block.timestamp + _duration) {
            } catch {
            }
        }

        poolInfo[poolAddress] = PoolInfo({
            poolAddress: poolAddress,
            name: _name,
            poolType: _poolType,
            rewardToken: rewardTokenToUse,
            creationTime: block.timestamp,
            isActive: true
        });

        EnumerableSet.add(stakingPools, poolAddress);
        EnumerableSet.add(poolsByType[_poolType], poolAddress);

        poolCount = poolCount + 1;

        if (rewardDistributor != address(0)) {
            uint256 allocPoint = 100;
            if (_poolType == PoolType.Promotional) {
                allocPoint = 150;
            }

            try IRewardDistributor(rewardDistributor).addRewardPool(poolAddress, allocPoint) {
            } catch {
            }
        }

        poolNameExists[_name] = true;

        emit PoolCreated(poolAddress, _name, _poolType, rewardTokenToUse);

        return poolAddress;
    }
    
    function createStandardPool(
        string memory _name,
        uint256 _baseAPR
    )
        external
        onlyRole(FACTORY_MANAGER_ROLE)
        whenNotPaused
        returns (address)
    {
        return createPool(_name, PoolType.Standard, address(0), _baseAPR, 0);
    }

    function createSpecialPool(
        string memory _name,
        address _rewardToken,
        uint256 _baseAPR
    )
        external
        onlyRole(FACTORY_MANAGER_ROLE)
        whenNotPaused
        returns (address)
    {
        return createPool(_name, PoolType.Special, _rewardToken, _baseAPR, 0);
    }

    function createPromotionalPool(
        string memory _name,
        address _rewardToken,
        uint256 _baseAPR,
        uint256 _duration
    )
        external
        onlyRole(FACTORY_MANAGER_ROLE)
        whenNotPaused
        returns (address)
    {
        return createPool(_name, PoolType.Promotional, _rewardToken, _baseAPR, _duration);
    }

    function validatePoolName(string memory _name) internal view {
        if (poolNameExists[_name]) revert TokenErrors.NameAlreadyTaken();

        bytes memory nameBytes = bytes(_name);
        if (nameBytes.length < minPoolNameLength || nameBytes.length > maxPoolNameLength)
            revert TokenErrors.InvalidNameLength(minPoolNameLength, maxPoolNameLength, nameBytes.length);

        bool onlyWhitespace = true;
        for (uint i = 0; i < nameBytes.length; i++) {
            if (nameBytes[i] != 0x20 && nameBytes[i] != 0x09) {
                onlyWhitespace = false;
                break;
            }
        }
        if (onlyWhitespace) revert TokenErrors.EmptyName();
    }
    

    function deactivatePool(address _poolAddress) 
        external 
        onlyRole(FACTORY_MANAGER_ROLE) 
    {
        if (!EnumerableSet.contains(stakingPools, _poolAddress)) revert TokenErrors.PoolNotFound();
        if (!poolInfo[_poolAddress].isActive) revert TokenErrors.PoolAlreadyInactive();
        
        poolInfo[_poolAddress].isActive = false;
        IStakingPool(_poolAddress).pause();
        
        emit PoolDeactivated(_poolAddress, msg.sender);
    }
    

    function activatePool(address _poolAddress) 
        external 
        onlyRole(FACTORY_MANAGER_ROLE) 
    {
        if (!EnumerableSet.contains(stakingPools, _poolAddress)) revert TokenErrors.PoolNotFound();
        if (poolInfo[_poolAddress].isActive) revert TokenErrors.PoolAlreadyActive();
        
        poolInfo[_poolAddress].isActive = true;
        IStakingPool(_poolAddress).unpause();
        
        emit PoolActivated(_poolAddress, msg.sender);
    }
    

    function updateRewardDistributor(address _newRewardDistributor) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        if (_newRewardDistributor == address(0)) revert TokenErrors.InvalidAddress("Reward Distributor");
        
        rewardDistributor = _newRewardDistributor;
        
        emit RewardDistributorUpdated(_newRewardDistributor);
    }
    

    function updateStandardImplementation(address _newImplementation) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        if (_newImplementation == address(0)) revert TokenErrors.InvalidAddress("Standard Implementation");
        
        stakingPoolImplementation = _newImplementation;
        
        emit StandardImplementationUpdated(_newImplementation);
    }
    

    function setNameLengthLimits(uint256 _minLength, uint256 _maxLength) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        if (_minLength == 0) revert TokenErrors.ZeroAmount();
        if (_minLength > _maxLength) revert TokenErrors.InvalidOperation();
        
        minPoolNameLength = _minLength;
        maxPoolNameLength = _maxLength;
    }
    

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }
    

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
    

    function recoverToken(address _token, uint256 _amount) external onlyRole(ADMIN_ROLE) {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        
        IERC20(_token).transfer(msg.sender, _amount);
        
        emit TokenRecovered(_token, _amount);
    }
    

    function getAllPools() external view returns (address[] memory) {
        uint256 count = EnumerableSet.length(stakingPools);
        address[] memory pools = new address[](count);
        
        for (uint256 i = 0; i < count; i++) {
            pools[i] = EnumerableSet.at(stakingPools, i);
        }
        
        return pools;
    }
    

    function getPoolsByType(PoolType _poolType) external view returns (address[] memory) {
        EnumerableSet.AddressSet storage poolSet = poolsByType[_poolType];
        uint256 count = EnumerableSet.length(poolSet);
        address[] memory pools = new address[](count);
        
        for (uint256 i = 0; i < count; i++) {
            pools[i] = EnumerableSet.at(poolSet, i);
        }
        
        return pools;
    }
    

    function getActivePools() external view returns (address[] memory) {
        uint256 count = EnumerableSet.length(stakingPools);
        

        uint256 activeCount = 0;
        for (uint256 i = 0; i < count; i++) {
            address poolAddress = EnumerableSet.at(stakingPools, i);
            if (poolInfo[poolAddress].isActive) {
                activeCount++;
            }
        }
        

        address[] memory activePools = new address[](activeCount);
        uint256 index = 0;
        
        for (uint256 i = 0; i < count; i++) {
            address poolAddress = EnumerableSet.at(stakingPools, i);
            if (poolInfo[poolAddress].isActive) {
                activePools[index] = poolAddress;
                index++;
            }
        }
        
        return activePools;
    }
    

    function poolExists(address _poolAddress) external view returns (bool) {
        return EnumerableSet.contains(stakingPools, _poolAddress);
    }
    

    function isPoolActive(address _poolAddress) external view returns (bool) {
        if (!EnumerableSet.contains(stakingPools, _poolAddress)) {
            return false;
        }
        
        return poolInfo[_poolAddress].isActive;
    }
    

    function getPoolsPaged(uint256 offset, uint256 limit) external view returns (address[] memory) {
        uint256 count = EnumerableSet.length(stakingPools);
        if (offset >= count) {
            return new address[](0);
        }
        uint256 end = offset + limit > count ? count : offset + limit;
        address[] memory pools = new address[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            pools[i - offset] = EnumerableSet.at(stakingPools, i);
        }
        return pools;
    }
}

interface IStakingPool {
    enum StakingLevel { DingJi, ChengJi, YiJi, JiaJi, ShiJue, ShuangShiJue, ZhiZun }
    struct StakeInfo {
        uint256 amount;
        uint256 startTime;
        uint256 endTime;
        uint256 lastRewardTime;
        StakingLevel level;
        bool isUnlocking;
        uint256 unlockTime;
        uint256 requestedDuration;
    }
    function initialize(address _pxtoken, address _rewardToken, uint256 _baseAPR) external;
    function pause() external;
    function unpause() external;
    function setPromotionEnd(uint256 endTime) external;
    function getUserStakingInfo(address _user) external view returns (
        uint256 amount,
        uint256 startTime,
        StakingLevel level,
        bool isUnlocking,
        uint256 unlockTime,
        uint256 requestedDuration,
        uint256 pendingReward
    );
} 