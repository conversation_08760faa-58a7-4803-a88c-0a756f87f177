// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title TimelockController
 * @dev 时间锁控制器 - 为关键操作提供延迟执行机制
 * 
 * 功能：
 * 1. 关键参数修改需要时间锁保护
 * 2. 分级时间锁：不同操作不同延迟时间
 * 3. 紧急取消机制
 * 4. 批量操作支持
 */
contract TimelockController is AccessControl, ReentrancyGuard {
    bytes32 public constant PROPOSER_ROLE = keccak256("PROPOSER_ROLE");
    bytes32 public constant EXECUTOR_ROLE = keccak256("EXECUTOR_ROLE");
    bytes32 public constant CANCELLER_ROLE = keccak256("CANCELLER_ROLE");
    bytes32 public constant TIMELOCK_ADMIN_ROLE = keccak256("TIMELOCK_ADMIN_ROLE");

    // 操作类型和对应的延迟时间
    enum OperationType {
        PARAMETER_CHANGE,    // 参数修改: 48小时
        CRITICAL_UPGRADE,    // 关键升级: 7天
        EMERGENCY_ACTION,    // 紧急操作: 24小时
        ROUTINE_MAINTENANCE, // 常规维护: 12小时
        GOVERNANCE_CHANGE    // 治理变更: 72小时
    }

    struct Operation {
        bytes32 id;
        address target;
        uint256 value;
        bytes data;
        bytes32 predecessor;
        bytes32 salt;
        uint256 scheduledTime;
        uint256 delay;
        OperationType operationType;
        bool executed;
        bool cancelled;
        address proposer;
        string description;
    }

    // 操作类型对应的最小延迟时间
    mapping(OperationType => uint256) public minDelays;
    
    // 操作ID到操作的映射
    mapping(bytes32 => Operation) public operations;
    
    // 已调度的操作列表
    bytes32[] public scheduledOperations;
    
    // 统计数据
    uint256 public totalOperations;
    uint256 public executedOperations;
    uint256 public cancelledOperations;

    event OperationScheduled(
        bytes32 indexed id,
        uint256 indexed index,
        address indexed target,
        uint256 value,
        bytes data,
        bytes32 predecessor,
        bytes32 salt,
        uint256 delay,
        OperationType operationType,
        string description
    );

    event OperationExecuted(bytes32 indexed id, uint256 indexed index, address indexed target, uint256 value, bytes data);
    event OperationCancelled(bytes32 indexed id);
    event MinDelayChanged(OperationType indexed operationType, uint256 oldDelay, uint256 newDelay);

    constructor(
        uint256 minDelayParameterChange,
        uint256 minDelayCriticalUpgrade,
        uint256 minDelayEmergencyAction,
        uint256 minDelayRoutineMaintenance,
        uint256 minDelayGovernanceChange,
        address[] memory proposers,
        address[] memory executors,
        address admin
    ) {
        // 设置最小延迟时间
        minDelays[OperationType.PARAMETER_CHANGE] = minDelayParameterChange;
        minDelays[OperationType.CRITICAL_UPGRADE] = minDelayCriticalUpgrade;
        minDelays[OperationType.EMERGENCY_ACTION] = minDelayEmergencyAction;
        minDelays[OperationType.ROUTINE_MAINTENANCE] = minDelayRoutineMaintenance;
        minDelays[OperationType.GOVERNANCE_CHANGE] = minDelayGovernanceChange;

        // 设置角色
        _grantRole(TIMELOCK_ADMIN_ROLE, admin);
        _setRoleAdmin(PROPOSER_ROLE, TIMELOCK_ADMIN_ROLE);
        _setRoleAdmin(EXECUTOR_ROLE, TIMELOCK_ADMIN_ROLE);
        _setRoleAdmin(CANCELLER_ROLE, TIMELOCK_ADMIN_ROLE);

        // 批量授予提案者角色
        for (uint256 i = 0; i < proposers.length; ++i) {
            _grantRole(PROPOSER_ROLE, proposers[i]);
        }

        // 批量授予执行者角色
        for (uint256 i = 0; i < executors.length; ++i) {
            _grantRole(EXECUTOR_ROLE, executors[i]);
        }

        // 授予取消者角色给管理员
        _grantRole(CANCELLER_ROLE, admin);
    }

    /**
     * @dev 调度操作
     */
    function schedule(
        address target,
        uint256 value,
        bytes calldata data,
        bytes32 predecessor,
        bytes32 salt,
        uint256 delay,
        OperationType operationType,
        string calldata description
    ) public onlyRole(PROPOSER_ROLE) returns (bytes32) {
        bytes32 id = hashOperation(target, value, data, predecessor, salt);
        
        require(!isOperation(id), "TimelockController: operation already scheduled");
        require(delay >= getMinDelay(operationType), "TimelockController: insufficient delay");

        uint256 scheduledTime = block.timestamp + delay;
        
        operations[id] = Operation({
            id: id,
            target: target,
            value: value,
            data: data,
            predecessor: predecessor,
            salt: salt,
            scheduledTime: scheduledTime,
            delay: delay,
            operationType: operationType,
            executed: false,
            cancelled: false,
            proposer: msg.sender,
            description: description
        });

        scheduledOperations.push(id);
        totalOperations++;

        emit OperationScheduled(
            id,
            scheduledOperations.length - 1,
            target,
            value,
            data,
            predecessor,
            salt,
            delay,
            operationType,
            description
        );

        return id;
    }

    /**
     * @dev 批量调度操作
     */
    function scheduleBatch(
        address[] calldata targets,
        uint256[] calldata values,
        bytes[] calldata payloads,
        bytes32 predecessor,
        bytes32 salt,
        uint256 delay,
        OperationType operationType,
        string calldata description
    ) public onlyRole(PROPOSER_ROLE) returns (bytes32) {
        require(targets.length == values.length, "TimelockController: length mismatch");
        require(targets.length == payloads.length, "TimelockController: length mismatch");

        bytes32 id = hashOperationBatch(targets, values, payloads, predecessor, salt);
        require(!isOperation(id), "TimelockController: operation already scheduled");
        require(delay >= getMinDelay(operationType), "TimelockController: insufficient delay");

        uint256 scheduledTime = block.timestamp + delay;
        
        // 将批量操作编码为单个操作
        bytes memory batchData = abi.encode(targets, values, payloads);
        
        operations[id] = Operation({
            id: id,
            target: address(this), // 批量操作的目标是自己
            value: 0,
            data: batchData,
            predecessor: predecessor,
            salt: salt,
            scheduledTime: scheduledTime,
            delay: delay,
            operationType: operationType,
            executed: false,
            cancelled: false,
            proposer: msg.sender,
            description: description
        });

        scheduledOperations.push(id);
        totalOperations++;

        emit OperationScheduled(
            id,
            scheduledOperations.length - 1,
            address(this),
            0,
            batchData,
            predecessor,
            salt,
            delay,
            operationType,
            description
        );

        return id;
    }

    /**
     * @dev 执行操作
     */
    function execute(
        address target,
        uint256 value,
        bytes calldata payload,
        bytes32 predecessor,
        bytes32 salt
    ) public payable onlyRole(EXECUTOR_ROLE) nonReentrant {
        bytes32 id = hashOperation(target, value, payload, predecessor, salt);
        
        require(isOperationReady(id), "TimelockController: operation is not ready");
        require(predecessor == bytes32(0) || isOperationDone(predecessor), "TimelockController: missing dependency");

        Operation storage operation = operations[id];
        operation.executed = true;
        executedOperations++;

        // 执行调用
        (bool success, ) = target.call{value: value}(payload);
        require(success, "TimelockController: underlying transaction reverted");

        emit OperationExecuted(id, 0, target, value, payload);
    }

    /**
     * @dev 批量执行操作
     */
    function executeBatch(
        address[] calldata targets,
        uint256[] calldata values,
        bytes[] calldata payloads,
        bytes32 predecessor,
        bytes32 salt
    ) public payable onlyRole(EXECUTOR_ROLE) nonReentrant {
        require(targets.length == values.length, "TimelockController: length mismatch");
        require(targets.length == payloads.length, "TimelockController: length mismatch");

        bytes32 id = hashOperationBatch(targets, values, payloads, predecessor, salt);
        
        require(isOperationReady(id), "TimelockController: operation is not ready");
        require(predecessor == bytes32(0) || isOperationDone(predecessor), "TimelockController: missing dependency");

        Operation storage operation = operations[id];
        operation.executed = true;
        executedOperations++;

        // 批量执行调用
        for (uint256 i = 0; i < targets.length; ++i) {
            (bool success, ) = targets[i].call{value: values[i]}(payloads[i]);
            require(success, "TimelockController: underlying transaction reverted");
            
            emit OperationExecuted(id, i, targets[i], values[i], payloads[i]);
        }
    }

    /**
     * @dev 取消操作
     */
    function cancel(bytes32 id) public onlyRole(CANCELLER_ROLE) {
        require(isOperationPending(id), "TimelockController: operation cannot be cancelled");
        
        Operation storage operation = operations[id];
        operation.cancelled = true;
        cancelledOperations++;

        emit OperationCancelled(id);
    }

    /**
     * @dev 更新最小延迟时间
     */
    function updateDelay(OperationType operationType, uint256 newDelay) external onlyRole(TIMELOCK_ADMIN_ROLE) {
        uint256 oldDelay = minDelays[operationType];
        minDelays[operationType] = newDelay;
        
        emit MinDelayChanged(operationType, oldDelay, newDelay);
    }

    /**
     * @dev 计算操作哈希
     */
    function hashOperation(
        address target,
        uint256 value,
        bytes calldata data,
        bytes32 predecessor,
        bytes32 salt
    ) public pure returns (bytes32) {
        return keccak256(abi.encode(target, value, data, predecessor, salt));
    }

    /**
     * @dev 计算批量操作哈希
     */
    function hashOperationBatch(
        address[] calldata targets,
        uint256[] calldata values,
        bytes[] calldata payloads,
        bytes32 predecessor,
        bytes32 salt
    ) public pure returns (bytes32) {
        return keccak256(abi.encode(targets, values, payloads, predecessor, salt));
    }

    /**
     * @dev 检查操作是否存在
     */
    function isOperation(bytes32 id) public view returns (bool) {
        return operations[id].scheduledTime > 0;
    }

    /**
     * @dev 检查操作是否等待中
     */
    function isOperationPending(bytes32 id) public view returns (bool) {
        Operation memory operation = operations[id];
        return operation.scheduledTime > 0 && !operation.executed && !operation.cancelled;
    }

    /**
     * @dev 检查操作是否准备就绪
     */
    function isOperationReady(bytes32 id) public view returns (bool) {
        Operation memory operation = operations[id];
        return operation.scheduledTime > 0 && 
               block.timestamp >= operation.scheduledTime && 
               !operation.executed && 
               !operation.cancelled;
    }

    /**
     * @dev 检查操作是否已完成
     */
    function isOperationDone(bytes32 id) public view returns (bool) {
        Operation memory operation = operations[id];
        return operation.executed || operation.cancelled;
    }

    /**
     * @dev 获取操作类型的最小延迟时间
     */
    function getMinDelay(OperationType operationType) public view returns (uint256) {
        return minDelays[operationType];
    }

    /**
     * @dev 获取操作详情
     */
    function getOperation(bytes32 id) external view returns (
        address target,
        uint256 value,
        bytes memory data,
        bytes32 predecessor,
        bytes32 salt,
        uint256 scheduledTime,
        uint256 delay,
        OperationType operationType,
        bool executed,
        bool cancelled,
        address proposer,
        string memory description
    ) {
        Operation memory operation = operations[id];
        return (
            operation.target,
            operation.value,
            operation.data,
            operation.predecessor,
            operation.salt,
            operation.scheduledTime,
            operation.delay,
            operation.operationType,
            operation.executed,
            operation.cancelled,
            operation.proposer,
            operation.description
        );
    }

    /**
     * @dev 获取等待中的操作列表
     */
    function getPendingOperations() external view returns (bytes32[] memory) {
        uint256 pendingCount = 0;
        
        // 计算等待中的操作数量
        for (uint256 i = 0; i < scheduledOperations.length; i++) {
            if (isOperationPending(scheduledOperations[i])) {
                pendingCount++;
            }
        }
        
        // 构建等待中的操作数组
        bytes32[] memory pending = new bytes32[](pendingCount);
        uint256 index = 0;
        
        for (uint256 i = 0; i < scheduledOperations.length; i++) {
            if (isOperationPending(scheduledOperations[i])) {
                pending[index] = scheduledOperations[i];
                index++;
            }
        }
        
        return pending;
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 total,
        uint256 executed,
        uint256 cancelled,
        uint256 pending
    ) {
        uint256 pendingCount = 0;
        for (uint256 i = 0; i < scheduledOperations.length; i++) {
            if (isOperationPending(scheduledOperations[i])) {
                pendingCount++;
            }
        }
        
        return (totalOperations, executedOperations, cancelledOperations, pendingCount);
    }

    /**
     * @dev 接收以太币
     */
    receive() external payable {}
}
