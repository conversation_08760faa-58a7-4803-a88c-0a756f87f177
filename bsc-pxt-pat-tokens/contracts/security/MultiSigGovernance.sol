// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title MultiSigGovernance
 * @dev 多重签名治理合约 - 重要操作需要多签确认
 * 
 * 功能：
 * 1. 分级多签：不同操作需要不同数量的签名
 * 2. 动态阈值：根据操作重要性调整签名要求
 * 3. 时间窗口：签名有效期限制
 * 4. 紧急响应：紧急情况下的快速决策机制
 */
contract MultiSigGovernance is AccessControl, ReentrancyGuard {
    using ECDSA for bytes32;

    bytes32 public constant SIGNER_ROLE = keccak256("SIGNER_ROLE");
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    // 操作类型和对应的签名要求
    enum OperationType {
        PARAMETER_CHANGE,    // 参数修改: 3/5
        FUND_TRANSFER,       // 资金转移: 4/5
        CONTRACT_UPGRADE,    // 合约升级: 4/5
        EMERGENCY_ACTION,    // 紧急操作: 2/5
        CRITICAL_DECISION    // 关键决策: 5/5
    }

    struct MultiSigConfig {
        uint256 requiredSignatures;  // 需要的签名数量
        uint256 validityPeriod;      // 签名有效期
        bool isActive;               // 是否激活
    }

    struct Transaction {
        uint256 id;
        address target;
        uint256 value;
        bytes data;
        OperationType operationType;
        uint256 createdAt;
        uint256 validUntil;
        bool executed;
        bool cancelled;
        address proposer;
        string description;
        bytes32[] signatures;
        address[] signers;
        mapping(address => bool) hasSigned;
    }

    // 配置映射
    mapping(OperationType => MultiSigConfig) public configs;
    
    // 交易映射
    mapping(uint256 => Transaction) public transactions;
    
    // 签名者列表
    address[] public signers;
    mapping(address => bool) public isSigner;
    
    // 统计数据
    uint256 public transactionCount;
    uint256 public executedCount;
    uint256 public cancelledCount;
    
    // 紧急模式
    bool public emergencyMode;
    uint256 public emergencyModeActivatedAt;
    uint256 public constant EMERGENCY_MODE_DURATION = 7 days;

    event TransactionProposed(
        uint256 indexed transactionId,
        address indexed proposer,
        address indexed target,
        uint256 value,
        bytes data,
        OperationType operationType,
        string description
    );

    event TransactionSigned(
        uint256 indexed transactionId,
        address indexed signer,
        uint256 signatureCount,
        uint256 requiredSignatures
    );

    event TransactionExecuted(uint256 indexed transactionId, address indexed executor);
    event TransactionCancelled(uint256 indexed transactionId, address indexed canceller);
    event SignerAdded(address indexed signer);
    event SignerRemoved(address indexed signer);
    event ConfigUpdated(OperationType indexed operationType, uint256 requiredSignatures, uint256 validityPeriod);
    event EmergencyModeToggled(bool enabled, address indexed triggeredBy);

    constructor(
        address[] memory _signers,
        address admin
    ) {
        require(_signers.length >= 3, "MultiSigGovernance: minimum 3 signers required");
        
        // 初始化签名者
        for (uint256 i = 0; i < _signers.length; i++) {
            require(_signers[i] != address(0), "MultiSigGovernance: invalid signer address");
            require(!isSigner[_signers[i]], "MultiSigGovernance: duplicate signer");
            
            signers.push(_signers[i]);
            isSigner[_signers[i]] = true;
            _grantRole(SIGNER_ROLE, _signers[i]);
        }

        // 设置默认配置
        configs[OperationType.PARAMETER_CHANGE] = MultiSigConfig({
            requiredSignatures: (signers.length * 60) / 100, // 60%
            validityPeriod: 7 days,
            isActive: true
        });

        configs[OperationType.FUND_TRANSFER] = MultiSigConfig({
            requiredSignatures: (signers.length * 80) / 100, // 80%
            validityPeriod: 3 days,
            isActive: true
        });

        configs[OperationType.CONTRACT_UPGRADE] = MultiSigConfig({
            requiredSignatures: (signers.length * 80) / 100, // 80%
            validityPeriod: 7 days,
            isActive: true
        });

        configs[OperationType.EMERGENCY_ACTION] = MultiSigConfig({
            requiredSignatures: (signers.length * 40) / 100, // 40%
            validityPeriod: 1 days,
            isActive: true
        });

        configs[OperationType.CRITICAL_DECISION] = MultiSigConfig({
            requiredSignatures: signers.length, // 100%
            validityPeriod: 14 days,
            isActive: true
        });

        _grantRole(ADMIN_ROLE, admin);
        _grantRole(EMERGENCY_ROLE, admin);
        _setRoleAdmin(SIGNER_ROLE, ADMIN_ROLE);
    }

    /**
     * @dev 提议交易
     */
    function proposeTransaction(
        address target,
        uint256 value,
        bytes calldata data,
        OperationType operationType,
        string calldata description
    ) external onlyRole(SIGNER_ROLE) returns (uint256) {
        require(configs[operationType].isActive, "MultiSigGovernance: operation type not active");
        
        uint256 transactionId = transactionCount++;
        Transaction storage txn = transactions[transactionId];
        
        txn.id = transactionId;
        txn.target = target;
        txn.value = value;
        txn.data = data;
        txn.operationType = operationType;
        txn.createdAt = block.timestamp;
        txn.validUntil = block.timestamp + configs[operationType].validityPeriod;
        txn.executed = false;
        txn.cancelled = false;
        txn.proposer = msg.sender;
        txn.description = description;

        emit TransactionProposed(
            transactionId,
            msg.sender,
            target,
            value,
            data,
            operationType,
            description
        );

        return transactionId;
    }

    /**
     * @dev 签名交易
     */
    function signTransaction(uint256 transactionId) external onlyRole(SIGNER_ROLE) {
        Transaction storage txn = transactions[transactionId];
        
        require(txn.createdAt > 0, "MultiSigGovernance: transaction does not exist");
        require(!txn.executed, "MultiSigGovernance: transaction already executed");
        require(!txn.cancelled, "MultiSigGovernance: transaction cancelled");
        require(block.timestamp <= txn.validUntil, "MultiSigGovernance: transaction expired");
        require(!txn.hasSigned[msg.sender], "MultiSigGovernance: already signed");

        // 在紧急模式下，只有紧急角色可以签名紧急操作
        if (emergencyMode && txn.operationType != OperationType.EMERGENCY_ACTION) {
            require(hasRole(EMERGENCY_ROLE, msg.sender), "MultiSigGovernance: emergency mode active");
        }

        txn.hasSigned[msg.sender] = true;
        txn.signers.push(msg.sender);
        
        // 创建签名哈希
        bytes32 txnHash = getTransactionHash(transactionId);
        txn.signatures.push(txnHash);

        uint256 requiredSignatures = configs[txn.operationType].requiredSignatures;
        
        emit TransactionSigned(
            transactionId,
            msg.sender,
            txn.signers.length,
            requiredSignatures
        );

        // 如果签名数量足够，自动执行
        if (txn.signers.length >= requiredSignatures) {
            _executeTransaction(transactionId);
        }
    }

    /**
     * @dev 执行交易
     */
    function executeTransaction(uint256 transactionId) external onlyRole(SIGNER_ROLE) {
        _executeTransaction(transactionId);
    }

    /**
     * @dev 内部执行交易函数
     */
    function _executeTransaction(uint256 transactionId) internal nonReentrant {
        Transaction storage txn = transactions[transactionId];
        
        require(txn.createdAt > 0, "MultiSigGovernance: transaction does not exist");
        require(!txn.executed, "MultiSigGovernance: transaction already executed");
        require(!txn.cancelled, "MultiSigGovernance: transaction cancelled");
        require(block.timestamp <= txn.validUntil, "MultiSigGovernance: transaction expired");
        
        uint256 requiredSignatures = configs[txn.operationType].requiredSignatures;
        require(txn.signers.length >= requiredSignatures, "MultiSigGovernance: insufficient signatures");

        txn.executed = true;
        executedCount++;

        // 执行交易
        (bool success, ) = txn.target.call{value: txn.value}(txn.data);
        require(success, "MultiSigGovernance: transaction execution failed");

        emit TransactionExecuted(transactionId, msg.sender);
    }

    /**
     * @dev 取消交易
     */
    function cancelTransaction(uint256 transactionId) external {
        Transaction storage txn = transactions[transactionId];
        
        require(txn.createdAt > 0, "MultiSigGovernance: transaction does not exist");
        require(!txn.executed, "MultiSigGovernance: transaction already executed");
        require(!txn.cancelled, "MultiSigGovernance: transaction already cancelled");
        
        // 只有提议者或管理员可以取消
        require(
            msg.sender == txn.proposer || hasRole(ADMIN_ROLE, msg.sender),
            "MultiSigGovernance: not authorized to cancel"
        );

        txn.cancelled = true;
        cancelledCount++;

        emit TransactionCancelled(transactionId, msg.sender);
    }

    /**
     * @dev 添加签名者
     */
    function addSigner(address newSigner) external onlyRole(ADMIN_ROLE) {
        require(newSigner != address(0), "MultiSigGovernance: invalid signer address");
        require(!isSigner[newSigner], "MultiSigGovernance: signer already exists");

        signers.push(newSigner);
        isSigner[newSigner] = true;
        _grantRole(SIGNER_ROLE, newSigner);

        // 更新配置中的签名要求
        _updateSignatureRequirements();

        emit SignerAdded(newSigner);
    }

    /**
     * @dev 移除签名者
     */
    function removeSigner(address signer) external onlyRole(ADMIN_ROLE) {
        require(isSigner[signer], "MultiSigGovernance: signer does not exist");
        require(signers.length > 3, "MultiSigGovernance: minimum 3 signers required");

        // 从数组中移除
        for (uint256 i = 0; i < signers.length; i++) {
            if (signers[i] == signer) {
                signers[i] = signers[signers.length - 1];
                signers.pop();
                break;
            }
        }

        isSigner[signer] = false;
        _revokeRole(SIGNER_ROLE, signer);

        // 更新配置中的签名要求
        _updateSignatureRequirements();

        emit SignerRemoved(signer);
    }

    /**
     * @dev 更新操作配置
     */
    function updateConfig(
        OperationType operationType,
        uint256 requiredSignatures,
        uint256 validityPeriod,
        bool isActive
    ) external onlyRole(ADMIN_ROLE) {
        require(requiredSignatures <= signers.length, "MultiSigGovernance: required signatures too high");
        require(requiredSignatures > 0, "MultiSigGovernance: required signatures must be positive");

        configs[operationType] = MultiSigConfig({
            requiredSignatures: requiredSignatures,
            validityPeriod: validityPeriod,
            isActive: isActive
        });

        emit ConfigUpdated(operationType, requiredSignatures, validityPeriod);
    }

    /**
     * @dev 切换紧急模式
     */
    function toggleEmergencyMode() external onlyRole(EMERGENCY_ROLE) {
        emergencyMode = !emergencyMode;
        
        if (emergencyMode) {
            emergencyModeActivatedAt = block.timestamp;
        } else {
            emergencyModeActivatedAt = 0;
        }

        emit EmergencyModeToggled(emergencyMode, msg.sender);
    }

    /**
     * @dev 自动退出紧急模式
     */
    function checkEmergencyModeExpiry() external {
        if (emergencyMode && 
            block.timestamp >= emergencyModeActivatedAt + EMERGENCY_MODE_DURATION) {
            emergencyMode = false;
            emergencyModeActivatedAt = 0;
            emit EmergencyModeToggled(false, address(0));
        }
    }

    /**
     * @dev 获取交易哈希
     */
    function getTransactionHash(uint256 transactionId) public view returns (bytes32) {
        Transaction storage txn = transactions[transactionId];
        return keccak256(abi.encodePacked(
            txn.target,
            txn.value,
            txn.data,
            txn.operationType,
            txn.createdAt,
            address(this)
        ));
    }

    /**
     * @dev 检查交易是否可执行
     */
    function isTransactionExecutable(uint256 transactionId) external view returns (bool) {
        Transaction storage txn = transactions[transactionId];
        
        if (txn.createdAt == 0 || txn.executed || txn.cancelled) {
            return false;
        }
        
        if (block.timestamp > txn.validUntil) {
            return false;
        }
        
        uint256 requiredSignatures = configs[txn.operationType].requiredSignatures;
        return txn.signers.length >= requiredSignatures;
    }

    /**
     * @dev 获取等待中的交易
     */
    function getPendingTransactions() external view returns (uint256[] memory) {
        uint256 pendingCount = 0;
        
        // 计算等待中的交易数量
        for (uint256 i = 0; i < transactionCount; i++) {
            Transaction storage txn = transactions[i];
            if (!txn.executed && !txn.cancelled && block.timestamp <= txn.validUntil) {
                pendingCount++;
            }
        }
        
        // 构建等待中的交易数组
        uint256[] memory pending = new uint256[](pendingCount);
        uint256 index = 0;
        
        for (uint256 i = 0; i < transactionCount; i++) {
            Transaction storage txn = transactions[i];
            if (!txn.executed && !txn.cancelled && block.timestamp <= txn.validUntil) {
                pending[index] = i;
                index++;
            }
        }
        
        return pending;
    }

    /**
     * @dev 获取交易签名者
     */
    function getTransactionSigners(uint256 transactionId) external view returns (address[] memory) {
        return transactions[transactionId].signers;
    }

    /**
     * @dev 获取签名者列表
     */
    function getSigners() external view returns (address[] memory) {
        return signers;
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 total,
        uint256 executed,
        uint256 cancelled,
        uint256 pending,
        uint256 signerCount
    ) {
        uint256 pendingCount = 0;
        for (uint256 i = 0; i < transactionCount; i++) {
            Transaction storage txn = transactions[i];
            if (!txn.executed && !txn.cancelled && block.timestamp <= txn.validUntil) {
                pendingCount++;
            }
        }
        
        return (transactionCount, executedCount, cancelledCount, pendingCount, signers.length);
    }

    /**
     * @dev 更新签名要求（内部函数）
     */
    function _updateSignatureRequirements() internal {
        uint256 signerCount = signers.length;
        
        configs[OperationType.PARAMETER_CHANGE].requiredSignatures = (signerCount * 60) / 100;
        configs[OperationType.FUND_TRANSFER].requiredSignatures = (signerCount * 80) / 100;
        configs[OperationType.CONTRACT_UPGRADE].requiredSignatures = (signerCount * 80) / 100;
        configs[OperationType.EMERGENCY_ACTION].requiredSignatures = (signerCount * 40) / 100;
        configs[OperationType.CRITICAL_DECISION].requiredSignatures = signerCount;
    }

    /**
     * @dev 接收以太币
     */
    receive() external payable {}
}
