// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title AuditFramework
 * @dev 专业安全审计框架 - 支持多轮审计和持续监控
 * 
 * 功能：
 * 1. 审计流程管理
 * 2. 漏洞跟踪和修复验证
 * 3. 审计报告存储和验证
 * 4. 持续安全评估
 */
contract AuditFramework is AccessControl, ReentrancyGuard {
    bytes32 public constant AUDITOR_ROLE = keccak256("AUDITOR_ROLE");
    bytes32 public constant AUDIT_ADMIN_ROLE = keccak256("AUDIT_ADMIN_ROLE");
    bytes32 public constant DEVELOPER_ROLE = keccak256("DEVELOPER_ROLE");

    // 审计状态
    enum AuditStatus {
        PLANNED,        // 计划中
        IN_PROGRESS,    // 进行中
        COMPLETED,      // 已完成
        VERIFIED,       // 已验证
        FAILED          // 失败
    }

    // 漏洞严重程度
    enum Severity {
        INFO,           // 信息
        LOW,            // 低危
        MEDIUM,         // 中危
        HIGH,           // 高危
        CRITICAL        // 严重
    }

    // 修复状态
    enum FixStatus {
        OPEN,           // 未修复
        IN_PROGRESS,    // 修复中
        FIXED,          // 已修复
        VERIFIED,       // 已验证
        WONT_FIX,       // 不修复
        FALSE_POSITIVE  // 误报
    }

    // 审计记录
    struct AuditRecord {
        uint256 id;
        string name;
        string description;
        address auditor;
        address targetContract;
        uint256 startTime;
        uint256 endTime;
        AuditStatus status;
        string reportHash;      // IPFS哈希
        uint256 findingsCount;
        uint256 criticalCount;
        uint256 highCount;
        uint256 mediumCount;
        uint256 lowCount;
        uint256 cost;
        bool isPaid;
    }

    // 审计发现
    struct Finding {
        uint256 id;
        uint256 auditId;
        string title;
        string description;
        Severity severity;
        string location;        // 代码位置
        string recommendation;
        FixStatus fixStatus;
        address reportedBy;
        uint256 reportedAt;
        address fixedBy;
        uint256 fixedAt;
        address verifiedBy;
        uint256 verifiedAt;
        string fixDescription;
        string verificationNotes;
    }

    // 审计机构信息
    struct AuditorInfo {
        address auditorAddress;
        string name;
        string website;
        uint256 reputation;     // 0-100
        uint256 auditsCompleted;
        uint256 findingsReported;
        uint256 falsePositives;
        bool isActive;
        uint256 registeredAt;
    }

    // 安全评分
    struct SecurityScore {
        uint256 timestamp;
        uint256 overallScore;   // 0-100
        uint256 codeQuality;
        uint256 testCoverage;
        uint256 auditCoverage;
        uint256 riskLevel;
        string notes;
        address evaluatedBy;
    }

    // 状态变量
    mapping(uint256 => AuditRecord) public audits;
    mapping(uint256 => Finding) public findings;
    mapping(address => AuditorInfo) public auditors;
    mapping(uint256 => SecurityScore) public securityScores;
    mapping(address => uint256[]) public contractAudits; // 合约对应的审计ID列表
    
    uint256 public auditCount;
    uint256 public findingCount;
    uint256 public securityScoreCount;
    uint256 public totalAuditCost;
    
    // 审计要求配置
    struct AuditRequirements {
        uint256 minAuditsRequired;      // 最少审计次数
        uint256 maxFindingAge;          // 最大漏洞存在时间
        uint256 minSecurityScore;       // 最低安全评分
        bool requiresVerification;      // 是否需要验证
    }
    
    AuditRequirements public requirements;

    event AuditScheduled(
        uint256 indexed auditId,
        address indexed auditor,
        address indexed targetContract,
        string name
    );

    event AuditCompleted(
        uint256 indexed auditId,
        address indexed auditor,
        AuditStatus status,
        uint256 findingsCount
    );

    event FindingReported(
        uint256 indexed findingId,
        uint256 indexed auditId,
        Severity indexed severity,
        string title
    );

    event FindingFixed(
        uint256 indexed findingId,
        address indexed fixedBy,
        string fixDescription
    );

    event FindingVerified(
        uint256 indexed findingId,
        address indexed verifiedBy,
        string verificationNotes
    );

    event AuditorRegistered(address indexed auditor, string name);
    event SecurityScoreUpdated(uint256 indexed scoreId, uint256 overallScore);

    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AUDIT_ADMIN_ROLE, admin);
        
        // 设置默认审计要求
        requirements = AuditRequirements({
            minAuditsRequired: 2,
            maxFindingAge: 30 days,
            minSecurityScore: 80,
            requiresVerification: true
        });
    }

    /**
     * @dev 注册审计机构
     */
    function registerAuditor(
        address auditorAddress,
        string calldata name,
        string calldata website
    ) external onlyRole(AUDIT_ADMIN_ROLE) {
        require(auditorAddress != address(0), "AuditFramework: invalid auditor address");
        require(!auditors[auditorAddress].isActive, "AuditFramework: auditor already registered");

        auditors[auditorAddress] = AuditorInfo({
            auditorAddress: auditorAddress,
            name: name,
            website: website,
            reputation: 50, // 初始声誉
            auditsCompleted: 0,
            findingsReported: 0,
            falsePositives: 0,
            isActive: true,
            registeredAt: block.timestamp
        });

        _grantRole(AUDITOR_ROLE, auditorAddress);
        
        emit AuditorRegistered(auditorAddress, name);
    }

    /**
     * @dev 安排审计
     */
    function scheduleAudit(
        string calldata name,
        string calldata description,
        address auditor,
        address targetContract,
        uint256 duration,
        uint256 cost
    ) external onlyRole(AUDIT_ADMIN_ROLE) returns (uint256) {
        require(auditors[auditor].isActive, "AuditFramework: auditor not registered");
        require(targetContract != address(0), "AuditFramework: invalid target contract");

        uint256 auditId = auditCount++;
        
        audits[auditId] = AuditRecord({
            id: auditId,
            name: name,
            description: description,
            auditor: auditor,
            targetContract: targetContract,
            startTime: block.timestamp,
            endTime: block.timestamp + duration,
            status: AuditStatus.PLANNED,
            reportHash: "",
            findingsCount: 0,
            criticalCount: 0,
            highCount: 0,
            mediumCount: 0,
            lowCount: 0,
            cost: cost,
            isPaid: false
        });

        contractAudits[targetContract].push(auditId);
        totalAuditCost += cost;

        emit AuditScheduled(auditId, auditor, targetContract, name);
        
        return auditId;
    }

    /**
     * @dev 开始审计
     */
    function startAudit(uint256 auditId) external onlyRole(AUDITOR_ROLE) {
        AuditRecord storage audit = audits[auditId];
        require(audit.auditor == msg.sender, "AuditFramework: not assigned auditor");
        require(audit.status == AuditStatus.PLANNED, "AuditFramework: audit not in planned status");

        audit.status = AuditStatus.IN_PROGRESS;
    }

    /**
     * @dev 报告审计发现
     */
    function reportFinding(
        uint256 auditId,
        string calldata title,
        string calldata description,
        Severity severity,
        string calldata location,
        string calldata recommendation
    ) external onlyRole(AUDITOR_ROLE) returns (uint256) {
        AuditRecord storage audit = audits[auditId];
        require(audit.auditor == msg.sender, "AuditFramework: not assigned auditor");
        require(audit.status == AuditStatus.IN_PROGRESS, "AuditFramework: audit not in progress");

        uint256 findingId = findingCount++;
        
        findings[findingId] = Finding({
            id: findingId,
            auditId: auditId,
            title: title,
            description: description,
            severity: severity,
            location: location,
            recommendation: recommendation,
            fixStatus: FixStatus.OPEN,
            reportedBy: msg.sender,
            reportedAt: block.timestamp,
            fixedBy: address(0),
            fixedAt: 0,
            verifiedBy: address(0),
            verifiedAt: 0,
            fixDescription: "",
            verificationNotes: ""
        });

        // 更新审计统计
        audit.findingsCount++;
        if (severity == Severity.CRITICAL) audit.criticalCount++;
        else if (severity == Severity.HIGH) audit.highCount++;
        else if (severity == Severity.MEDIUM) audit.mediumCount++;
        else if (severity == Severity.LOW) audit.lowCount++;

        // 更新审计师统计
        auditors[msg.sender].findingsReported++;

        emit FindingReported(findingId, auditId, severity, title);
        
        return findingId;
    }

    /**
     * @dev 完成审计
     */
    function completeAudit(
        uint256 auditId,
        string calldata reportHash
    ) external onlyRole(AUDITOR_ROLE) {
        AuditRecord storage audit = audits[auditId];
        require(audit.auditor == msg.sender, "AuditFramework: not assigned auditor");
        require(audit.status == AuditStatus.IN_PROGRESS, "AuditFramework: audit not in progress");

        audit.status = AuditStatus.COMPLETED;
        audit.reportHash = reportHash;
        audit.endTime = block.timestamp;

        // 更新审计师统计
        auditors[msg.sender].auditsCompleted++;
        
        // 更新声誉（基于发现的漏洞质量）
        _updateAuditorReputation(msg.sender, auditId);

        emit AuditCompleted(auditId, msg.sender, AuditStatus.COMPLETED, audit.findingsCount);
    }

    /**
     * @dev 修复漏洞
     */
    function fixFinding(
        uint256 findingId,
        string calldata fixDescription
    ) external onlyRole(DEVELOPER_ROLE) {
        Finding storage finding = findings[findingId];
        require(finding.fixStatus == FixStatus.OPEN, "AuditFramework: finding not open");

        finding.fixStatus = FixStatus.FIXED;
        finding.fixedBy = msg.sender;
        finding.fixedAt = block.timestamp;
        finding.fixDescription = fixDescription;

        emit FindingFixed(findingId, msg.sender, fixDescription);
    }

    /**
     * @dev 验证修复
     */
    function verifyFix(
        uint256 findingId,
        string calldata verificationNotes,
        bool isVerified
    ) external onlyRole(AUDITOR_ROLE) {
        Finding storage finding = findings[findingId];
        require(finding.fixStatus == FixStatus.FIXED, "AuditFramework: finding not fixed");

        if (isVerified) {
            finding.fixStatus = FixStatus.VERIFIED;
        } else {
            finding.fixStatus = FixStatus.OPEN; // 重新打开
        }
        
        finding.verifiedBy = msg.sender;
        finding.verifiedAt = block.timestamp;
        finding.verificationNotes = verificationNotes;

        emit FindingVerified(findingId, msg.sender, verificationNotes);
    }

    /**
     * @dev 更新安全评分
     */
    function updateSecurityScore(
        address /* targetContract */,
        uint256 codeQuality,
        uint256 testCoverage,
        uint256 auditCoverage,
        uint256 riskLevel,
        string calldata notes
    ) external onlyRole(AUDIT_ADMIN_ROLE) returns (uint256) {
        require(codeQuality <= 100 && testCoverage <= 100 && auditCoverage <= 100 && riskLevel <= 100,
                "AuditFramework: scores must be <= 100");

        uint256 scoreId = securityScoreCount++;
        uint256 overallScore = (codeQuality + testCoverage + auditCoverage + (100 - riskLevel)) / 4;
        
        securityScores[scoreId] = SecurityScore({
            timestamp: block.timestamp,
            overallScore: overallScore,
            codeQuality: codeQuality,
            testCoverage: testCoverage,
            auditCoverage: auditCoverage,
            riskLevel: riskLevel,
            notes: notes,
            evaluatedBy: msg.sender
        });

        emit SecurityScoreUpdated(scoreId, overallScore);
        
        return scoreId;
    }

    /**
     * @dev 检查合约是否满足审计要求
     */
    function checkAuditCompliance(address targetContract) external view returns (
        bool isCompliant,
        string memory reason
    ) {
        uint256[] memory auditIds = contractAudits[targetContract];
        
        // 检查最少审计次数
        if (auditIds.length < requirements.minAuditsRequired) {
            return (false, "Insufficient audit count");
        }
        
        // 检查是否有未修复的严重漏洞
        uint256 openCriticalFindings = 0;
        for (uint256 i = 0; i < auditIds.length; i++) {
            AuditRecord memory audit = audits[auditIds[i]];
            if (audit.status != AuditStatus.COMPLETED && audit.status != AuditStatus.VERIFIED) {
                continue;
            }
            
            // 检查该审计的发现
            for (uint256 j = 0; j < findingCount; j++) {
                Finding memory finding = findings[j];
                if (finding.auditId == auditIds[i] && 
                    finding.severity == Severity.CRITICAL &&
                    finding.fixStatus != FixStatus.VERIFIED &&
                    finding.fixStatus != FixStatus.FALSE_POSITIVE) {
                    openCriticalFindings++;
                }
            }
        }
        
        if (openCriticalFindings > 0) {
            return (false, "Open critical findings exist");
        }
        
        return (true, "Audit requirements met");
    }

    /**
     * @dev 获取合约的审计历史
     */
    function getContractAudits(address targetContract) external view returns (uint256[] memory) {
        return contractAudits[targetContract];
    }

    /**
     * @dev 获取审计的所有发现
     */
    function getAuditFindings(uint256 auditId) external view returns (uint256[] memory) {
        uint256 count = 0;
        
        // 计算该审计的发现数量
        for (uint256 i = 0; i < findingCount; i++) {
            if (findings[i].auditId == auditId) {
                count++;
            }
        }
        
        // 构建发现数组
        uint256[] memory auditFindings = new uint256[](count);
        uint256 index = 0;
        
        for (uint256 i = 0; i < findingCount; i++) {
            if (findings[i].auditId == auditId) {
                auditFindings[index] = i;
                index++;
            }
        }
        
        return auditFindings;
    }

    /**
     * @dev 获取未修复的严重漏洞
     */
    function getCriticalOpenFindings() external view returns (uint256[] memory) {
        uint256 count = 0;
        
        // 计算严重未修复漏洞数量
        for (uint256 i = 0; i < findingCount; i++) {
            Finding memory finding = findings[i];
            if (finding.severity == Severity.CRITICAL && 
                finding.fixStatus != FixStatus.VERIFIED &&
                finding.fixStatus != FixStatus.FALSE_POSITIVE) {
                count++;
            }
        }
        
        // 构建严重漏洞数组
        uint256[] memory criticalFindings = new uint256[](count);
        uint256 index = 0;
        
        for (uint256 i = 0; i < findingCount; i++) {
            Finding memory finding = findings[i];
            if (finding.severity == Severity.CRITICAL && 
                finding.fixStatus != FixStatus.VERIFIED &&
                finding.fixStatus != FixStatus.FALSE_POSITIVE) {
                criticalFindings[index] = i;
                index++;
            }
        }
        
        return criticalFindings;
    }

    /**
     * @dev 更新审计师声誉
     */
    function _updateAuditorReputation(address auditor, uint256 auditId) internal {
        AuditorInfo storage auditorInfo = auditors[auditor];
        AuditRecord memory audit = audits[auditId];
        
        // 基于发现的漏洞数量和严重程度调整声誉
        uint256 reputationChange = 0;
        reputationChange += audit.criticalCount * 10;
        reputationChange += audit.highCount * 5;
        reputationChange += audit.mediumCount * 2;
        reputationChange += audit.lowCount * 1;
        
        if (auditorInfo.reputation + reputationChange <= 100) {
            auditorInfo.reputation += reputationChange;
        } else {
            auditorInfo.reputation = 100;
        }
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 totalAudits,
        uint256 totalFindings,
        uint256 openCriticalFindings,
        uint256 totalCost,
        uint256 activeAuditors
    ) {
        uint256 criticalCount = 0;
        uint256 activeAuditorCount = 0;
        
        // 计算未修复的严重漏洞
        for (uint256 i = 0; i < findingCount; i++) {
            Finding memory finding = findings[i];
            if (finding.severity == Severity.CRITICAL && 
                finding.fixStatus != FixStatus.VERIFIED &&
                finding.fixStatus != FixStatus.FALSE_POSITIVE) {
                criticalCount++;
            }
        }
        
        // 这里简化了活跃审计师的计算
        // 实际应该遍历所有注册的审计师
        
        return (auditCount, findingCount, criticalCount, totalAuditCost, activeAuditorCount);
    }
}
