// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title SecurityMonitor
 * @dev 链上安全监控和异常检测系统
 * 
 * 功能：
 * 1. 实时监控关键指标
 * 2. 异常行为检测和告警
 * 3. 自动风险评估
 * 4. 威胁情报收集
 */
contract SecurityMonitor is AccessControl, ReentrancyGuard {
    bytes32 public constant MONITOR_ROLE = keccak256("MONITOR_ROLE");
    bytes32 public constant ANALYST_ROLE = keccak256("ANALYST_ROLE");
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");

    // 告警级别
    enum AlertLevel {
        INFO,       // 信息
        WARNING,    // 警告
        CRITICAL,   // 严重
        EMERGENCY   // 紧急
    }

    // 监控指标类型
    enum MetricType {
        TRANSACTION_VOLUME,     // 交易量
        PRICE_VOLATILITY,       // 价格波动
        LARGE_TRANSFERS,        // 大额转账
        UNUSUAL_PATTERNS,       // 异常模式
        GOVERNANCE_ACTIVITY,    // 治理活动
        STAKING_CHANGES,        // 质押变化
        CONTRACT_INTERACTIONS   // 合约交互
    }

    // 监控指标
    struct Metric {
        MetricType metricType;
        uint256 value;
        uint256 threshold;
        uint256 timestamp;
        bool isActive;
        uint256 alertCount;
        uint256 lastAlertTime;
    }

    // 告警事件
    struct Alert {
        uint256 id;
        AlertLevel level;
        MetricType metricType;
        uint256 value;
        uint256 threshold;
        uint256 timestamp;
        address triggeredBy;
        string description;
        bool acknowledged;
        address acknowledgedBy;
        uint256 acknowledgedAt;
        string response;
    }

    // 风险评估
    struct RiskAssessment {
        uint256 timestamp;
        uint256 overallRiskScore;  // 0-100
        uint256 liquidityRisk;
        uint256 governanceRisk;
        uint256 technicalRisk;
        uint256 marketRisk;
        string riskFactors;
        address assessedBy;
    }

    // 异常行为模式
    struct AnomalyPattern {
        bytes32 patternId;
        string description;
        uint256 detectionCount;
        uint256 lastDetected;
        bool isActive;
        uint256 severity; // 1-10
        address[] suspiciousAddresses;
    }

    // 监控配置
    struct MonitorConfig {
        uint256 alertCooldown;        // 告警冷却期
        uint256 maxAlertsPerHour;     // 每小时最大告警数
        uint256 riskAssessmentInterval; // 风险评估间隔
        bool autoResponse;            // 自动响应
        mapping(MetricType => uint256) thresholds;
    }

    // 状态变量
    mapping(MetricType => Metric) public metrics;
    mapping(uint256 => Alert) public alerts;
    mapping(bytes32 => AnomalyPattern) public anomalyPatterns;
    mapping(uint256 => RiskAssessment) public riskAssessments;
    
    MonitorConfig public config;
    
    uint256 public alertCount;
    uint256 public riskAssessmentCount;
    uint256 public lastRiskAssessment;
    uint256 public alertsInLastHour;
    uint256 public lastHourReset;

    // 监控的合约地址
    mapping(bytes32 => address) public monitoredContracts;
    bytes32[] public contractNames;

    // 白名单地址（可信地址）
    mapping(address => bool) public whitelist;
    
    // 黑名单地址（可疑地址）
    mapping(address => bool) public blacklist;
    mapping(address => uint256) public suspicionScore; // 0-100

    event AlertTriggered(
        uint256 indexed alertId,
        AlertLevel indexed level,
        MetricType indexed metricType,
        uint256 value,
        uint256 threshold,
        string description
    );

    event AlertAcknowledged(
        uint256 indexed alertId,
        address indexed acknowledgedBy,
        string response
    );

    event AnomalyDetected(
        bytes32 indexed patternId,
        string description,
        address[] suspiciousAddresses,
        uint256 severity
    );

    event RiskAssessmentCompleted(
        uint256 indexed assessmentId,
        uint256 overallRiskScore,
        string riskFactors
    );

    event ThresholdUpdated(MetricType indexed metricType, uint256 oldThreshold, uint256 newThreshold);
    event AddressWhitelisted(address indexed addr);
    event AddressBlacklisted(address indexed addr);
    event SuspicionScoreUpdated(address indexed addr, uint256 oldScore, uint256 newScore);

    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(ADMIN_ROLE, admin);
        _grantRole(MONITOR_ROLE, admin);
        _grantRole(ANALYST_ROLE, admin);

        // 初始化配置
        config.alertCooldown = 1 hours;
        config.maxAlertsPerHour = 10;
        config.riskAssessmentInterval = 6 hours;
        config.autoResponse = false;

        // 设置默认阈值
        _setDefaultThresholds();
        
        lastHourReset = block.timestamp;
    }

    /**
     * @dev 更新监控指标
     */
    function updateMetric(
        MetricType metricType,
        uint256 value
    ) external onlyRole(MONITOR_ROLE) {
        Metric storage metric = metrics[metricType];
        metric.metricType = metricType;
        metric.value = value;
        metric.timestamp = block.timestamp;
        metric.isActive = true;

        // 检查是否超过阈值
        if (value > metric.threshold) {
            _triggerAlert(metricType, value, metric.threshold);
        }

        // 检查异常模式
        _checkAnomalyPatterns(metricType, value);
    }

    /**
     * @dev 触发告警
     */
    function _triggerAlert(
        MetricType metricType,
        uint256 value,
        uint256 threshold
    ) internal {
        // 检查告警冷却期
        Metric storage metric = metrics[metricType];
        if (block.timestamp < metric.lastAlertTime + config.alertCooldown) {
            return;
        }

        // 检查每小时告警限制
        _resetHourlyAlertCount();
        if (alertsInLastHour >= config.maxAlertsPerHour) {
            return;
        }

        // 确定告警级别
        AlertLevel level = _calculateAlertLevel(metricType, value, threshold);
        
        uint256 alertId = alertCount++;
        Alert storage alert = alerts[alertId];
        
        alert.id = alertId;
        alert.level = level;
        alert.metricType = metricType;
        alert.value = value;
        alert.threshold = threshold;
        alert.timestamp = block.timestamp;
        alert.triggeredBy = msg.sender;
        alert.description = _generateAlertDescription(metricType, value, threshold);
        alert.acknowledged = false;

        metric.alertCount++;
        metric.lastAlertTime = block.timestamp;
        alertsInLastHour++;

        emit AlertTriggered(alertId, level, metricType, value, threshold, alert.description);

        // 自动响应
        if (config.autoResponse && level >= AlertLevel.CRITICAL) {
            _executeAutoResponse(metricType, level);
        }
    }

    /**
     * @dev 确认告警
     */
    function acknowledgeAlert(
        uint256 alertId,
        string calldata response
    ) external onlyRole(ANALYST_ROLE) {
        Alert storage alert = alerts[alertId];
        require(alert.timestamp > 0, "SecurityMonitor: alert does not exist");
        require(!alert.acknowledged, "SecurityMonitor: alert already acknowledged");

        alert.acknowledged = true;
        alert.acknowledgedBy = msg.sender;
        alert.acknowledgedAt = block.timestamp;
        alert.response = response;

        emit AlertAcknowledged(alertId, msg.sender, response);
    }

    /**
     * @dev 检测异常模式
     */
    function _checkAnomalyPatterns(MetricType metricType, uint256 /* value */) internal {
        // 检测大额转账模式
        if (metricType == MetricType.LARGE_TRANSFERS) {
            bytes32 patternId = keccak256("LARGE_TRANSFER_SPIKE");
            _updateAnomalyPattern(patternId, "Large transfer spike detected", 7);
        }

        // 检测价格操纵模式
        if (metricType == MetricType.PRICE_VOLATILITY) {
            bytes32 patternId = keccak256("PRICE_MANIPULATION");
            _updateAnomalyPattern(patternId, "Potential price manipulation", 8);
        }

        // 检测治理攻击模式
        if (metricType == MetricType.GOVERNANCE_ACTIVITY) {
            bytes32 patternId = keccak256("GOVERNANCE_ATTACK");
            _updateAnomalyPattern(patternId, "Unusual governance activity", 9);
        }
    }

    /**
     * @dev 更新异常模式
     */
    function _updateAnomalyPattern(
        bytes32 patternId,
        string memory description,
        uint256 severity
    ) internal {
        AnomalyPattern storage pattern = anomalyPatterns[patternId];
        
        if (pattern.patternId == bytes32(0)) {
            pattern.patternId = patternId;
            pattern.description = description;
            pattern.severity = severity;
            pattern.isActive = true;
        }
        
        pattern.detectionCount++;
        pattern.lastDetected = block.timestamp;

        emit AnomalyDetected(patternId, description, pattern.suspiciousAddresses, severity);
    }

    /**
     * @dev 执行风险评估
     */
    function performRiskAssessment() external onlyRole(ANALYST_ROLE) returns (uint256) {
        require(
            block.timestamp >= lastRiskAssessment + config.riskAssessmentInterval,
            "SecurityMonitor: assessment interval not met"
        );

        uint256 assessmentId = riskAssessmentCount++;
        RiskAssessment storage assessment = riskAssessments[assessmentId];
        
        assessment.timestamp = block.timestamp;
        assessment.assessedBy = msg.sender;
        
        // 计算各类风险分数
        assessment.liquidityRisk = _calculateLiquidityRisk();
        assessment.governanceRisk = _calculateGovernanceRisk();
        assessment.technicalRisk = _calculateTechnicalRisk();
        assessment.marketRisk = _calculateMarketRisk();
        
        // 计算总体风险分数
        assessment.overallRiskScore = (
            assessment.liquidityRisk +
            assessment.governanceRisk +
            assessment.technicalRisk +
            assessment.marketRisk
        ) / 4;
        
        assessment.riskFactors = _generateRiskFactors(assessment);
        lastRiskAssessment = block.timestamp;

        emit RiskAssessmentCompleted(assessmentId, assessment.overallRiskScore, assessment.riskFactors);
        
        return assessmentId;
    }

    /**
     * @dev 添加地址到白名单
     */
    function addToWhitelist(address addr) external onlyRole(ADMIN_ROLE) {
        whitelist[addr] = true;
        if (blacklist[addr]) {
            blacklist[addr] = false;
        }
        suspicionScore[addr] = 0;
        
        emit AddressWhitelisted(addr);
    }

    /**
     * @dev 添加地址到黑名单
     */
    function addToBlacklist(address addr) external onlyRole(ADMIN_ROLE) {
        blacklist[addr] = true;
        if (whitelist[addr]) {
            whitelist[addr] = false;
        }
        suspicionScore[addr] = 100;
        
        emit AddressBlacklisted(addr);
    }

    /**
     * @dev 更新可疑分数
     */
    function updateSuspicionScore(address addr, uint256 score) external onlyRole(ANALYST_ROLE) {
        require(score <= 100, "SecurityMonitor: score must be <= 100");
        
        uint256 oldScore = suspicionScore[addr];
        suspicionScore[addr] = score;
        
        // 自动加入黑名单
        if (score >= 80 && !whitelist[addr]) {
            blacklist[addr] = true;
        }
        
        emit SuspicionScoreUpdated(addr, oldScore, score);
    }

    /**
     * @dev 更新阈值
     */
    function updateThreshold(MetricType metricType, uint256 newThreshold) external onlyRole(ADMIN_ROLE) {
        uint256 oldThreshold = config.thresholds[metricType];
        config.thresholds[metricType] = newThreshold;
        metrics[metricType].threshold = newThreshold;
        
        emit ThresholdUpdated(metricType, oldThreshold, newThreshold);
    }

    /**
     * @dev 获取活跃告警
     */
    function getActiveAlerts() external view returns (uint256[] memory) {
        uint256 activeCount = 0;
        
        // 计算未确认的告警数量
        for (uint256 i = 0; i < alertCount; i++) {
            if (!alerts[i].acknowledged) {
                activeCount++;
            }
        }
        
        // 构建活跃告警数组
        uint256[] memory activeAlerts = new uint256[](activeCount);
        uint256 index = 0;
        
        for (uint256 i = 0; i < alertCount; i++) {
            if (!alerts[i].acknowledged) {
                activeAlerts[index] = i;
                index++;
            }
        }
        
        return activeAlerts;
    }

    /**
     * @dev 获取高风险地址
     */
    function getHighRiskAddresses() external pure returns (address[] memory) {
        // 这里简化实现，实际应该维护一个动态列表
        address[] memory highRisk = new address[](0);
        return highRisk;
    }

    /**
     * @dev 计算告警级别
     */
    function _calculateAlertLevel(
        MetricType /* metricType */,
        uint256 value,
        uint256 threshold
    ) internal pure returns (AlertLevel) {
        uint256 ratio = (value * 100) / threshold;
        
        if (ratio >= 300) return AlertLevel.EMERGENCY;
        if (ratio >= 200) return AlertLevel.CRITICAL;
        if (ratio >= 150) return AlertLevel.WARNING;
        return AlertLevel.INFO;
    }

    /**
     * @dev 生成告警描述
     */
    function _generateAlertDescription(
        MetricType metricType,
        uint256 /* value */,
        uint256 /* threshold */
    ) internal pure returns (string memory) {
        if (metricType == MetricType.TRANSACTION_VOLUME) {
            return "Transaction volume exceeded threshold";
        } else if (metricType == MetricType.LARGE_TRANSFERS) {
            return "Large transfer detected";
        } else if (metricType == MetricType.PRICE_VOLATILITY) {
            return "High price volatility detected";
        }
        return "Threshold exceeded";
    }

    /**
     * @dev 执行自动响应
     */
    function _executeAutoResponse(MetricType metricType, AlertLevel level) internal {
        // 这里可以实现自动响应逻辑
        // 例如：暂停某些功能、通知紧急管理器等
    }

    /**
     * @dev 重置每小时告警计数
     */
    function _resetHourlyAlertCount() internal {
        if (block.timestamp >= lastHourReset + 1 hours) {
            alertsInLastHour = 0;
            lastHourReset = block.timestamp;
        }
    }

    /**
     * @dev 设置默认阈值
     */
    function _setDefaultThresholds() internal {
        config.thresholds[MetricType.TRANSACTION_VOLUME] = 1000000 * 10**18; // 100万代币
        config.thresholds[MetricType.PRICE_VOLATILITY] = 2000; // 20%
        config.thresholds[MetricType.LARGE_TRANSFERS] = 100000 * 10**18; // 10万代币
        config.thresholds[MetricType.UNUSUAL_PATTERNS] = 100;
        config.thresholds[MetricType.GOVERNANCE_ACTIVITY] = 50;
        config.thresholds[MetricType.STAKING_CHANGES] = 500000 * 10**18; // 50万代币
        config.thresholds[MetricType.CONTRACT_INTERACTIONS] = 1000;
    }

    /**
     * @dev 计算流动性风险
     */
    function _calculateLiquidityRisk() internal pure returns (uint256) {
        // 简化实现，实际应该基于多个指标
        return 20; // 0-100
    }

    /**
     * @dev 计算治理风险
     */
    function _calculateGovernanceRisk() internal pure returns (uint256) {
        return 15;
    }

    /**
     * @dev 计算技术风险
     */
    function _calculateTechnicalRisk() internal pure returns (uint256) {
        return 10;
    }

    /**
     * @dev 计算市场风险
     */
    function _calculateMarketRisk() internal pure returns (uint256) {
        return 25;
    }

    /**
     * @dev 生成风险因子描述
     */
    function _generateRiskFactors(RiskAssessment memory /* assessment */) internal pure returns (string memory) {
        return "Market volatility, governance concentration, technical complexity";
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 totalAlerts,
        uint256 activeAlerts,
        uint256 totalRiskAssessments,
        uint256 lastAssessmentTime,
        uint256 currentRiskScore
    ) {
        uint256 activeCount = 0;
        for (uint256 i = 0; i < alertCount; i++) {
            if (!alerts[i].acknowledged) {
                activeCount++;
            }
        }
        
        uint256 currentRisk = 0;
        if (riskAssessmentCount > 0) {
            currentRisk = riskAssessments[riskAssessmentCount - 1].overallRiskScore;
        }
        
        return (alertCount, activeCount, riskAssessmentCount, lastRiskAssessment, currentRisk);
    }
}
