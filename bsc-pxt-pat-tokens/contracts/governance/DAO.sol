// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

// This contract is non-upgradeable, all upgrade-related functions have been completely removed to enhance security and user trust

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/Context.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";
import "../staking/StakingPool.sol";
import "./ProposalManager.sol";

contract DAO is Context, Ownable, Pausable, ReentrancyGuard {
    IPXT public pxtoken;
    IPAT public patoken;
    StakingPool public stakingPool;
    ProposalManager public proposalManager;
    address public treasury;
    uint256 private constant MAX_PAGE_SIZE = 100;

    struct Member {
        bool isActive;
        uint40 joinTime;
        uint40 lastActivityTime;
        uint16 proposalsCreated;
        uint16 proposalsVoted;
    }

    mapping(address => Member) public members;
    mapping(string => address) public contracts;

    struct SystemParameters {
        uint64 treasuryThreshold;
        uint64 rewardPerVote;
        uint64 rewardPerProposal;
        uint64 emergencyThreshold;
        bool emergencyMode;
    }

    SystemParameters public systemParams;
    bool private _paused;

    event MemberJoined(address indexed member);
    event MemberLeft(address indexed member);
    event TreasuryFunded(address indexed funder, uint256 amount);
    event TreasuryWithdrawn(address indexed recipient, uint256 amount);
    event ContractUpgraded(string contractName, address indexed oldContract, address indexed newContract);
    event EmergencyModeActivated(address indexed activator);
    event EmergencyModeDeactivated(address indexed deactivator);
    event SystemParameterUpdated(string paramName, uint256 oldValue, uint256 newValue);
    event RewardDistributed(address indexed member, uint256 amount, string reason);
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    modifier onlyActiveMember() {
        if (!members[_msgSender()].isActive) revert TokenErrors.NotDAOMember();
        _;
        members[_msgSender()].lastActivityTime = uint40(block.timestamp);
    }

    modifier notEmergency() {
        if (systemParams.emergencyMode) revert TokenErrors.EmergencyModeActive();
        _;
    }

    constructor() {
        systemParams = SystemParameters({
            treasuryThreshold: 75,
            rewardPerVote: 1 * 10**18,
            rewardPerProposal: 10 * 10**18,
            emergencyThreshold: 25,
            emergencyMode: false
        });
    }

    function initialize(
        address _pxtoken,
        address _patoken,
        address _stakingPool,
        address _proposalManager
    ) external {
        require(contracts["PXT"] == address(0), "Already initialized");

        contracts["PXT"] = _pxtoken;
        contracts["PAT"] = _patoken;
        contracts["StakingPool"] = _stakingPool;
        contracts["ProposalManager"] = _proposalManager;
    }

    function joinDAO() external whenNotPaused nonReentrant {
        if (members[_msgSender()].isActive) revert TokenErrors.AlreadyDAOMember();

        (uint256 stakedAmount, , , , , , , , ) = stakingPool.getUserStakingInfo(_msgSender());
        if (stakedAmount == 0) revert TokenErrors.InsufficientStakingAmount();

        members[_msgSender()] = Member({
            isActive: true,
            joinTime: uint40(block.timestamp),
            lastActivityTime: uint40(block.timestamp),
            proposalsCreated: 0,
            proposalsVoted: 0
        });

        emit MemberJoined(_msgSender());
    }

    function leaveDAO() external onlyActiveMember whenNotPaused nonReentrant {
        if (!proposalManager.isLockReleased(_msgSender())) {
            revert TokenErrors.LockedProposal();
        }

        members[_msgSender()].isActive = false;

        emit MemberLeft(_msgSender());
    }
    
    function fundTreasury() external payable whenNotPaused nonReentrant {
        if (msg.value == 0) revert TokenErrors.ZeroAmount();

        address funder = _msgSender();
        uint256 amount = msg.value;

        (bool success, ) = treasury.call{value: msg.value}("");
        if (!success) revert TokenErrors.TransferFailed();

        emit TreasuryFunded(funder, amount);
    }

    function withdrawFromTreasury(
        uint256 _amount,
        address _recipient
    ) external onlyOwner whenNotPaused nonReentrant {
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();

        address recipient = _recipient;
        uint256 amount = _amount;

        ITreasury(treasury).withdraw(recipient, amount);

        emit TreasuryWithdrawn(recipient, amount);
    }

    function checkEmergencyThreshold() external whenNotPaused nonReentrant notEmergency {
        uint256 totalStaked = stakingPool.totalStaked();
        uint256 totalSupply = pxtoken.totalSupply();

        if (totalSupply == 0) revert TokenErrors.DivisionByZero();

        if (totalStaked * 100 / totalSupply >= systemParams.emergencyThreshold) {
            systemParams.emergencyMode = true;

            emit EmergencyModeActivated(_msgSender());
        }
    }

    function deactivateEmergencyMode() external onlyOwner whenNotPaused nonReentrant {
        if (!systemParams.emergencyMode) revert TokenErrors.NotInEmergencyMode();

        systemParams.emergencyMode = false;

        emit EmergencyModeDeactivated(_msgSender());
    }
    
    function updateSystemParameter(string memory _paramName, uint256 _value) external onlyOwner nonReentrant {
        if (bytes(_paramName).length == 0) revert TokenErrors.InvalidParameter();

        uint256 oldValue;

        if (keccak256(bytes(_paramName)) == keccak256(bytes("treasuryThreshold"))) {
            if (_value > 100) revert TokenErrors.InvalidPercentage();
            oldValue = systemParams.treasuryThreshold;
            systemParams.treasuryThreshold = uint64(_value);
        } else if (keccak256(bytes(_paramName)) == keccak256(bytes("rewardPerVote"))) {
            oldValue = systemParams.rewardPerVote;
            systemParams.rewardPerVote = uint64(_value);
        } else if (keccak256(bytes(_paramName)) == keccak256(bytes("rewardPerProposal"))) {
            oldValue = systemParams.rewardPerProposal;
            systemParams.rewardPerProposal = uint64(_value);
        } else if (keccak256(bytes(_paramName)) == keccak256(bytes("emergencyThreshold"))) {
            if (_value > 100) revert TokenErrors.InvalidPercentage();
            oldValue = systemParams.emergencyThreshold;
            systemParams.emergencyThreshold = uint64(_value);
        } else {
            revert TokenErrors.InvalidParameter();
        }

        emit SystemParameterUpdated(_paramName, oldValue, _value);
    }

    function distributeReward(address _member, uint256 _amount, string memory _reason) external onlyOwner nonReentrant {
        if (_member == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (!members[_member].isActive) revert TokenErrors.NotDAOMember();

        address member = _member;
        uint256 amount = _amount;
        string memory reason = _reason;

        if (!patoken.transfer(member, amount)) revert TokenErrors.TransferFailed();

        emit RewardDistributed(member, amount, reason);
    }
    
    function getMemberStats() external view returns (
        uint256 activeMemberCount,
        uint256 totalProposalsCreated,
        uint256 totalProposalsVoted
    ) {
        if (members[_msgSender()].isActive) {
            activeMemberCount = 1;
            totalProposalsCreated = members[_msgSender()].proposalsCreated;
            totalProposalsVoted = members[_msgSender()].proposalsVoted;
        }
    }

    function getStakingLevelName(StakingPool.StakingLevel _level) external pure returns (string memory) {
        if (_level == StakingPool.StakingLevel.DingJi) {
            return "DingJi";
        } else if (_level == StakingPool.StakingLevel.ChengJi) {
            return "ChengJi";
        } else if (_level == StakingPool.StakingLevel.YiJi) {
            return "YiJi";
        } else if (_level == StakingPool.StakingLevel.JiaJi) {
            return "JiaJi";
        } else if (_level == StakingPool.StakingLevel.ShiJue) {
            return "ShiJue";
        } else if (_level == StakingPool.StakingLevel.ShuangShiJue) {
            return "ShuangShiJue";
        } else if (_level == StakingPool.StakingLevel.ZhiZun) {
            return "ZhiZun";
        }
        return "UNKNOWN";
    }

    function getUserStakingLevel(address _user) external view returns (StakingPool.StakingLevel) {
        if (_user == address(0)) revert TokenErrors.ZeroAddress();
        (uint256 amount, , , , , , , , ) = stakingPool.getUserStakingInfo(_user);
        return stakingPool.determineStakingLevel(amount);
    }

    function getTreasuryBalance() external view returns (uint256) {
        return address(treasury).balance;
    }

    function pause() external onlyOwner {
        _paused = true;
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyOwner {
        _paused = false;
        emit ContractUnpaused(msg.sender);
    }

    function getMembers(uint256 /* __offset */, uint256 _limit) external pure returns (address[] memory) {
        if (_limit > MAX_PAGE_SIZE) {
            _limit = MAX_PAGE_SIZE;
        }

        address[] memory result = new address[](0);
        return result;
    }

    function updateTreasury(address _newTreasury) external onlyOwner nonReentrant {
        if (_newTreasury == address(0)) revert TokenErrors.ZeroAddress();

        address oldTreasury = treasury;
        treasury = _newTreasury;

        contracts["Treasury"] = _newTreasury;

        emit ContractUpgraded("Treasury", oldTreasury, _newTreasury);
    }
}

interface ITreasury {
    function withdraw(address to, uint256 amount) external;
}