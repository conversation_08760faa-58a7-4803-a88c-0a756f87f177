// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "../interfaces/TokenErrors.sol";

contract Treasury is Ownable, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;

    address public daoContract;

    struct Expenditure {
        uint40 timestamp;
        address recipient;
        address token;
        uint128 amount;
        string purpose;
    }

    Expenditure[] public expenditures;
    uint256 public lastExpenditureTime;
    uint256 public constant EXPENDITURE_COOLDOWN = 24 hours;
    uint256 public constant MAX_EXPENDITURE_PERCENT = 10;
    uint256 private constant MAX_PAGE_SIZE = 100;

    event FundsReceived(address indexed sender, uint256 amount);
    event TokensReceived(address indexed sender, address indexed token, uint256 amount);
    event FundsWithdrawn(address indexed recipient, uint256 amount, string purpose);
    event TokensWithdrawn(address indexed recipient, address indexed token, uint256 amount, string purpose);
    event DAOContractUpdated(address indexed oldDAO, address indexed newDAO);
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    modifier onlyDAO() {
        if (_msgSender() != daoContract) revert TokenErrors.Unauthorized();
        _;
    }

    constructor(address _daoContract) {
        if (_daoContract == address(0)) revert TokenErrors.ZeroAddress();
        daoContract = _daoContract;
    }

    function updateDAOContract(address _newDAOContract) external onlyOwner nonReentrant {
        if (_newDAOContract == address(0)) revert TokenErrors.ZeroAddress();

        address oldDAO = daoContract;

        daoContract = _newDAOContract;

        emit DAOContractUpdated(oldDAO, _newDAOContract);
    }

    function withdraw(
        address _recipient,
        uint256 _amount
    ) external onlyDAO whenNotPaused nonReentrant {
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_amount > address(this).balance) revert TokenErrors.InsufficientBalance();
        if (_amount > type(uint128).max) revert TokenErrors.ValueTooLarge();

        checkExpenditureLimits(_amount, address(this).balance);

        address recipient = _recipient;
        uint256 amount = _amount;

        expenditures.push(Expenditure({
            timestamp: uint40(block.timestamp),
            recipient: recipient,
            token: address(0),
            amount: uint128(amount),
            purpose: "ETH Withdrawal"
        }));

        lastExpenditureTime = block.timestamp;

        (bool success, ) = recipient.call{value: amount}("");
        if (!success) revert TokenErrors.TransferFailed();

        emit FundsWithdrawn(recipient, amount, "ETH Withdrawal");
    }

    function withdrawToken(
        address _recipient,
        address _token,
        uint256 _amount,
        string memory _purpose
    ) external onlyDAO whenNotPaused nonReentrant {
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_amount > type(uint128).max) revert TokenErrors.ValueTooLarge();
        if (bytes(_purpose).length == 0) revert TokenErrors.EmptyString();

        IERC20 token = IERC20(_token);

        uint256 balance = token.balanceOf(address(this));
        if (_amount > balance) revert TokenErrors.InsufficientBalance();

        checkExpenditureLimits(_amount, balance);

        address recipient = _recipient;
        address tokenAddress = _token;
        uint256 amount = _amount;
        string memory purpose = _purpose;

        expenditures.push(Expenditure({
            timestamp: uint40(block.timestamp),
            recipient: recipient,
            token: tokenAddress,
            amount: uint128(amount),
            purpose: purpose
        }));

        lastExpenditureTime = block.timestamp;

        token.safeTransfer(recipient, amount);

        emit TokensWithdrawn(recipient, tokenAddress, amount, purpose);
    }

    function checkExpenditureLimits(
        uint256 _amount,
        uint256 _totalBalance
    ) internal view {
        if (lastExpenditureTime != 0) {
            if (block.timestamp < lastExpenditureTime + EXPENDITURE_COOLDOWN) {
                revert TokenErrors.CooldownPeriodNotElapsed();
            }
        }

        if (_totalBalance == 0) revert TokenErrors.ZeroBalance();
        uint256 maxAmount = _totalBalance * MAX_EXPENDITURE_PERCENT / 100;
        if (_amount > maxAmount) {
            revert TokenErrors.MaxExpenditureLimitExceeded();
        }
    }

    function getExpendituresCount() external view returns (uint256) {
        return expenditures.length;
    }

    function getTokenBalance(address _token) external view returns (uint256) {
        if (_token == address(0)) {
            return address(this).balance;
        } else {
            return IERC20(_token).balanceOf(address(this));
        }
    }

    receive() external payable {
        emit FundsReceived(_msgSender(), msg.value);
    }

    function depositToken(address _token, uint256 _amount) external whenNotPaused nonReentrant {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        address sender = _msgSender();
        address tokenAddress = _token;
        uint256 amount = _amount;

        IERC20(tokenAddress).safeTransferFrom(sender, address(this), amount);

        emit TokensReceived(sender, tokenAddress, amount);
    }

    function getExpendituresPaged(uint256 offset, uint256 limit) external view returns (Expenditure[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = expenditures.length;
        if (offset >= total) return new Expenditure[](0);

        uint256 end = offset + limit > total ? total : offset + limit;
        Expenditure[] memory result = new Expenditure[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = expenditures[i];
            unchecked { i++; }
        }

        return result;
    }

    function getTokenBalances(address[] memory tokens) external view returns (uint256[] memory) {
        uint256[] memory balances = new uint256[](tokens.length);

        for (uint256 i = 0; i < tokens.length;) {
            if (tokens[i] == address(0)) {
                balances[i] = address(this).balance;
            } else {
                balances[i] = IERC20(tokens[i]).balanceOf(address(this));
            }
            unchecked { i++; }
        }

        return balances;
    }

    function filterExpendituresByTime(uint256 startTime, uint256 endTime) external view returns (uint256[] memory) {
        if (endTime < startTime) revert TokenErrors.InvalidTimeRange();

        uint256 count = 0;
        for (uint256 i = 0; i < expenditures.length;) {
            if (expenditures[i].timestamp >= startTime && expenditures[i].timestamp <= endTime) {
                count++;
            }
            unchecked { i++; }
        }

        uint256[] memory result = new uint256[](count);

        uint256 index = 0;
        for (uint256 i = 0; i < expenditures.length;) {
            if (expenditures[i].timestamp >= startTime && expenditures[i].timestamp <= endTime) {
                result[index] = i;
                unchecked { index++; }
            }
            unchecked { i++; }
        }

        return result;
    }

    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }
}