// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title AutoChainManager
 * @dev 自动上链管理合约 - 实现PAT代币的自动化上链功能
 * 
 * 核心功能：
 * 1. 自动监控系统账户PAT余额
 * 2. 当余额低于阈值时自动补充
 * 3. 支持多种触发条件和策略
 * 4. 提供紧急干预机制
 */
contract AutoChainManager is AccessControl, ReentrancyGuard, Pausable {
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant KEEPER_ROLE = keccak256("KEEPER_ROLE");

    // 自动化配置
    struct AutoConfig {
        uint256 minBalance;           // 最小余额阈值
        uint256 targetBalance;        // 目标补充余额
        uint256 maxSingleTransfer;    // 单次最大转账量
        uint256 cooldownPeriod;       // 冷却期（防止频繁转账）
        bool autoExecute;             // 是否启用自动执行
        bool emergencyMode;           // 紧急模式
    }

    // 转账记录
    struct TransferRecord {
        uint256 timestamp;
        uint256 amount;
        address from;
        address to;
        string reason;
        bool success;
    }

    IPAT public immutable patToken;
    address public systemAccount;        // 系统账户地址
    address public treasuryAccount;      // 国库账户地址
    
    AutoConfig public config;
    uint256 public lastTransferTime;
    uint256 public totalAutoTransferred;
    uint256 public transferCount;
    
    TransferRecord[] public transferHistory;
    
    // 事件
    event AutoTransferExecuted(
        address indexed from,
        address indexed to,
        uint256 amount,
        string reason,
        uint256 timestamp
    );
    
    event ConfigUpdated(
        uint256 minBalance,
        uint256 targetBalance,
        uint256 maxSingleTransfer,
        uint256 cooldownPeriod,
        bool autoExecute
    );
    
    event EmergencyModeToggled(bool enabled);
    event SystemAccountUpdated(address indexed oldAccount, address indexed newAccount);
    event TreasuryAccountUpdated(address indexed oldAccount, address indexed newAccount);

    constructor(
        address _patToken,
        address _systemAccount,
        address _treasuryAccount,
        address _admin
    ) {
        if (_patToken == address(0)) revert TokenErrors.ZeroAddress();
        if (_systemAccount == address(0)) revert TokenErrors.ZeroAddress();
        if (_treasuryAccount == address(0)) revert TokenErrors.ZeroAddress();
        if (_admin == address(0)) revert TokenErrors.ZeroAddress();

        patToken = IPAT(_patToken);
        systemAccount = _systemAccount;
        treasuryAccount = _treasuryAccount;

        // 设置默认配置
        config = AutoConfig({
            minBalance: 1000 * 10**18,      // 1000 PAT
            targetBalance: 5000 * 10**18,   // 5000 PAT
            maxSingleTransfer: 10000 * 10**18, // 10000 PAT
            cooldownPeriod: 1 hours,        // 1小时冷却期
            autoExecute: true,              // 默认启用自动执行
            emergencyMode: false            // 默认关闭紧急模式
        });

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(OPERATOR_ROLE, _admin);
    }

    /**
     * @dev 检查是否需要自动转账
     */
    function checkAutoTransfer() external view returns (
        bool needsTransfer,
        uint256 currentBalance,
        uint256 transferAmount,
        string memory reason
    ) {
        currentBalance = patToken.balanceOf(systemAccount);
        
        if (!config.autoExecute || config.emergencyMode) {
            return (false, currentBalance, 0, "Auto transfer disabled");
        }
        
        if (block.timestamp < lastTransferTime + config.cooldownPeriod) {
            return (false, currentBalance, 0, "Cooldown period active");
        }
        
        if (currentBalance >= config.minBalance) {
            return (false, currentBalance, 0, "Balance sufficient");
        }
        
        transferAmount = config.targetBalance - currentBalance;
        if (transferAmount > config.maxSingleTransfer) {
            transferAmount = config.maxSingleTransfer;
        }
        
        return (true, currentBalance, transferAmount, "Balance below threshold");
    }

    /**
     * @dev 执行自动转账（可由Keeper或管理员调用）
     */
    function executeAutoTransfer() external nonReentrant whenNotPaused {
        require(
            hasRole(KEEPER_ROLE, msg.sender) || hasRole(OPERATOR_ROLE, msg.sender),
            "AutoChainManager: Insufficient permissions"
        );

        (bool needsTransfer, , uint256 transferAmount, string memory reason) =
            this.checkAutoTransfer();
        
        if (!needsTransfer) {
            revert TokenErrors.InvalidOperation();
        }

        // 检查国库余额
        uint256 treasuryBalance = patToken.balanceOf(treasuryAccount);
        if (treasuryBalance < transferAmount) {
            revert TokenErrors.InsufficientBalance();
        }

        // 执行转账
        bool success = _executeTransfer(treasuryAccount, systemAccount, transferAmount, reason);
        
        if (success) {
            lastTransferTime = block.timestamp;
            totalAutoTransferred += transferAmount;
            transferCount++;
            
            emit AutoTransferExecuted(
                treasuryAccount,
                systemAccount,
                transferAmount,
                reason,
                block.timestamp
            );
        }
    }

    /**
     * @dev 手动紧急转账
     */
    function emergencyTransfer(
        address from,
        address to,
        uint256 amount,
        string calldata reason
    ) external onlyRole(OPERATOR_ROLE) nonReentrant {
        require(config.emergencyMode, "AutoChainManager: Emergency mode not active");
        
        bool success = _executeTransfer(from, to, amount, reason);
        
        if (success) {
            emit AutoTransferExecuted(from, to, amount, reason, block.timestamp);
        }
    }

    /**
     * @dev 内部转账执行函数
     */
    function _executeTransfer(
        address from,
        address to,
        uint256 amount,
        string memory reason
    ) internal returns (bool success) {
        try patToken.transferFrom(from, to, amount) returns (bool result) {
            success = result;
        } catch {
            success = false;
        }
        
        // 记录转账历史
        transferHistory.push(TransferRecord({
            timestamp: block.timestamp,
            amount: amount,
            from: from,
            to: to,
            reason: reason,
            success: success
        }));
        
        return success;
    }

    /**
     * @dev 更新自动化配置
     */
    function updateConfig(
        uint256 _minBalance,
        uint256 _targetBalance,
        uint256 _maxSingleTransfer,
        uint256 _cooldownPeriod,
        bool _autoExecute
    ) external onlyRole(OPERATOR_ROLE) {
        if (_minBalance == 0) revert TokenErrors.ZeroAmount();
        if (_targetBalance <= _minBalance) revert TokenErrors.InvalidOperation();
        if (_maxSingleTransfer == 0) revert TokenErrors.ZeroAmount();
        
        config.minBalance = _minBalance;
        config.targetBalance = _targetBalance;
        config.maxSingleTransfer = _maxSingleTransfer;
        config.cooldownPeriod = _cooldownPeriod;
        config.autoExecute = _autoExecute;
        
        emit ConfigUpdated(
            _minBalance,
            _targetBalance,
            _maxSingleTransfer,
            _cooldownPeriod,
            _autoExecute
        );
    }

    /**
     * @dev 切换紧急模式
     */
    function toggleEmergencyMode() external onlyRole(OPERATOR_ROLE) {
        config.emergencyMode = !config.emergencyMode;
        emit EmergencyModeToggled(config.emergencyMode);
    }

    /**
     * @dev 更新系统账户地址
     */
    function updateSystemAccount(address _newAccount) external onlyRole(OPERATOR_ROLE) {
        if (_newAccount == address(0)) revert TokenErrors.ZeroAddress();
        
        address oldAccount = systemAccount;
        systemAccount = _newAccount;
        
        emit SystemAccountUpdated(oldAccount, _newAccount);
    }

    /**
     * @dev 更新国库账户地址
     */
    function updateTreasuryAccount(address _newAccount) external onlyRole(OPERATOR_ROLE) {
        if (_newAccount == address(0)) revert TokenErrors.ZeroAddress();
        
        address oldAccount = treasuryAccount;
        treasuryAccount = _newAccount;
        
        emit TreasuryAccountUpdated(oldAccount, _newAccount);
    }

    /**
     * @dev 获取转账历史记录数量
     */
    function getTransferHistoryCount() external view returns (uint256) {
        return transferHistory.length;
    }

    /**
     * @dev 获取最近的转账记录
     */
    function getRecentTransfers(uint256 count) external view returns (TransferRecord[] memory) {
        uint256 total = transferHistory.length;
        if (count > total) count = total;
        
        TransferRecord[] memory recent = new TransferRecord[](count);
        for (uint256 i = 0; i < count; i++) {
            recent[i] = transferHistory[total - 1 - i];
        }
        
        return recent;
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyRole(OPERATOR_ROLE) {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyRole(OPERATOR_ROLE) {
        _unpause();
    }
}
