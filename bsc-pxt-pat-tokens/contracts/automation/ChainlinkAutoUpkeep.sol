// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title ChainlinkAutoUpkeep
 * @dev Chainlink Automation兼容的PAT自动上链合约
 *
 * 功能：
 * 1. 实现Chainlink Automation接口
 * 2. 自动监控和补充PAT余额
 * 3. 支持多种触发条件
 * 4. Gas优化的批量操作
 */

// Chainlink Automation接口定义
interface AutomationCompatibleInterface {
    function checkUpkeep(bytes calldata checkData) external returns (bool upkeepNeeded, bytes memory performData);
    function performUpkeep(bytes calldata performData) external;
}

contract ChainlinkAutoUpkeep is AutomationCompatibleInterface, Ownable, ReentrancyGuard {
    
    IPAT public immutable patToken;
    
    // 监控的账户配置
    struct MonitoredAccount {
        address account;              // 监控的账户地址
        uint256 minBalance;          // 最小余额阈值
        uint256 targetBalance;       // 目标余额
        uint256 maxRefillAmount;     // 最大单次补充量
        bool isActive;               // 是否激活监控
        uint256 lastRefillTime;      // 上次补充时间
        uint256 cooldownPeriod;      // 冷却期
    }
    
    // 资金来源配置
    struct FundingSource {
        address sourceAccount;       // 资金来源账户
        uint256 maxDailyAmount;     // 每日最大转出量
        uint256 dailyUsed;          // 今日已使用量
        uint256 lastResetTime;      // 上次重置时间
        bool isActive;              // 是否激活
    }
    
    mapping(uint256 => MonitoredAccount) public monitoredAccounts;
    mapping(uint256 => FundingSource) public fundingSources;
    
    uint256 public monitoredAccountCount;
    uint256 public fundingSourceCount;
    uint256 public lastUpkeepTime;
    uint256 public upkeepInterval = 300; // 5分钟检查间隔
    
    // 统计数据
    uint256 public totalRefills;
    uint256 public totalAmountRefilled;
    
    event AccountRefilled(
        uint256 indexed accountId,
        address indexed account,
        uint256 amount,
        uint256 newBalance,
        uint256 timestamp
    );
    
    event MonitoredAccountAdded(
        uint256 indexed accountId,
        address indexed account,
        uint256 minBalance,
        uint256 targetBalance
    );
    
    event FundingSourceAdded(
        uint256 indexed sourceId,
        address indexed sourceAccount,
        uint256 maxDailyAmount
    );
    
    event UpkeepPerformed(uint256 timestamp, uint256 accountsProcessed);

    constructor(address _patToken) {
        if (_patToken == address(0)) revert TokenErrors.ZeroAddress();
        patToken = IPAT(_patToken);
    }

    /**
     * @dev Chainlink Automation检查函数
     */
    function checkUpkeep(bytes calldata /* checkData */) 
        external 
        view 
        override 
        returns (bool upkeepNeeded, bytes memory performData) 
    {
        // 检查是否到了执行时间
        if (block.timestamp < lastUpkeepTime + upkeepInterval) {
            return (false, "");
        }
        
        // 检查是否有账户需要补充
        uint256[] memory needsRefill = new uint256[](monitoredAccountCount);
        uint256 count = 0;
        
        for (uint256 i = 0; i < monitoredAccountCount; i++) {
            MonitoredAccount storage account = monitoredAccounts[i];
            
            if (!account.isActive) continue;
            
            // 检查冷却期
            if (block.timestamp < account.lastRefillTime + account.cooldownPeriod) {
                continue;
            }
            
            // 检查余额
            uint256 currentBalance = patToken.balanceOf(account.account);
            if (currentBalance < account.minBalance) {
                needsRefill[count] = i;
                count++;
            }
        }
        
        if (count > 0) {
            // 压缩数组
            uint256[] memory accountsToRefill = new uint256[](count);
            for (uint256 i = 0; i < count; i++) {
                accountsToRefill[i] = needsRefill[i];
            }
            
            upkeepNeeded = true;
            performData = abi.encode(accountsToRefill);
        }
    }

    /**
     * @dev Chainlink Automation执行函数
     */
    function performUpkeep(bytes calldata performData) external override nonReentrant {
        uint256[] memory accountsToRefill = abi.decode(performData, (uint256[]));
        
        uint256 processedCount = 0;
        
        for (uint256 i = 0; i < accountsToRefill.length; i++) {
            uint256 accountId = accountsToRefill[i];
            
            if (_refillAccount(accountId)) {
                processedCount++;
            }
        }
        
        lastUpkeepTime = block.timestamp;
        emit UpkeepPerformed(block.timestamp, processedCount);
    }

    /**
     * @dev 内部补充账户函数
     */
    function _refillAccount(uint256 accountId) internal returns (bool success) {
        if (accountId >= monitoredAccountCount) return false;
        
        MonitoredAccount storage account = monitoredAccounts[accountId];
        if (!account.isActive) return false;
        
        uint256 currentBalance = patToken.balanceOf(account.account);
        if (currentBalance >= account.minBalance) return false;
        
        uint256 refillAmount = account.targetBalance - currentBalance;
        if (refillAmount > account.maxRefillAmount) {
            refillAmount = account.maxRefillAmount;
        }
        
        // 寻找可用的资金来源
        address fundingSource = _findAvailableFundingSource(refillAmount);
        if (fundingSource == address(0)) return false;
        
        // 执行转账
        try patToken.transferFrom(fundingSource, account.account, refillAmount) {
            account.lastRefillTime = block.timestamp;
            totalRefills++;
            totalAmountRefilled += refillAmount;
            
            // 更新资金来源的使用量
            _updateFundingSourceUsage(fundingSource, refillAmount);
            
            emit AccountRefilled(
                accountId,
                account.account,
                refillAmount,
                currentBalance + refillAmount,
                block.timestamp
            );
            
            return true;
        } catch {
            return false;
        }
    }

    /**
     * @dev 寻找可用的资金来源
     */
    function _findAvailableFundingSource(uint256 amount) internal view returns (address) {
        for (uint256 i = 0; i < fundingSourceCount; i++) {
            FundingSource storage source = fundingSources[i];
            
            if (!source.isActive) continue;
            
            // 检查每日限额
            uint256 dailyUsed = source.dailyUsed;
            if (block.timestamp >= source.lastResetTime + 1 days) {
                dailyUsed = 0; // 新的一天，重置使用量
            }
            
            if (dailyUsed + amount <= source.maxDailyAmount) {
                // 检查账户余额
                uint256 balance = patToken.balanceOf(source.sourceAccount);
                if (balance >= amount) {
                    return source.sourceAccount;
                }
            }
        }
        
        return address(0);
    }

    /**
     * @dev 更新资金来源使用量
     */
    function _updateFundingSourceUsage(address sourceAccount, uint256 amount) internal {
        for (uint256 i = 0; i < fundingSourceCount; i++) {
            FundingSource storage source = fundingSources[i];
            
            if (source.sourceAccount == sourceAccount) {
                // 检查是否需要重置每日使用量
                if (block.timestamp >= source.lastResetTime + 1 days) {
                    source.dailyUsed = amount;
                    source.lastResetTime = block.timestamp;
                } else {
                    source.dailyUsed += amount;
                }
                break;
            }
        }
    }

    /**
     * @dev 添加监控账户
     */
    function addMonitoredAccount(
        address account,
        uint256 minBalance,
        uint256 targetBalance,
        uint256 maxRefillAmount,
        uint256 cooldownPeriod
    ) external onlyOwner {
        if (account == address(0)) revert TokenErrors.ZeroAddress();
        if (minBalance == 0) revert TokenErrors.ZeroAmount();
        if (targetBalance <= minBalance) revert TokenErrors.InvalidOperation();
        
        uint256 accountId = monitoredAccountCount++;
        
        monitoredAccounts[accountId] = MonitoredAccount({
            account: account,
            minBalance: minBalance,
            targetBalance: targetBalance,
            maxRefillAmount: maxRefillAmount,
            isActive: true,
            lastRefillTime: 0,
            cooldownPeriod: cooldownPeriod
        });
        
        emit MonitoredAccountAdded(accountId, account, minBalance, targetBalance);
    }

    /**
     * @dev 添加资金来源
     */
    function addFundingSource(
        address sourceAccount,
        uint256 maxDailyAmount
    ) external onlyOwner {
        if (sourceAccount == address(0)) revert TokenErrors.ZeroAddress();
        if (maxDailyAmount == 0) revert TokenErrors.ZeroAmount();
        
        uint256 sourceId = fundingSourceCount++;
        
        fundingSources[sourceId] = FundingSource({
            sourceAccount: sourceAccount,
            maxDailyAmount: maxDailyAmount,
            dailyUsed: 0,
            lastResetTime: block.timestamp,
            isActive: true
        });
        
        emit FundingSourceAdded(sourceId, sourceAccount, maxDailyAmount);
    }

    /**
     * @dev 更新监控账户状态
     */
    function updateMonitoredAccountStatus(uint256 accountId, bool isActive) external onlyOwner {
        if (accountId >= monitoredAccountCount) revert TokenErrors.InvalidOperation();
        monitoredAccounts[accountId].isActive = isActive;
    }

    /**
     * @dev 更新资金来源状态
     */
    function updateFundingSourceStatus(uint256 sourceId, bool isActive) external onlyOwner {
        if (sourceId >= fundingSourceCount) revert TokenErrors.InvalidOperation();
        fundingSources[sourceId].isActive = isActive;
    }

    /**
     * @dev 设置检查间隔
     */
    function setUpkeepInterval(uint256 interval) external onlyOwner {
        if (interval == 0) revert TokenErrors.ZeroAmount();
        upkeepInterval = interval;
    }

    /**
     * @dev 手动触发补充（紧急情况）
     */
    function manualRefill(uint256 accountId) external onlyOwner {
        require(_refillAccount(accountId), "ChainlinkAutoUpkeep: Refill failed");
    }

    /**
     * @dev 获取监控账户信息
     */
    function getMonitoredAccount(uint256 accountId) external view returns (
        address account,
        uint256 minBalance,
        uint256 targetBalance,
        uint256 currentBalance,
        bool isActive,
        bool needsRefill
    ) {
        if (accountId >= monitoredAccountCount) revert TokenErrors.InvalidOperation();
        
        MonitoredAccount storage monitoredAccount = monitoredAccounts[accountId];
        currentBalance = patToken.balanceOf(monitoredAccount.account);
        needsRefill = monitoredAccount.isActive && currentBalance < monitoredAccount.minBalance;
        
        return (
            monitoredAccount.account,
            monitoredAccount.minBalance,
            monitoredAccount.targetBalance,
            currentBalance,
            monitoredAccount.isActive,
            needsRefill
        );
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 _totalRefills,
        uint256 _totalAmountRefilled,
        uint256 _monitoredAccountCount,
        uint256 _fundingSourceCount,
        uint256 _lastUpkeepTime
    ) {
        return (
            totalRefills,
            totalAmountRefilled,
            monitoredAccountCount,
            fundingSourceCount,
            lastUpkeepTime
        );
    }
}
