// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

/**
 * @title ContentMetadata
 * @dev 内容元数据处理库 - 学习xLog的元数据标准
 */
library ContentMetadata {
    
    // 内容类型信息结构体
    struct ContentTypeInfo {
        string typeName;        // 类型名称
        uint256 defaultFee;     // 默认费用 (PAT代币，18位精度)
        bool isActive;          // 是否激活
        string description;     // 类型描述
        uint256 createdAt;      // 创建时间
    }
    
    // 通用内容元数据结构 (存储在IPFS) - 支持所有内容类型
    struct Metadata {
        // 基础信息 (所有内容类型通用)
        string title;                // 标题
        string description;          // 描述
        string originalTitle;        // 原标题 (可选)
        string contentType;          // 内容类型
        string[] tags;               // 标签
        string language;             // 语言
        string licenseNumber;        // 许可证号 (可选)
        uint256 releaseDate;         // 发布日期
        string coverImage;           // 封面图片IPFS哈希 (可选)
        string[] attachments;        // 附件IPFS哈希列表 (可选)
        string[] reviewers;          // 审核员列表
        uint256 version;             // 版本号

        // 创作者信息 (灵活数组，支持多种角色)
        string[] creators;           // 创作者列表 (格式: "role:name", 如 "author:张三", "director:李四")
        string[] contributors;       // 贡献者列表 (格式: "role:name", 如 "editor:王五", "translator:赵六")

        // 扩展属性 (键值对，支持任意自定义属性)
        string[] attributeKeys;      // 属性键列表
        string[] attributeValues;    // 属性值列表 (与键一一对应)

        // 数值属性 (用于存储数字类型的属性)
        string[] numericKeys;        // 数值属性键列表
        uint256[] numericValues;     // 数值属性值列表 (与键一一对应)
    }

    // 默认内容类型常量 (用于初始化)
    string constant DEFAULT_VIDEO = "video";
    string constant DEFAULT_NOVEL = "novel";
    string constant DEFAULT_SHORT_DRAMA = "short_drama";
    string constant DEFAULT_ANIME = "anime";
    string constant DEFAULT_MANGA = "manga";
    string constant DEFAULT_MUSIC = "music";
    string constant DEFAULT_ARTICLE = "article";
    string constant DEFAULT_SHORT_VIDEO = "short_video";

    /**
     * @dev 验证内容类型字符串格式
     */
    function isValidContentTypeFormat(string memory contentType) internal pure returns (bool) {
        bytes memory typeBytes = bytes(contentType);

        // 检查长度 (1-50字符)
        if (typeBytes.length == 0 || typeBytes.length > 50) {
            return false;
        }

        // 检查字符 (只允许小写字母、数字、下划线)
        for (uint256 i = 0; i < typeBytes.length; i++) {
            bytes1 char = typeBytes[i];
            if (!(
                (char >= 0x61 && char <= 0x7A) || // a-z
                (char >= 0x30 && char <= 0x39) || // 0-9
                char == 0x5F                       // _
            )) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * @dev 获取默认内容类型费用 (PAT代币，18位精度) - 仅用于初始化
     */
    function getDefaultContentTypeFee(string memory contentType) internal pure returns (uint256) {
        bytes32 typeHash = keccak256(bytes(contentType));

        if (typeHash == keccak256(bytes(DEFAULT_VIDEO))) {
            return 1 * 10**18;      // 1 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_NOVEL))) {
            return 0.1 * 10**18;    // 0.1 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_SHORT_DRAMA))) {
            return 0.25 * 10**18;   // 0.25 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_ANIME))) {
            return 0.8 * 10**18;    // 0.8 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_MANGA))) {
            return 0.3 * 10**18;    // 0.3 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_MUSIC))) {
            return 0.5 * 10**18;    // 0.5 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_ARTICLE))) {
            return 0.05 * 10**18;   // 0.05 PAT
        } else if (typeHash == keccak256(bytes(DEFAULT_SHORT_VIDEO))) {
            return 0.2 * 10**18;    // 0.2 PAT
        } else {
            return 0.1 * 10**18;    // 默认 0.1 PAT
        }
    }

    /**
     * @dev 获取默认内容类型描述
     */
    function getDefaultContentTypeDescription(string memory contentType) internal pure returns (string memory) {
        bytes32 typeHash = keccak256(bytes(contentType));

        if (typeHash == keccak256(bytes(DEFAULT_VIDEO))) {
            return "Video content";
        } else if (typeHash == keccak256(bytes(DEFAULT_NOVEL))) {
            return "Novel content";
        } else if (typeHash == keccak256(bytes(DEFAULT_SHORT_DRAMA))) {
            return "Short drama content";
        } else if (typeHash == keccak256(bytes(DEFAULT_ANIME))) {
            return "Anime content";
        } else if (typeHash == keccak256(bytes(DEFAULT_MANGA))) {
            return "Manga content";
        } else if (typeHash == keccak256(bytes(DEFAULT_MUSIC))) {
            return "Music content";
        } else if (typeHash == keccak256(bytes(DEFAULT_ARTICLE))) {
            return "Article content";
        } else if (typeHash == keccak256(bytes(DEFAULT_SHORT_VIDEO))) {
            return "Short video content";
        } else {
            return "Custom content type";
        }
    }
    
    /**
     * @dev 验证IPFS哈希格式
     */
    function isValidIPFSHash(string memory ipfsHash) internal pure returns (bool) {
        bytes memory hashBytes = bytes(ipfsHash);

        // 基础长度检查 (至少5个字符，最多100个字符)
        if (hashBytes.length < 5 || hashBytes.length > 100) {
            return false;
        }

        // 检查是否为真实IPFS哈希格式
        if (hashBytes.length == 46 && hashBytes[0] == 'Q' && hashBytes[1] == 'm') {
            // IPFS v0: 46字符，以 "Qm" 开头
            return true;
        } else if (hashBytes.length == 59 && hashBytes[0] == 'b' && hashBytes[1] == 'a' &&
                   hashBytes[2] == 'f' && hashBytes[3] == 'y') {
            // IPFS v1: 59字符，以 "bafy" 开头
            return true;
        } else if (hashBytes[0] == 'Q' && hashBytes[1] == 'm') {
            // 宽松验证：以"Qm"开头的测试哈希 (用于开发测试)
            return true;
        } else if (hashBytes.length >= 10 &&
                   hashBytes[0] == 'b' && hashBytes[1] == 'a' && hashBytes[2] == 'f') {
            // 宽松验证：以"baf"开头的测试哈希 (用于开发测试)
            return true;
        }

        return false;
    }
    
    /**
     * @dev 生成内容哈希 (用于防重复)
     */
    function generateContentHash(
        address creator,
        string memory title,
        string memory ipfsHash,
        uint256 timestamp
    ) internal pure returns (bytes32) {
        return keccak256(abi.encodePacked(creator, title, ipfsHash, timestamp));
    }
    
    /**
     * @dev 验证元数据完整性 (基础格式验证)
     */
    function validateMetadata(
        string memory title,
        string memory contentType,
        string memory ipfsHash
    ) internal pure returns (bool) {
        return (
            bytes(title).length > 0 &&
            bytes(title).length <= 500 &&
            isValidContentTypeFormat(contentType) &&
            isValidIPFSHash(ipfsHash)
        );
    }

    /**
     * @dev 验证扩展元数据结构 (合约层面的基础验证)
     */
    function validateExtendedMetadata(Metadata memory metadata) internal pure returns (bool) {
        // 验证基础字段
        if (!validateMetadata(metadata.title, metadata.contentType, "")) {
            return false;
        }

        // 验证数组长度匹配
        if (metadata.attributeKeys.length != metadata.attributeValues.length) {
            return false;
        }

        if (metadata.numericKeys.length != metadata.numericValues.length) {
            return false;
        }

        // 验证数组长度限制 (防止gas过高)
        if (metadata.attributeKeys.length > 50 ||
            metadata.numericKeys.length > 50 ||
            metadata.creators.length > 20 ||
            metadata.contributors.length > 20 ||
            metadata.tags.length > 30 ||
            metadata.attachments.length > 10) {
            return false;
        }

        // 验证属性键名格式
        for (uint256 i = 0; i < metadata.attributeKeys.length; i++) {
            if (!isValidAttributeKey(metadata.attributeKeys[i])) {
                return false;
            }
        }

        for (uint256 i = 0; i < metadata.numericKeys.length; i++) {
            if (!isValidAttributeKey(metadata.numericKeys[i])) {
                return false;
            }
        }

        return true;
    }

    /**
     * @dev 解析创作者角色 (从 "role:name" 格式)
     */
    function parseCreatorRole(string memory creator) internal pure returns (string memory role, string memory name) {
        bytes memory creatorBytes = bytes(creator);
        uint256 colonIndex = 0;
        bool foundColon = false;

        // 查找冒号位置
        for (uint256 i = 0; i < creatorBytes.length; i++) {
            if (creatorBytes[i] == ':') {
                colonIndex = i;
                foundColon = true;
                break;
            }
        }

        if (!foundColon) {
            // 如果没有冒号，默认角色为 "creator"
            return ("creator", creator);
        }

        // 提取角色和姓名
        bytes memory roleBytes = new bytes(colonIndex);
        bytes memory nameBytes = new bytes(creatorBytes.length - colonIndex - 1);

        for (uint256 i = 0; i < colonIndex; i++) {
            roleBytes[i] = creatorBytes[i];
        }

        for (uint256 i = 0; i < nameBytes.length; i++) {
            nameBytes[i] = creatorBytes[colonIndex + 1 + i];
        }

        return (string(roleBytes), string(nameBytes));
    }

    /**
     * @dev 验证属性键名格式 (只允许小写字母、数字、下划线)
     */
    function isValidAttributeKey(string memory key) internal pure returns (bool) {
        bytes memory keyBytes = bytes(key);

        // 检查长度 (1-50字符)
        if (keyBytes.length == 0 || keyBytes.length > 50) {
            return false;
        }

        // 检查字符 (只允许小写字母、数字、下划线)
        for (uint256 i = 0; i < keyBytes.length; i++) {
            bytes1 char = keyBytes[i];
            if (!(
                (char >= 0x61 && char <= 0x7A) || // a-z
                (char >= 0x30 && char <= 0x39) || // 0-9
                char == 0x5F                       // _
            )) {
                return false;
            }
        }

        return true;
    }
}
