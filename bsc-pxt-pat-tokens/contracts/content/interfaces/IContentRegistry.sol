// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

/**
 * @title IContentRegistry
 * @dev 内容注册合约接口 - 学习xLog的IPFS + 链上元数据方案
 */
interface IContentRegistry {
    
    // 内容结构体
    struct ContentInfo {
        uint256 contentId;           // 内容ID
        address creator;             // 创建者地址
        string contentType;          // 内容类型 (video, novel, article等)
        string title;                // 标题
        string ipfsHash;             // IPFS内容哈希
        string metadataURI;          // 元数据URI (IPFS)
        uint256 patFee;              // PAT费用
        uint256 timestamp;           // 创建时间
        bool isLocked;               // 是否锁定 (锁定后不可修改)
        bool isActive;               // 是否激活
        uint256 mintCount;           // 铸造次数
        uint256 totalEarnings;       // 总收益
    }
    
    // 内容统计结构体
    struct ContentStats {
        uint256 totalContents;       // 总内容数
        uint256 activeContents;      // 活跃内容数
        uint256 totalPATConsumed;    // 总PAT消耗
        uint256 totalMints;          // 总铸造次数
    }
    
    // 事件定义
    event ContentRegistered(
        uint256 indexed contentId,
        address indexed creator,
        string contentType,
        string ipfsHash,
        uint256 patFee
    );
    
    event ContentUpdated(
        uint256 indexed contentId,
        string newIpfsHash,
        string newMetadataURI
    );
    
    event ContentLocked(uint256 indexed contentId);
    
    event ContentMinted(
        uint256 indexed contentId,
        address indexed minter,
        uint256 mintPrice,
        uint256 mintId
    );
    
    event ContentDeactivated(uint256 indexed contentId);
    
    event PATFeeUpdated(string contentType, uint256 newFee);

    event ContentTypeAdded(string contentType, uint256 defaultFee, string description);

    event ContentTypeUpdated(string contentType, uint256 newFee, string newDescription);

    event ContentTypeDeactivated(string contentType);

    // 主要功能函数
    function registerContent(
        string memory contentType,
        string memory title,
        string memory ipfsHash,
        string memory metadataURI,
        string[] memory reviewers
    ) external returns (uint256 contentId);
    
    function updateContent(
        uint256 contentId,
        string memory newIpfsHash,
        string memory newMetadataURI
    ) external;
    
    function lockContent(uint256 contentId) external;
    
    function deactivateContent(uint256 contentId) external;
    
    function mintContent(uint256 contentId) external payable returns (uint256 mintId);
    
    // 查询函数
    function getContent(uint256 contentId) external view returns (ContentInfo memory);
    
    function getContentsByCreator(address creator) external view returns (uint256[] memory);
    
    function getContentStats() external view returns (ContentStats memory);
    
    function getContentFee(string memory contentType) external view returns (uint256);
    
    function isContentOwner(uint256 contentId, address user) external view returns (bool);
    
    function getContentCount() external view returns (uint256);

    // 内容类型管理函数
    function addContentType(
        string memory contentType,
        uint256 defaultFee,
        string memory description
    ) external;

    function updateContentType(
        string memory contentType,
        uint256 newFee,
        string memory newDescription
    ) external;

    function deactivateContentType(string memory contentType) external;

    function isContentTypeActive(string memory contentType) external view returns (bool);

    function getContentTypeInfo(string memory contentType) external view returns (
        string memory typeName,
        uint256 defaultFee,
        bool isActive,
        string memory description,
        uint256 createdAt
    );

    function getAllContentTypes() external view returns (string[] memory);

    function getActiveContentTypes() external view returns (string[] memory);

    // 管理函数
    function setContentTypeFee(string memory contentType, uint256 fee) external;
    
    function setTreasuryAddress(address newTreasury) external;
    
    function pause() external;
    
    function unpause() external;
}
