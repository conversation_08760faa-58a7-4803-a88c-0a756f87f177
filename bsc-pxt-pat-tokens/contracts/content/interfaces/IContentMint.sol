// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

/**
 * @title IContentMint
 * @dev 内容铸造NFT合约接口 - 学习xLog的内容铸造机制
 */
interface IContentMint {
    
    // 铸造信息结构体
    struct MintInfo {
        uint256 tokenId;        // NFT ID
        uint256 contentId;      // 内容ID
        address minter;         // 铸造者
        address creator;        // 创作者
        uint256 mintPrice;      // 铸造价格
        uint256 timestamp;      // 铸造时间
        string contentType;     // 内容类型
        string title;           // 内容标题
    }
    
    // 事件定义
    event ContentMinted(
        uint256 indexed tokenId,
        uint256 indexed contentId,
        address indexed minter,
        address creator,
        uint256 mintPrice
    );
    
    event MintPriceUpdated(uint256 indexed contentId, uint256 newPrice);
    
    // 主要功能函数
    function mintContent(uint256 contentId) external payable returns (uint256 tokenId);
    
    function batchMintContent(uint256[] memory contentIds) external payable returns (uint256[] memory tokenIds);
    
    // 查询函数
    function getMintInfo(uint256 tokenId) external view returns (MintInfo memory);
    
    function getContentMints(uint256 contentId) external view returns (uint256[] memory);
    
    function getUserMints(address user) external view returns (uint256[] memory);
    
    function getMintPrice(uint256 contentId) external view returns (uint256);
    
    function hasUserMinted(address user, uint256 contentId) external view returns (bool);
    
    function getMintStats() external view returns (
        uint256 totalMinted,
        uint256 totalVolume,
        uint256 averagePrice
    );
    
    function getContentMintStats(uint256 contentId) external view returns (
        uint256 mintCount,
        uint256 totalEarnings,
        uint256 averagePrice
    );
    
    // 管理函数
    function setTreasuryAddress(address newTreasury) external;
    
    function pause() external;
    
    function unpause() external;
    
    function emergencyWithdraw() external;
}
