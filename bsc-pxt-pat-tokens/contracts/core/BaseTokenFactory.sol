// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./TokenRegistry.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

abstract contract BaseTokenFactory is Ownable, Pausable, ReentrancyGuard {
    TokenRegistry public registry;

    event TokenCreated(
        address indexed tokenAddress,
        string name,
        string symbol,
        uint256 totalSupply,
        address indexed deployer,
        bool isPXT
    );

    event EmergencyWithdraw(address indexed recipient, uint256 amount);

    constructor(address _registry) {
        if(_registry == address(0)) revert TokenErrors.ZeroAddress();
        registry = TokenRegistry(_registry);
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    function emergencyWithdraw(address payable recipient) external onlyOwner {
        if(recipient == address(0)) revert TokenErrors.ZeroAddress();
        uint256 balance = address(this).balance;
        (bool success, ) = recipient.call{value: balance}("");
        if (!success) revert TokenErrors.WithdrawalFailed();
        emit EmergencyWithdraw(recipient, balance);
    }

    function setRegistry(address _registry) external onlyOwner {
        if(_registry == address(0)) revert TokenErrors.ZeroAddress();
        registry = TokenRegistry(_registry);
    }

    receive() external payable {}
} 