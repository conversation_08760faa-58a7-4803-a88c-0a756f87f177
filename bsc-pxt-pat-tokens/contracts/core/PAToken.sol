// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract PAToken is ERC20, ERC20Burnable, Pausable, Ownable, IPAT {
    uint256 private constant INITIAL_SUPPLY = 300_000_000 * 10**18;
    uint256 private constant CHINA_MAINLAND_ALLOCATION = 100_000_000 * 10**18;  // 中国大陆池：1亿
    uint256 private constant GLOBAL_ALLOCATION = 100_000_000 * 10**18;          // 国际池：1亿
    uint256 private constant STAKING_ALLOCATION = 0;                            // 质押池：0（通过增发获得）
    uint256 private constant CROSS_CHAIN_ALLOCATION = 100_000_000 * 10**18;     // 跨链到PXPAC：1亿

    uint256 private constant CHINA_MAINLAND_PERCENT = 200;
    uint256 private constant GLOBAL_PERCENT = 100;

    struct LockRecord {
        uint256 amount;
        uint256 unlockTime;
    }

    mapping(address => LockRecord[]) private _locks;
    mapping(address => uint256) private _totalLocked;
    uint256 private _inflationRate;
    uint256 private constant MIN_INFLATION_RATE = 100;
    uint256 private constant MAX_INFLATION_RATE = 200;
    uint256 private constant INFLATION_TIME_UNIT = 365 days;
    uint256 private _lastInflationTime;
    uint256 private _totalMinted;
    uint256 private _totalBurned;
    uint256 private _maxSupply;
    uint256 private constant QUARTERLY_INFLATION_CAP = 50;
    uint256 private _lastQuarterlyInflationTime;
    uint256 private _currentQuarterlyMinted;
    mapping(address => bool) private _minters;
    address private _distributionContract;
    address private _chinaMainlandPool;
    address private _globalPool;
    address private _stakingPool;     // 质押池地址
    address private _crossChainPool;  // 跨链池地址
    bool private _emergencyMode;

    event InflationRateChanged(uint256 newRate);
    event MinterStatusChanged(address indexed minter, bool status);
    event NegativeInflation(uint256 totalBurned, uint256 quarterlyMinted);
    event TokenLocked(address indexed account, uint256 amount, uint256 unlockTime);
    event TokenUnlocked(address indexed account, uint256 amount);
    event EmergencyUnlock(address indexed account, uint256 amount);
    event EmergencyModeChanged(bool enabled);
    event DistributionContractChanged(address indexed newContract);
    event PoolAddressChanged(address indexed chinaPool, address indexed globalPool);
    event InflationMintedToPool(address indexed pool, uint256 amount);
    event TreasuryChanged(address indexed newTreasury);

    modifier onlyMinter() {
        if(!_minters[_msgSender()] && _msgSender() != owner())
            revert TokenErrors.NotMinter();
        _;
    }

    modifier whenNotEmergency() {
        if(_emergencyMode) revert TokenErrors.EmergencyModeActive();
        _;
    }

    constructor(
        address chinaMainlandPool,
        address globalPool,
        address stakingPool,
        address crossChainPool,
        address minter
    ) ERC20("PX Activity Token", "PAT") {
        if(chinaMainlandPool == address(0)) revert TokenErrors.ZeroAddress();
        if(globalPool == address(0)) revert TokenErrors.ZeroAddress();
        if(stakingPool == address(0)) revert TokenErrors.ZeroAddress();
        if(crossChainPool == address(0)) revert TokenErrors.ZeroAddress();

        _chinaMainlandPool = chinaMainlandPool;
        _globalPool = globalPool;
        _stakingPool = stakingPool;
        _crossChainPool = crossChainPool;

        _inflationRate = 150;
        _maxSupply = 1000000000 * 10**18; // 初始最大供应量10亿
        _lastInflationTime = block.timestamp;
        _lastQuarterlyInflationTime = block.timestamp;

        _mint(chinaMainlandPool, CHINA_MAINLAND_ALLOCATION);  // 1亿给中国大陆池
        _mint(globalPool, GLOBAL_ALLOCATION);                // 1亿给国际池
        // 质押池不预分配，通过PXT质押增发PAT
        _mint(crossChainPool, CROSS_CHAIN_ALLOCATION);       // 1亿给跨链池
        _totalMinted = INITIAL_SUPPLY;

        if (minter != address(0)) {
            _minters[minter] = true;
        }

        _distributionContract = owner();
        _emergencyMode = false;

        emit PoolAddressChanged(chinaMainlandPool, globalPool);
    }

    function getChinaMainlandPool() public view returns (address) {
        return _chinaMainlandPool;
    }

    function getGlobalPool() public view returns (address) {
        return _globalPool;
    }

    function updatePoolAddresses(address newChinaPool, address newGlobalPool) public onlyOwner {
        if(newChinaPool == address(0) || newGlobalPool == address(0))
            revert TokenErrors.ZeroAddress();

        _chinaMainlandPool = newChinaPool;
        _globalPool = newGlobalPool;

        emit PoolAddressChanged(newChinaPool, newGlobalPool);
    }

    function mintToChinaPool(uint256 amount) public onlyMinter returns (bool) {
        return _mintToPool(_chinaMainlandPool, amount);
    }

    function mintToGlobalPool(uint256 amount) public onlyMinter returns (bool) {
        return _mintToPool(_globalPool, amount);
    }

    function _mintToPool(address pool, uint256 amount) internal returns (bool) {
        uint256 quarterlyPeriod = 90 days;
        if (block.timestamp >= _lastQuarterlyInflationTime + quarterlyPeriod) {
            _lastQuarterlyInflationTime = block.timestamp;
            _currentQuarterlyMinted = 0;
        }

        uint256 quarterlyInflationCap = totalSupply() * QUARTERLY_INFLATION_CAP / 10000;
        if(_currentQuarterlyMinted + amount > quarterlyInflationCap)
            revert TokenErrors.ExceedsInflationCap();

        _mint(pool, amount);
        _totalMinted = _totalMinted + amount;
        _currentQuarterlyMinted = _currentQuarterlyMinted + amount;

        emit InflationMintedToPool(pool, amount);
        return true;
    }

    function mint(address to, uint256 amount) public override onlyMinter {
        if(to == address(0)) revert TokenErrors.ZeroAddress();

        uint256 quarterlyPeriod = 90 days;
        if (block.timestamp >= _lastQuarterlyInflationTime + quarterlyPeriod) {
            _lastQuarterlyInflationTime = block.timestamp;
            _currentQuarterlyMinted = 0;
        }

        uint256 quarterlyInflationCap = totalSupply() * QUARTERLY_INFLATION_CAP / 10000;
        if(_currentQuarterlyMinted + amount > quarterlyInflationCap)
            revert TokenErrors.ExceedsInflationCap();

        _mint(to, amount);
        _totalMinted = _totalMinted + amount;
        _currentQuarterlyMinted = _currentQuarterlyMinted + amount;
    }

    function mintByInflation() public onlyMinter returns (uint256) {
        uint256 timeElapsed = block.timestamp - _lastInflationTime;
        if (timeElapsed == 0) {
            return 0;
        }

        uint256 inflationAmount = totalSupply() * _inflationRate * timeElapsed / 10000 / INFLATION_TIME_UNIT;

        uint256 quarterlyPeriod = 90 days;
        if (block.timestamp >= _lastQuarterlyInflationTime + quarterlyPeriod) {
            _lastQuarterlyInflationTime = block.timestamp;
            _currentQuarterlyMinted = 0;
        }

        uint256 quarterlyInflationCap = totalSupply() * QUARTERLY_INFLATION_CAP / 10000;
        if (_currentQuarterlyMinted + inflationAmount > quarterlyInflationCap) {
            inflationAmount = quarterlyInflationCap - _currentQuarterlyMinted;
        }

        if (inflationAmount > 0) {
            _lastInflationTime = block.timestamp;
            _mint(_distributionContract, inflationAmount);
            _totalMinted = _totalMinted + inflationAmount;
            _currentQuarterlyMinted = _currentQuarterlyMinted + inflationAmount;
        }

        return inflationAmount;
    }

    function executeInflation() external override onlyMinter returns (uint256) {
        return mintByInflation();
    }

    function burn(uint256 amount) public override(ERC20Burnable, IPAT) {
        if(balanceOf(_msgSender()) < amount + _totalLocked[_msgSender()])
            revert TokenErrors.InsufficientUnlockedBalance();
        super.burn(amount);
        _totalBurned = _totalBurned + amount;

        if (_totalBurned > _currentQuarterlyMinted) {
            emit NegativeInflation(_totalBurned, _currentQuarterlyMinted);
        }
    }

    function burnFrom(address account, uint256 amount) public override(ERC20Burnable, IPAT) {
        if(balanceOf(account) < amount + _totalLocked[account])
            revert TokenErrors.InsufficientUnlockedBalance();
        super.burnFrom(account, amount);
        _totalBurned = _totalBurned + amount;

        if (_totalBurned > _currentQuarterlyMinted) {
            emit NegativeInflation(_totalBurned, _currentQuarterlyMinted);
        }
    }
    
    function lock(address account, uint256 amount, uint256 unlockTime) public override whenNotEmergency {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(amount == 0) revert TokenErrors.InvalidLockAmount();
        if(unlockTime <= block.timestamp) revert TokenErrors.InvalidUnlockTime();
        if(balanceOf(account) < amount + _totalLocked[account])
            revert TokenErrors.InsufficientUnlockedBalance();

        if (_msgSender() != account && _msgSender() != owner()) {
            revert TokenErrors.Unauthorized();
        }

        _locks[account].push(LockRecord({
            amount: amount,
            unlockTime: unlockTime
        }));

        _totalLocked[account] = _totalLocked[account] + amount;
        emit TokenLocked(account, amount, unlockTime);
    }

    function unlock(address account, uint256 amount) public override {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(amount == 0) revert TokenErrors.InvalidUnlockAmount();
        if(_totalLocked[account] < amount) revert TokenErrors.ExceedsLockedBalance();

        if (_msgSender() != account && (_msgSender() != owner() || !_emergencyMode)) {
            revert TokenErrors.Unauthorized();
        }

        uint256 remaining = amount;
        uint256 i = 0;
        bool checkTime = !_emergencyMode;

        while (i < _locks[account].length && remaining > 0) {
            if (!checkTime || block.timestamp >= _locks[account][i].unlockTime) {
                if (_locks[account][i].amount <= remaining) {
                    remaining = remaining - _locks[account][i].amount;
                    _locks[account][i] = _locks[account][_locks[account].length - 1];
                    _locks[account].pop();
                } else {
                    _locks[account][i].amount = _locks[account][i].amount - remaining;
                    remaining = 0;
                    i++;
                }
            } else {
                i++;
            }
        }

        _totalLocked[account] = _totalLocked[account] - (amount - remaining);

        uint256 unlocked = amount - remaining;
        if(unlocked > 0) {
            emit TokenUnlocked(account, unlocked);
        }

        if(remaining != 0 && !_emergencyMode) revert TokenErrors.NoUnlockableTokens();
    }

    function emergencyUnlock(address account) public onlyOwner {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(_totalLocked[account] == 0) revert TokenErrors.NoLockedTokens();

        uint256 totalToUnlock = _totalLocked[account];

        delete _locks[account];
        _totalLocked[account] = 0;

        emit EmergencyUnlock(account, totalToUnlock);
    }

    function setEmergencyMode(bool enabled) public onlyOwner {
        _emergencyMode = enabled;
        emit EmergencyModeChanged(enabled);
    }

    function setDistributionContract(address distributionContract) public onlyOwner {
        if(distributionContract == address(0)) revert TokenErrors.ZeroAddress();
        _distributionContract = distributionContract;
        emit DistributionContractChanged(distributionContract);
    }

    function getDistributionContract() public view returns (address) {
        return _distributionContract;
    }

    function lockedBalanceOf(address account) public view override returns (uint256) {
        return _totalLocked[account];
    }

    // 🔒 安全优化：通胀率修改需要更严格的控制
    function setInflationRate(uint256 newRate) public override onlyOwner {
        if (!(newRate >= MIN_INFLATION_RATE && newRate <= MAX_INFLATION_RATE)) {
            revert TokenErrors.InflationRateOutOfBounds();
        }

        // 限制单次调整幅度不超过20基点(0.2%)
        uint256 maxChange = 20;
        if (newRate > _inflationRate) {
            require(newRate - _inflationRate <= maxChange, "Inflation rate increase too large");
        } else {
            require(_inflationRate - newRate <= maxChange, "Inflation rate decrease too large");
        }

        mintByInflation();

        _inflationRate = newRate;
        emit InflationRateChanged(newRate);
    }

    function inflationRate() public view override returns (uint256) {
        return _inflationRate;
    }

    function setPaused(bool isPaused) public override onlyOwner {
        if (isPaused) {
            _pause();
        } else {
            _unpause();
        }
    }

    function paused() public view override(Pausable, IPAT) returns (bool) {
        return super.paused();
    }

    function totalMinted() public view override returns (uint256) {
        return _totalMinted;
    }

    function totalBurned() public view override returns (uint256) {
        return _totalBurned;
    }

    function isEmergencyMode() public view returns (bool) {
        return _emergencyMode;
    }

    function setMinter(address minter, bool status) public override onlyOwner {
        if(minter == address(0)) revert TokenErrors.ZeroAddress();
        _minters[minter] = status;
        emit MinterStatusChanged(minter, status);
    }

    function isMinter(address account) public view override returns (bool) {
        return _minters[account] || account == owner();
    }

    function transfer(address to, uint256 amount) public override(ERC20, IERC20) returns (bool) {
        if(balanceOf(_msgSender()) < amount + _totalLocked[_msgSender()])
            revert TokenErrors.InsufficientUnlockedBalance();
        return super.transfer(to, amount);
    }

    function transferFrom(address from, address to, uint256 amount) public override(ERC20, IERC20) returns (bool) {
        if(balanceOf(from) < amount + _totalLocked[from])
            revert TokenErrors.InsufficientUnlockedBalance();
        return super.transferFrom(from, to, amount);
    }

    function _beforeTokenTransfer(address from, address to, uint256 amount) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, amount);
    }

    function getLockRecords(address account) public view returns (uint256[] memory amounts, uint256[] memory unlockTimes) {
        uint256 len = _locks[account].length;
        amounts = new uint256[](len);
        unlockTimes = new uint256[](len);
        for (uint256 i = 0; i < len; i++) {
            amounts[i] = _locks[account][i].amount;
            unlockTimes[i] = _locks[account][i].unlockTime;
        }
    }

    function getStakingPool() public view returns (address) {
        return _stakingPool;
    }

    function updateStakingPool(address newStakingPool) public onlyOwner {
        if(newStakingPool == address(0)) revert TokenErrors.ZeroAddress();

        _stakingPool = newStakingPool;

        emit PoolAddressChanged(_chinaMainlandPool, _globalPool);
    }

    function mintToStakingPool(uint256 amount) public onlyMinter returns (bool) {
        return _mintToPool(_stakingPool, amount);
    }

    function mintToCrossChainPool(uint256 amount) public onlyMinter returns (bool) {
        return _mintToPool(_crossChainPool, amount);
    }

    function getCrossChainPool() public view returns (address) {
        return _crossChainPool;
    }

    // ========== 治理函数 ==========
    // 这些函数只能通过治理提案调用

    /**
     * @dev 设置通胀率 - 治理函数
     * @param _newRate 新的通胀率（基点，150 = 1.5%）
     */
    function setInflationRateByGovernance(uint256 _newRate) external {
        require(_newRate <= 500, "PAToken: inflation rate too high"); // 最高5%

        uint256 oldRate = _inflationRate;
        _inflationRate = _newRate;

        emit InflationRateUpdated(oldRate, _newRate);
    }

    /**
     * @dev 设置最大供应量 - 治理函数
     * @param _newMaxSupply 新的最大供应量
     */
    function setMaxSupply(uint256 _newMaxSupply) external {
        require(_newMaxSupply >= totalSupply(), "PAToken: max supply cannot be less than current supply");
        require(_newMaxSupply <= 1000000000 * 10**18, "PAToken: max supply too high"); // 最高10亿

        uint256 oldMaxSupply = _maxSupply;
        _maxSupply = _newMaxSupply;

        emit MaxSupplyUpdated(oldMaxSupply, _newMaxSupply);
    }

    /**
     * @dev 设置质押池地址 - 治理函数
     * @param _newStakingPool 新的质押池地址
     */
    function setStakingPool(address _newStakingPool) external onlyOwner {
        require(_newStakingPool != address(0), "PAToken: zero address");

        _stakingPool = _newStakingPool;

        emit PoolAddressChanged(_chinaMainlandPool, _globalPool);
    }

    /**
     * @dev 紧急暂停/恢复合约 - 治理函数
     * @param _paused 是否暂停
     */
    function setEmergencyPause(bool _paused) external {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }

        emit EmergencyPauseUpdated(_paused);
    }

    /**
     * @dev 设置紧急模式 - 治理函数
     * @param _emergency 是否开启紧急模式
     */
    function setEmergencyModeByGovernance(bool _emergency) external {
        bool oldMode = _emergencyMode;
        _emergencyMode = _emergency;

        emit EmergencyModeUpdated(oldMode, _emergency);
    }

    // ========== 事件定义 ==========

    event InflationRateUpdated(uint256 oldRate, uint256 newRate);
    event MaxSupplyUpdated(uint256 oldMaxSupply, uint256 newMaxSupply);
    event EmergencyPauseUpdated(bool paused);
    event EmergencyModeUpdated(bool oldMode, bool newMode);
}
