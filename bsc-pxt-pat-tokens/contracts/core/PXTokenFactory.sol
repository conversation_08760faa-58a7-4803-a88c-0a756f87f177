// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./PXToken.sol";
import "./BaseTokenFactory.sol";
import "../interfaces/TokenErrors.sol";

contract PXTokenFactory is BaseTokenFactory {
    constructor(address _registry) BaseTokenFactory(_registry) {}

    function createPXToken(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address communityAddress,
        address teamAddress,
        address platformFundAddress,
        address privateSaleAddress,
        address strategicPartnerAddress,
        address marketingAddress,
        address reserveAddress,
        address chinaMainlandPool
    ) external onlyOwner whenNotPaused nonReentrant returns (address) {
        if(bytes(name).length == 0) revert TokenErrors.EmptyName();
        if(bytes(symbol).length == 0) revert TokenErrors.EmptySymbol();
        if(initialSupply == 0) revert TokenErrors.ZeroSupply();
        if(communityAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(teamAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(platformFundAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(privateSaleAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(strategicPartnerAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(marketingAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(reserveAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(chinaMainlandPool == address(0)) revert TokenErrors.ZeroAddress();

        PXToken newToken = new PXToken(
            name,
            symbol,
            initialSupply,
            communityAddress,
            teamAddress,
            platformFundAddress,
            privateSaleAddress,
            strategicPartnerAddress,
            marketingAddress,
            reserveAddress,
            chinaMainlandPool
        );
        address tokenAddress = address(newToken);

        registry.registerToken(
            tokenAddress,
            name,
            symbol,
            18,
            initialSupply,
            msg.sender,
            true
        );

        emit TokenCreated(tokenAddress, name, symbol, initialSupply, msg.sender, true);
        return tokenAddress;
    }
} 