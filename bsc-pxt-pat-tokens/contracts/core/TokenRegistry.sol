// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "../interfaces/TokenErrors.sol";

contract TokenRegistry is Ownable {
    struct TokenInfo {
        string name;
        string symbol;
        uint8 decimals;
        uint256 totalSupply;
        uint256 deploymentTime;
        address deployer;
        bool isPXT;
    }

    mapping(address => bool) public isFactory;
    address[] public allTokens;
    address[] public allPXTokens;
    address[] public allPATokens;
    mapping(address => TokenInfo) public tokenDetails;

    event TokenRegistered(address indexed token, string name, string symbol, bool isPXT);
    event FactoryAdded(address indexed factoryAddress);
    event FactoryRemoved(address indexed factoryAddress);

    modifier onlyFactory() {
        if (!isFactory[msg.sender]) revert TokenErrors.Unauthorized();
        _;
    }

    constructor() {}

    function addFactory(address _factory) external onlyOwner {
        if(_factory == address(0)) revert TokenErrors.ZeroAddress();
        if(isFactory[_factory]) revert TokenErrors.AlreadyRegistered();
        isFactory[_factory] = true;
        emit FactoryAdded(_factory);
    }

    function removeFactory(address _factory) external onlyOwner {
        if(_factory == address(0)) revert TokenErrors.ZeroAddress();
        if(!isFactory[_factory]) revert TokenErrors.NotRegistered();
        isFactory[_factory] = false;
        emit FactoryRemoved(_factory);
    }

    function registerToken(
        address token,
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 totalSupply,
        address deployer,
        bool isPXT
    ) external onlyFactory {
        if(token == address(0)) revert TokenErrors.ZeroAddress();
        if(tokenDetails[token].deploymentTime != 0) revert TokenErrors.AlreadyRegistered();
        tokenDetails[token] = TokenInfo({
            name: name,
            symbol: symbol,
            decimals: decimals,
            totalSupply: totalSupply,
            deploymentTime: block.timestamp,
            deployer: deployer,
            isPXT: isPXT
        });
        allTokens.push(token);
        if (isPXT) {
            allPXTokens.push(token);
        } else {
            allPATokens.push(token);
        }
        emit TokenRegistered(token, name, symbol, isPXT);
    }

    function getAllTokens() external view returns (address[] memory) {
        return allTokens;
    }

    function getAllPXTokens() external view returns (address[] memory) {
        return allPXTokens;
    }

    function getAllPATokens() external view returns (address[] memory) {
        return allPATokens;
    }

    function isPXToken(address token) external view returns (bool) {
        return tokenDetails[token].deploymentTime != 0 && tokenDetails[token].isPXT;
    }

    function isPAToken(address token) external view returns (bool) {
        return tokenDetails[token].deploymentTime != 0 && !tokenDetails[token].isPXT;
    }

    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        if(tokenDetails[token].deploymentTime == 0) revert TokenErrors.NotRegistered();
        return tokenDetails[token];
    }

    function getPXTokenCount() external view returns (uint256) {
        return allPXTokens.length;
    }

    function getPATokenCount() external view returns (uint256) {
        return allPATokens.length;
    }
} 