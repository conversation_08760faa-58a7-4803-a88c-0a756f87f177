// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./PAToken.sol";
import "./BaseTokenFactory.sol";
import "../interfaces/TokenErrors.sol";

contract PATokenFactory is BaseTokenFactory {
    constructor(address _registry) BaseTokenFactory(_registry) {}

    function createPAToken(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address chinaMainlandPool,
        address globalPool,
        address stakingRewardPool,
        address crossChainPool,
        address minter
    ) external onlyOwner whenNotPaused nonReentrant returns (address) {
        if(bytes(name).length == 0) revert TokenErrors.EmptyName();
        if(bytes(symbol).length == 0) revert TokenErrors.EmptySymbol();
        if(initialSupply == 0) revert TokenErrors.ZeroSupply();
        if(chinaMainlandPool == address(0)) revert TokenErrors.ZeroAddress();
        if(globalPool == address(0)) revert TokenErrors.ZeroAddress();
        if(stakingRewardPool == address(0)) revert TokenErrors.ZeroAddress();
        if(crossChainPool == address(0)) revert TokenErrors.ZeroAddress();
        if(minter == address(0)) revert TokenErrors.ZeroAddress();

        PAToken newToken = new PAToken(
            chinaMainlandPool,
            globalPool,
            stakingRewardPool,
            crossChainPool,
            minter
        );
        address tokenAddress = address(newToken);

        registry.registerToken(
            tokenAddress,
            "Paper Author Token",
            "PAT",
            18,
            300_000_000 * 10**18,
            msg.sender,
            false
        );

        emit TokenCreated(tokenAddress, "Paper Author Token", "PAT", 300_000_000 * 10**18, msg.sender, false);
        return tokenAddress;
    }
} 