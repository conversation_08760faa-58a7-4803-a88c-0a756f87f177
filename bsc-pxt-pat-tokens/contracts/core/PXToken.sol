// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";

contract PXToken is ERC20, ERC20Burnable, Pausable, Ownable, IPXT {
    uint256 private constant TOTAL_SUPPLY = 100_000_000 * 10**18;
    uint256 private constant CHINA_MAINLAND_ALLOCATION = 20_000_000 * 10**18;

    struct LockRecord {
        uint256 amount;
        uint256 unlockTime;
    }

    mapping(address => LockRecord[]) private _locks;
    mapping(address => uint256) private _totalLocked;
    uint256 private _totalBurned;
    uint256 private _totalMinted;
    bool private _emergencyMode;
    address private _chinaMainlandPool;

    uint256 private constant COMMUNITY_PERCENT = 45;
    uint256 private constant TEAM_PERCENT = 10;
    uint256 private constant PLATFORM_FUND_PERCENT = 15;
    uint256 private constant PRIVATE_SALE_PERCENT = 15;
    uint256 private constant STRATEGIC_PARTNER_PERCENT = 5;
    uint256 private constant MARKETING_PERCENT = 5;
    uint256 private constant RESERVE_PERCENT = 5;

    uint256 private constant CHINA_MAINLAND_RATIO_IN_COMMUNITY = 4444;
    uint256 private constant GLOBAL_RATIO_IN_COMMUNITY = 5556;

    event TokenLocked(address indexed account, uint256 amount, uint256 unlockTime);
    event TokenUnlocked(address indexed account, uint256 amount);
    event EmergencyUnlock(address indexed account, uint256 amount);
    event EmergencyModeChanged(bool enabled);
    event ChinaMainlandPoolChanged(address indexed newPool);

    modifier whenNotEmergency() {
        if(_emergencyMode) revert TokenErrors.EmergencyModeActive();
        _;
    }
    
    constructor(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address communityAddress,
        address teamAddress,
        address platformFundAddress,
        address privateSaleAddress,
        address strategicPartnerAddress,
        address marketingAddress,
        address reserveAddress,
        address chinaMainlandPool
    ) ERC20(name, symbol) {
        if(communityAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(teamAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(platformFundAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(privateSaleAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(strategicPartnerAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(marketingAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(reserveAddress == address(0)) revert TokenErrors.ZeroAddress();
        if(chinaMainlandPool == address(0)) revert TokenErrors.ZeroAddress();

        _chinaMainlandPool = chinaMainlandPool;

        uint256 communityAmount = (initialSupply * COMMUNITY_PERCENT) / 100;
        uint256 teamAmount = (initialSupply * TEAM_PERCENT) / 100;
        uint256 platformFundAmount = (initialSupply * PLATFORM_FUND_PERCENT) / 100;
        uint256 privateSaleAmount = (initialSupply * PRIVATE_SALE_PERCENT) / 100;
        uint256 strategicPartnerAmount = (initialSupply * STRATEGIC_PARTNER_PERCENT) / 100;
        uint256 marketingAmount = (initialSupply * MARKETING_PERCENT) / 100;
        uint256 reserveAmount = (initialSupply * RESERVE_PERCENT) / 100;

        uint256 chinaMainlandAmount = (communityAmount * CHINA_MAINLAND_RATIO_IN_COMMUNITY) / 10000;
        uint256 adjustedCommunityAmount = communityAmount - chinaMainlandAmount;

        _mint(communityAddress, adjustedCommunityAmount);
        _mint(teamAddress, teamAmount);
        _mint(platformFundAddress, platformFundAmount);
        _mint(privateSaleAddress, privateSaleAmount);
        _mint(strategicPartnerAddress, strategicPartnerAmount);
        _mint(marketingAddress, marketingAmount);
        _mint(reserveAddress, reserveAmount);
        _mint(chinaMainlandPool, chinaMainlandAmount);

        _totalMinted = initialSupply;

        // 🔒 安全优化：批量锁定以节省Gas
        uint256 unlockPeriod = 90 days;
        uint256 teamPerQuarter = teamAmount / 40;

        // 使用内部批量锁定函数减少Gas消耗
        _batchLockTeamTokens(teamAddress, teamPerQuarter, unlockPeriod, 40);

        uint256 privateUnlockStart = block.timestamp + 365 days;
        uint256 privateUnlockPeriod = 30 days;
        uint256 privatePerMonth = privateSaleAmount / 24;

        for(uint256 i = 0; i < 24; i++) {
            uint256 unlockTime = privateUnlockStart + (privateUnlockPeriod * i);
            lock(privateSaleAddress, privatePerMonth, unlockTime);
        }

        _emergencyMode = false;
    }

    function getChinaMainlandPool() public view returns (address) {
        return _chinaMainlandPool;
    }

    function updateChinaMainlandPool(address newPool) public onlyOwner {
        if(newPool == address(0)) revert TokenErrors.ZeroAddress();
        _chinaMainlandPool = newPool;
        emit ChinaMainlandPoolChanged(newPool);
    }

    function lock(address account, uint256 amount, uint256 unlockTime) public override whenNotEmergency {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(amount == 0) revert TokenErrors.InvalidLockAmount();
        if(unlockTime <= block.timestamp) revert TokenErrors.InvalidUnlockTime();
        if(balanceOf(account) < amount + _totalLocked[account])
            revert TokenErrors.InsufficientUnlockedBalance();

        if (_msgSender() != account && _msgSender() != owner()) {
            revert TokenErrors.Unauthorized();
        }

        _locks[account].push(LockRecord({
            amount: amount,
            unlockTime: unlockTime
        }));

        _totalLocked[account] += amount;
        emit TokenLocked(account, amount, unlockTime);
    }

    function unlock(address account, uint256 amount) public override {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(amount == 0) revert TokenErrors.InvalidUnlockAmount();
        if(_totalLocked[account] < amount) revert TokenErrors.ExceedsLockedBalance();

        if (_msgSender() != account && (_msgSender() != owner() || !_emergencyMode)) {
            revert TokenErrors.Unauthorized();
        }

        uint256 remaining = amount;
        uint256 i = 0;
        bool checkTime = !_emergencyMode;

        while (i < _locks[account].length && remaining > 0) {
            if (!checkTime || block.timestamp >= _locks[account][i].unlockTime) {
                if (_locks[account][i].amount <= remaining) {
                    remaining -= _locks[account][i].amount;
                    _locks[account][i] = _locks[account][_locks[account].length - 1];
                    _locks[account].pop();
                } else {
                    _locks[account][i].amount -= remaining;
                    remaining = 0;
                    i++;
                }
            } else {
                i++;
            }
        }

        _totalLocked[account] -= (amount - remaining);

        uint256 unlocked = amount - remaining;
        if(unlocked > 0) {
            emit TokenUnlocked(account, unlocked);
        }

        if(remaining != 0 && !_emergencyMode) revert TokenErrors.NoUnlockableTokens();
    }

    function emergencyUnlock(address account) public onlyOwner {
        if(account == address(0)) revert TokenErrors.ZeroAddress();
        if(_totalLocked[account] == 0) revert TokenErrors.NoLockedTokens();

        uint256 totalToUnlock = _totalLocked[account];

        delete _locks[account];
        _totalLocked[account] = 0;

        emit EmergencyUnlock(account, totalToUnlock);
    }

    function setEmergencyMode(bool enabled) public onlyOwner {
        _emergencyMode = enabled;
        emit EmergencyModeChanged(enabled);
    }

    function lockedBalanceOf(address account) public view override returns (uint256) {
        return _totalLocked[account];
    }

    function lockInfo(address account) public view returns (uint256[] memory, uint256[] memory) {
        if (_locks[account].length == 0) {
            return (new uint256[](0), new uint256[](0));
        }

        uint256[] memory amounts = new uint256[](_locks[account].length);
        uint256[] memory unlockTimes = new uint256[](_locks[account].length);

        for (uint256 i = 0; i < _locks[account].length; i++) {
            amounts[i] = _locks[account][i].amount;
            unlockTimes[i] = _locks[account][i].unlockTime;
        }

        return (amounts, unlockTimes);
    }

    function setPaused(bool isPaused) public override onlyOwner {
        if (isPaused) {
            _pause();
        } else {
            _unpause();
        }
    }

    function transfer(address to, uint256 amount) public override(ERC20, IERC20) returns (bool) {
        if(balanceOf(_msgSender()) < amount + _totalLocked[_msgSender()])
            revert TokenErrors.InsufficientUnlockedBalance();
        return super.transfer(to, amount);
    }

    function transferFrom(address from, address to, uint256 amount) public override(ERC20, IERC20) returns (bool) {
        if(balanceOf(from) < amount + _totalLocked[from])
            revert TokenErrors.InsufficientUnlockedBalance();
        return super.transferFrom(from, to, amount);
    }

    function burn(uint256 amount) public override(ERC20Burnable, IPXT) {
        if(balanceOf(_msgSender()) < amount + _totalLocked[_msgSender()])
            revert TokenErrors.InsufficientUnlockedBalance();
        super.burn(amount);
        _totalBurned += amount;
    }

    function burnFrom(address account, uint256 amount) public override(ERC20Burnable, IPXT) {
        if(balanceOf(account) < amount + _totalLocked[account])
            revert TokenErrors.InsufficientUnlockedBalance();
        super.burnFrom(account, amount);
        _totalBurned += amount;
    }

    function mint(address to, uint256 amount) public onlyOwner {
        if (_totalMinted + amount > TOTAL_SUPPLY) revert TokenErrors.ExceedsTotalSupply();
        _mint(to, amount);
        _totalMinted = _totalMinted + amount;
    }

    function totalBurned() public view override returns (uint256) {
        return _totalBurned;
    }

    function totalMinted() public view returns (uint256) {
        return _totalMinted;
    }

    function isEmergencyMode() public view returns (bool) {
        return _emergencyMode;
    }

    function _beforeTokenTransfer(address from, address to, uint256 amount) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, amount);
    }

    function getLockRecords(address account) public view returns (uint256[] memory amounts, uint256[] memory unlockTimes) {
        uint256 len = _locks[account].length;
        amounts = new uint256[](len);
        unlockTimes = new uint256[](len);
        for (uint256 i = 0; i < len; i++) {
            amounts[i] = _locks[account][i].amount;
            unlockTimes[i] = _locks[account][i].unlockTime;
        }
    }

    function paused() public view override(Pausable, IPXT) returns (bool) {
        return super.paused();
    }

    function batchLock(address[] calldata accounts, uint256[] calldata amounts, uint256 unlockTime) external override onlyOwner whenNotEmergency {
        if(accounts.length != amounts.length) revert TokenErrors.ArrayLengthMismatch();
        if(unlockTime <= block.timestamp) revert TokenErrors.InvalidUnlockTime();

        for(uint256 i = 0; i < accounts.length; i++) {
            lock(accounts[i], amounts[i], unlockTime);
        }
    }

    function batchUnlock(address[] calldata accounts, uint256[] calldata amounts) external override onlyOwner {
        if(accounts.length != amounts.length) revert TokenErrors.ArrayLengthMismatch();

        for(uint256 i = 0; i < accounts.length; i++) {
            unlock(accounts[i], amounts[i]);
        }
    }

    function batchBurnFrom(address[] calldata accounts, uint256[] calldata amounts) external override onlyOwner {
        if(accounts.length != amounts.length) revert TokenErrors.ArrayLengthMismatch();

        for(uint256 i = 0; i < accounts.length; i++) {
            burnFrom(accounts[i], amounts[i]);
        }
    }

    function unlockTimesOf(address account) external view override returns (uint256[] memory) {
        uint256[] memory unlockTimes = new uint256[](_locks[account].length);

        for(uint256 i = 0; i < _locks[account].length; i++) {
            unlockTimes[i] = _locks[account][i].unlockTime;
        }

        return unlockTimes;
    }

    function canUnlock(address account, uint256 amount) external view override returns (bool) {
        if(_totalLocked[account] < amount) return false;

        uint256 unlockable = 0;
        for(uint256 i = 0; i < _locks[account].length; i++) {
            if(block.timestamp >= _locks[account][i].unlockTime) {
                unlockable += _locks[account][i].amount;
                if(unlockable >= amount) return true;
            }
        }

        return false;
    }

    function batchGrantRole(address[] calldata accounts, uint256 id) external override onlyOwner {
        for(uint256 i = 0; i < accounts.length; i++) {
            grantRole(accounts[i], id);
        }
    }

    function batchRevokeRole(address[] calldata accounts, uint256 id) external override onlyOwner {
        for(uint256 i = 0; i < accounts.length; i++) {
            revokeRole(accounts[i], id);
        }
    }

    function recoverERC20(address tokenAddress, uint256 amount) external override onlyOwner {
        if(tokenAddress == address(this)) revert TokenErrors.CannotRecoverSelf();

        IERC20 token = IERC20(tokenAddress);
        token.transfer(owner(), amount);
    }

    function getAllRoles() external pure override returns (uint256[] memory ids, string[] memory descriptions) {
        ids = new uint256[](4);
        descriptions = new string[](4);

        ids[0] = 1;
        ids[1] = 2;
        ids[2] = 3;
        ids[3] = 4;

        descriptions[0] = "Admin";
        descriptions[1] = "Minter";
        descriptions[2] = "Burner";
        descriptions[3] = "Pauser";

        return (ids, descriptions);
    }

    function batchHasRole(address[] calldata accounts, uint256 id) external view override returns (bool[] memory) {
        bool[] memory results = new bool[](accounts.length);

        for(uint256 i = 0; i < accounts.length; i++) {
            results[i] = hasRole(accounts[i], id);
        }

        return results;
    }

    function hasRole(address account, uint256 /* _id */) public view override returns (bool) {
        return account == owner();
    }

    function grantRole(address account, uint256 id) public override onlyOwner {
        emit RoleStatusChanged(account, id, true);
    }

    function revokeRole(address account, uint256 id) public override onlyOwner {
        emit RoleStatusChanged(account, id, false);
    }

    // 🔒 安全优化：内部批量锁定函数，减少Gas消耗
    function _batchLockTeamTokens(
        address account,
        uint256 amountPerPeriod,
        uint256 period,
        uint256 count
    ) internal {
        require(count <= 50, "Too many lock periods"); // 防止Gas耗尽

        for(uint256 i = 1; i <= count; i++) {
            uint256 unlockTime = block.timestamp + (period * i);
            _locks[account].push(LockRecord({
                amount: amountPerPeriod,
                unlockTime: unlockTime
            }));
            _totalLocked[account] += amountPerPeriod;
            emit TokenLocked(account, amountPerPeriod, unlockTime);
        }
    }
}
