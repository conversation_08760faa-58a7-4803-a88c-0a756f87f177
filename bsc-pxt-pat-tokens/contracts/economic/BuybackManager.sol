// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/IDEXRouter.sol";

contract BuybackManager is
    Ownable,
    ReentrancyGuard,
    Pausable
{
    IPXT public immutable pxtoken;
    IPAT public immutable patoken;
    address public immutable burnManager;
    address public immutable treasury;
    address public immutable wrappedNativeCoin;
    address public constant DEAD_ADDRESS = 0x000000000000000000000000000000000000dEaD;

    struct BuybackRecord {
        uint40 timestamp;
        uint128 platformRevenue;
        uint128 buybackAmount;
        uint128 pxtPurchased;
        uint128 patPurchased;
        address[] dexRouters;
        uint256[] exchangeRates;
        bytes32 transactionHash;
        bool verified;
        bool sent;
    }

    BuybackRecord[] public buybackRecords;
    uint128 public totalBuybackAmount;
    uint128 public totalPXTPurchased;
    uint128 public totalPATPurchased;

    struct BuybackConfig {
        uint16 platformRevenuePercent;
        uint40 minBuybackInterval;
        uint128 minBuybackThreshold;
        uint16 pxtAllocationPercent;
        bool autoExecute;
        uint16 maxSlippage;
        uint128 maxSingleSwapAmount;
    }

    BuybackConfig public buybackConfig;
    address[] public dexRouters;
    mapping(address => uint256) public dexRouterPriority;
    uint40 public lastBuybackTime;
    uint128 public pendingBuybackAmount;
    uint128 public pendingPXTForBurn;
    uint128 public pendingPATForBurn;

    event RevenueReceived(uint256 amount, uint256 buybackShare);
    event BuybackExecuted(
        uint256 indexed recordId,
        uint256 amount,
        uint256 pxtPurchased,
        uint256 patPurchased
    );
    event BuybackVerified(uint256 indexed recordId, address verifier);
    event BuybackConfigUpdated(
        uint256 platformRevenuePercent,
        uint256 minBuybackInterval,
        uint256 minBuybackThreshold,
        uint256 pxtAllocationPercent,
        bool autoExecute,
        uint256 maxSlippage,
        uint256 maxSingleSwapAmount
    );
    event DEXRouterAdded(address router, uint256 priority);
    event DEXRouterRemoved(address router);
    event BuybackSent(address indexed burnManager, uint256 pxtAmount, uint256 patAmount);
    event SwapExecuted(
        address indexed dexRouter,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );
    event EmergencyWithdraw(address indexed recipient, uint256 amount);
    event EmergencyTokenWithdraw(address indexed token, address indexed recipient, uint256 amount);

    constructor(
        address _pxtoken,
        address _patoken,
        address _burnManager,
        address _treasury,
        address _wrappedNativeCoin
    ) {
        require(_pxtoken != address(0), "BuybackManager: PXT token address cannot be zero");
        require(_patoken != address(0), "BuybackManager: PAT token address cannot be zero");
        require(_burnManager != address(0), "BuybackManager: Burn manager address cannot be zero");
        require(_treasury != address(0), "BuybackManager: Treasury address cannot be zero");
        require(_wrappedNativeCoin != address(0), "BuybackManager: Wrapped coin address cannot be zero");
        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        burnManager = _burnManager;
        treasury = _treasury;
        wrappedNativeCoin = _wrappedNativeCoin;
        buybackConfig = BuybackConfig({
            platformRevenuePercent: 3000,
            minBuybackInterval: 7 days,
            minBuybackThreshold: 1 ether,
            pxtAllocationPercent: 5000,
            autoExecute: true,
            maxSlippage: 300,
            maxSingleSwapAmount: 10 ether
        });
    }

    receive() external payable {
        if (msg.value == 0) return;

        if (msg.value > type(uint128).max) revert("BuybackManager: Amount too large");

        uint256 buybackShare = msg.value * buybackConfig.platformRevenuePercent / 10000;

        uint256 newPendingAmount = uint256(pendingBuybackAmount) + buybackShare;
        if (newPendingAmount > type(uint128).max) revert("BuybackManager: Pending amount would overflow");

        pendingBuybackAmount = uint128(newPendingAmount);

        uint256 treasuryShare = msg.value - buybackShare;
        if (treasuryShare > 0) {
            (bool success, ) = treasury.call{value: treasuryShare}("");
            require(success, "BuybackManager: Treasury transfer failed");
        }

        emit RevenueReceived(msg.value, buybackShare);

        if (
            buybackConfig.autoExecute &&
            !paused() &&
            pendingBuybackAmount >= buybackConfig.minBuybackThreshold &&
            block.timestamp >= lastBuybackTime + buybackConfig.minBuybackInterval
        ) {
            executeBuyback();
        }
    }

    function executeBuyback() public nonReentrant whenNotPaused {
        require(
            pendingBuybackAmount >= buybackConfig.minBuybackThreshold,
            "BuybackManager: Pending amount below threshold"
        );
        require(
            block.timestamp >= lastBuybackTime + buybackConfig.minBuybackInterval,
            "BuybackManager: Buyback interval not met"
        );
        require(
            dexRouters.length > 0,
            "BuybackManager: No DEX router configured"
        );

        uint256 buybackAmount = pendingBuybackAmount;
        pendingBuybackAmount = 0;

        lastBuybackTime = uint40(block.timestamp);

        uint256 pxtoAllocate = buybackAmount * buybackConfig.pxtAllocationPercent / 10000;
        uint256 patoAllocate = buybackAmount - pxtoAllocate;

        uint256 pxtPurchased = 0;
        uint256 patPurchased = 0;

        if (pxtoAllocate > 0) {
            uint256 remainingPxt = pxtoAllocate;
            while (remainingPxt > 0) {
                uint256 swapAmount = remainingPxt > buybackConfig.maxSingleSwapAmount ?
                                    buybackConfig.maxSingleSwapAmount : remainingPxt;
                uint256 purchased = _executeDEXSwap(swapAmount, address(pxtoken));

                if (pxtPurchased + purchased > type(uint128).max)
                    revert("BuybackManager: PXT purchase would overflow");

                pxtPurchased = pxtPurchased + purchased;
                remainingPxt = remainingPxt > swapAmount ? remainingPxt - swapAmount : 0;
            }
        }

        if (patoAllocate > 0) {
            uint256 remainingPat = patoAllocate;
            while (remainingPat > 0) {
                uint256 swapAmount = remainingPat > buybackConfig.maxSingleSwapAmount ?
                                    buybackConfig.maxSingleSwapAmount : remainingPat;
                uint256 purchased = _executeDEXSwap(swapAmount, address(patoken));

                if (patPurchased + purchased > type(uint128).max)
                    revert("BuybackManager: PAT purchase would overflow");

                patPurchased = patPurchased + purchased;
                remainingPat = remainingPat > swapAmount ? remainingPat - swapAmount : 0;
            }
        }

        if (uint256(totalBuybackAmount) + buybackAmount > type(uint128).max)
            revert("BuybackManager: Total buyback would overflow");
        if (uint256(totalPXTPurchased) + pxtPurchased > type(uint128).max)
            revert("BuybackManager: Total PXT purchased would overflow");
        if (uint256(totalPATPurchased) + patPurchased > type(uint128).max)
            revert("BuybackManager: Total PAT purchased would overflow");

        totalBuybackAmount = uint128(uint256(totalBuybackAmount) + buybackAmount);
        totalPXTPurchased = uint128(uint256(totalPXTPurchased) + pxtPurchased);
        totalPATPurchased = uint128(uint256(totalPATPurchased) + patPurchased);

        if (uint256(pendingPXTForBurn) + pxtPurchased > type(uint128).max)
            revert("BuybackManager: Pending PXT for burn would overflow");
        if (uint256(pendingPATForBurn) + patPurchased > type(uint128).max)
            revert("BuybackManager: Pending PAT for burn would overflow");

        pendingPXTForBurn = uint128(uint256(pendingPXTForBurn) + pxtPurchased);
        pendingPATForBurn = uint128(uint256(pendingPATForBurn) + patPurchased);

        address[] memory routers = new address[](1);
        routers[0] = dexRouters[0];

        uint256[] memory rates = new uint256[](2);
        if (pxtoAllocate > 0 && pxtPurchased > 0) {
            rates[0] = pxtPurchased * 1e18 / pxtoAllocate;
        }
        if (patoAllocate > 0 && patPurchased > 0) {
            rates[1] = patPurchased * 1e18 / patoAllocate;
        }

        if (buybackAmount > type(uint128).max) revert("BuybackManager: Buyback amount too large");
        if (pxtPurchased > type(uint128).max) revert("BuybackManager: PXT purchased too large");
        if (patPurchased > type(uint128).max) revert("BuybackManager: PAT purchased too large");

        buybackRecords.push(BuybackRecord({
            timestamp: uint40(block.timestamp),
            platformRevenue: uint128(buybackAmount),
            buybackAmount: uint128(buybackAmount),
            pxtPurchased: uint128(pxtPurchased),
            patPurchased: uint128(patPurchased),
            dexRouters: routers,
            exchangeRates: rates,
            transactionHash: blockhash(block.number - 1),
            verified: false,
            sent: false
        }));

        uint256 recordId = buybackRecords.length - 1;

        emit BuybackExecuted(recordId, buybackAmount, pxtPurchased, patPurchased);
    }

    function _executeDEXSwap(uint256 _amount, address _tokenOut) internal returns (uint256) {
        address[] memory sortedRouters = _getSortedRouters();

        uint256 expectedAmount = _getExpectedAmount(_amount, _tokenOut);
        uint256 minAmountOut = expectedAmount * (10000 - buybackConfig.maxSlippage) / 10000;

        for (uint256 i = 0; i < sortedRouters.length;) {
            address router = sortedRouters[i];

            try IDEXRouter(router).swapExactETHForTokens{value: _amount}(
                minAmountOut,
                _getPathForETHToToken(_tokenOut),
                address(this),
                block.timestamp + 300
            ) returns (uint[] memory amounts) {
                emit SwapExecuted(router, wrappedNativeCoin, _tokenOut, _amount, amounts[amounts.length - 1]);
                return amounts[amounts.length - 1];
            } catch {
                unchecked { i++; }
                continue;
            }
        }

        revert("BuybackManager: All DEX swaps failed");
    }

    function _getSortedRouters() internal view returns (address[] memory) {
        address[] memory sortedRouters = new address[](dexRouters.length);

        for (uint256 i = 0; i < dexRouters.length;) {
            sortedRouters[i] = dexRouters[i];
            unchecked { i++; }
        }

        for (uint256 i = 1; i < sortedRouters.length;) {
            address key = sortedRouters[i];
            uint256 keyPriority = dexRouterPriority[key];
            int j = int(i) - 1;

            while (j >= 0 && dexRouterPriority[sortedRouters[uint(j)]] > keyPriority) {
                sortedRouters[uint(j + 1)] = sortedRouters[uint(j)];
                j--;
            }

            sortedRouters[uint(j + 1)] = key;
            unchecked { i++; }
        }

        return sortedRouters;
    }

    function _getPathForETHToToken(address _tokenOut) internal view returns (address[] memory) {
        address[] memory path = new address[](2);
        path[0] = wrappedNativeCoin;
        path[1] = _tokenOut;
        return path;
    }

    function _getExpectedAmount(uint256 _amount, address _tokenOut) internal view returns (uint256) {
        if (_tokenOut == address(pxtoken)) {
            return _amount * 1000;
        } else if (_tokenOut == address(patoken)) {
            return _amount * 2000;
        }
        return 0;
    }

    function sendTokensToBurnManager() external nonReentrant whenNotPaused {
        require(pendingPXTForBurn > 0 || pendingPATForBurn > 0, "BuybackManager: No tokens pending for burn");

        uint256 pxtAmount = pendingPXTForBurn;
        uint256 patAmount = pendingPATForBurn;

        pendingPXTForBurn = 0;
        pendingPATForBurn = 0;

        if (buybackRecords.length > 0 && !buybackRecords[buybackRecords.length - 1].sent) {
            buybackRecords[buybackRecords.length - 1].sent = true;
        }

        if (pxtAmount > 0) {
            require(
                pxtoken.balanceOf(address(this)) >= pxtAmount,
                "BuybackManager: Insufficient PXT balance"
            );
            require(
                pxtoken.transfer(burnManager, pxtAmount),
                "BuybackManager: PXT transfer failed"
            );
        }

        if (patAmount > 0) {
            require(
                patoken.balanceOf(address(this)) >= patAmount,
                "BuybackManager: Insufficient PAT balance"
            );
            require(
                patoken.transfer(burnManager, patAmount),
                "BuybackManager: PAT transfer failed"
            );
        }

        emit BuybackSent(burnManager, pxtAmount, patAmount);
    }

    function verifyBuyback(uint256 _recordId) external onlyOwner nonReentrant {
        require(_recordId < buybackRecords.length, "BuybackManager: Invalid record ID");

        BuybackRecord storage record = buybackRecords[_recordId];
        require(!record.verified, "BuybackManager: Record already verified");

        record.verified = true;

        emit BuybackVerified(_recordId, _msgSender());
    }
    
    function setBuybackConfig(
        uint256 _platformRevenuePercent,
        uint256 _minBuybackInterval,
        uint256 _minBuybackThreshold,
        uint256 _pxtAllocationPercent,
        bool _autoExecute,
        uint256 _maxSlippage,
        uint256 _maxSingleSwapAmount
    ) external onlyOwner nonReentrant {
        require(_platformRevenuePercent <= 10000, "BuybackManager: Platform revenue percentage out of range");
        require(_pxtAllocationPercent <= 10000, "BuybackManager: PXT allocation percentage out of range");
        require(_maxSlippage <= 5000, "BuybackManager: Max slippage cannot exceed 50%");
        require(_maxSingleSwapAmount > 0, "BuybackManager: Single swap amount must be greater than 0");

        require(_platformRevenuePercent <= type(uint16).max, "BuybackManager: Platform revenue percentage too large");
        require(_minBuybackInterval <= type(uint40).max, "BuybackManager: Min buyback interval too large");
        require(_minBuybackThreshold <= type(uint128).max, "BuybackManager: Min buyback threshold too large");
        require(_pxtAllocationPercent <= type(uint16).max, "BuybackManager: PXT allocation percentage too large");
        require(_maxSlippage <= type(uint16).max, "BuybackManager: Max slippage too large");
        require(_maxSingleSwapAmount <= type(uint128).max, "BuybackManager: Max single swap amount too large");

        buybackConfig.platformRevenuePercent = uint16(_platformRevenuePercent);
        buybackConfig.minBuybackInterval = uint40(_minBuybackInterval);
        buybackConfig.minBuybackThreshold = uint128(_minBuybackThreshold);
        buybackConfig.pxtAllocationPercent = uint16(_pxtAllocationPercent);
        buybackConfig.autoExecute = _autoExecute;
        buybackConfig.maxSlippage = uint16(_maxSlippage);
        buybackConfig.maxSingleSwapAmount = uint128(_maxSingleSwapAmount);

        emit BuybackConfigUpdated(
            _platformRevenuePercent,
            _minBuybackInterval,
            _minBuybackThreshold,
            _pxtAllocationPercent,
            _autoExecute,
            _maxSlippage,
            _maxSingleSwapAmount
        );
    }
    
    function addDEXRouter(address _router, uint256 _priority) external onlyOwner nonReentrant {
        require(_router != address(0), "BuybackManager: DEX router address cannot be zero");

        for (uint256 i = 0; i < dexRouters.length;) {
            require(dexRouters[i] != _router, "BuybackManager: DEX router already exists");
            unchecked { i++; }
        }

        dexRouters.push(_router);
        dexRouterPriority[_router] = _priority;

        emit DEXRouterAdded(_router, _priority);
    }

    function removeDEXRouter(address _router) external onlyOwner nonReentrant {
        require(_router != address(0), "BuybackManager: DEX router address cannot be zero");

        for (uint256 i = 0; i < dexRouters.length;) {
            if (dexRouters[i] == _router) {
                dexRouters[i] = dexRouters[dexRouters.length - 1];
                dexRouters.pop();
                delete dexRouterPriority[_router];

                emit DEXRouterRemoved(_router);
                return;
            }
            unchecked { i++; }
        }

        revert("BuybackManager: DEX router does not exist");
    }

    function updateRouterPriority(address _router, uint256 _priority) external onlyOwner nonReentrant {
        require(_router != address(0), "BuybackManager: DEX router address cannot be zero");

        bool routerExists = false;
        for (uint256 i = 0; i < dexRouters.length;) {
            if (dexRouters[i] == _router) {
                routerExists = true;
                break;
            }
            unchecked { i++; }
        }

        require(routerExists, "BuybackManager: DEX router does not exist");

        dexRouterPriority[_router] = _priority;
    }
    
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    function getBuybackRecordsCount() external view returns (uint256) {
        return buybackRecords.length;
    }

    function getDEXRoutersCount() external view returns (uint256) {
        return dexRouters.length;
    }

    function emergencyWithdraw(address _recipient, uint256 _amount) external onlyOwner nonReentrant {
        require(_recipient != address(0), "BuybackManager: Recipient address cannot be zero");
        require(_amount <= address(this).balance, "BuybackManager: Insufficient balance");

        (bool success, ) = _recipient.call{value: _amount}("");
        require(success, "BuybackManager: Transfer failed");

        emit EmergencyWithdraw(_recipient, _amount);
    }
    
    function emergencyTokenWithdraw(
        address _token,
        uint256 _amount,
        address _recipient
    ) external onlyOwner nonReentrant {
        require(_recipient != address(0), "BuybackManager: Recipient address cannot be zero");
        require(_token != address(0), "BuybackManager: Token address cannot be zero");

        IERC20 token = IERC20(_token);
        require(token.balanceOf(address(this)) >= _amount, "BuybackManager: Insufficient token balance");

        require(token.transfer(_recipient, _amount), "BuybackManager: Token transfer failed");

        emit EmergencyTokenWithdraw(_token, _recipient, _amount);
    }

    function getBuybackStatus() external view returns (
        uint256 pendingAmount,
        uint256 nextBuybackTime,
        bool canExecute,
        uint256 pendingPXT,
        uint256 pendingPAT
    ) {
        pendingAmount = pendingBuybackAmount;
        nextBuybackTime = lastBuybackTime + buybackConfig.minBuybackInterval;
        canExecute = (
            pendingBuybackAmount >= buybackConfig.minBuybackThreshold &&
            block.timestamp >= nextBuybackTime &&
            !paused()
        );
        pendingPXT = pendingPXTForBurn;
        pendingPAT = pendingPATForBurn;

        return (pendingAmount, nextBuybackTime, canExecute, pendingPXT, pendingPAT);
    }

    function getBuybackRecordsPaged(uint256 offset, uint256 limit) external view returns (BuybackRecord[] memory) {
        uint256 total = buybackRecords.length;
        if (offset >= total) return new BuybackRecord[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        BuybackRecord[] memory result = new BuybackRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = buybackRecords[i];
            unchecked { i++; }
        }
        return result;
    }

    function getDEXRoutersPaged(uint256 offset, uint256 limit) external view returns (address[] memory) {
        uint256 total = dexRouters.length;
        if (offset >= total) return new address[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        address[] memory result = new address[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = dexRouters[i];
            unchecked { i++; }
        }
        return result;
    }
}