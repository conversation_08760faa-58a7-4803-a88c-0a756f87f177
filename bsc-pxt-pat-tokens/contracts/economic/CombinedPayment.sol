// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract CombinedPayment is
    Ownable,
    ReentrancyGuard,
    Pausable
{
    IPXT public immutable pxtoken;
    IPAT public immutable patoken;

    enum ConsumptionType {
        CREATOR_UPGRADE,
        CONTENT_UPLOAD,
        ADVERTISEMENT,
        COMMENT_APPEAL,
        VIDEO_PLANNING
    }

    struct ScenarioConfig {
        bool isActive;
        uint128 baseFee;
        bool supportsCombined;
        uint8 burnPercentage;
        address beneficiary;
    }

    mapping(ConsumptionType => ScenarioConfig) public scenarioConfigs;

    struct CombinedPaymentPlan {
        uint8 patPercentage;
        uint8 pxtPercentage;
        uint8 discountPercentage;
    }

    CombinedPaymentPlan[] public paymentPlans;
    mapping(address => mapping(ConsumptionType => uint128)) public userConsumption;
    mapping(ConsumptionType => uint128) public totalConsumption;

    event PaymentProcessed(
        address indexed user,
        ConsumptionType consumptionType,
        uint256 patAmount,
        uint256 pxtAmount,
        uint256 burnAmount,
        bool isCombined
    );
    event ScenarioConfigUpdated(ConsumptionType consumptionType, uint256 baseFee, bool supportsCombined, uint256 burnPercentage, address beneficiary);
    event PaymentPlanAdded(uint256 index, uint256 patPercentage, uint256 pxtPercentage, uint256 discountPercentage);
    event PaymentPlanRemoved(uint256 index);

    constructor(
        address _pxtoken,
        address _patoken
    ) {
        require(_pxtoken != address(0), "CombinedPayment: PXT address cannot be zero");
        require(_patoken != address(0), "CombinedPayment: PAT address cannot be zero");
        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        paymentPlans.push(CombinedPaymentPlan({
            patPercentage: 70,
            pxtPercentage: 30,
            discountPercentage: 5
        }));
        paymentPlans.push(CombinedPaymentPlan({
            patPercentage: 50,
            pxtPercentage: 50,
            discountPercentage: 10
        }));
        paymentPlans.push(CombinedPaymentPlan({
            patPercentage: 30,
            pxtPercentage: 70,
            discountPercentage: 15
        }));
    }
    
    function setScenarioConfig(
        ConsumptionType _consumptionType,
        uint256 _baseFee,
        bool _supportsCombined,
        uint256 _burnPercentage,
        address _beneficiary
    ) external onlyOwner nonReentrant {
        if (_burnPercentage > 100) revert TokenErrors.InvalidPercentage();
        if (_beneficiary == address(0)) revert TokenErrors.ZeroAddress();
        if (_baseFee > type(uint128).max) revert TokenErrors.ValueTooLarge();
        if (_burnPercentage > type(uint8).max) revert TokenErrors.ValueTooLarge();

        scenarioConfigs[_consumptionType] = ScenarioConfig({
            isActive: true,
            baseFee: uint128(_baseFee),
            supportsCombined: _supportsCombined,
            burnPercentage: uint8(_burnPercentage),
            beneficiary: _beneficiary
        });

        emit ScenarioConfigUpdated(_consumptionType, _baseFee, _supportsCombined, _burnPercentage, _beneficiary);
    }

    function addPaymentPlan(
        uint256 _patPercentage,
        uint256 _pxtPercentage,
        uint256 _discountPercentage
    ) external onlyOwner nonReentrant {
        if (_patPercentage + _pxtPercentage != 100) revert TokenErrors.RatioSumNotHundred();
        if (_discountPercentage > 50) revert TokenErrors.ExceedsDiscountLimit();
        if (_patPercentage > type(uint8).max || _pxtPercentage > type(uint8).max || _discountPercentage > type(uint8).max)
            revert TokenErrors.ValueTooLarge();

        paymentPlans.push(CombinedPaymentPlan({
            patPercentage: uint8(_patPercentage),
            pxtPercentage: uint8(_pxtPercentage),
            discountPercentage: uint8(_discountPercentage)
        }));

        emit PaymentPlanAdded(paymentPlans.length - 1, _patPercentage, _pxtPercentage, _discountPercentage);
    }

    function removePaymentPlan(uint256 _index) external onlyOwner nonReentrant {
        if (_index >= paymentPlans.length) revert TokenErrors.IndexOutOfRange();

        if (_index < paymentPlans.length - 1) {
            paymentPlans[_index] = paymentPlans[paymentPlans.length - 1];
        }
        paymentPlans.pop();

        emit PaymentPlanRemoved(_index);
    }

    function payWithPAT(
        ConsumptionType _consumptionType
    ) external nonReentrant whenNotPaused returns (uint256) {
        ScenarioConfig storage config = scenarioConfigs[_consumptionType];
        if (!config.isActive) revert TokenErrors.ScenarioNotActive();

        uint256 fee = config.baseFee;
        if (fee == 0) revert TokenErrors.ZeroAmount();

        uint256 burnAmount = fee * config.burnPercentage / 100;
        uint256 beneficiaryAmount = fee - burnAmount;

        if (patoken.balanceOf(msg.sender) < fee)
            revert TokenErrors.InsufficientBalance();
        if (patoken.allowance(msg.sender, address(this)) < fee)
            revert TokenErrors.InsufficientAllowance();

        if (uint256(userConsumption[msg.sender][_consumptionType]) + fee > type(uint128).max)
            revert TokenErrors.ValueTooLarge();
        if (uint256(totalConsumption[_consumptionType]) + fee > type(uint128).max)
            revert TokenErrors.ValueTooLarge();

        userConsumption[msg.sender][_consumptionType] = uint128(uint256(userConsumption[msg.sender][_consumptionType]) + fee);
        totalConsumption[_consumptionType] = uint128(uint256(totalConsumption[_consumptionType]) + fee);

        patoken.transferFrom(msg.sender, address(this), fee);

        if (burnAmount > 0) {
            patoken.burn(burnAmount);
        }

        if (beneficiaryAmount > 0) {
            patoken.transfer(config.beneficiary, beneficiaryAmount);
        }

        emit PaymentProcessed(msg.sender, _consumptionType, fee, 0, burnAmount, false);

        return fee;
    }

    function payCombined(
        ConsumptionType _consumptionType,
        uint256 _planIndex
    ) external nonReentrant whenNotPaused returns (uint256 patAmount, uint256 pxtAmount) {
        ScenarioConfig storage config = scenarioConfigs[_consumptionType];
        if (!config.isActive) revert TokenErrors.ScenarioNotActive();
        if (!config.supportsCombined) revert TokenErrors.CombinedPaymentNotSupported();
        if (_planIndex >= paymentPlans.length) revert TokenErrors.InvalidPlanIndex();

        CombinedPaymentPlan storage plan = paymentPlans[_planIndex];

        uint256 originalFee = config.baseFee;
        uint256 discountedFee = originalFee * (100 - plan.discountPercentage) / 100;

        patAmount = discountedFee * plan.patPercentage / 100;
        pxtAmount = discountedFee * plan.pxtPercentage / 100;

        if (patAmount + pxtAmount != discountedFee) revert TokenErrors.InvalidPaymentCalculation();

        uint256 patBurnAmount = patAmount * config.burnPercentage / 100;
        uint256 patBeneficiaryAmount = patAmount - patBurnAmount;
        uint256 pxtBurnAmount = pxtAmount;

        if (patoken.balanceOf(msg.sender) < patAmount)
            revert TokenErrors.InsufficientBalance();
        if (patoken.allowance(msg.sender, address(this)) < patAmount)
            revert TokenErrors.InsufficientAllowance();
        if (pxtoken.balanceOf(msg.sender) < pxtAmount)
            revert TokenErrors.InsufficientBalance();
        if (pxtoken.allowance(msg.sender, address(this)) < pxtAmount)
            revert TokenErrors.InsufficientAllowance();

        if (uint256(userConsumption[msg.sender][_consumptionType]) + originalFee > type(uint128).max)
            revert TokenErrors.ValueTooLarge();
        if (uint256(totalConsumption[_consumptionType]) + originalFee > type(uint128).max)
            revert TokenErrors.ValueTooLarge();

        userConsumption[msg.sender][_consumptionType] = uint128(uint256(userConsumption[msg.sender][_consumptionType]) + originalFee);
        totalConsumption[_consumptionType] = uint128(uint256(totalConsumption[_consumptionType]) + originalFee);

        patoken.transferFrom(msg.sender, address(this), patAmount);
        pxtoken.transferFrom(msg.sender, address(this), pxtAmount);

        if (patBurnAmount > 0) {
            patoken.burn(patBurnAmount);
        }

        if (patBeneficiaryAmount > 0) {
            patoken.transfer(config.beneficiary, patBeneficiaryAmount);
        }

        if (pxtBurnAmount > 0) {
            pxtoken.burn(pxtBurnAmount);
        }

        emit PaymentProcessed(
            msg.sender,
            _consumptionType,
            patAmount,
            pxtAmount,
            patBurnAmount + pxtBurnAmount,
            true
        );

        return (patAmount, pxtAmount);
    }
    
    function calculateCombinedPayment(
        ConsumptionType _consumptionType,
        uint256 _planIndex
    ) external view returns (uint256 patAmount, uint256 pxtAmount, uint256 totalDiscount) {
        ScenarioConfig storage config = scenarioConfigs[_consumptionType];
        if (!config.isActive) revert TokenErrors.ScenarioNotActive();
        if (!config.supportsCombined) revert TokenErrors.CombinedPaymentNotSupported();
        if (_planIndex >= paymentPlans.length) revert TokenErrors.InvalidPlanIndex();

        CombinedPaymentPlan storage plan = paymentPlans[_planIndex];

        uint256 originalFee = config.baseFee;
        uint256 discountedFee = originalFee * (100 - plan.discountPercentage) / 100;
        totalDiscount = originalFee - discountedFee;

        patAmount = discountedFee * plan.patPercentage / 100;
        pxtAmount = discountedFee * plan.pxtPercentage / 100;

        return (patAmount, pxtAmount, totalDiscount);
    }

    function getUserConsumption(
        address _user,
        ConsumptionType _consumptionType
    ) external view returns (uint256) {
        return userConsumption[_user][_consumptionType];
    }

    function getTotalConsumption(
        ConsumptionType _consumptionType
    ) external view returns (uint256) {
        return totalConsumption[_consumptionType];
    }

    function getPaymentPlansCount() external view returns (uint256) {
        return paymentPlans.length;
    }

    function getPaymentPlansPaged(uint256 offset, uint256 limit) external view returns (CombinedPaymentPlan[] memory) {
        uint256 total = paymentPlans.length;
        if (offset >= total) return new CombinedPaymentPlan[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        CombinedPaymentPlan[] memory result = new CombinedPaymentPlan[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = paymentPlans[i];
            unchecked { i++; }
        }
        return result;
    }

    function setPaused(bool _paused) external onlyOwner {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }
    }

    function setScenarioActive(
        ConsumptionType _consumptionType,
        bool _isActive
    ) external onlyOwner nonReentrant {
        scenarioConfigs[_consumptionType].isActive = _isActive;
    }

    function emergencyWithdrawToken(address _token, uint256 _amount) external onlyOwner nonReentrant {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        IPAT token = IPAT(_token);
        if (_amount > token.balanceOf(address(this))) revert TokenErrors.InsufficientBalance();
        token.transfer(owner(), _amount);
    }
}