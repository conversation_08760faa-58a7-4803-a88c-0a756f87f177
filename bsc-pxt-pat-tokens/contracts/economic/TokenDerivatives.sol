// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";

contract TokenDerivatives is
    Ownable,
    ReentrancyGuard,
    ERC721URIStorage
{
    using Counters for Counters.Counter;

    Counters.Counter private _tokenIdCounter;
    IPAT public immutable patoken;
    IPXT public immutable pxtoken;
    address public yieldPool;

    enum NFTType {
        UNKNOWN,
        YIELD_CERT,
        ART_NFT,
        OPTION,
        MEMBERSHIP
    }

    struct NFTMetadata {
        uint256 tokenId;
        NFTType nftType;
        string name;
        string description;
        address creator;
        uint40 creationTime;
        uint128 baseValue;
        bool transferable;
    }

    struct YieldCertificate {
        uint256 tokenId;
        address tokenAddress;
        uint128 lockedAmount;
        uint40 lockStartTime;
        uint40 lockEndTime;
        uint16 yieldRate;
        bool autoRenew;
        bool claimed;
    }

    struct TokenOption {
        uint256 tokenId;
        address underlyingToken;
        uint128 strikePrice;
        uint40 expiration;
        bool isCall;
        uint128 premium;
        bool exercised;
    }

    mapping(uint256 => NFTMetadata) public nftMetadata;
    mapping(uint256 => YieldCertificate) public yieldCertificates;
    mapping(uint256 => TokenOption) public tokenOptions;
    mapping(address => uint256[]) public creatorNFTs;

    struct GlobalConfig {
        uint128 mintFee;
        address feeReceiver;
        uint16 creatorRoyalty;
        uint16 platformRoyalty;
        uint16 maxYieldRate;
        uint40 minLockPeriod;
    }

    GlobalConfig public globalConfig;

    event NFTMinted(
        uint256 indexed tokenId,
        address indexed creator,
        NFTType nftType,
        string tokenURI
    );
    event YieldCertificateCreated(
        uint256 indexed tokenId,
        address indexed owner,
        address tokenAddress,
        uint128 lockedAmount,
        uint40 lockEndTime,
        uint16 yieldRate
    );
    event TokenOptionCreated(
        uint256 indexed tokenId,
        address indexed creator,
        address underlyingToken,
        uint128 strikePrice,
        uint40 expiration,
        bool isCall,
        uint128 premium
    );
    event YieldClaimed(
        uint256 indexed tokenId,
        address indexed owner,
        uint128 yieldAmount
    );
    event OptionExercised(
        uint256 indexed tokenId,
        address indexed owner,
        uint128 amount
    );
    event ConfigUpdated(
        uint128 mintFee,
        address feeReceiver,
        uint16 creatorRoyalty,
        uint16 platformRoyalty,
        uint16 maxYieldRate,
        uint40 minLockPeriod
    );

    constructor(
        address _patoken,
        address _pxtoken,
        address _yieldPool,
        address _feeReceiver
    ) ERC721("PXPat Derivatives", "PXPD") {
        patoken = IPAT(_patoken);
        pxtoken = IPXT(_pxtoken);
        yieldPool = _yieldPool;
        globalConfig = GlobalConfig({
            mintFee: 100 * 10**18,
            feeReceiver: _feeReceiver,
            creatorRoyalty: 500,
            platformRoyalty: 250,
            maxYieldRate: 1000,
            minLockPeriod: 30 days
        });
    }

    function mintYieldCertificate(
        address _tokenAddress,
        uint128 _amount,
        uint40 _lockPeriod,
        uint16 _yieldRate,
        bool _autoRenew,
        string memory _name,
        string memory _description,
        string memory _tokenURI
    ) external nonReentrant returns (uint256) {
        if (_tokenAddress != address(patoken) && _tokenAddress != address(pxtoken)) {
            revert TokenErrors.InvalidOperation();
        }
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_lockPeriod < globalConfig.minLockPeriod) revert TokenErrors.InsufficientStakingDuration();
        if (_yieldRate > globalConfig.maxYieldRate) revert TokenErrors.InvalidRewardBoost();

        if (globalConfig.mintFee > 0) {
            patoken.transferFrom(_msgSender(), globalConfig.feeReceiver, globalConfig.mintFee);
        }

        IERC20(address(patoken)).transferFrom(_msgSender(), yieldPool, _amount);

        uint256 tokenId = _mintNFT(_msgSender(), _tokenURI);

        nftMetadata[tokenId] = NFTMetadata({
            tokenId: tokenId,
            nftType: NFTType.YIELD_CERT,
            name: _name,
            description: _description,
            creator: _msgSender(),
            creationTime: uint40(block.timestamp),
            baseValue: _amount,
            transferable: true
        });

        yieldCertificates[tokenId] = YieldCertificate({
            tokenId: tokenId,
            tokenAddress: _tokenAddress,
            lockedAmount: _amount,
            lockStartTime: uint40(block.timestamp),
            lockEndTime: uint40(block.timestamp + _lockPeriod),
            yieldRate: _yieldRate,
            autoRenew: _autoRenew,
            claimed: false
        });

        creatorNFTs[_msgSender()].push(tokenId);

        emit YieldCertificateCreated(
            tokenId,
            _msgSender(),
            _tokenAddress,
            _amount,
            uint40(block.timestamp + _lockPeriod),
            _yieldRate
        );

        return tokenId;
    }
    
    function mintTokenOption(
        address _underlyingToken,
        uint128 _strikePrice,
        uint40 _duration,
        bool _isCall,
        uint128 _premium,
        string memory _name,
        string memory _description,
        string memory _tokenURI
    ) external nonReentrant returns (uint256) {
        if (_underlyingToken != address(patoken) && _underlyingToken != address(pxtoken)) {
            revert TokenErrors.InvalidOperation();
        }
        if (_strikePrice == 0) revert TokenErrors.ZeroAmount();
        if (_duration == 0) revert TokenErrors.InvalidTimeRange();
        if (_premium == 0) revert TokenErrors.ZeroAmount();

        if (globalConfig.mintFee > 0) {
            patoken.transferFrom(_msgSender(), globalConfig.feeReceiver, globalConfig.mintFee);
        }

        patoken.transferFrom(_msgSender(), address(this), _premium);

        uint256 tokenId = _mintNFT(_msgSender(), _tokenURI);

        nftMetadata[tokenId] = NFTMetadata({
            tokenId: tokenId,
            nftType: NFTType.OPTION,
            name: _name,
            description: _description,
            creator: _msgSender(),
            creationTime: uint40(block.timestamp),
            baseValue: _premium,
            transferable: true
        });

        tokenOptions[tokenId] = TokenOption({
            tokenId: tokenId,
            underlyingToken: _underlyingToken,
            strikePrice: _strikePrice,
            expiration: uint40(block.timestamp + _duration),
            isCall: _isCall,
            premium: _premium,
            exercised: false
        });

        creatorNFTs[_msgSender()].push(tokenId);

        emit TokenOptionCreated(
            tokenId,
            _msgSender(),
            _underlyingToken,
            _strikePrice,
            uint40(block.timestamp + _duration),
            _isCall,
            _premium
        );

        return tokenId;
    }
    
    function mintArtNFT(
        uint128 _baseValue,
        string memory _name,
        string memory _description,
        string memory _tokenURI,
        bool _transferable
    ) external nonReentrant returns (uint256) {
        if (globalConfig.mintFee > 0) {
            patoken.transferFrom(_msgSender(), globalConfig.feeReceiver, globalConfig.mintFee);
        }

        uint256 tokenId = _mintNFT(_msgSender(), _tokenURI);

        nftMetadata[tokenId] = NFTMetadata({
            tokenId: tokenId,
            nftType: NFTType.ART_NFT,
            name: _name,
            description: _description,
            creator: _msgSender(),
            creationTime: uint40(block.timestamp),
            baseValue: _baseValue,
            transferable: _transferable
        });

        creatorNFTs[_msgSender()].push(tokenId);

        emit NFTMinted(
            tokenId,
            _msgSender(),
            NFTType.ART_NFT,
            _tokenURI
        );

        return tokenId;
    }
    
    function claimYield(uint256 _tokenId) external nonReentrant returns (uint128) {
        if (!_exists(_tokenId)) revert TokenErrors.InvalidRecordId();
        if (ownerOf(_tokenId) != _msgSender()) revert TokenErrors.Unauthorized();
        if (nftMetadata[_tokenId].nftType != NFTType.YIELD_CERT) revert TokenErrors.InvalidOperation();

        YieldCertificate storage cert = yieldCertificates[_tokenId];
        if (cert.claimed) revert TokenErrors.AlreadyClaimed();
        if (block.timestamp < cert.lockEndTime) revert TokenErrors.NotUnlocked();

        uint256 lockDuration = cert.lockEndTime - cert.lockStartTime;
        uint128 yieldAmount = uint128((uint256(cert.lockedAmount) * cert.yieldRate * lockDuration) / (365 days) / 10000);

        cert.claimed = true;

        address tokenAddress = cert.tokenAddress;
        uint256 totalAmount = cert.lockedAmount + yieldAmount;

        IERC20(tokenAddress).transferFrom(yieldPool, _msgSender(), totalAmount);

        if (cert.autoRenew) {

        } else {
            _burn(_tokenId);
        }

        emit YieldClaimed(_tokenId, _msgSender(), yieldAmount);

        return yieldAmount;
    }
    
    function exerciseOption(uint256 _tokenId, uint128 _amount) external nonReentrant {
        if (!_exists(_tokenId)) revert TokenErrors.InvalidRecordId();
        if (ownerOf(_tokenId) != _msgSender()) revert TokenErrors.Unauthorized();
        if (nftMetadata[_tokenId].nftType != NFTType.OPTION) revert TokenErrors.InvalidOperation();

        TokenOption storage option = tokenOptions[_tokenId];
        if (option.exercised) revert TokenErrors.AlreadyClaimed();
        if (block.timestamp > option.expiration) revert TokenErrors.InvalidTimeRange();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        option.exercised = true;

        emit OptionExercised(_tokenId, _msgSender(), _amount);

        _burn(_tokenId);
    }
    
    function updateConfig(
        uint128 _mintFee,
        address _feeReceiver,
        uint16 _creatorRoyalty,
        uint16 _platformRoyalty,
        uint16 _maxYieldRate,
        uint40 _minLockPeriod
    ) external onlyOwner {
        if (_feeReceiver == address(0)) revert TokenErrors.ZeroAddress();
        if (_creatorRoyalty > 1000) revert TokenErrors.InvalidPercentage();
        if (_platformRoyalty > 1000) revert TokenErrors.InvalidPercentage();

        globalConfig.mintFee = _mintFee;
        globalConfig.feeReceiver = _feeReceiver;
        globalConfig.creatorRoyalty = _creatorRoyalty;
        globalConfig.platformRoyalty = _platformRoyalty;
        globalConfig.maxYieldRate = _maxYieldRate;
        globalConfig.minLockPeriod = _minLockPeriod;

        emit ConfigUpdated(
            _mintFee,
            _feeReceiver,
            _creatorRoyalty,
            _platformRoyalty,
            _maxYieldRate,
            _minLockPeriod
        );
    }

    function updateYieldPool(address _yieldPool) external onlyOwner {
        if (_yieldPool == address(0)) revert TokenErrors.ZeroAddress();
        yieldPool = _yieldPool;
    }

    function isTransferable(uint256 _tokenId) public view returns (bool) {
        if (!_exists(_tokenId)) revert TokenErrors.InvalidRecordId();

        return nftMetadata[_tokenId].transferable;
    }
    
    function getCreatorNFTCount(address _creator) external view returns (uint256) {
        return creatorNFTs[_creator].length;
    }

    function getCreatorNFTs(
        address _creator,
        uint256 _start,
        uint256 _count
    ) external view returns (uint256[] memory tokenIds) {
        if (_start >= creatorNFTs[_creator].length) revert TokenErrors.IndexOutOfRange();

        uint256 endIndex = _start + _count;
        if (endIndex > creatorNFTs[_creator].length) {
            endIndex = creatorNFTs[_creator].length;
        }

        uint256 resultCount = endIndex - _start;
        tokenIds = new uint256[](resultCount);

        for (uint256 i = 0; i < resultCount;) {
            uint256 index = _start + i;
            tokenIds[i] = creatorNFTs[_creator][index];
            unchecked { i++; }
        }

        return tokenIds;
    }

    function _mintNFT(address _to, string memory _tokenURI) internal returns (uint256) {
        _tokenIdCounter.increment();
        uint256 tokenId = _tokenIdCounter.current();

        _safeMint(_to, tokenId);
        _setTokenURI(tokenId, _tokenURI);

        return tokenId;
    }

    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override {
        super._beforeTokenTransfer(from, to, tokenId, batchSize);

        if (from != address(0)) {
            if (!isTransferable(tokenId)) revert TokenErrors.InvalidOperation();
        }
    }

    function supportsInterface(bytes4 interfaceId) public view override returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    function getNFTsPaged(uint256 offset, uint256 limit) external view returns (NFTMetadata[] memory) {
        uint256 total = _tokenIdCounter.current();
        if (offset >= total) return new NFTMetadata[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        NFTMetadata[] memory result = new NFTMetadata[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = nftMetadata[i + 1];
            unchecked { i++; }
        }
        return result;
    }

    function getYieldCertificatesPaged(uint256 offset, uint256 limit) external view returns (YieldCertificate[] memory) {
        uint256 total = _tokenIdCounter.current();
        if (offset >= total) return new YieldCertificate[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        YieldCertificate[] memory result = new YieldCertificate[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = yieldCertificates[i + 1];
            unchecked { i++; }
        }
        return result;
    }

    function getTokenOptionsPaged(uint256 offset, uint256 limit) external view returns (TokenOption[] memory) {
        uint256 total = _tokenIdCounter.current();
        if (offset >= total) return new TokenOption[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        TokenOption[] memory result = new TokenOption[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = tokenOptions[i + 1];
            unchecked { i++; }
        }
        return result;
    }
}