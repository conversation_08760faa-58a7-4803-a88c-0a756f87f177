// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract InflationDashboard is Ownable, ReentrancyGuard {
    using SafeMath for uint256;

    uint256 private constant SECONDS_PER_DAY = 86400;
    uint256 private constant SECONDS_PER_YEAR = 365 * SECONDS_PER_DAY;

    IPAT public patoken;
    address public immutable inflationControl;
    uint32 public dataCaptureInterval;

    struct AlertThreshold {
        uint16 quarterlyInflationWarning;
        uint16 annualInflationWarning;
        uint16 supplyGrowthRateWarning;
        uint16 burnRatioWarning;
    }

    AlertThreshold public alertThreshold;

    struct InflationDataPoint {
        uint40 timestamp;
        uint128 totalSupply;
        uint128 circulatingSupply;
        uint128 mintedAmount;
        uint128 burnedAmount;
        uint128 netInflationAmount;
        uint16 netInflationRate;
        bool isAlertTriggered;
        string alertMessage;
    }

    InflationDataPoint[] public inflationHistory;

    struct QuarterlyInflation {
        uint40 startTimestamp;
        uint40 endTimestamp;
        uint128 startSupply;
        uint128 endSupply;
        uint128 totalMinted;
        uint128 totalBurned;
        uint128 netInflation;
        uint16 inflationRate;
    }

    struct AnnualInflation {
        uint16 year;
        uint128 startSupply;
        uint128 endSupply;
        uint128 totalMinted;
        uint128 totalBurned;
        uint128 netInflation;
        uint16 inflationRate;
        QuarterlyInflation[4] quarters;
    }

    mapping(uint16 => AnnualInflation) public annualInflationData;
    uint16[] public recordedYears;
    uint40 public lastDataCaptureTime;

    struct AlertRecord {
        uint40 timestamp;
        string alertType;
        string message;
        uint16 currentValue;
        uint16 thresholdValue;
        bool isActive;
        uint40 resolvedTimestamp;
    }

    AlertRecord[] public alertRecords;
    uint16 public activeAlertCount;

    event DataCaptured(uint40 indexed timestamp, uint128 totalSupply, uint16 netInflationRate);
    event QuarterlyDataUpdated(uint16 indexed year, uint8 indexed quarter, uint16 inflationRate);
    event AnnualDataUpdated(uint16 indexed year, uint16 inflationRate);
    event AlertTriggered(uint256 indexed alertId, string alertType, string message);
    event AlertResolved(uint256 indexed alertId, uint40 resolvedTimestamp);
    event AlertThresholdUpdated(
        uint16 quarterlyInflationWarning,
        uint16 annualInflationWarning,
        uint16 supplyGrowthRateWarning,
        uint16 burnRatioWarning
    );

    constructor(
        address _patoken,
        address _inflationControl
    ) {
        patoken = IPAT(_patoken);
        inflationControl = _inflationControl;
        dataCaptureInterval = 1 days;
        alertThreshold = AlertThreshold({
            quarterlyInflationWarning: 50,
            annualInflationWarning: 150,
            supplyGrowthRateWarning: 200,
            burnRatioWarning: 50
        });
        captureDataPoint();
    }

    function captureDataPoint() public nonReentrant returns (uint256) {
        if (block.timestamp < lastDataCaptureTime + dataCaptureInterval && lastDataCaptureTime != 0) {
            revert TokenErrors.CapturePeriodNotElapsed();
        }

        uint256 currentTotalSupply = patoken.totalSupply();
        uint256 currentCirculatingSupply = patoken.totalSupply() - patoken.balanceOf(address(0));

        uint256 mintedAmount = 0;
        uint256 burnedAmount = 0;
        uint256 netInflationAmount = 0;
        uint16 netInflationRate = 0;

        if (inflationHistory.length > 0) {
            InflationDataPoint memory lastPoint = inflationHistory[inflationHistory.length - 1];

            if (currentTotalSupply > lastPoint.totalSupply) {
                mintedAmount = currentTotalSupply - lastPoint.totalSupply;
                netInflationAmount = mintedAmount;
            } else if (currentTotalSupply < lastPoint.totalSupply) {
                burnedAmount = lastPoint.totalSupply - currentTotalSupply;
                netInflationAmount = 0;
            }

            if (lastPoint.totalSupply > 0) {
                if (currentTotalSupply > lastPoint.totalSupply) {
                    netInflationRate = uint16((mintedAmount * 10000) / lastPoint.totalSupply);
                }
            }
        }

        bool isAlertTriggered = false;
        string memory alertMessage = "";

        if (netInflationRate > alertThreshold.quarterlyInflationWarning && inflationHistory.length > 0) {
            isAlertTriggered = true;
            alertMessage = "Quarterly inflation rate exceeds warning threshold";

            alertRecords.push(AlertRecord({
                timestamp: uint40(block.timestamp),
                alertType: "QUARTERLY_INFLATION",
                message: alertMessage,
                currentValue: netInflationRate,
                thresholdValue: alertThreshold.quarterlyInflationWarning,
                isActive: true,
                resolvedTimestamp: 0
            }));

            activeAlertCount++;

            emit AlertTriggered(
                alertRecords.length - 1,
                "QUARTERLY_INFLATION",
                alertMessage
            );
        }

        inflationHistory.push(InflationDataPoint({
            timestamp: uint40(block.timestamp),
            totalSupply: uint128(currentTotalSupply),
            circulatingSupply: uint128(currentCirculatingSupply),
            mintedAmount: uint128(mintedAmount),
            burnedAmount: uint128(burnedAmount),
            netInflationAmount: uint128(netInflationAmount),
            netInflationRate: netInflationRate,
            isAlertTriggered: isAlertTriggered,
            alertMessage: alertMessage
        }));

        lastDataCaptureTime = uint40(block.timestamp);

        updatePeriodicalStats();

        emit DataCaptured(uint40(block.timestamp), uint128(currentTotalSupply), netInflationRate);

        return inflationHistory.length - 1;
    }

    function updatePeriodicalStats() internal {
        if (inflationHistory.length < 2) {
            return;
        }

        (uint16 currentYear, uint8 currentQuarter) = getCurrentYearAndQuarter();

        bool yearExists = false;
        uint256 recordedYearsLength = recordedYears.length;
        for (uint256 i = 0; i < recordedYearsLength;) {
            if (recordedYears[i] == currentYear) {
                yearExists = true;
                break;
            }
            unchecked { i++; }
        }

        if (!yearExists) {
            recordedYears.push(currentYear);
            annualInflationData[currentYear].year = currentYear;
            annualInflationData[currentYear].startSupply = uint128(getFirstSupplyOfYear(currentYear));
        }

        QuarterlyInflation storage currentQuarterData = annualInflationData[currentYear].quarters[currentQuarter - 1];

        if (currentQuarterData.startTimestamp == 0) {
            currentQuarterData.startTimestamp = uint40(getQuarterStartTime(currentYear, currentQuarter));
            currentQuarterData.startSupply = uint128(getSupplyAtTimestamp(currentQuarterData.startTimestamp));
        }

        (uint256 quarterlyMinted, uint256 quarterlyBurned) = getQuarterlyMintedAndBurned(currentYear, currentQuarter);

        currentQuarterData.endTimestamp = uint40(block.timestamp);
        currentQuarterData.endSupply = uint128(patoken.totalSupply());
        currentQuarterData.totalMinted = uint128(quarterlyMinted);
        currentQuarterData.totalBurned = uint128(quarterlyBurned);
        currentQuarterData.netInflation = quarterlyMinted > quarterlyBurned ?
            uint128(quarterlyMinted - quarterlyBurned) : 0;

        if (currentQuarterData.startSupply > 0) {
            currentQuarterData.inflationRate = uint16((currentQuarterData.netInflation * 10000) / currentQuarterData.startSupply);
        }

        annualInflationData[currentYear].endSupply = uint128(patoken.totalSupply());
        annualInflationData[currentYear].totalMinted = 0;
        annualInflationData[currentYear].totalBurned = 0;

        for (uint8 q = 0; q < 4;) {
            annualInflationData[currentYear].totalMinted += annualInflationData[currentYear].quarters[q].totalMinted;
            annualInflationData[currentYear].totalBurned += annualInflationData[currentYear].quarters[q].totalBurned;
            unchecked { q++; }
        }

        annualInflationData[currentYear].netInflation = annualInflationData[currentYear].totalMinted >
            annualInflationData[currentYear].totalBurned ?
            annualInflationData[currentYear].totalMinted - annualInflationData[currentYear].totalBurned : 0;

        if (annualInflationData[currentYear].startSupply > 0) {
            annualInflationData[currentYear].inflationRate = uint16((annualInflationData[currentYear].netInflation * 10000) /
                annualInflationData[currentYear].startSupply);
        }

        emit QuarterlyDataUpdated(currentYear, currentQuarter, currentQuarterData.inflationRate);
        emit AnnualDataUpdated(currentYear, annualInflationData[currentYear].inflationRate);

        checkQuarterlyInflationAlert(currentYear, currentQuarter);
        checkAnnualInflationAlert(currentYear);
    }
    
    function checkQuarterlyInflationAlert(uint16 year, uint8 quarter) internal {
        QuarterlyInflation storage quarterData = annualInflationData[year].quarters[quarter - 1];

        if (quarterData.inflationRate > alertThreshold.quarterlyInflationWarning) {
            string memory message = string(abi.encodePacked(
                "Quarterly inflation rate exceeds warning threshold: ",
                uintToString(quarterData.inflationRate / 100),
                ".",
                uintToString(quarterData.inflationRate % 100),
                "% > ",
                uintToString(alertThreshold.quarterlyInflationWarning / 100),
                ".",
                uintToString(alertThreshold.quarterlyInflationWarning % 100),
                "%"
            ));

            alertRecords.push(AlertRecord({
                timestamp: uint40(block.timestamp),
                alertType: "QUARTERLY_INFLATION",
                message: message,
                currentValue: quarterData.inflationRate,
                thresholdValue: alertThreshold.quarterlyInflationWarning,
                isActive: true,
                resolvedTimestamp: 0
            }));

            activeAlertCount++;

            emit AlertTriggered(
                alertRecords.length - 1,
                "QUARTERLY_INFLATION",
                message
            );
        }
    }

    function checkAnnualInflationAlert(uint16 year) internal {
        AnnualInflation storage yearData = annualInflationData[year];

        if (yearData.inflationRate > alertThreshold.annualInflationWarning) {
            string memory message = string(abi.encodePacked(
                "Annual inflation rate exceeds warning threshold: ",
                uintToString(yearData.inflationRate / 100),
                ".",
                uintToString(yearData.inflationRate % 100),
                "% > ",
                uintToString(alertThreshold.annualInflationWarning / 100),
                ".",
                uintToString(alertThreshold.annualInflationWarning % 100),
                "%"
            ));

            alertRecords.push(AlertRecord({
                timestamp: uint40(block.timestamp),
                alertType: "ANNUAL_INFLATION",
                message: message,
                currentValue: yearData.inflationRate,
                thresholdValue: alertThreshold.annualInflationWarning,
                isActive: true,
                resolvedTimestamp: 0
            }));

            activeAlertCount++;

            emit AlertTriggered(
                alertRecords.length - 1,
                "ANNUAL_INFLATION",
                message
            );
        }
    }
    
    function resolveAlert(uint256 alertId) external onlyOwner {
        if (alertId >= alertRecords.length) revert TokenErrors.InvalidRecordId();
        if (!alertRecords[alertId].isActive) revert TokenErrors.AlertNotActive();

        alertRecords[alertId].isActive = false;
        alertRecords[alertId].resolvedTimestamp = uint40(block.timestamp);

        activeAlertCount--;

        emit AlertResolved(alertId, uint40(block.timestamp));
    }

    function setAlertThresholds(
        uint16 _quarterlyInflationWarning,
        uint16 _annualInflationWarning,
        uint16 _supplyGrowthRateWarning,
        uint16 _burnRatioWarning
    ) external onlyOwner {
        alertThreshold.quarterlyInflationWarning = _quarterlyInflationWarning;
        alertThreshold.annualInflationWarning = _annualInflationWarning;
        alertThreshold.supplyGrowthRateWarning = _supplyGrowthRateWarning;
        alertThreshold.burnRatioWarning = _burnRatioWarning;

        emit AlertThresholdUpdated(
            _quarterlyInflationWarning,
            _annualInflationWarning,
            _supplyGrowthRateWarning,
            _burnRatioWarning
        );
    }

    function setDataCaptureInterval(uint32 _interval) external onlyOwner {
        if (_interval == 0) revert TokenErrors.ZeroAmount();
        dataCaptureInterval = _interval;
    }
    
    function getCurrentYearAndQuarter() public view returns (uint16 year, uint8 quarter) {
        (year, , ) = timestampToDate(block.timestamp);

        uint8 month = uint8((block.timestamp / SECONDS_PER_DAY % 365) / 30 + 1);

        if (month <= 3) {
            quarter = 1;
        } else if (month <= 6) {
            quarter = 2;
        } else if (month <= 9) {
            quarter = 3;
        } else {
            quarter = 4;
        }

        return (year, quarter);
    }

    function getQuarterStartTime(uint16 year, uint8 quarter) public pure returns (uint256) {
        if (quarter < 1 || quarter > 4) revert TokenErrors.InvalidQuarter();

        uint8 startMonth;
        if (quarter == 1) startMonth = 1;
        else if (quarter == 2) startMonth = 4;
        else if (quarter == 3) startMonth = 7;
        else startMonth = 10;

        return dateToTimestamp(year, startMonth, 1);
    }

    function getQuarterEndTime(uint16 year, uint8 quarter) public pure returns (uint256) {
        if (quarter < 1 || quarter > 4) revert TokenErrors.InvalidQuarter();

        uint8 endMonth;
        uint8 endDay;

        if (quarter == 1) {
            endMonth = 3;
            endDay = 31;
        } else if (quarter == 2) {
            endMonth = 6;
            endDay = 30;
        } else if (quarter == 3) {
            endMonth = 9;
            endDay = 30;
        } else {
            endMonth = 12;
            endDay = 31;
        }

        return dateToTimestamp(year, endMonth, endDay) + 86399;
    }
    
    function getFirstSupplyOfYear(uint16 year) public view returns (uint256) {
        uint256 timestamp = dateToTimestamp(year, 1, 1);
        return getSupplyAtTimestamp(timestamp);
    }

    function getSupplyAtTimestamp(uint256 timestamp) public view returns (uint256) {
        if (inflationHistory.length == 0 || timestamp < inflationHistory[0].timestamp) {
            return 0;
        }

        uint256 closestIndex = 0;
        uint256 minDiff = type(uint256).max;
        uint256 historyLength = inflationHistory.length;

        for (uint256 i = 0; i < historyLength;) {
            uint256 diff = timestamp > inflationHistory[i].timestamp ?
                timestamp - inflationHistory[i].timestamp :
                inflationHistory[i].timestamp - timestamp;

            if (diff < minDiff) {
                minDiff = diff;
                closestIndex = i;
            }
            unchecked { i++; }
        }

        return inflationHistory[closestIndex].totalSupply;
    }

    function getQuarterlyMintedAndBurned(uint16 year, uint8 quarter) public view returns (
        uint256 minted,
        uint256 burned
    ) {
        uint256 startTime = getQuarterStartTime(year, quarter);
        uint256 endTime = getQuarterEndTime(year, quarter);

        minted = 0;
        burned = 0;
        uint256 historyLength = inflationHistory.length;

        for (uint256 i = 0; i < historyLength;) {
            if (inflationHistory[i].timestamp >= startTime && inflationHistory[i].timestamp <= endTime) {
                minted += inflationHistory[i].mintedAmount;
                burned += inflationHistory[i].burnedAmount;
            }
            unchecked { i++; }
        }

        return (minted, burned);
    }
    
    function getInflationHistoryLength() external view returns (uint256) {
        return inflationHistory.length;
    }

    function getAlertRecordsLength() external view returns (uint256) {
        return alertRecords.length;
    }

    function getRecordedYearsLength() external view returns (uint256) {
        return recordedYears.length;
    }

    function getInflationData(uint256 startIndex, uint256 count) external view returns (
        uint256[] memory timestamps,
        uint256[] memory supplies,
        uint256[] memory rates
    ) {
        if (startIndex >= inflationHistory.length) revert TokenErrors.IndexOutOfRange();

        uint256 endIndex = startIndex + count;
        if (endIndex > inflationHistory.length) {
            endIndex = inflationHistory.length;
        }

        uint256 resultCount = endIndex - startIndex;

        timestamps = new uint256[](resultCount);
        supplies = new uint256[](resultCount);
        rates = new uint256[](resultCount);

        for (uint256 i = 0; i < resultCount;) {
            uint256 index = startIndex + i;
            timestamps[i] = inflationHistory[index].timestamp;
            supplies[i] = inflationHistory[index].totalSupply;
            rates[i] = inflationHistory[index].netInflationRate;
            unchecked { i++; }
        }

        return (timestamps, supplies, rates);
    }

    function getActiveAlerts() external view returns (
        uint256[] memory ids,
        string[] memory types,
        string[] memory messages
    ) {
        ids = new uint256[](activeAlertCount);
        types = new string[](activeAlertCount);
        messages = new string[](activeAlertCount);

        uint256 index = 0;
        uint256 recordsLength = alertRecords.length;

        for (uint256 i = 0; i < recordsLength;) {
            if (alertRecords[i].isActive) {
                ids[index] = i;
                types[index] = alertRecords[i].alertType;
                messages[index] = alertRecords[i].message;
                index++;
            }
            unchecked { i++; }
        }

        return (ids, types, messages);
    }
    
    function timestampToDate(uint256 timestamp) internal pure returns (
        uint16 year,
        uint8 month,
        uint8 day
    ) {
        unchecked {
            uint256 z = timestamp / 86400 + 719468;
            uint256 era = (z >= 0 ? z : z - 146096) / 146097;
            uint256 doe = z - era * 146097;
            uint256 yoe = (doe - doe/1460 + doe/36524 - doe/146096) / 365;
            year = uint16(yoe + era * 400);
            uint256 doy = doe - (365*yoe + yoe/4 - yoe/100 + yoe/400);
            month = uint8((5*doy + 2)/153);
            day = uint8(doy - (153*month+2)/5 + 1);
            if (month < 10) {
                month = month + 3;
            } else {
                month = month - 9;
            }
            year = year + (month <= 2 ? 1 : 0);
        }
    }

    function getMonthFromTimestamp(uint256 timestamp) internal pure returns (uint8) {
        (,uint8 month,) = timestampToDate(timestamp);
        return month;
    }

    function dateToTimestamp(uint16 year, uint8 month, uint8 day) internal pure returns (uint256) {
        unchecked {
            uint16 m = month;
            uint16 y = year;

            if (m <= 2) {
                m += 12;
                y -= 1;
            }

            uint256 era = y / 400;
            uint256 yoe = y - era * 400;
            uint256 doy = (153 * (m - 3) + 2) / 5 + day - 1;
            uint256 doe = yoe * 365 + yoe / 4 - yoe / 100 + yoe / 400 + doy;
            uint256 z = era * 146097 + doe - 719468;

            return z * 86400;
        }
    }

    function uintToString(uint256 _i) internal pure returns (string memory _uintAsString) {
        if (_i == 0) {
            return "0";
        }

        uint256 j = _i;
        uint256 len;

        while (j != 0) {
            len++;
            j /= 10;
        }

        bytes memory bstr = new bytes(len);
        uint256 k = len;

        j = _i;
        while (j != 0) {
            bstr[--k] = bytes1(uint8(48 + j % 10));
            j /= 10;
        }

        return string(bstr);
    }
    
    function getInflationHistoryPaged(uint256 offset, uint256 limit) external view returns (InflationDataPoint[] memory) {
        uint256 total = inflationHistory.length;
        if (offset >= total) return new InflationDataPoint[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        InflationDataPoint[] memory result = new InflationDataPoint[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = inflationHistory[i];
            unchecked { i++; }
        }
        return result;
    }

    function getAlertRecordsPaged(uint256 offset, uint256 limit) external view returns (AlertRecord[] memory) {
        uint256 total = alertRecords.length;
        if (offset >= total) return new AlertRecord[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        AlertRecord[] memory result = new AlertRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = alertRecords[i];
            unchecked { i++; }
        }
        return result;
    }

    function getRecordedYearsPaged(uint256 offset, uint256 limit) external view returns (uint16[] memory) {
        uint256 total = recordedYears.length;
        if (offset >= total) return new uint16[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        uint16[] memory result = new uint16[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = recordedYears[i];
            unchecked { i++; }
        }
        return result;
    }
    
    function getDateAsUint(uint256 timestamp) external pure returns (uint256) {
        uint256 daysSinceEpoch = timestamp / 86400;

        uint256 year = 1970;

        uint256 daysInYear = 365;
        while (daysSinceEpoch >= daysInYear) {
            if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                if (daysSinceEpoch >= 366) {
                    daysSinceEpoch -= 366;
                    year += 1;
                } else {
                    break;
                }
            } else {
                daysSinceEpoch -= 365;
                year += 1;
            }
        }

        uint8[12] memory daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
            daysInMonth[1] = 29;
        }

        uint256 month = 1;
        for (uint256 i = 0; i < 12; i++) {
            if (daysSinceEpoch >= daysInMonth[i]) {
                daysSinceEpoch -= daysInMonth[i];
                month += 1;
            } else {
                break;
            }
        }

        uint256 day = daysSinceEpoch + 1;

        return year * 10000 + month * 100 + day;
    }

    function getTimestampFromDate(uint16 year, uint8 month, uint8 day) external pure returns (uint256) {
        require(year >= 1970 && month >= 1 && month <= 12 && day >= 1 && day <= 31, "Invalid date");

        uint256 dayOffset = 0;

        for (uint16 y = 1970; y < year; y++) {
            if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
                dayOffset += 366;
            } else {
                dayOffset += 365;
            }
        }

        uint8[12] memory daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
            daysInMonth[1] = 29;
        }

        for (uint8 m = 1; m < month; m++) {
            dayOffset += daysInMonth[m - 1];
        }

        dayOffset += day - 1;

        return dayOffset * 86400;
    }
}