// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/proxy/Clones.sol";

contract TokenFactory is Ownable {
    
    struct DeploymentAddresses {
        address communityAddress;
        address teamAddress;
        address platformFundAddress;
        address privateSaleAddress;
        address strategicPartnerAddress;
        address marketingAddress;
        address reserveAddress;
        address chinaMainlandPool;
        address globalPool;
        address stakingRewardPool;
    }

    // 模板合约地址
    address public immutable pxtokenTemplate;
    address public immutable patokenTemplate;
    address public immutable stakingPoolTemplate;
    address public immutable miningPoolTemplate;
    
    // 部署的合约地址
    address public pxtoken;
    address public patoken;
    address public stakingPool;
    address public miningPool;
    bool public isDeployed;

    event SystemDeployed(
        address indexed pxtoken,
        address indexed patoken,
        address indexed stakingPool,
        address miningPool
    );

    constructor(
        address _pxtokenTemplate,
        address _patokenTemplate,
        address _stakingPoolTemplate,
        address _miningPoolTemplate
    ) {
        pxtokenTemplate = _pxtokenTemplate;
        patokenTemplate = _patokenTemplate;
        stakingPoolTemplate = _stakingPoolTemplate;
        miningPoolTemplate = _miningPoolTemplate;
    }

    function deployTokenSystem(
        DeploymentAddresses calldata addresses,
        uint256 baseAPR
    ) external onlyOwner {
        require(!isDeployed, "Already deployed");
        require(baseAPR >= 100 && baseAPR <= 5000, "Invalid APR");
        
        _validateAddresses(addresses);

        // 使用克隆模式部署合约
        pxtoken = Clones.clone(pxtokenTemplate);
        patoken = Clones.clone(patokenTemplate);
        stakingPool = Clones.clone(stakingPoolTemplate);
        miningPool = Clones.clone(miningPoolTemplate);

        // 初始化合约（需要在模板合约中实现initialize函数）
        _initializeContracts(addresses, baseAPR);

        isDeployed = true;
        emit SystemDeployed(pxtoken, patoken, stakingPool, miningPool);
    }

    function _validateAddresses(DeploymentAddresses calldata addresses) private pure {
        require(addresses.communityAddress != address(0), "Invalid community");
        require(addresses.teamAddress != address(0), "Invalid team");
        require(addresses.platformFundAddress != address(0), "Invalid platform");
        require(addresses.privateSaleAddress != address(0), "Invalid private sale");
        require(addresses.strategicPartnerAddress != address(0), "Invalid strategic");
        require(addresses.marketingAddress != address(0), "Invalid marketing");
        require(addresses.reserveAddress != address(0), "Invalid reserve");
        require(addresses.chinaMainlandPool != address(0), "Invalid china pool");
        require(addresses.globalPool != address(0), "Invalid global pool");
        require(addresses.stakingRewardPool != address(0), "Invalid staking pool");
    }

    function _initializeContracts(
        DeploymentAddresses calldata addresses,
        uint256 baseAPR
    ) private {
        // 这里需要调用各个合约的initialize函数
        // 具体实现取决于各个合约的接口
        
        // 示例：
        // IPXToken(pxtoken).initialize(...);
        // IPAToken(patoken).initialize(...);
        // IStakingPool(stakingPool).initialize(pxtoken, patoken, baseAPR);
        // IMiningPool(miningPool).initialize(...);
        
        // 为了简化，这里暂时留空
        // 实际使用时需要根据具体的初始化接口来实现
    }

    function getSystemAddresses() external view returns (
        address _pxtoken,
        address _patoken,
        address _stakingPool,
        address _miningPool
    ) {
        return (pxtoken, patoken, stakingPool, miningPool);
    }

    function createPromotionalStakingPool(uint256 baseAPR) external onlyOwner returns (address) {
        require(isDeployed, "Main system not deployed");
        require(baseAPR >= 100 && baseAPR <= 5000, "Invalid APR");
        
        address promotionalPool = Clones.clone(stakingPoolTemplate);
        // 初始化推广池
        // IStakingPool(promotionalPool).initialize(pxtoken, patoken, baseAPR);
        
        return promotionalPool;
    }
}
