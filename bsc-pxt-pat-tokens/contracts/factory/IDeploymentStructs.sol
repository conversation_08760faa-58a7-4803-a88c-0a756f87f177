// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

interface IDeploymentStructs {
    struct DeploymentAddresses {
        address communityAddress;
        address teamAddress;
        address platformFundAddress;
        address privateSaleAddress;
        address strategicPartnerAddress;
        address marketingAddress;
        address reserveAddress;
        address chinaMainlandPool;
        address globalPool;
        address stakingRewardPool;
    }
}
