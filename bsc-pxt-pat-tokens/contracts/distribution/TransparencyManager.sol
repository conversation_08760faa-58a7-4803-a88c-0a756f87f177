// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/utils/Counters.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

contract TransparencyManager is AccessControl, ReentrancyGuard {
    using Counters for Counters.Counter;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant REPORTER_ROLE = keccak256("REPORTER_ROLE");
    bytes32 public constant AUDITOR_ROLE = keccak256("AUDITOR_ROLE");

    enum RevenueType {
        CONTENT_SALES,
        ADVERTISING,
        SUBSCRIPTION,
        TRANSACTION_FEES,
        OTHER
    }

    enum ExpenseType {
        CREATOR_PAYMENT,
        VALIDATOR_REWARD,
        PLATFORM_OPERATION,
        DIVIDEND,
        BUYBACK,
        OTHER
    }

    struct RevenueRecord {
        uint32 recordId;
        RevenueType revenueType;
        uint128 amount;
        address token;
        uint40 timestamp;
        string description;
        string sourceReference;
        address reporter;
        bool isVerified;
    }

    struct ExpenseRecord {
        uint32 recordId;
        ExpenseType expenseType;
        uint128 amount;
        address token;
        uint40 timestamp;
        string description;
        string destinationReference;
        address reporter;
        bool isVerified;
    }

    struct AuditRecord {
        uint32 auditId;
        uint40 startTimestamp;
        uint40 endTimestamp;
        address auditor;
        string reportURI;
        string summary;
        bool isPassed;
    }

    struct TransparencyStats {
        uint128 totalRevenue;
        uint128 totalExpense;
        uint128 creatorPayouts;
        uint128 validatorRewards;
        uint128 platformOperations;
        uint128 dividendPayouts;
        uint128 buybackAmount;
    }

    Counters.Counter private _revenueIdCounter;
    Counters.Counter private _expenseIdCounter;
    Counters.Counter private _auditIdCounter;
    mapping(uint256 => RevenueRecord) public revenueRecords;
    mapping(uint256 => ExpenseRecord) public expenseRecords;
    mapping(uint256 => AuditRecord) public auditRecords;
    mapping(address => TransparencyStats) public tokenStats;
    mapping(uint256 => uint256[]) public revenuesByTimestamp;
    mapping(uint256 => uint256[]) public expensesByTimestamp;
    mapping(RevenueType => uint256[]) public revenuesByType;
    mapping(ExpenseType => uint256[]) public expensesByType;
    TransparencyStats public totalStats;
    uint40 public lastUpdated;
    uint40 public updateCycle;

    event RevenueRecorded(uint256 indexed recordId, RevenueType revenueType, uint256 amount, address token);
    event ExpenseRecorded(uint256 indexed recordId, ExpenseType expenseType, uint256 amount, address token);
    event RecordVerified(uint256 indexed recordId, bool isRevenue, address verifier);
    event AuditSubmitted(uint256 indexed auditId, address auditor, bool isPassed);
    event StatsUpdated(uint256 timestamp);

    function initialize(
        address _admin,
        address _reporter,
        address _auditor
    ) public {
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(REPORTER_ROLE, _reporter);
        _grantRole(AUDITOR_ROLE, _auditor);

        updateCycle = uint40(1 days);
        lastUpdated = uint40(block.timestamp);
    }

    function recordRevenue(
        RevenueType _revenueType,
        uint256 _amount,
        address _token,
        string memory _description,
        string memory _sourceReference
    ) external onlyRole(REPORTER_ROLE) nonReentrant {
        if(_amount == 0) revert TokenErrors.ZeroAmount();
        if(_token == address(0)) revert TokenErrors.ZeroAddress();
        if(_amount > type(uint128).max) revert TokenErrors.ValueTooLarge();

        _revenueIdCounter.increment();
        uint256 newRecordId = _revenueIdCounter.current();
        if(newRecordId > type(uint32).max) revert TokenErrors.ValueTooLarge();

        RevenueRecord memory newRecord = RevenueRecord({
            recordId: uint32(newRecordId),
            revenueType: _revenueType,
            amount: uint128(_amount),
            token: _token,
            timestamp: uint40(block.timestamp),
            description: _description,
            sourceReference: _sourceReference,
            reporter: msg.sender,
            isVerified: false
        });

        revenueRecords[newRecordId] = newRecord;

        uint256 dayTimestamp = block.timestamp - (block.timestamp % 1 days);
        revenuesByTimestamp[dayTimestamp].push(newRecordId);
        revenuesByType[_revenueType].push(newRecordId);

        emit RevenueRecorded(newRecordId, _revenueType, _amount, _token);
    }

    function recordExpense(
        ExpenseType _expenseType,
        uint256 _amount,
        address _token,
        string memory _description,
        string memory _destinationReference
    ) external onlyRole(REPORTER_ROLE) nonReentrant {
        if(_amount == 0) revert TokenErrors.ZeroAmount();
        if(_token == address(0)) revert TokenErrors.ZeroAddress();
        if(_amount > type(uint128).max) revert TokenErrors.ValueTooLarge();

        _expenseIdCounter.increment();
        uint256 newRecordId = _expenseIdCounter.current();
        if(newRecordId > type(uint32).max) revert TokenErrors.ValueTooLarge();

        ExpenseRecord memory newRecord = ExpenseRecord({
            recordId: uint32(newRecordId),
            expenseType: _expenseType,
            amount: uint128(_amount),
            token: _token,
            timestamp: uint40(block.timestamp),
            description: _description,
            destinationReference: _destinationReference,
            reporter: msg.sender,
            isVerified: false
        });

        expenseRecords[newRecordId] = newRecord;

        uint256 dayTimestamp = block.timestamp - (block.timestamp % 1 days);
        expensesByTimestamp[dayTimestamp].push(newRecordId);
        expensesByType[_expenseType].push(newRecordId);

        emit ExpenseRecorded(newRecordId, _expenseType, _amount, _token);
    }

    function verifyRevenueRecord(uint256 _recordId) external onlyRole(AUDITOR_ROLE) nonReentrant {
        if(_recordId == 0 || _recordId > _revenueIdCounter.current())
            revert TokenErrors.InvalidRecordId();

        RevenueRecord storage record = revenueRecords[_recordId];
        if(record.isVerified) revert TokenErrors.RecordAlreadyVerified();

        record.isVerified = true;

        uint128 recordAmount = record.amount;
        address recordToken = record.token;

        TransparencyStats storage stats = tokenStats[recordToken];
        TransparencyStats storage globalStats = totalStats;

        if(uint256(stats.totalRevenue) + recordAmount > type(uint128).max)
            revert TokenErrors.ValueTooLarge();
        if(uint256(globalStats.totalRevenue) + recordAmount > type(uint128).max)
            revert TokenErrors.ValueTooLarge();

        stats.totalRevenue += recordAmount;
        globalStats.totalRevenue += recordAmount;

        emit RecordVerified(_recordId, true, msg.sender);
    }

    function verifyExpenseRecord(uint256 _recordId) external onlyRole(AUDITOR_ROLE) nonReentrant {
        if(_recordId == 0 || _recordId > _expenseIdCounter.current())
            revert TokenErrors.InvalidRecordId();

        ExpenseRecord storage record = expenseRecords[_recordId];
        if(record.isVerified) revert TokenErrors.RecordAlreadyVerified();

        record.isVerified = true;

        uint128 recordAmount = record.amount;
        address recordToken = record.token;
        ExpenseType recordType = record.expenseType;

        TransparencyStats storage stats = tokenStats[recordToken];
        TransparencyStats storage globalStats = totalStats;

        if(uint256(stats.totalExpense) + recordAmount > type(uint128).max)
            revert TokenErrors.ValueTooLarge();
        if(uint256(globalStats.totalExpense) + recordAmount > type(uint128).max)
            revert TokenErrors.ValueTooLarge();

        stats.totalExpense += recordAmount;
        globalStats.totalExpense += recordAmount;

        if (recordType == ExpenseType.CREATOR_PAYMENT) {
            if(uint256(stats.creatorPayouts) + recordAmount > type(uint128).max ||
               uint256(globalStats.creatorPayouts) + recordAmount > type(uint128).max)
                revert TokenErrors.ValueTooLarge();

            stats.creatorPayouts += recordAmount;
            globalStats.creatorPayouts += recordAmount;
        } else if (recordType == ExpenseType.VALIDATOR_REWARD) {
            if(uint256(stats.validatorRewards) + recordAmount > type(uint128).max ||
               uint256(globalStats.validatorRewards) + recordAmount > type(uint128).max)
                revert TokenErrors.ValueTooLarge();

            stats.validatorRewards += recordAmount;
            globalStats.validatorRewards += recordAmount;
        } else if (recordType == ExpenseType.PLATFORM_OPERATION) {
            if(uint256(stats.platformOperations) + recordAmount > type(uint128).max ||
               uint256(globalStats.platformOperations) + recordAmount > type(uint128).max)
                revert TokenErrors.ValueTooLarge();

            stats.platformOperations += recordAmount;
            globalStats.platformOperations += recordAmount;
        } else if (recordType == ExpenseType.DIVIDEND) {
            if(uint256(stats.dividendPayouts) + recordAmount > type(uint128).max ||
               uint256(globalStats.dividendPayouts) + recordAmount > type(uint128).max)
                revert TokenErrors.ValueTooLarge();

            stats.dividendPayouts += recordAmount;
            globalStats.dividendPayouts += recordAmount;
        } else if (recordType == ExpenseType.BUYBACK) {
            if(uint256(stats.buybackAmount) + recordAmount > type(uint128).max ||
               uint256(globalStats.buybackAmount) + recordAmount > type(uint128).max)
                revert TokenErrors.ValueTooLarge();

            stats.buybackAmount += recordAmount;
            globalStats.buybackAmount += recordAmount;
        }

        emit RecordVerified(_recordId, false, msg.sender);
    }

    function submitAudit(
        uint256 _startTimestamp,
        uint256 _endTimestamp,
        string memory _reportURI,
        string memory _summary,
        bool _isPassed
    ) external onlyRole(AUDITOR_ROLE) nonReentrant {
        if(_startTimestamp >= _endTimestamp) revert TokenErrors.InvalidTimeRange();
        if(bytes(_reportURI).length == 0) revert TokenErrors.EmptyReportURI();
        if(_startTimestamp > type(uint40).max || _endTimestamp > type(uint40).max)
            revert TokenErrors.ValueTooLarge();

        _auditIdCounter.increment();
        uint256 newAuditId = _auditIdCounter.current();
        if(newAuditId > type(uint32).max) revert TokenErrors.ValueTooLarge();

        auditRecords[newAuditId] = AuditRecord({
            auditId: uint32(newAuditId),
            startTimestamp: uint40(_startTimestamp),
            endTimestamp: uint40(_endTimestamp),
            auditor: msg.sender,
            reportURI: _reportURI,
            summary: _summary,
            isPassed: _isPassed
        });

        emit AuditSubmitted(newAuditId, msg.sender, _isPassed);
    }

    function updateStats() external nonReentrant {
        if(block.timestamp < lastUpdated + updateCycle) revert TokenErrors.UpdateCycleTooShort();

        lastUpdated = uint40(block.timestamp);

        emit StatsUpdated(block.timestamp);
    }

    function getRevenueRecord(uint256 _recordId) external view returns (
        uint256 recordId,
        RevenueType revenueType,
        uint256 amount,
        address token,
        uint256 timestamp,
        string memory description,
        string memory sourceReference,
        address reporter,
        bool isVerified
    ) {
        if(_recordId == 0 || _recordId > _revenueIdCounter.current())
            revert TokenErrors.InvalidRecordId();

        RevenueRecord memory record = revenueRecords[_recordId];

        return (
            record.recordId,
            record.revenueType,
            record.amount,
            record.token,
            record.timestamp,
            record.description,
            record.sourceReference,
            record.reporter,
            record.isVerified
        );
    }

    function getExpenseRecord(uint256 _recordId) external view returns (
        uint256 recordId,
        ExpenseType expenseType,
        uint256 amount,
        address token,
        uint256 timestamp,
        string memory description,
        string memory destinationReference,
        address reporter,
        bool isVerified
    ) {
        if(_recordId == 0 || _recordId > _expenseIdCounter.current())
            revert TokenErrors.InvalidRecordId();

        ExpenseRecord memory record = expenseRecords[_recordId];

        return (
            record.recordId,
            record.expenseType,
            record.amount,
            record.token,
            record.timestamp,
            record.description,
            record.destinationReference,
            record.reporter,
            record.isVerified
        );
    }

    function getAuditRecord(uint256 _auditId) external view returns (
        uint256 auditId,
        uint256 startTimestamp,
        uint256 endTimestamp,
        address auditor,
        string memory reportURI,
        string memory summary,
        bool isPassed
    ) {
        if(_auditId == 0 || _auditId > _auditIdCounter.current())
            revert TokenErrors.InvalidAuditId();

        AuditRecord memory record = auditRecords[_auditId];

        return (
            record.auditId,
            record.startTimestamp,
            record.endTimestamp,
            record.auditor,
            record.reportURI,
            record.summary,
            record.isPassed
        );
    }

    function getRevenueIdsForDay(uint256 _timestamp) external view returns (uint256[] memory) {
        uint256 dayTimestamp = _timestamp - (_timestamp % 1 days);
        return revenuesByTimestamp[dayTimestamp];
    }

    function getExpenseIdsForDay(uint256 _timestamp) external view returns (uint256[] memory) {
        uint256 dayTimestamp = _timestamp - (_timestamp % 1 days);
        return expensesByTimestamp[dayTimestamp];
    }

    function getRevenueIdsByType(RevenueType _revenueType) external view returns (uint256[] memory) {
        return revenuesByType[_revenueType];
    }

    function getExpenseIdsByType(ExpenseType _expenseType) external view returns (uint256[] memory) {
        return expensesByType[_expenseType];
    }

    function getTokenStats(address _token) external view returns (
        uint256 totalRevenue,
        uint256 totalExpense,
        uint256 creatorPayouts,
        uint256 validatorRewards,
        uint256 platformOperations,
        uint256 dividendPayouts,
        uint256 buybackAmount
    ) {
        TransparencyStats memory stats = tokenStats[_token];

        return (
            stats.totalRevenue,
            stats.totalExpense,
            stats.creatorPayouts,
            stats.validatorRewards,
            stats.platformOperations,
            stats.dividendPayouts,
            stats.buybackAmount
        );
    }

    function getTotalStats() external view returns (
        uint256 totalRevenue,
        uint256 totalExpense,
        uint256 creatorPayouts,
        uint256 validatorRewards,
        uint256 platformOperations,
        uint256 dividendPayouts,
        uint256 buybackAmount
    ) {
        return (
            totalStats.totalRevenue,
            totalStats.totalExpense,
            totalStats.creatorPayouts,
            totalStats.validatorRewards,
            totalStats.platformOperations,
            totalStats.dividendPayouts,
            totalStats.buybackAmount
        );
    }

    function setUpdateCycle(uint256 _updateCycle) external onlyRole(ADMIN_ROLE) nonReentrant {
        if(_updateCycle == 0) revert TokenErrors.ZeroAmount();
        if(_updateCycle > type(uint40).max) revert TokenErrors.ValueTooLarge();
        updateCycle = uint40(_updateCycle);
    }

    function getRevenueRecordsPaged(uint256 offset, uint256 limit) external view returns (RevenueRecord[] memory) {
        uint256 total = _revenueIdCounter.current();
        if (offset >= total) {
            return new RevenueRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        RevenueRecord[] memory result = new RevenueRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = revenueRecords[i + 1];
            unchecked { i++; }
        }
        return result;
    }

    function getExpenseRecordsPaged(uint256 offset, uint256 limit) external view returns (ExpenseRecord[] memory) {
        uint256 total = _expenseIdCounter.current();
        if (offset >= total) {
            return new ExpenseRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        ExpenseRecord[] memory result = new ExpenseRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = expenseRecords[i + 1];
            unchecked { i++; }
        }
        return result;
    }

    function getAuditRecordsPaged(uint256 offset, uint256 limit) external view returns (AuditRecord[] memory) {
        uint256 total = _auditIdCounter.current();
        if (offset >= total) {
            return new AuditRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        AuditRecord[] memory result = new AuditRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = auditRecords[i + 1];
            unchecked { i++; }
        }
        return result;
    }
} 