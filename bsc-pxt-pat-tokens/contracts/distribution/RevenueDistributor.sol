// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/TokenErrors.sol";

contract RevenueDistributor is AccessControl, Pausable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant TREASURY_ROLE = keccak256("TREASURY_ROLE");

    struct DistributionRatio {
        uint16 creatorShare;
        uint16 validatorShare;
        uint16 stakeholderShare;
        uint16 treasuryShare;
        uint16 buybackShare;
    }

    mapping(uint8 => DistributionRatio) public distributionRatios;

    uint8 public constant CONTENT_REVENUE = 0;
    uint8 public constant ADVERTISING_REVENUE = 1;
    uint8 public constant SUBSCRIPTION_REVENUE = 2;
    uint8 public constant TRANSACTION_REVENUE = 3;
    uint8 public constant OTHER_REVENUE = 4;

    address public immutable treasuryAddress;
    address public immutable buybackAddress;
    address public immutable stakingPoolAddress;

    struct DistributionRecord {
        uint40 timestamp;
        uint8 revenueType;
        uint128 totalAmount;
        address token;
        uint128 creatorAmount;
        uint128 validatorAmount;
        uint128 stakeholderAmount;
        uint128 treasuryAmount;
        uint128 buybackAmount;
    }

    DistributionRecord[] public distributionHistory;
    uint40 public creatorLockupPeriod;

    struct LockedRevenue {
        uint128 amount;
        uint40 unlockTime;
        address token;
        bool claimed;
    }

    mapping(address => LockedRevenue[]) public creatorLockedRevenues;

    event RevenueDistributed(
        uint256 indexed distributionId,
        uint8 revenueType,
        uint256 totalAmount,
        address token,
        uint256 creatorAmount,
        uint256 validatorAmount,
        uint256 stakeholderAmount,
        uint256 treasuryAmount,
        uint256 buybackAmount
    );
    event RatiosUpdated(uint8 revenueType, DistributionRatio newRatios);
    event LockupPeriodUpdated(uint256 newPeriod);
    event CreatorRevenueClaimed(address indexed creator, uint256 amount, address token);

    constructor(
        address _admin,
        address _treasury,
        address _buyback,
        address _stakingPool
    ) {
        if (_admin == address(0) || _treasury == address(0) ||
            _buyback == address(0) || _stakingPool == address(0)) {
            revert TokenErrors.ZeroAddress();
        }

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(OPERATOR_ROLE, _admin);
        _grantRole(TREASURY_ROLE, _treasury);

        treasuryAddress = _treasury;
        buybackAddress = _buyback;
        stakingPoolAddress = _stakingPool;

        distributionRatios[CONTENT_REVENUE] = DistributionRatio({
            creatorShare: 7000,
            validatorShare: 500,
            stakeholderShare: 1000,
            treasuryShare: 1000,
            buybackShare: 500
        });
        distributionRatios[ADVERTISING_REVENUE] = DistributionRatio({
            creatorShare: 6000,
            validatorShare: 500,
            stakeholderShare: 1500,
            treasuryShare: 1000,
            buybackShare: 1000
        });
        distributionRatios[SUBSCRIPTION_REVENUE] = DistributionRatio({
            creatorShare: 6500,
            validatorShare: 0,
            stakeholderShare: 1500,
            treasuryShare: 1000,
            buybackShare: 1000
        });
        distributionRatios[TRANSACTION_REVENUE] = DistributionRatio({
            creatorShare: 4000,
            validatorShare: 0,
            stakeholderShare: 3000,
            treasuryShare: 1000,
            buybackShare: 2000
        });
        distributionRatios[OTHER_REVENUE] = DistributionRatio({
            creatorShare: 0,
            validatorShare: 0,
            stakeholderShare: 5000,
            treasuryShare: 3000,
            buybackShare: 2000
        });

        creatorLockupPeriod = 30 days;
    }

    function distributeRevenue(
        uint8 revenueType,
        uint256 totalAmount,
        address token,
        address creator,
        address[] memory validators
    ) external onlyRole(OPERATOR_ROLE) whenNotPaused nonReentrant {
        if(revenueType > OTHER_REVENUE) revert TokenErrors.InvalidRevenueType();
        if(totalAmount == 0) revert TokenErrors.ZeroAmount();
        if(token == address(0)) revert TokenErrors.ZeroAddress();
        if(totalAmount > type(uint128).max) revert TokenErrors.ValueTooLarge();

        (uint256 creatorAmount, uint256 validatorAmount, uint256 stakeholderAmount,
         uint256 treasuryAmount, uint256 buybackAmount) = _calculateDistribution(revenueType, totalAmount);

        uint256 distributionId = _recordDistribution(
            revenueType,
            totalAmount,
            token,
            creatorAmount,
            validatorAmount,
            stakeholderAmount,
            treasuryAmount,
            buybackAmount
        );

        IERC20 tokenContract = IERC20(token);

        if (creator != address(0) && creatorAmount > 0) {
            _distributeCreatorRevenue(creator, creatorAmount, tokenContract);
        }

        if (validators.length > 0 && validatorAmount > 0) {
            _distributeValidatorRevenue(validators, validatorAmount, tokenContract);
        }

        if (stakeholderAmount > 0) {
            tokenContract.safeTransferFrom(msg.sender, stakingPoolAddress, stakeholderAmount);
        }

        if (treasuryAmount > 0) {
            tokenContract.safeTransferFrom(msg.sender, treasuryAddress, treasuryAmount);
        }

        if (buybackAmount > 0) {
            tokenContract.safeTransferFrom(msg.sender, buybackAddress, buybackAmount);
        }

        emit RevenueDistributed(
            distributionId,
            revenueType,
            totalAmount,
            token,
            creatorAmount,
            validatorAmount,
            stakeholderAmount,
            treasuryAmount,
            buybackAmount
        );
    }

    function _calculateDistribution(uint8 revenueType, uint256 totalAmount) private view returns (
        uint256 creatorAmount,
        uint256 validatorAmount,
        uint256 stakeholderAmount,
        uint256 treasuryAmount,
        uint256 buybackAmount
    ) {
        DistributionRatio memory ratio = distributionRatios[revenueType];

        if(ratio.creatorShare + ratio.validatorShare + ratio.stakeholderShare +
           ratio.treasuryShare + ratio.buybackShare != 10000)
            revert TokenErrors.RatioSumNotHundred();

        creatorAmount = (totalAmount * ratio.creatorShare) / 10000;
        validatorAmount = (totalAmount * ratio.validatorShare) / 10000;
        stakeholderAmount = (totalAmount * ratio.stakeholderShare) / 10000;
        treasuryAmount = (totalAmount * ratio.treasuryShare) / 10000;
        buybackAmount = (totalAmount * ratio.buybackShare) / 10000;

        if(creatorAmount > type(uint128).max || validatorAmount > type(uint128).max ||
           stakeholderAmount > type(uint128).max || treasuryAmount > type(uint128).max ||
           buybackAmount > type(uint128).max)
            revert TokenErrors.ValueTooLarge();

        return (creatorAmount, validatorAmount, stakeholderAmount, treasuryAmount, buybackAmount);
    }

    function _recordDistribution(
        uint8 revenueType,
        uint256 totalAmount,
        address token,
        uint256 creatorAmount,
        uint256 validatorAmount,
        uint256 stakeholderAmount,
        uint256 treasuryAmount,
        uint256 buybackAmount
    ) private returns (uint256) {
        uint256 distributionId = distributionHistory.length;
        distributionHistory.push(DistributionRecord({
            timestamp: uint40(block.timestamp),
            revenueType: revenueType,
            totalAmount: uint128(totalAmount),
            token: token,
            creatorAmount: uint128(creatorAmount),
            validatorAmount: uint128(validatorAmount),
            stakeholderAmount: uint128(stakeholderAmount),
            treasuryAmount: uint128(treasuryAmount),
            buybackAmount: uint128(buybackAmount)
        }));
        return distributionId;
    }

    function _distributeCreatorRevenue(
        address creator,
        uint256 creatorAmount,
        IERC20 tokenContract
    ) private {
        uint256 immediateAmount = creatorAmount / 2;
        uint256 lockedAmount = creatorAmount - immediateAmount;

        if (immediateAmount > 0) {
            if (lockedAmount > 0) {
                creatorLockedRevenues[creator].push(LockedRevenue({
                    amount: uint128(lockedAmount),
                    unlockTime: uint40(block.timestamp + creatorLockupPeriod),
                    token: address(tokenContract),
                    claimed: false
                }));
            }

            tokenContract.safeTransferFrom(msg.sender, creator, immediateAmount);

            if (lockedAmount > 0) {
                tokenContract.safeTransferFrom(msg.sender, address(this), lockedAmount);
            }
        }
    }

    function _distributeValidatorRevenue(
        address[] memory validators,
        uint256 validatorAmount,
        IERC20 tokenContract
    ) private {
        uint256 validatorShare = validatorAmount / validators.length;
        for (uint256 i = 0; i < validators.length;) {
            if (validators[i] != address(0)) {
                tokenContract.safeTransferFrom(msg.sender, validators[i], validatorShare);
            }
            unchecked { i++; }
        }
    }

    function claimLockedRevenue(uint256[] calldata indices) external whenNotPaused nonReentrant {
        LockedRevenue[] storage lockedRevenues = creatorLockedRevenues[msg.sender];

        for (uint256 i = 0; i < indices.length;) {
            uint256 index = indices[i];
            if(index >= lockedRevenues.length) revert TokenErrors.IndexOutOfRange();

            LockedRevenue storage lockedRevenue = lockedRevenues[index];
            if(lockedRevenue.claimed) revert TokenErrors.AlreadyClaimed();
            if(block.timestamp < lockedRevenue.unlockTime) revert TokenErrors.NotUnlocked();

            uint256 amount = lockedRevenue.amount;
            address token = lockedRevenue.token;

            lockedRevenue.claimed = true;

            IERC20(token).safeTransfer(msg.sender, amount);

            emit CreatorRevenueClaimed(msg.sender, amount, token);

            unchecked { i++; }
        }
    }

    function getLockedRevenueCount(address creator) external view returns (uint256) {
        return creatorLockedRevenues[creator].length;
    }

    function getLockedRevenues(
        address creator,
        uint256 start,
        uint256 limit
    ) external view returns (
        uint256[] memory amounts,
        uint256[] memory unlockTimes,
        address[] memory tokens,
        bool[] memory claimStatus
    ) {
        LockedRevenue[] storage lockedRevenues = creatorLockedRevenues[creator];

        uint256 end = start + limit;
        if (end > lockedRevenues.length) {
            end = lockedRevenues.length;
        }

        uint256 resultSize = end - start;

        amounts = new uint256[](resultSize);
        unlockTimes = new uint256[](resultSize);
        tokens = new address[](resultSize);
        claimStatus = new bool[](resultSize);

        for (uint256 i = 0; i < resultSize;) {
            LockedRevenue storage lockedRevenue = lockedRevenues[start + i];
            amounts[i] = lockedRevenue.amount;
            unlockTimes[i] = lockedRevenue.unlockTime;
            tokens[i] = lockedRevenue.token;
            claimStatus[i] = lockedRevenue.claimed;

            unchecked { i++; }
        }
    }

    function updateDistributionRatio(
        uint8 revenueType,
        DistributionRatio memory newRatio
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if(revenueType > OTHER_REVENUE) revert TokenErrors.InvalidRevenueType();

        if(newRatio.creatorShare + newRatio.validatorShare + newRatio.stakeholderShare +
           newRatio.treasuryShare + newRatio.buybackShare != 10000)
            revert TokenErrors.RatioSumNotHundred();

        distributionRatios[revenueType] = newRatio;

        emit RatiosUpdated(revenueType, newRatio);
    }

    function updateLockupPeriod(uint256 newPeriod) external onlyRole(ADMIN_ROLE) nonReentrant {
        if(newPeriod > type(uint40).max) revert TokenErrors.ValueTooLarge();
        creatorLockupPeriod = uint40(newPeriod);
        emit LockupPeriodUpdated(newPeriod);
    }

    function getDistributionHistoryCount() external view returns (uint256) {
        return distributionHistory.length;
    }

    function emergencyWithdraw(address token) external onlyRole(ADMIN_ROLE) nonReentrant {
        IERC20 tokenContract = IERC20(token);
        uint256 balance = tokenContract.balanceOf(address(this));
        tokenContract.safeTransfer(treasuryAddress, balance);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    function getDistributionHistoryPaged(uint256 offset, uint256 limit) external view returns (DistributionRecord[] memory) {
        uint256 total = distributionHistory.length;
        if (offset >= total) {
            return new DistributionRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        DistributionRecord[] memory result = new DistributionRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = distributionHistory[i];
            unchecked { i++; }
        }
        return result;
    }
} 