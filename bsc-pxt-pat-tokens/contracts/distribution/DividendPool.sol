// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "../interfaces/TokenErrors.sol";

contract DividendPool is AccessControl, Pausable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant DISTRIBUTOR_ROLE = keccak256("DISTRIBUTOR_ROLE");
    bytes32 public constant REVENUE_MANAGER_ROLE = keccak256("REVENUE_MANAGER_ROLE");

    uint256 private constant MAX_PAGE_SIZE = 100;
    uint256 private constant PERCENTAGE_BASE = 100;

    struct DividendCycle {
        uint32 cycleId;
        uint40 startTime;
        uint40 endTime;
        uint128 totalDistributed;
        address token;
        bool isFinalized;
        bool isPaid;
    }

    struct DividendEligibility {
        uint128 stakingAmount;
        uint16 stakingDuration;
        uint32 contributionScore;
        uint128 weight;
    }

    struct UserDividend {
        uint128 amount;
        bool claimed;
        uint40 claimedTime;
    }

    struct WeightingParams {
        uint16 stakingWeight;
        uint16 durationWeight;
        uint16 contributionWeight;
        uint16 stakingFactor;
        uint16 minStakingDays;
    }

    IERC20 public immutable patoken;
    IERC20 public immutable pxtoken;
    DividendCycle[] public dividendCycles;
    uint256 public currentCycleId;
    mapping(uint256 => mapping(address => DividendEligibility)) public eligibilities;
    mapping(uint256 => mapping(address => UserDividend)) public userDividends;
    mapping(uint256 => address[]) public cycleParticipants;
    mapping(uint256 => uint256) public totalWeights;
    WeightingParams public weightingParams;
    uint256 public minEligibilityPXT;
    uint256 public lockupPercentage;
    address public treasuryAddress;
    mapping(address => uint256[]) public userDividendCycles;

    struct CycleInfo {
        uint256 startTime;
        uint256 endTime;
        uint256 totalDistributed;
        address token;
        bool isFinalized;
        bool isPaid;
        uint256 participantCount;
    }

    event NewDividendCycle(uint256 indexed cycleId, uint256 startTime, uint256 endTime);
    event EligibilityRecorded(uint256 indexed cycleId, address indexed user, uint256 weight);
    event DividendDistributed(uint256 indexed cycleId, uint256 totalAmount, address token);
    event DividendClaimed(address indexed user, uint256 indexed cycleId, uint256 amount, uint256 directAmount, uint256 lockedAmount);
    event ParametersUpdated(uint256 stakingWeight, uint256 durationWeight, uint256 contributionWeight);
    event MinEligibilityUpdated(uint256 newMinAmount);

    constructor(
        address _admin,
        address _patoken,
        address _pxtoken,
        address _treasury
    ) {
        if (_admin == address(0) || _patoken == address(0) ||
            _pxtoken == address(0) || _treasury == address(0)) {
            revert TokenErrors.ZeroAddress();
        }

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(DISTRIBUTOR_ROLE, _admin);
        _grantRole(REVENUE_MANAGER_ROLE, _admin);
        patoken = IERC20(_patoken);
        pxtoken = IERC20(_pxtoken);
        treasuryAddress = _treasury;

        weightingParams = WeightingParams({
            stakingWeight: 60,
            durationWeight: 20,
            contributionWeight: 20,
            stakingFactor: 10,
            minStakingDays: 7
        });
        minEligibilityPXT = 100 * 10**18;
        lockupPercentage = 30;

        _startNewCycle();
    }

    function startNewCycle() external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        if(dividendCycles[currentCycleId].endTime >= block.timestamp)
            revert TokenErrors.CycleNotEnded();

        if (!dividendCycles[currentCycleId].isFinalized) {
            _finalizeCycle(currentCycleId);
        }

        _startNewCycle();
    }

    function _startNewCycle() internal {
        uint256 newCycleId = dividendCycles.length;
        uint256 startTime = block.timestamp;
        uint256 endTime = startTime + 7 days;

        if (newCycleId > type(uint32).max) revert TokenErrors.ValueTooLarge();
        if (startTime > type(uint40).max || endTime > type(uint40).max) revert TokenErrors.ValueTooLarge();

        dividendCycles.push(DividendCycle({
            cycleId: uint32(newCycleId),
            startTime: uint40(startTime),
            endTime: uint40(endTime),
            totalDistributed: 0,
            token: address(0),
            isFinalized: false,
            isPaid: false
        }));

        currentCycleId = newCycleId;

        emit NewDividendCycle(newCycleId, startTime, endTime);
    }

    function recordEligibilities(
        address[] calldata _users,
        uint256[] calldata _stakingAmounts,
        uint256[] calldata _stakingDurations,
        uint256[] calldata _contributionScores
    ) external onlyRole(DISTRIBUTOR_ROLE) whenNotPaused nonReentrant {
        if(_users.length != _stakingAmounts.length) revert TokenErrors.InvalidArrayLength();
        if(_users.length != _stakingDurations.length) revert TokenErrors.InvalidArrayLength();
        if(_users.length != _contributionScores.length) revert TokenErrors.InvalidArrayLength();
        if(dividendCycles[currentCycleId].isFinalized) revert TokenErrors.CycleAlreadyFinalized();

        uint256 cycleId = currentCycleId;

        for (uint256 i = 0; i < _users.length;) {
            address user = _users[i];
            uint256 stakingAmount = _stakingAmounts[i];
            uint256 stakingDuration = _stakingDurations[i];
            uint256 contributionScore = _contributionScores[i];

            if (user == address(0)) {
                unchecked { i++; }
                continue;
            }
            if (stakingAmount > type(uint128).max) revert TokenErrors.ValueTooLarge();
            if (stakingDuration > type(uint16).max) revert TokenErrors.ValueTooLarge();
            if (contributionScore > type(uint32).max) revert TokenErrors.ValueTooLarge();

            if (stakingAmount < minEligibilityPXT || stakingDuration < weightingParams.minStakingDays) {
                unchecked { i++; }
                continue;
            }

            uint256 weight = _calculateWeight(stakingAmount, stakingDuration, contributionScore);
            if (weight > type(uint128).max) revert TokenErrors.ValueTooLarge();

            uint256 oldWeight = eligibilities[cycleId][user].weight;

            eligibilities[cycleId][user] = DividendEligibility({
                stakingAmount: uint128(stakingAmount),
                stakingDuration: uint16(stakingDuration),
                contributionScore: uint32(contributionScore),
                weight: uint128(weight)
            });

            bool existingUser = false;
            for (uint256 j = 0; j < userDividendCycles[user].length; j++) {
                if (userDividendCycles[user][j] == cycleId) {
                    existingUser = true;
                    break;
                }
            }

            if (!existingUser) {
                userDividendCycles[user].push(cycleId);
            }

            if (oldWeight > 0) {
                totalWeights[cycleId] = totalWeights[cycleId] - oldWeight + weight;
            } else {
                totalWeights[cycleId] += weight;
            }

            bool isParticipantExists = false;
            for (uint256 j = 0; j < cycleParticipants[cycleId].length; j++) {
                if (cycleParticipants[cycleId][j] == user) {
                    isParticipantExists = true;
                    break;
                }
            }

            if (!isParticipantExists) {
                cycleParticipants[cycleId].push(user);
            }

            emit EligibilityRecorded(cycleId, user, weight);

            unchecked { i++; }
        }
    }

    function _calculateWeight(
        uint256 _stakingAmount,
        uint256 _stakingDuration,
        uint256 _contributionScore
    ) internal view returns (uint256) {
        uint256 stakingPart = (_stakingAmount / (weightingParams.stakingFactor * 10**18)) * weightingParams.stakingWeight;
        uint256 durationPart = (_stakingDuration * weightingParams.durationWeight) / 30;
        uint256 contributionPart = (_contributionScore * weightingParams.contributionWeight) / PERCENTAGE_BASE;
        return stakingPart + durationPart + contributionPart;
    }

    function distributeDividend(
        uint256 _totalAmount,
        address _token
    ) external onlyRole(REVENUE_MANAGER_ROLE) whenNotPaused nonReentrant {
        if(dividendCycles[currentCycleId].isFinalized) revert TokenErrors.CycleAlreadyFinalized();
        if(_totalAmount == 0) revert TokenErrors.ZeroAmount();
        if(_token == address(0)) revert TokenErrors.ZeroAddress();
        if(_totalAmount > type(uint128).max) revert TokenErrors.ValueTooLarge();

        uint256 cycleId = currentCycleId;
        uint256 amount = _totalAmount;
        address token = _token;

        dividendCycles[cycleId].totalDistributed = uint128(amount);
        dividendCycles[cycleId].token = token;

        _finalizeCycle(cycleId);

        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);

        emit DividendDistributed(cycleId, amount, token);
    }

    function _finalizeCycle(uint256 _cycleId) internal {
        DividendCycle storage cycle = dividendCycles[_cycleId];
        cycle.isFinalized = true;

        if (cycle.totalDistributed == 0 || totalWeights[_cycleId] == 0) {
            cycle.isPaid = true;
            return;
        }

        address[] memory participants = cycleParticipants[_cycleId];

        for (uint256 i = 0; i < participants.length;) {
            address participant = participants[i];
            uint256 weight = eligibilities[_cycleId][participant].weight;

            uint256 dividendAmount = (weight * cycle.totalDistributed) / totalWeights[_cycleId];

            if (dividendAmount > 0) {
                userDividends[_cycleId][participant] = UserDividend({
                    amount: uint128(dividendAmount),
                    claimed: false,
                    claimedTime: 0
                });
            }

            unchecked { i++; }
        }

        cycle.isPaid = true;
    }

    function claimDividends(uint256[] calldata _cycleIds) external whenNotPaused nonReentrant {
        for (uint256 i = 0; i < _cycleIds.length;) {
            uint256 cycleId = _cycleIds[i];

            if(!dividendCycles[cycleId].isFinalized) revert TokenErrors.CycleNotFinalized();
            if(!dividendCycles[cycleId].isPaid) revert TokenErrors.CycleNotPaid();

            UserDividend storage userDividend = userDividends[cycleId][msg.sender];

            if(userDividend.amount == 0) revert TokenErrors.NoDividendToClaim();
            if(userDividend.claimed) revert TokenErrors.DividendAlreadyClaimed();

            uint256 totalAmount = userDividend.amount;
            address token = dividendCycles[cycleId].token;

            userDividend.claimed = true;
            userDividend.claimedTime = uint40(block.timestamp);

            uint256 directAmount = (totalAmount * (PERCENTAGE_BASE - lockupPercentage)) / PERCENTAGE_BASE;
            uint256 lockedAmount = totalAmount - directAmount;

            if (directAmount > 0) {
                IERC20(token).safeTransfer(msg.sender, directAmount);
            }

            if (lockedAmount > 0) {
                IERC20(token).safeTransfer(treasuryAddress, lockedAmount);
            }

            emit DividendClaimed(msg.sender, cycleId, totalAmount, directAmount, lockedAmount);

            unchecked { i++; }
        }
    }

    function getUserDividendInfo(
        address _user,
        uint256 _cycleId
    ) external view returns (
        uint256 amount,
        bool claimed,
        uint256 claimedTime,
        uint256 weight,
        uint256 totalWeight
    ) {
        if(_cycleId >= dividendCycles.length) revert TokenErrors.InvalidCycleId();

        UserDividend storage userDividend = userDividends[_cycleId][_user];
        DividendEligibility storage eligibility = eligibilities[_cycleId][_user];

        return (
            userDividend.amount,
            userDividend.claimed,
            userDividend.claimedTime,
            eligibility.weight,
            totalWeights[_cycleId]
        );
    }

    function getCycleInfo(uint256 _cycleId) external view returns (CycleInfo memory info) {
        if(_cycleId >= dividendCycles.length) revert TokenErrors.InvalidCycleId();
        DividendCycle storage cycle = dividendCycles[_cycleId];
        info = CycleInfo({
            startTime: cycle.startTime,
            endTime: cycle.endTime,
            totalDistributed: cycle.totalDistributed,
            token: cycle.token,
            isFinalized: cycle.isFinalized,
            isPaid: cycle.isPaid,
            participantCount: cycleParticipants[_cycleId].length
        });
    }

    function setCycleTimeframe(
        uint256 _cycleId,
        uint256 _startTime,
        uint256 _endTime
    ) external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        if(_cycleId >= dividendCycles.length) revert TokenErrors.InvalidCycleId();
        if(dividendCycles[_cycleId].isFinalized) revert TokenErrors.CycleAlreadyFinalized();
        if(_startTime >= _endTime) revert TokenErrors.InvalidDates();
        if(_startTime > type(uint40).max || _endTime > type(uint40).max) revert TokenErrors.ValueTooLarge();

        DividendCycle storage cycle = dividendCycles[_cycleId];
        cycle.startTime = uint40(_startTime);
        cycle.endTime = uint40(_endTime);
    }

    function updateWeightingParams(
        uint16 _stakingWeight,
        uint16 _durationWeight,
        uint16 _contributionWeight,
        uint16 _stakingFactor,
        uint16 _minStakingDays
    ) external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        if(_stakingWeight + _durationWeight + _contributionWeight != PERCENTAGE_BASE)
            revert TokenErrors.InvalidWeight();
        if(_stakingFactor == 0) revert TokenErrors.ZeroAmount();

        weightingParams.stakingWeight = _stakingWeight;
        weightingParams.durationWeight = _durationWeight;
        weightingParams.contributionWeight = _contributionWeight;
        weightingParams.stakingFactor = _stakingFactor;
        weightingParams.minStakingDays = _minStakingDays;

        emit ParametersUpdated(_stakingWeight, _durationWeight, _contributionWeight);
    }

    function updateMinEligibility(
        uint256 _minEligibilityPXT
    ) external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        minEligibilityPXT = _minEligibilityPXT;

        emit MinEligibilityUpdated(_minEligibilityPXT);
    }

    function updateLockupPercentage(
        uint256 _lockupPercentage
    ) external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        if(_lockupPercentage > PERCENTAGE_BASE) revert TokenErrors.InvalidPercentage();

        lockupPercentage = _lockupPercentage;
    }

    function setTreasuryAddress(
        address _treasuryAddress
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if(_treasuryAddress == address(0)) revert TokenErrors.ZeroAddress();

        treasuryAddress = _treasuryAddress;
    }

    function emergencyWithdraw(
        address _token,
        uint256 _amount
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if(_token == address(0)) revert TokenErrors.ZeroAddress();
        if(_amount == 0) revert TokenErrors.ZeroAmount();

        address token = _token;
        uint256 amount = _amount;

        IERC20(token).safeTransfer(treasuryAddress, amount);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    function getCycleParticipantsPaged(uint256 cycleId, uint256 offset, uint256 limit) external view returns (address[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        if(cycleId >= dividendCycles.length) revert TokenErrors.InvalidCycleId();

        uint256 total = cycleParticipants[cycleId].length;
        if (offset >= total) {
            return new address[](0);
        }

        uint256 end = offset + limit > total ? total : offset + limit;
        address[] memory result = new address[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = cycleParticipants[cycleId][i];
            unchecked { i++; }
        }

        return result;
    }

    function getUserDividendCyclesPaged(address user, uint256 offset, uint256 limit) external view returns (uint256[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = userDividendCycles[user].length;
        if (offset >= total) {
            return new uint256[](0);
        }

        uint256 end = offset + limit > total ? total : offset + limit;
        uint256[] memory result = new uint256[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = userDividendCycles[user][i];
            unchecked { i++; }
        }

        return result;
    }
} 