// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title TokenBridgeV2
 * @dev 支持双向跨链的代币桥接合约
 * 新增功能：
 * - unlockTokens: 解锁代币用于反向跨链
 * - 验证者机制
 * - 更完善的安全机制
 */
contract TokenBridgeV2 is Ownable, ReentrancyGuard, Pausable {
    using ECDSA for bytes32;

    IPXT public pxtoken;
    IPAT public patoken;

    // ============ 验证者管理 ============
    mapping(address => bool) public validators;
    address[] public validatorList;
    uint256 public requiredValidators = 1;
    
    // ============ 跨链状态管理 ============
    mapping(uint256 => bool) public supportedChainIds;
    mapping(uint256 => string) public chainNames;
    
    // 锁定的代币记录
    mapping(address => uint256) public lockedTokens;
    
    // 已处理的解锁请求
    mapping(bytes32 => bool) public processedUnlockRequests;
    
    // ============ 费用配置 ============
    struct FeeConfig {
        uint256 baseFee;
        uint256 percentFee;
        uint256 minFee;
        uint256 maxFee;
        uint256 lastUpdated;
    }

    mapping(uint256 => FeeConfig) public chainFees;

    // ============ 跨链请求结构 ============
    struct BridgeRequest {
        uint256 id;
        address sender;
        address receiver;
        uint256 amount;
        uint256 fee;
        uint256 sourceChainId;
        uint256 targetChainId;
        address tokenAddress;
        uint256 timestamp;
        uint256 expiry;
        uint256 nonce;
        bool isProcessed;
        bytes[] signatures;
    }

    struct UnlockRequest {
        bytes32 requestId;
        address tokenAddress;
        address recipient;
        uint256 amount;
        uint256 sourceChainId;
        bytes32 sourceBurnTxHash;
        uint256 timestamp;
        bool isProcessed;
        address[] approvedValidators;
    }

    mapping(bytes32 => BridgeRequest) public bridgeRequests;
    mapping(bytes32 => UnlockRequest) public unlockRequests;
    mapping(uint256 => mapping(uint256 => mapping(uint256 => bool))) public usedNonces;

    uint256 public requestCounter;
    uint256 public unlockRequestCounter;

    // ============ 事件定义 ============
    event TokensLocked(
        bytes32 indexed requestHash,
        address indexed sender,
        address indexed receiver,
        uint256 amount,
        uint256 targetChainId,
        address tokenAddress,
        uint256 fee
    );

    event TokensUnlocked(
        bytes32 indexed unlockRequestId,
        address indexed recipient,
        uint256 amount,
        address tokenAddress
    );

    event UnlockRequestCreated(
        bytes32 indexed unlockRequestId,
        address indexed recipient,
        uint256 amount,
        address tokenAddress,
        bytes32 sourceBurnTxHash
    );

    event ValidatorAdded(address indexed validator);
    event ValidatorRemoved(address indexed validator);

    // ============ 修饰符 ============
    modifier onlyValidator() {
        require(validators[msg.sender], "Not a validator");
        _;
    }

    modifier validChain(uint256 _chainId) {
        require(supportedChainIds[_chainId], "Chain not supported");
        _;
    }

    // ============ 构造函数 ============
    constructor(
        address _pxtoken,
        address _patoken,
        address _admin
    ) {
        require(_pxtoken != address(0), "Invalid PXT address");
        require(_patoken != address(0), "Invalid PAT address");
        require(_admin != address(0), "Invalid admin address");

        // 在OpenZeppelin v4中，需要手动转移所有权
        _transferOwnership(_admin);

        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        
        // 添加管理员为验证者
        validators[_admin] = true;
        validatorList.push(_admin);
        
        emit ValidatorAdded(_admin);
    }

    // ============ 正向跨链功能（BSC → PXPAC）============
    
    /**
     * @dev 锁定代币进行跨链（现有功能，保持兼容）
     */
    function lockTokens(
        address _receiver,
        uint256 _amount,
        uint256 _targetChainId,
        address _tokenAddress
    ) external payable whenNotPaused nonReentrant validChain(_targetChainId) returns (bytes32 requestHash) {
        require(_receiver != address(0), "Invalid receiver");
        require(_amount > 0, "Invalid amount");
        require(_tokenAddress != address(0), "Invalid token address");

        // 计算费用
        uint256 fee = calculateFee(_targetChainId, _amount);
        require(msg.value >= fee, "Insufficient fee");

        // 转移代币到合约
        IERC20(_tokenAddress).transferFrom(msg.sender, address(this), _amount);
        
        // 更新锁定记录
        lockedTokens[_tokenAddress] += _amount;

        // 生成请求
        requestCounter++;
        requestHash = keccak256(abi.encodePacked(
            requestCounter,
            msg.sender,
            _receiver,
            _amount,
            _targetChainId,
            _tokenAddress,
            block.timestamp
        ));

        bridgeRequests[requestHash] = BridgeRequest({
            id: requestCounter,
            sender: msg.sender,
            receiver: _receiver,
            amount: _amount,
            fee: fee,
            sourceChainId: block.chainid,
            targetChainId: _targetChainId,
            tokenAddress: _tokenAddress,
            timestamp: block.timestamp,
            expiry: block.timestamp + 24 hours,
            nonce: requestCounter,
            isProcessed: false,
            signatures: new bytes[](0)
        });

        emit TokensLocked(requestHash, msg.sender, _receiver, _amount, _targetChainId, _tokenAddress, fee);

        // 退还多余的费用
        if (msg.value > fee) {
            payable(msg.sender).transfer(msg.value - fee);
        }

        return requestHash;
    }

    // ============ 反向跨链功能（PXPAC → BSC）============
    
    /**
     * @dev 创建解锁请求（验证者调用）
     */
    function createUnlockRequest(
        address tokenAddress,
        address recipient,
        uint256 amount,
        uint256 sourceChainId,
        bytes32 sourceBurnTxHash
    ) external onlyValidator whenNotPaused returns (bytes32 unlockRequestId) {
        require(tokenAddress != address(0), "Invalid token address");
        require(recipient != address(0), "Invalid recipient");
        require(amount > 0, "Invalid amount");
        require(sourceBurnTxHash != bytes32(0), "Invalid source transaction");
        require(lockedTokens[tokenAddress] >= amount, "Insufficient locked tokens");

        // 生成解锁请求ID
        unlockRequestCounter++;
        unlockRequestId = keccak256(abi.encodePacked(
            unlockRequestCounter,
            tokenAddress,
            recipient,
            amount,
            sourceChainId,
            sourceBurnTxHash,
            block.timestamp
        ));

        require(!processedUnlockRequests[unlockRequestId], "Unlock request already exists");

        // 创建解锁请求
        unlockRequests[unlockRequestId] = UnlockRequest({
            requestId: unlockRequestId,
            tokenAddress: tokenAddress,
            recipient: recipient,
            amount: amount,
            sourceChainId: sourceChainId,
            sourceBurnTxHash: sourceBurnTxHash,
            timestamp: block.timestamp,
            isProcessed: false,
            approvedValidators: new address[](0)
        });

        // 添加当前验证者的批准
        unlockRequests[unlockRequestId].approvedValidators.push(msg.sender);

        emit UnlockRequestCreated(unlockRequestId, recipient, amount, tokenAddress, sourceBurnTxHash);

        return unlockRequestId;
    }

    /**
     * @dev 批准解锁请求（其他验证者调用）
     */
    function approveUnlockRequest(bytes32 unlockRequestId) external onlyValidator whenNotPaused {
        UnlockRequest storage request = unlockRequests[unlockRequestId];
        require(request.timestamp > 0, "Unlock request not found");
        require(!request.isProcessed, "Unlock request already processed");

        // 检查验证者是否已经批准
        for (uint i = 0; i < request.approvedValidators.length; i++) {
            require(request.approvedValidators[i] != msg.sender, "Already approved");
        }

        // 添加批准
        request.approvedValidators.push(msg.sender);

        // 检查是否达到所需批准数
        if (request.approvedValidators.length >= requiredValidators) {
            _executeUnlock(unlockRequestId);
        }
    }

    /**
     * @dev 执行解锁（内部函数）
     */
    function _executeUnlock(bytes32 unlockRequestId) internal {
        UnlockRequest storage request = unlockRequests[unlockRequestId];
        require(!request.isProcessed, "Already processed");
        require(lockedTokens[request.tokenAddress] >= request.amount, "Insufficient locked tokens");

        // 标记为已处理
        request.isProcessed = true;
        processedUnlockRequests[unlockRequestId] = true;

        // 减少锁定数量
        lockedTokens[request.tokenAddress] -= request.amount;

        // 转移代币给接收者
        IERC20(request.tokenAddress).transfer(request.recipient, request.amount);

        emit TokensUnlocked(unlockRequestId, request.recipient, request.amount, request.tokenAddress);
    }

    // ============ 验证者管理 ============
    
    /**
     * @dev 添加验证者
     */
    function addValidator(address validator) external onlyOwner {
        require(validator != address(0), "Invalid validator");
        require(!validators[validator], "Already a validator");

        validators[validator] = true;
        validatorList.push(validator);

        emit ValidatorAdded(validator);
    }

    /**
     * @dev 移除验证者
     */
    function removeValidator(address validator) external onlyOwner {
        require(validators[validator], "Not a validator");

        validators[validator] = false;

        // 从列表中移除
        for (uint i = 0; i < validatorList.length; i++) {
            if (validatorList[i] == validator) {
                validatorList[i] = validatorList[validatorList.length - 1];
                validatorList.pop();
                break;
            }
        }

        emit ValidatorRemoved(validator);
    }

    /**
     * @dev 设置所需验证者数量
     */
    function setRequiredValidators(uint256 _required) external onlyOwner {
        require(_required > 0, "Invalid required validators");
        require(_required <= validatorList.length, "Required exceeds available validators");
        requiredValidators = _required;
    }

    // ============ 查询功能 ============
    
    /**
     * @dev 获取验证者列表
     */
    function getValidators() external view returns (address[] memory) {
        return validatorList;
    }

    /**
     * @dev 获取解锁请求信息
     */
    function getUnlockRequest(bytes32 unlockRequestId) external view returns (UnlockRequest memory) {
        return unlockRequests[unlockRequestId];
    }

    /**
     * @dev 计算费用（现有功能）
     */
    function calculateFee(uint256 _targetChainId, uint256 _amount) public view returns (uint256) {
        FeeConfig memory config = chainFees[_targetChainId];
        
        if (config.lastUpdated == 0) {
            return 0.001 ether; // 默认费用
        }
        
        uint256 percentFee = (_amount * config.percentFee) / 10000;
        uint256 totalFee = config.baseFee + percentFee;
        
        if (totalFee < config.minFee) {
            totalFee = config.minFee;
        }
        if (totalFee > config.maxFee) {
            totalFee = config.maxFee;
        }
        
        return totalFee;
    }

    // ============ 管理功能 ============
    
    /**
     * @dev 设置费用配置
     */
    function setChainFee(
        uint256 _chainId,
        uint256 _baseFee,
        uint256 _percentFee,
        uint256 _minFee,
        uint256 _maxFee
    ) external onlyOwner {
        chainFees[_chainId] = FeeConfig({
            baseFee: _baseFee,
            percentFee: _percentFee,
            minFee: _minFee,
            maxFee: _maxFee,
            lastUpdated: block.timestamp
        });
    }

    /**
     * @dev 添加支持的链
     */
    function addSupportedChain(uint256 _chainId, string memory _name) external onlyOwner {
        supportedChainIds[_chainId] = true;
        chainNames[_chainId] = _name;
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取（仅所有者）
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        require(amount <= IERC20(token).balanceOf(address(this)), "Insufficient balance");
        IERC20(token).transfer(owner(), amount);
    }
}
