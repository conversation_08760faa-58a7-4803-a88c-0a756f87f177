// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

contract ChainMapper is Ownable, ReentrancyGuard {
    struct ChainConfig {
        string name;
        uint256 chainId;
        bool isActive;
        mapping(string => address) contracts;
        uint256 lastUpdated;
    }

    uint256[] public supportedChainIds;

    mapping(uint256 => ChainConfig) public chainConfigs;

    string[] public contractTypes;

    struct MappingLog {
        uint256 timestamp;
        uint256 sourceChainId;
        uint256 targetChainId;
        string contractType;
        address sourceAddress;
        address targetAddress;
        bytes32 mappingId;
    }

    MappingLog[] private mappingLogs;

    mapping(address => bool) public administrators;

    uint256 public constant MAX_LOGS = 1000;

    event ChainAdded(uint256 indexed chainId, string name);
    event ChainStatusUpdated(uint256 indexed chainId, bool isActive);
    event ContractTypeAdded(string contractType);
    event ContractMapped(
        bytes32 indexed mappingId,
        uint256 indexed sourceChainId,
        uint256 indexed targetChainId,
        string contractType,
        address sourceAddress,
        address targetAddress
    );
    event ContractAddressUpdated(
        uint256 indexed chainId,
        string indexed contractType,
        address oldAddress,
        address newAddress
    );
    event AdministratorUpdated(address indexed admin, bool status);

    modifier onlyAdmin() {
        if (!administrators[_msgSender()] && _msgSender() != owner()) revert TokenErrors.Unauthorized();
        _;
    }

    constructor() {
        administrators[_msgSender()] = true;
        emit AdministratorUpdated(_msgSender(), true);
    }

    function setAdministrator(address _admin, bool _status) external onlyOwner {
        if (_admin == address(0)) revert TokenErrors.ZeroAddress();
        administrators[_admin] = _status;
        emit AdministratorUpdated(_admin, _status);
    }

    function addChain(uint256 _chainId, string memory _name) external onlyAdmin {
        if (_chainId == 0) revert TokenErrors.InvalidOperation();
        if (isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (bytes(_name).length == 0) revert TokenErrors.EmptyName();

        ChainConfig storage config = chainConfigs[_chainId];
        config.name = _name;
        config.chainId = _chainId;
        config.isActive = true;
        config.lastUpdated = block.timestamp;

        supportedChainIds.push(_chainId);

        emit ChainAdded(_chainId, _name);
    }

    function updateChainStatus(uint256 _chainId, bool _isActive) external onlyAdmin {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        chainConfigs[_chainId].isActive = _isActive;
        chainConfigs[_chainId].lastUpdated = block.timestamp;

        emit ChainStatusUpdated(_chainId, _isActive);
    }

    function addContractType(string memory _contractType) external onlyAdmin {
        if (bytes(_contractType).length == 0) revert TokenErrors.EmptyName();

        for (uint256 i = 0; i < contractTypes.length; i++) {
            if (_compareStringsIgnoreCase(contractTypes[i], _contractType)) {
                revert TokenErrors.InvalidOperation();
            }
        }

        contractTypes.push(_contractType);

        emit ContractTypeAdded(_contractType);
    }

    function batchAddContractTypes(string[] memory _contractTypes) external onlyAdmin {
        if (_contractTypes.length == 0) revert TokenErrors.InvalidOperation();

        for (uint256 i = 0; i < _contractTypes.length; i++) {
            if (bytes(_contractTypes[i]).length == 0) revert TokenErrors.EmptyName();

            bool exists = false;
            for (uint256 j = 0; j < contractTypes.length; j++) {
                if (_compareStringsIgnoreCase(contractTypes[j], _contractTypes[i])) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                contractTypes.push(_contractTypes[i]);
                emit ContractTypeAdded(_contractTypes[i]);
            }
        }
    }

    function setContractAddress(
        uint256 _chainId,
        string memory _contractType,
        address _contractAddress
    ) external onlyAdmin {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();
        if (_contractAddress == address(0)) revert TokenErrors.ZeroAddress();

        address oldAddress = chainConfigs[_chainId].contracts[_contractType];
        chainConfigs[_chainId].contracts[_contractType] = _contractAddress;
        chainConfigs[_chainId].lastUpdated = block.timestamp;

        emit ContractAddressUpdated(_chainId, _contractType, oldAddress, _contractAddress);
    }

    function batchSetContractAddresses(
        uint256[] memory _chainIds,
        string[] memory _contractTypes,
        address[] memory _contractAddresses
    ) external onlyAdmin {
        if (_chainIds.length != _contractTypes.length || _contractTypes.length != _contractAddresses.length) {
            revert TokenErrors.InvalidArrayLength();
        }

        for (uint256 i = 0; i < _chainIds.length; i++) {
            if (!isChainSupported(_chainIds[i])) continue;
            if (!contractTypeExists(_contractTypes[i])) continue;
            if (_contractAddresses[i] == address(0)) continue;

            address oldAddress = chainConfigs[_chainIds[i]].contracts[_contractTypes[i]];
            chainConfigs[_chainIds[i]].contracts[_contractTypes[i]] = _contractAddresses[i];
            chainConfigs[_chainIds[i]].lastUpdated = block.timestamp;

            emit ContractAddressUpdated(_chainIds[i], _contractTypes[i], oldAddress, _contractAddresses[i]);
        }
    }

    function mapContract(
        uint256 _sourceChainId,
        uint256 _targetChainId,
        string memory _contractType,
        address _sourceAddress,
        address _targetAddress
    ) public onlyAdmin returns (bytes32 mappingId) {
        if (!isChainSupported(_sourceChainId)) revert TokenErrors.InvalidOperation();
        if (!isChainSupported(_targetChainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();
        if (_sourceAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (_targetAddress == address(0)) revert TokenErrors.ZeroAddress();

        chainConfigs[_sourceChainId].contracts[_contractType] = _sourceAddress;
        chainConfigs[_targetChainId].contracts[_contractType] = _targetAddress;

        chainConfigs[_sourceChainId].lastUpdated = block.timestamp;
        chainConfigs[_targetChainId].lastUpdated = block.timestamp;

        mappingId = keccak256(abi.encodePacked(
            _sourceChainId,
            _targetChainId,
            _contractType,
            _sourceAddress,
            _targetAddress,
            block.timestamp
        ));

        if (mappingLogs.length >= MAX_LOGS) {
            for (uint256 i = 0; i < mappingLogs.length - 1; i++) {
                mappingLogs[i] = mappingLogs[i + 1];
            }
            mappingLogs.pop();
        }

        mappingLogs.push(MappingLog({
            timestamp: block.timestamp,
            sourceChainId: _sourceChainId,
            targetChainId: _targetChainId,
            contractType: _contractType,
            sourceAddress: _sourceAddress,
            targetAddress: _targetAddress,
            mappingId: mappingId
        }));

        emit ContractMapped(
            mappingId,
            _sourceChainId,
            _targetChainId,
            _contractType,
            _sourceAddress,
            _targetAddress
        );

        return mappingId;
    }

    function batchMapContracts(
        uint256[] memory _sourceChainIds,
        uint256[] memory _targetChainIds,
        string[] memory _contractTypes,
        address[] memory _sourceAddresses,
        address[] memory _targetAddresses
    ) external onlyAdmin returns (bytes32[] memory mappingIds) {
        uint256 length = _sourceChainIds.length;
        if (
            length != _targetChainIds.length ||
            length != _contractTypes.length ||
            length != _sourceAddresses.length ||
            length != _targetAddresses.length
        ) {
            revert TokenErrors.InvalidArrayLength();
        }

        mappingIds = new bytes32[](length);

        for (uint256 i = 0; i < length; i++) {
            if (
                !isChainSupported(_sourceChainIds[i]) ||
                !isChainSupported(_targetChainIds[i]) ||
                !contractTypeExists(_contractTypes[i]) ||
                _sourceAddresses[i] == address(0) ||
                _targetAddresses[i] == address(0)
            ) {
                continue;
            }

            mappingIds[i] = mapContract(
                _sourceChainIds[i],
                _targetChainIds[i],
                _contractTypes[i],
                _sourceAddresses[i],
                _targetAddresses[i]
            );
        }

        return mappingIds;
    }

    function getContractAddress(
        uint256 _chainId,
        string memory _contractType
    ) external view returns (address) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();

        return chainConfigs[_chainId].contracts[_contractType];
    }

    function batchGetContractAddresses(
        uint256[] memory _chainIds,
        string[] memory _contractTypes
    ) external view returns (address[] memory addresses) {
        if (_chainIds.length != _contractTypes.length) {
            revert TokenErrors.InvalidArrayLength();
        }

        addresses = new address[](_chainIds.length);

        for (uint256 i = 0; i < _chainIds.length; i++) {
            if (isChainSupported(_chainIds[i]) && contractTypeExists(_contractTypes[i])) {
                addresses[i] = chainConfigs[_chainIds[i]].contracts[_contractTypes[i]];
            } else {
                addresses[i] = address(0);
            }
        }

        return addresses;
    }

    function getChainCount() external view returns (uint256) {
        return supportedChainIds.length;
    }

    function getChainName(uint256 _chainId) external view returns (string memory) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        return chainConfigs[_chainId].name;
    }

    function getAllChainIds() external view returns (uint256[] memory) {
        return supportedChainIds;
    }

    function getAllContractTypes() external view returns (string[] memory) {
        return contractTypes;
    }

    function getChainConfig(uint256 _chainId) external view returns (
        string memory name,
        uint256 chainId,
        bool isActive,
        uint256 lastUpdated
    ) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        ChainConfig storage config = chainConfigs[_chainId];

        return (
            config.name,
            config.chainId,
            config.isActive,
            config.lastUpdated
        );
    }

    function getMappingLogsCount() external view returns (uint256) {
        return mappingLogs.length;
    }

    function isChainSupported(uint256 _chainId) public view returns (bool) {
        for (uint256 i = 0; i < supportedChainIds.length; i++) {
            if (supportedChainIds[i] == _chainId) {
                return true;
            }
        }
        return false;
    }

    function contractTypeExists(string memory _contractType) public view returns (bool) {
        for (uint256 i = 0; i < contractTypes.length; i++) {
            if (_compareStringsIgnoreCase(contractTypes[i], _contractType)) {
                return true;
            }
        }
        return false;
    }

    function getAllContractAddresses(uint256 _chainId) external view returns (string[] memory, address[] memory) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        address[] memory addresses = new address[](contractTypes.length);
        for (uint256 i = 0; i < contractTypes.length; i++) {
            addresses[i] = chainConfigs[_chainId].contracts[contractTypes[i]];
        }
        return (contractTypes, addresses);
    }

    function getMappingLogsPaged(uint256 offset, uint256 limit) external view returns (MappingLog[] memory) {
        uint256 total = mappingLogs.length;
        if (offset >= total) {
            return new MappingLog[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        MappingLog[] memory result = new MappingLog[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            result[i - offset] = mappingLogs[i];
        }
        return result;
    }

    function _compareStringsIgnoreCase(string memory a, string memory b) internal pure returns (bool) {
        return keccak256(bytes(_toLower(a))) == keccak256(bytes(_toLower(b)));
    }

    function _toLower(string memory str) internal pure returns (string memory) {
        bytes memory bStr = bytes(str);
        bytes memory bLower = new bytes(bStr.length);

        for (uint256 i = 0; i < bStr.length; i++) {
            if (uint8(bStr[i]) >= 65 && uint8(bStr[i]) <= 90) {
                bLower[i] = bytes1(uint8(bStr[i]) + 32);
            } else {
                bLower[i] = bStr[i];
            }
        }

        return string(bLower);
    }
}