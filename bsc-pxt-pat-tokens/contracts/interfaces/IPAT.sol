// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.17;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

interface IPAT is IERC20 {
    function mint(address account, uint256 amount) external;

    function burn(uint256 amount) external;

    function burnFrom(address account, uint256 amount) external;

    function setInflationRate(uint256 rate) external;

    function inflationRate() external view returns (uint256);

    function executeInflation() external returns (uint256);

    function totalMinted() external view returns (uint256);

    function totalBurned() external view returns (uint256);

    function lock(address account, uint256 amount, uint256 unlockTime) external;

    function unlock(address account, uint256 amount) external;

    function lockedBalanceOf(address account) external view returns (uint256);

    function setPaused(bool paused) external;

    function paused() external view returns (bool);

    function isMinter(address account) external view returns (bool);

    function setMinter(address account, bool status) external;
}
