// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.17;

interface IAssetInterface {
    enum RegionType {
        INTERNATIONAL,
        MAINLAND_CHINA
    }

    enum AssetType {
        PXT,
        PAT
    }

    enum OperationType {
        TRANSFER,
        STAKE,
        REWARD,
        BURN,
        MINT,
        LOCK,
        UNLOCK
    }

    struct OperationLog {
        uint256 timestamp;
        address from;
        address to;
        AssetType assetType;
        OperationType operation;
        uint256 amount;
        bytes metadata;
    }

    event OperationExecuted(
        RegionType indexed region,
        AssetType indexed assetType,
        OperationType indexed operation,
        address from,
        address to,
        uint256 amount,
        bool success
    );

    event WalletAllocated(string indexed userId, address wallet);

    event AssetsMigrated(
        string indexed userId,
        uint256 pxtAmount,
        uint256 patAmount,
        bool success
    );

    function executeOperation(
        RegionType region,
        AssetType assetType,
        OperationType operation,
        address from,
        address to,
        uint256 amount,
        bytes calldata metadata
    ) external returns (bool);

    function balanceOf(
        RegionType region,
        AssetType assetType,
        address account
    ) external view returns (uint256);

    function totalSupply(
        RegionType region,
        AssetType assetType
    ) external view returns (uint256);

    function lockedBalanceOf(
        RegionType region,
        AssetType assetType,
        address account
    ) external view returns (uint256);

    function unlockTimesOf(
        RegionType region,
        AssetType assetType,
        address account
    ) external view returns (uint256[] memory);

    function preAllocateWallet(string calldata userId) external returns (address);

    function getWalletAddress(string calldata userId) external view returns (address);

    function needsMigration() external view returns (bool);

    function migrate(
        string calldata userId,
        uint256 pxtAmount,
        uint256 patAmount,
        uint256 nonce,
        uint256 expiry,
        bytes calldata signature
    ) external returns (bool);

    function isMigrated(string calldata userId) external view returns (bool);

    function isNonceUsed(uint256 nonce) external view returns (bool);

    function getOperationLogs(
        address account,
        uint256 startTime,
        uint256 endTime
    ) external view returns (OperationLog[] memory);

    function batchBalanceOf(
        RegionType region,
        AssetType assetType,
        address[] calldata accounts
    ) external view returns (uint256[] memory);
}
