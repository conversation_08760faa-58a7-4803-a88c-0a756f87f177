// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.17;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

interface IPXT is IERC20 {
    event TokensLocked(address indexed account, uint256 amount, uint256 unlockTime);

    event TokensUnlocked(address indexed account, uint256 amount);

    event TokensBurned(address indexed account, uint256 amount);

    event PauseStatusChanged(bool paused);

    event RoleStatusChanged(address indexed account, uint256 indexed roleId, bool granted);

    event StakingStatusChanged(address indexed account, uint256 amount, bool isStake);

    function lock(address account, uint256 amount, uint256 unlockTime) external;

    function unlock(address account, uint256 amount) external;

    function batchLock(address[] calldata accounts, uint256[] calldata amounts, uint256 unlockTime) external;

    function batchUnlock(address[] calldata accounts, uint256[] calldata amounts) external;

    function lockedBalanceOf(address account) external view returns (uint256);

    function unlockTimesOf(address account) external view returns (uint256[] memory);

    function canUnlock(address account, uint256 amount) external view returns (bool);

    function burn(uint256 amount) external;

    function burnFrom(address account, uint256 amount) external;

    function batchBurnFrom(address[] calldata accounts, uint256[] calldata amounts) external;

    function setPaused(bool paused) external;

    function paused() external view returns (bool);

    function totalMinted() external view returns (uint256);

    function totalBurned() external view returns (uint256);

    function hasRole(address account, uint256 id) external view returns (bool);

    function grantRole(address account, uint256 id) external;

    function revokeRole(address account, uint256 id) external;

    function batchGrantRole(address[] calldata accounts, uint256 id) external;

    function batchRevokeRole(address[] calldata accounts, uint256 id) external;

    function getAllRoles() external view returns (uint256[] memory ids, string[] memory descriptions);

    function recoverERC20(address tokenAddress, uint256 amount) external;

    function batchHasRole(address[] calldata accounts, uint256 id) external view returns (bool[] memory);
}
