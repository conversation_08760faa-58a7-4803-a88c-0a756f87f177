// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.17;

interface IDEXRouter {
    event Swap(
        address indexed sender,
        uint256 amountIn,
        uint256 amountOut,
        address[] path,
        address indexed to
    );

    event RouterParametersUpdated(
        address indexed factory,
        address indexed weth
    );

    function swapExactETHForTokens(
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external payable returns (uint[] memory amounts);

    function swapExactTokensForETH(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);

    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);

    function swapTokensForExactTokens(
        uint amountOut,
        uint amountInMax,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);

    function swapETHForExactTokens(
        uint amountOut,
        address[] calldata path,
        address to,
        uint deadline
    ) external payable returns (uint[] memory amounts);

    function getAmountsOut(
        uint amountIn,
        address[] calldata path
    ) external view returns (uint[] memory amounts);

    function getAmountsIn(
        uint amountOut,
        address[] calldata path
    ) external view returns (uint[] memory amounts);

    function getPriceImpact(
        uint amountIn,
        address[] calldata path
    ) external view returns (uint priceImpact);

    function getCurrentBlockTimestamp() external view returns (uint);

    function WETH() external view returns (address);

    function factory() external view returns (address);

    function isValidPath(
        address[] calldata path
    ) external view returns (bool);
}