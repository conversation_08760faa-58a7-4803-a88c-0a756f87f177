// 从BSC测试网跨链PAT代币到PXA本地链
const { ethers } = require("hardhat");
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🌉 BSC测试网 → PXA本地链跨链测试");
    console.log("=====================================");
    
    const network = hre.network;
    console.log("🌐 当前网络:", network.name);
    
    if (network.name !== "bscTestnet") {
        console.error("❌ 请在BSC测试网上运行此脚本:");
        console.error("npx hardhat run scripts/cross-chain/bridge-to-pxa-local.js --network bscTestnet");
        process.exit(1);
    }
    
    // 检查PXA本地链是否运行
    console.log("\n=== 1. 检查PXA本地链状态 ===");
    
    try {
        // 使用ethers v5兼容的语法
        const pxaProvider = new ethers.providers.JsonRpcProvider("http://127.0.0.1:8545");
        const pxaNetwork = await pxaProvider.getNetwork();
        const pxaBlockNumber = await pxaProvider.getBlockNumber();

        console.log("✅ PXA本地链运行正常");
        console.log("- 链ID:", pxaNetwork.chainId.toString());
        console.log("- 当前区块:", pxaBlockNumber);

        if (pxaNetwork.chainId !== 327) {
            console.error("❌ PXA链ID不正确，期望327，实际:", pxaNetwork.chainId.toString());
            process.exit(1);
        }
        
    } catch (error) {
        console.error("❌ PXA本地链未运行，请先启动:");
        console.error("cd ../pxa-chain/local-chain && ./local-chain-setup.sh");
        process.exit(1);
    }
    
    console.log("\n=== 2. 加载BSC测试网部署信息 ===");
    
    // 读取BSC测试网部署信息
    let bscDeployment;
    try {
        const deploymentDir = path.join(__dirname, "../../deployments", network.name);
        // 尝试多种文件名格式
        let deploymentFiles = fs.readdirSync(deploymentDir)
            .filter(file => file.startsWith('core-deployment-') && file.endsWith('.json'))
            .sort();

        // 如果没找到带时间戳的文件，尝试简单格式
        if (deploymentFiles.length === 0) {
            deploymentFiles = fs.readdirSync(deploymentDir)
                .filter(file => file === 'core-deployment.json');
        }

        if (deploymentFiles.length === 0) {
            throw new Error("未找到BSC核心部署文件");
        }

        const latestFile = deploymentFiles[deploymentFiles.length - 1];
        bscDeployment = JSON.parse(fs.readFileSync(path.join(deploymentDir, latestFile), 'utf8'));

        // 尝试加载桥接部署信息
        let bridgeDeployment = null;
        try {
            const bridgeFile = path.join(deploymentDir, 'bridge-deployment.json');
            if (fs.existsSync(bridgeFile)) {
                bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
            }
        } catch (error) {
            console.log("⚠️ 未找到桥接部署文件");
        }

        console.log("✅ 已加载BSC部署信息");
        console.log("- PAT代币:", bscDeployment.contracts.PAToken.address);

        if (bridgeDeployment && bridgeDeployment.contracts.TokenBridge) {
            console.log("- 跨链桥:", bridgeDeployment.contracts.TokenBridge.address);
            // 将桥接合约信息合并到主部署信息中
            bscDeployment.contracts.TokenBridge = bridgeDeployment.contracts.TokenBridge;
        } else if (bscDeployment.pools && bscDeployment.pools.crossChainPool) {
            console.log("- 跨链池:", bscDeployment.pools.crossChainPool.address);
        } else {
            throw new Error("未找到跨链合约信息");
        }
        
    } catch (error) {
        console.error("❌ 未找到BSC部署信息，请先部署BSC合约:");
        console.error("npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network bscTestnet");
        process.exit(1);
    }
    
    console.log("\n=== 3. 连接BSC合约 ===");
    
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者地址:", deployer.address);
    
    // 连接PAT代币合约和跨链合约
    const patToken = await ethers.getContractAt("PAToken", bscDeployment.contracts.PAToken.address);

    let bridgeContract;
    if (bscDeployment.contracts.TokenBridge) {
        bridgeContract = await ethers.getContractAt("TokenBridge", bscDeployment.contracts.TokenBridge.address);
        console.log("✅ 使用TokenBridge合约");
    } else {
        // 回退到使用跨链池地址（如果有的话）
        console.log("⚠️ 未找到TokenBridge，请先部署跨链桥合约");
        process.exit(1);
    }
    
    // 检查PAT余额
    const patBalance = await patToken.balanceOf(deployer.address);
    console.log("💰 PAT余额:", ethers.utils.formatEther(patBalance), "PAT");

    if (patBalance.eq(0)) {
        console.error("❌ PAT余额不足，无法进行跨链");
        process.exit(1);
    }

    console.log("\n=== 4. 执行跨链操作 ===");

    // 跨链金额 (1000 PAT)
    let bridgeAmount = ethers.utils.parseEther("1000");

    if (patBalance.lt(bridgeAmount)) {
        console.log("⚠️ PAT余额不足1000，使用全部余额进行跨链");
        bridgeAmount = patBalance;
    }

    console.log("🌉 准备跨链:", ethers.utils.formatEther(bridgeAmount), "PAT");
    
    // 检查授权
    const allowance = await patToken.allowance(deployer.address, bridgeContract.address);
    if (allowance.lt(bridgeAmount)) {
        console.log("🔐 授权PAT代币给跨链桥...");
        const approveTx = await patToken.approve(bridgeContract.address, bridgeAmount);
        await approveTx.wait();
        console.log("✅ 授权完成");
    }

    // 执行跨链
    console.log("🚀 发起跨链交易...");
    const bridgeTx = await bridgeContract.lockTokens(
        bscDeployment.contracts.PAToken.address, // PAT代币地址
        bridgeAmount,                            // 跨链金额
        327,                                     // PXA链ID
        deployer.address                         // 接收地址
    );
    
    console.log("⏳ 等待交易确认...");
    const receipt = await bridgeTx.wait();
    
    console.log("✅ 跨链交易成功!");
    console.log("- 交易哈希:", receipt.hash);
    console.log("- Gas使用:", receipt.gasUsed.toString());
    console.log("- 区块号:", receipt.blockNumber);
    
    // 查找跨链事件
    const bridgeEvents = receipt.logs.filter(log => {
        try {
            const parsed = bridgeContract.interface.parseLog(log);
            return parsed.name === "TokensLocked" || parsed.name === "TokensBridged";
        } catch {
            return false;
        }
    });

    if (bridgeEvents.length > 0) {
        const event = bridgeContract.interface.parseLog(bridgeEvents[0]);
        console.log("📋 跨链事件详情:");
        console.log("- 事件名称:", event.name);
        console.log("- 代币:", event.args.token || event.args.tokenAddress);
        console.log("- 金额:", ethers.utils.formatEther(event.args.amount), "PAT");
        console.log("- 目标链:", event.args.targetChain?.toString() || "327");
        console.log("- 接收者:", event.args.recipient || event.args.to);
        if (event.args.bridgeId) {
            console.log("- 跨链ID:", event.args.bridgeId.toString());
        }
    }
    
    console.log("\n=== 5. 下一步操作 ===");
    
    console.log("🎯 现在需要在PXA链上接收跨链代币:");
    console.log("1. 切换到PXA链目录:");
    console.log("   cd ../pxa-chain");
    console.log("2. 执行跨链接收:");
    console.log("   npx hardhat run scripts/bridge/mint-bridged-tokens.js --network localhost");
    console.log("3. 查询跨链统计:");
    console.log("   npx hardhat run scripts/query/cross-chain-stats.js --network localhost");
    
    console.log("\n🎊 BSC → PXA跨链发起成功!");
    console.log("=====================================");
    console.log("✅ 跨链交易已提交到BSC测试网");
    console.log("✅ 等待PXA链处理跨链请求");
    console.log("✅ 跨链完成后将获得wPAT代币");
}

// 错误处理
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 跨链脚本执行失败:", error);
        process.exit(1);
    });
