const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 跨链1万PAT到PXA链");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("操作账户:", deployer.address);
    
    // 跨链参数
    const BRIDGE_AMOUNT = ethers.parseEther("10000"); // 1万PAT
    const TARGET_CHAIN_ID = 327; // PXA链ID（修正为正确的链ID）
    const TARGET_ADDRESS = deployer.address; // 接收地址（可以改为其他地址）
    
    console.log("跨链金额:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
    console.log("目标链ID:", TARGET_CHAIN_ID);
    console.log("接收地址:", TARGET_ADDRESS);
    
    // 加载合约地址
    let patTokenAddress, bridgeAddress;
    
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        const bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        
        patTokenAddress = coreDeployment.contracts.PAToken.address;
        bridgeAddress = bridgeDeployment.contracts.TokenBridge.address;
        
        console.log("✅ 已加载合约地址");
        console.log("PAT代币:", patTokenAddress);
        console.log("跨链桥:", bridgeAddress);
    } catch (error) {
        console.error("❌ 未找到部署文件，请先部署相关合约");
        process.exit(1);
    }
    
    // 连接合约
    const patToken = await ethers.getContractAt("PAToken", patTokenAddress);
    const tokenBridge = await ethers.getContractAt("TokenBridge", bridgeAddress);
    
    console.log("\n=== 1. 检查余额和授权 ===");
    
    // 检查PAT余额
    const patBalance = await patToken.balanceOf(deployer.address);
    console.log("当前PAT余额:", ethers.formatEther(patBalance), "PAT");
    
    if (patBalance < BRIDGE_AMOUNT) {
        console.error("❌ PAT余额不足，需要:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
        process.exit(1);
    }
    
    // 检查BNB余额
    const bnbBalance = await ethers.provider.getBalance(deployer.address);
    console.log("当前BNB余额:", ethers.formatEther(bnbBalance), "BNB");
    
    if (bnbBalance < ethers.parseEther("0.2")) {
        console.warn("⚠️ BNB余额较低，可能不足以支付跨链费用");
    }
    
    // 检查授权
    const allowance = await patToken.allowance(deployer.address, bridgeAddress);
    console.log("当前授权额度:", ethers.formatEther(allowance), "PAT");
    
    if (allowance < BRIDGE_AMOUNT) {
        console.log("需要授权PAT代币...");
        const approveTx = await patToken.approve(bridgeAddress, BRIDGE_AMOUNT);
        await approveTx.wait();
        console.log("✅ PAT代币授权完成");
    }
    
    console.log("\n=== 2. 计算跨链费用 ===");
    
    // 获取跨链费用
    const bridgeFee = await tokenBridge.calculateFee(BRIDGE_AMOUNT, TARGET_CHAIN_ID);
    console.log("跨链费用:", ethers.formatEther(bridgeFee), "BNB");
    
    // 验证BNB余额是否足够
    if (bnbBalance < bridgeFee) {
        console.error("❌ BNB余额不足以支付跨链费用");
        console.error("需要:", ethers.formatEther(bridgeFee), "BNB");
        console.error("当前:", ethers.formatEther(bnbBalance), "BNB");
        process.exit(1);
    }
    
    console.log("\n=== 3. 执行跨链操作 ===");
    
    console.log("正在发起跨链交易...");
    const bridgeTx = await tokenBridge.lockTokens(
        TARGET_ADDRESS,
        BRIDGE_AMOUNT,
        TARGET_CHAIN_ID,
        patTokenAddress,
        { value: bridgeFee }
    );
    
    console.log("交易哈希:", bridgeTx.hash);
    console.log("等待交易确认...");
    
    const receipt = await bridgeTx.wait();
    console.log("✅ 跨链交易确认成功");
    console.log("区块号:", receipt.blockNumber);
    console.log("Gas使用:", receipt.gasUsed.toString());
    
    console.log("\n=== 4. 验证跨链结果 ===");
    
    // 检查余额变化
    const newPatBalance = await patToken.balanceOf(deployer.address);
    const newBnbBalance = await ethers.provider.getBalance(deployer.address);
    
    console.log("跨链后PAT余额:", ethers.formatEther(newPatBalance), "PAT");
    console.log("跨链后BNB余额:", ethers.formatEther(newBnbBalance), "BNB");
    
    const patUsed = patBalance - newPatBalance;
    const bnbUsed = bnbBalance - newBnbBalance;
    
    console.log("PAT消耗:", ethers.formatEther(patUsed), "PAT");
    console.log("BNB消耗:", ethers.formatEther(bnbUsed), "BNB");
    
    console.log("\n=== 5. 跨链信息总结 ===");
    console.log("🎉 跨链操作完成!");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("📊 跨链详情:");
    console.log("  源链: BSC (链ID: 97)");
    console.log("  目标链: PXA (链ID: 327)");
    console.log("  跨链金额:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
    console.log("  接收地址:", TARGET_ADDRESS);
    console.log("  交易哈希:", bridgeTx.hash);
    console.log("  跨链费用:", ethers.formatEther(bridgeFee), "BNB");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("🔧 下一步:");
    console.log("1. 等待PXA链验证者确认（通常需要几分钟）");
    console.log("2. 在PXA链查看wPAT余额变化");
    console.log("3. 验证跨链统计数据");
    
    return {
        bridgeAmount: ethers.formatEther(BRIDGE_AMOUNT),
        bridgeFee: ethers.formatEther(bridgeFee),
        txHash: bridgeTx.hash,
        blockNumber: receipt.blockNumber,
        targetChain: TARGET_CHAIN_ID,
        targetAddress: TARGET_ADDRESS
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("跨链操作失败:", error);
            process.exit(1);
        });
}

module.exports = main;
