// 直接使用跨链池账户执行10,000 PAT跨链到PXA链
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🌉 使用跨链池直接执行10,000 PAT跨链到PXA链");
    console.log("================================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 部署者地址:", deployer.address);
    console.log("⏰ 时间:", new Date().toISOString());
    
    // 跨链参数
    const BRIDGE_AMOUNT = ethers.parseEther("10000"); // 1万PAT
    const TARGET_CHAIN_ID = 327; // PXA链ID
    const TARGET_ADDRESS = deployer.address; // 接收地址
    
    console.log("🎯 跨链参数:");
    console.log("- 跨链金额:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
    console.log("- 目标链ID:", TARGET_CHAIN_ID);
    console.log("- 接收地址:", TARGET_ADDRESS);
    
    // 从部署记录加载地址和私钥
    let coreDeployment, bridgeDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件");
        process.exit(1);
    }
    
    const patTokenAddress = coreDeployment.contracts.PAToken.address;
    const tokenBridgeAddress = bridgeDeployment.contracts.TokenBridge.address;
    const crossChainPoolAddress = coreDeployment.pools.crossChainPool.address;
    const crossChainPoolPrivateKey = coreDeployment.pools.crossChainPool.privateKey;
    
    console.log("📝 合约地址:");
    console.log("- PAT代币:", patTokenAddress);
    console.log("- 跨链桥:", tokenBridgeAddress);
    console.log("- 跨链池:", crossChainPoolAddress);
    
    // 创建跨链池钱包
    const provider = ethers.provider;
    const poolWallet = new ethers.Wallet(crossChainPoolPrivateKey, provider);
    
    console.log("- 跨链池钱包:", poolWallet.address);
    
    // 验证跨链池地址匹配
    if (poolWallet.address.toLowerCase() !== crossChainPoolAddress.toLowerCase()) {
        console.error("❌ 跨链池私钥不匹配!");
        process.exit(1);
    }
    
    // 连接合约（使用跨链池钱包）
    const patToken = await ethers.getContractAt("PAToken", patTokenAddress);
    const tokenBridge = await ethers.getContractAt("TokenBridge", tokenBridgeAddress);
    
    const patTokenFromPool = patToken.connect(poolWallet);
    const tokenBridgeFromPool = tokenBridge.connect(poolWallet);
    
    console.log("\n=== 1. 检查跨链池余额和Gas费 ===");
    
    // 检查PAT余额
    const poolPATBalance = await patToken.balanceOf(poolWallet.address);
    console.log("跨链池PAT余额:", ethers.formatEther(poolPATBalance), "PAT");
    
    if (poolPATBalance < BRIDGE_AMOUNT) {
        console.error("❌ 跨链池PAT余额不足");
        console.error("需要:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
        console.error("拥有:", ethers.formatEther(poolPATBalance), "PAT");
        process.exit(1);
    }
    
    // 检查BNB余额
    const poolBNBBalance = await provider.getBalance(poolWallet.address);
    console.log("跨链池BNB余额:", ethers.formatEther(poolBNBBalance), "BNB");
    
    // 如果跨链池BNB不足，从部署者转一些
    const minBNBRequired = ethers.parseEther("0.15"); // 至少需要0.15 BNB（预留一些余量）
    if (poolBNBBalance < minBNBRequired) {
        console.log("\n💰 跨链池BNB不足，从部署者转账...");
        const transferAmount = ethers.parseEther("0.2"); // 转0.2 BNB（足够支付0.1 BNB手续费）

        console.log("转账金额:", ethers.formatEther(transferAmount), "BNB");
        console.log("从:", deployer.address);
        console.log("到:", poolWallet.address);

        const transferTx = await deployer.sendTransaction({
            to: poolWallet.address,
            value: transferAmount
        });

        console.log("⏳ 等待BNB转账确认...");
        console.log("转账交易哈希:", transferTx.hash);
        await transferTx.wait();

        const newPoolBNBBalance = await provider.getBalance(poolWallet.address);
        console.log("✅ BNB转账成功，跨链池新余额:", ethers.formatEther(newPoolBNBBalance), "BNB");
    }
    
    console.log("\n=== 2. 计算跨链费用 ===");

    // 计算跨链费用
    const bridgeFee = await tokenBridge.calculateFee(BRIDGE_AMOUNT, TARGET_CHAIN_ID);
    console.log("跨链手续费:", ethers.formatEther(bridgeFee), "BNB");
    
    // 验证BNB余额足够支付费用
    const finalPoolBNBBalance = await provider.getBalance(poolWallet.address);
    if (finalPoolBNBBalance < bridgeFee) {
        console.log("⚠️ 跨链池BNB余额仍然不足，需要额外转账...");
        console.log("需要:", ethers.formatEther(bridgeFee), "BNB");
        console.log("拥有:", ethers.formatEther(finalPoolBNBBalance), "BNB");

        // 计算需要额外转账的金额（加上一些余量）
        const additionalAmount = bridgeFee - finalPoolBNBBalance + ethers.parseEther("0.05");
        console.log("额外转账:", ethers.formatEther(additionalAmount), "BNB");

        const additionalTx = await deployer.sendTransaction({
            to: poolWallet.address,
            value: additionalAmount
        });

        console.log("⏳ 等待额外BNB转账确认...");
        await additionalTx.wait();

        const updatedPoolBNBBalance = await provider.getBalance(poolWallet.address);
        console.log("✅ 额外转账成功，跨链池最终余额:", ethers.formatEther(updatedPoolBNBBalance), "BNB");

        if (updatedPoolBNBBalance < bridgeFee) {
            console.error("❌ 转账后余额仍然不足，请检查部署者BNB余额");
            process.exit(1);
        }
    }
    
    console.log("\n=== 3. 授权PAT代币给跨链桥 ===");
    
    // 检查当前授权额度
    const currentAllowance = await patToken.allowance(poolWallet.address, tokenBridgeAddress);
    console.log("当前授权额度:", ethers.formatEther(currentAllowance), "PAT");
    
    if (currentAllowance < BRIDGE_AMOUNT) {
        console.log("授权额度不足，正在授权...");
        
        const approveTx = await patTokenFromPool.approve(tokenBridgeAddress, BRIDGE_AMOUNT);
        console.log("⏳ 等待授权交易确认...");
        await approveTx.wait();
        
        const newAllowance = await patToken.allowance(poolWallet.address, tokenBridgeAddress);
        console.log("✅ 授权成功，新授权额度:", ethers.formatEther(newAllowance), "PAT");
    } else {
        console.log("✅ 授权额度充足");
    }
    
    console.log("\n=== 4. 执行跨链操作 ===");
    
    console.log("🚀 开始跨链转账...");
    console.log("参数确认:");
    console.log("- PAT代币地址:", patTokenAddress);
    console.log("- 跨链金额:", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
    console.log("- 目标链ID:", TARGET_CHAIN_ID);
    console.log("- 接收地址:", TARGET_ADDRESS);
    console.log("- 手续费:", ethers.formatEther(bridgeFee), "BNB");
    
    try {
        const bridgeTx = await tokenBridgeFromPool.lockTokens(
            TARGET_ADDRESS,
            BRIDGE_AMOUNT,
            TARGET_CHAIN_ID,
            patTokenAddress,
            { value: bridgeFee }
        );
        
        console.log("⏳ 等待跨链交易确认...");
        console.log("交易哈希:", bridgeTx.hash);
        
        const receipt = await bridgeTx.wait();
        
        console.log("✅ 跨链交易成功!");
        console.log("- 区块号:", receipt.blockNumber);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 检查余额变化
        const newPoolPATBalance = await patToken.balanceOf(poolWallet.address);
        const newPoolBNBBalance = await provider.getBalance(poolWallet.address);
        
        console.log("\n💰 跨链后余额:");
        console.log("- 跨链池PAT余额:", ethers.formatEther(newPoolPATBalance), "PAT");
        console.log("- 跨链池BNB余额:", ethers.formatEther(newPoolBNBBalance), "BNB");
        
        console.log("\n🎉 跨链操作完成!");
        console.log("=====================================");
        console.log("✅ 已从BSC链跨链", ethers.formatEther(BRIDGE_AMOUNT), "PAT");
        console.log("✅ 目标链: PXA (链ID:", TARGET_CHAIN_ID, ")");
        console.log("✅ 接收地址:", TARGET_ADDRESS);
        console.log("✅ 交易哈希:", bridgeTx.hash);
        
        console.log("\n📋 下一步:");
        console.log("1. 等待PXA链验证者处理跨链请求");
        console.log("2. 检查PXA链上的wPAT余额变化");
        console.log("3. 验证跨链是否成功完成");
        
        return {
            success: true,
            txHash: bridgeTx.hash,
            bridgeAmount: ethers.formatEther(BRIDGE_AMOUNT),
            targetChainId: TARGET_CHAIN_ID,
            recipient: TARGET_ADDRESS,
            fee: ethers.formatEther(bridgeFee)
        };
        
    } catch (error) {
        console.error("❌ 跨链操作失败:", error.message);
        
        // 提供详细的错误信息
        if (error.message.includes("insufficient funds")) {
            console.error("💡 可能原因: BNB余额不足支付Gas费");
        } else if (error.message.includes("allowance")) {
            console.error("💡 可能原因: PAT代币授权不足");
        } else if (error.message.includes("unsupported chain")) {
            console.error("💡 可能原因: 目标链ID不受支持");
        }
        
        process.exit(1);
    }
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}

module.exports = main;
