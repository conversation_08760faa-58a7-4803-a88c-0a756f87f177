const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔗 设置BSC-PXA跨链桥连接");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    console.log("时间:", new Date().toISOString());
    
    const [deployer, treasury, operator] = await ethers.getSigners();
    console.log("部署者地址:", deployer.address);
    console.log("国库地址:", treasury.address);
    console.log("操作员地址:", operator.address);
    
    // 加载部署信息
    let coreDeployment, bridgeDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件，请先部署相关合约");
        process.exit(1);
    }
    
    const patTokenAddress = coreDeployment.contracts.PAToken.address;
    const tokenBridgeAddress = bridgeDeployment.contracts.TokenBridge.address;
    
    console.log("\n📝 合约地址:");
    console.log("- PAT代币:", patTokenAddress);
    console.log("- TokenBridge:", tokenBridgeAddress);
    
    // 连接合约
    const patToken = await ethers.getContractAt("PAToken", patTokenAddress);
    const tokenBridge = await ethers.getContractAt("TokenBridge", tokenBridgeAddress);
    
    console.log("\n=== 1. 检查桥接合约状态 ===");

    // 检查PXA链支持状态
    const pxaChainId = 327;
    const isPXASupported = await tokenBridge.supportedChainIds(pxaChainId);
    console.log("PXA链支持状态:", isPXASupported ? "✅ 支持" : "❌ 不支持");

    if (!isPXASupported) {
        console.error("❌ PXA链未配置，请检查桥接合约部署");
        process.exit(1);
    }

    // 检查验证者配置
    const validators = await tokenBridge.getChainValidators(pxaChainId);
    console.log("PXA链验证者数量:", validators.length);

    if (validators.length < 2) {
        console.warn("⚠️ 验证者数量较少，建议至少配置2个验证者");
    }

    // 显示验证者列表
    console.log("验证者列表:");
    for (let i = 0; i < validators.length; i++) {
        console.log(`  ${i + 1}. ${validators[i]}`);
    }
    
    console.log("\n=== 2. 检查费用配置 ===");
    
    // 检查费用配置
    const testAmount = ethers.parseEther("1000"); // 测试1000 PAT的费用
    const bridgeFee = await tokenBridge.calculateFee(testAmount, pxaChainId);
    console.log("跨链费用示例 (1000 PAT):", ethers.formatEther(bridgeFee), "BNB");
    
    // 获取费用配置详情
    const feeConfig = await tokenBridge.chainFees(pxaChainId);
    console.log("费用配置:");
    console.log("- 基础费用:", ethers.formatEther(feeConfig.baseFee), "BNB");
    console.log("- 百分比费用:", feeConfig.percentFee.toString(), "基点");
    console.log("- 最小费用:", ethers.formatEther(feeConfig.minFee), "BNB");
    console.log("- 最大费用:", ethers.formatEther(feeConfig.maxFee), "BNB");
    
    console.log("\n=== 3. 检查代币支持 ===");

    // 检查PAT代币是否支持跨链（TokenBridge合约默认支持PXT和PAT）
    console.log("PAT代币地址:", patTokenAddress);
    console.log("✅ PAT代币跨链支持: 默认支持（合约内置）");

    // 验证PAT代币地址是否匹配合约中的patoken
    try {
        const contractPATAddress = await tokenBridge.patoken();
        const isMatching = contractPATAddress.toLowerCase() === patTokenAddress.toLowerCase();
        console.log("合约PAT地址:", contractPATAddress);
        console.log("地址匹配:", isMatching ? "✅ 匹配" : "❌ 不匹配");

        if (!isMatching) {
            console.warn("⚠️ PAT代币地址不匹配，可能影响跨链功能");
        }
    } catch (error) {
        console.error("❌ 验证PAT代币地址失败:", error.message);
    }
    
    console.log("\n=== 4. 准备跨链池资金 ===");
    
    // 检查跨链池配置
    const crossChainPool = coreDeployment.pools.crossChainPool;
    console.log("跨链池地址:", crossChainPool.address);
    
    // 检查跨链池PAT余额
    const poolPATBalance = await patToken.balanceOf(crossChainPool.address);
    console.log("跨链池PAT余额:", ethers.formatEther(poolPATBalance), "PAT");
    
    // 检查跨链池BNB余额
    const poolBNBBalance = await ethers.provider.getBalance(crossChainPool.address);
    console.log("跨链池BNB余额:", ethers.formatEther(poolBNBBalance), "BNB");
    
    // 如果跨链池PAT余额不足，从部署者转一些
    const minPATRequired = ethers.parseEther("50000"); // 至少需要5万PAT
    if (poolPATBalance < minPATRequired) {
        console.log("\n💰 跨链池PAT余额不足，正在转账...");
        
        const deployerPATBalance = await patToken.balanceOf(deployer.address);
        console.log("部署者PAT余额:", ethers.formatEther(deployerPATBalance), "PAT");
        
        if (deployerPATBalance >= minPATRequired) {
            const transferAmount = minPATRequired;
            console.log("转账金额:", ethers.formatEther(transferAmount), "PAT");
            
            const transferTx = await patToken.transfer(crossChainPool.address, transferAmount);
            await transferTx.wait();
            
            const newPoolBalance = await patToken.balanceOf(crossChainPool.address);
            console.log("✅ PAT转账成功，跨链池新余额:", ethers.formatEther(newPoolBalance), "PAT");
        } else {
            console.warn("⚠️ 部署者PAT余额不足，无法为跨链池充值");
        }
    }
    
    // 如果跨链池BNB余额不足，从部署者转一些
    const minBNBRequired = ethers.parseEther("1.0"); // 至少需要1 BNB
    if (poolBNBBalance < minBNBRequired) {
        console.log("\n💰 跨链池BNB余额不足，正在转账...");
        
        const transferAmount = ethers.parseEther("2.0"); // 转2 BNB
        console.log("转账金额:", ethers.formatEther(transferAmount), "BNB");
        
        const transferTx = await deployer.sendTransaction({
            to: crossChainPool.address,
            value: transferAmount
        });
        await transferTx.wait();
        
        const newPoolBNBBalance = await ethers.provider.getBalance(crossChainPool.address);
        console.log("✅ BNB转账成功，跨链池新余额:", ethers.formatEther(newPoolBNBBalance), "BNB");
    }
    
    console.log("\n=== 5. 桥接连接状态总结 ===");
    console.log("🎉 BSC-PXA跨链桥连接配置完成!");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("📊 配置状态:");
    console.log("✅ PXA链支持: 已启用");
    console.log("✅ PAT代币支持: 已启用");
    console.log("✅ 验证者配置:", validators.length.toString(), "个验证者");
    console.log("✅ 费用配置: 已设置");
    console.log("✅ 跨链池资金: 已准备");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    console.log("\n🔧 下一步操作:");
    console.log("1. 🔄 切换到PXA链，启动PXA本地链");
    console.log("2. 🔄 在PXA链部署桥接接收合约");
    console.log("3. 🔄 在PXA链设置BSC桥接连接");
    console.log("4. 🔄 测试跨链功能");
    
    console.log("\n📋 PXA链操作命令:");
    console.log("cd ../pxa-chain/local-chain");
    console.log("./local-chain-setup.sh");
    console.log("cd ..");
    console.log("npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost");
    
    console.log("\n⚠️ 重要提示:");
    console.log("- 确保PXA链正常运行后再执行跨链操作");
    console.log("- 跨链需要PXA链验证者确认，通常需要几分钟");
    console.log("- 建议先用小额测试跨链功能");
    
    return {
        success: true,
        bridgeAddress: tokenBridgeAddress,
        pxaChainSupported: isPXASupported,
        validatorCount: validators.length.toString(),
        crossChainPoolAddress: crossChainPool.address,
        patTokenSupported: true
    };
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 桥接连接设置失败:", error);
            process.exit(1);
        });
}

module.exports = main;
