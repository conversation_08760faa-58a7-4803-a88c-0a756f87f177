// 检查账户余额
const { ethers } = require("hardhat");

async function main() {
    console.log("\n💰 检查账户余额");
    console.log("=====================================");
    
    const [deployer] = await ethers.getSigners();
    
    console.log("📝 部署者地址:", deployer.address);
    
    // 检查BNB余额
    const bnbBalance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 BNB余额:", ethers.formatEther(bnbBalance), "BNB");

    // 检查PAT余额
    const patAddress = "******************************************";
    const patToken = await ethers.getContractAt("PAToken", patAddress);
    const patBalance = await patToken.balanceOf(deployer.address);
    console.log("💰 PAT余额:", ethers.formatEther(patBalance), "PAT");
    
    if (bnbBalance.eq(0)) {
        console.log("\n❌ BNB余额为0，无法支付Gas费用");
        console.log("💡 解决方案:");
        console.log("1. 从BSC测试网水龙头获取BNB:");
        console.log("   https://testnet.binance.org/faucet-smart");
        console.log("2. 或从其他账户转入BNB");
    } else if (bnbBalance.lt(ethers.utils.parseEther("0.01"))) {
        console.log("\n⚠️ BNB余额较低，可能不足支付跨链费用");
        console.log("💡 建议获取更多BNB用于跨链测试");
    } else {
        console.log("\n✅ BNB余额充足");
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 脚本执行失败:", error);
        process.exit(1);
    });
