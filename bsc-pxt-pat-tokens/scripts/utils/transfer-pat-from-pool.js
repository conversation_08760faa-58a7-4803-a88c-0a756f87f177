// 从跨链池转PAT代币给部署者
const { ethers } = require("hardhat");
const hre = require("hardhat");

async function main() {
    console.log("\n💰 从跨链池转PAT代币给部署者");
    console.log("=====================================");
    
    const network = hre.network;
    console.log("🌐 当前网络:", network.name);
    
    // 合约地址
    const contractAddresses = {
        PAToken: "******************************************",
        crossChainPool: "******************************************"
    };
    
    // 跨链池私钥 (从部署记录中获取)
    const crossChainPoolPrivateKey = "0x23bf0c900bb5dbdda12aab13a9aadae85c8084ed93420445baa439033c15e23c";
    
    console.log("📝 合约地址:");
    console.log("- PAT代币:", contractAddresses.PAToken);
    console.log("- 跨链池:", contractAddresses.crossChainPool);
    
    // 创建跨链池钱包
    const provider = ethers.provider;
    const poolWallet = new ethers.Wallet(crossChainPoolPrivateKey, provider);
    const [deployer] = await ethers.getSigners();
    
    console.log("\n📝 账户信息:");
    console.log("- 部署者地址:", deployer.address);
    console.log("- 跨链池地址:", poolWallet.address);
    
    // 验证跨链池地址
    if (poolWallet.address.toLowerCase() !== contractAddresses.crossChainPool.toLowerCase()) {
        console.error("❌ 跨链池私钥不匹配!");
        console.error("期望地址:", contractAddresses.crossChainPool);
        console.error("实际地址:", poolWallet.address);
        process.exit(1);
    }
    
    // 连接PAT代币合约
    const patToken = await ethers.getContractAt("PAToken", contractAddresses.PAToken);
    
    console.log("\n💰 检查余额:");
    
    // 检查余额
    const poolBalance = await patToken.balanceOf(poolWallet.address);
    const deployerBalance = await patToken.balanceOf(deployer.address);
    
    console.log("- 跨链池PAT余额:", ethers.utils.formatEther(poolBalance), "PAT");
    console.log("- 部署者PAT余额:", ethers.utils.formatEther(deployerBalance), "PAT");
    
    if (poolBalance.eq(0)) {
        console.error("❌ 跨链池没有PAT代币!");
        process.exit(1);
    }
    
    // 转账金额 (1000 PAT)
    const transferAmount = ethers.utils.parseEther("1000");
    
    if (poolBalance.lt(transferAmount)) {
        console.log("⚠️ 跨链池余额不足1000 PAT，使用全部余额");
        transferAmount = poolBalance;
    }
    
    console.log("\n🚀 执行转账:");
    console.log("- 转账金额:", ethers.utils.formatEther(transferAmount), "PAT");
    console.log("- 从:", poolWallet.address);
    console.log("- 到:", deployer.address);
    
    try {
        // 使用跨链池钱包连接合约并转账
        const patTokenFromPool = patToken.connect(poolWallet);
        const transferTx = await patTokenFromPool.transfer(deployer.address, transferAmount);
        
        console.log("⏳ 等待交易确认...");
        const receipt = await transferTx.wait();
        
        console.log("✅ 转账成功!");
        console.log("- 交易哈希:", receipt.transactionHash);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 检查转账后余额
        const newPoolBalance = await patToken.balanceOf(poolWallet.address);
        const newDeployerBalance = await patToken.balanceOf(deployer.address);
        
        console.log("\n💰 转账后余额:");
        console.log("- 跨链池PAT余额:", ethers.utils.formatEther(newPoolBalance), "PAT");
        console.log("- 部署者PAT余额:", ethers.utils.formatEther(newDeployerBalance), "PAT");
        
        console.log("\n🎉 转账完成!");
        console.log("=====================================");
        console.log("✅ 部署者现在有PAT代币可用于跨链测试");
        console.log("✅ 可以运行跨链脚本:");
        console.log("   npx hardhat run scripts/cross-chain/bridge-to-pxa-simple.js --network bscTestnet");
        
    } catch (error) {
        console.error("❌ 转账失败:", error.message);
        process.exit(1);
    }
}

// 错误处理
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 脚本执行失败:", error);
        process.exit(1);
    });
