const fs = require("fs");
const path = require("path");
const { ethers } = require("hardhat");

/**
 * 地址管理工具 - 动态读取合约地址，避免硬编码
 */
class AddressManager {
    constructor(network = "localhost") {
        this.network = network;
        this.bscDeploymentDir = path.join(__dirname, "../../deployments", network);
        this.pxpacDeploymentDir = path.join(__dirname, "../../../pxpac-chain/deployments", network);
    }

    /**
     * 读取BSC链部署信息
     */
    getBSCDeployment() {
        try {
            // 核心代币部署信息
            const coreFile = path.join(this.bscDeploymentDir, "core-deployment.json");
            const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));

            // 跨链桥部署信息
            const bridgeFile = path.join(this.bscDeploymentDir, "bridge-deployment.json");
            const bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));

            return {
                core: coreDeployment,
                bridge: bridgeDeployment
            };
        } catch (error) {
            throw new Error(`无法读取BSC部署信息: ${error.message}`);
        }
    }

    /**
     * 读取PXPAC链部署信息
     */
    getPXPACDeployment() {
        try {
            // 从TestDev.md读取地址（修正路径）
            const testDevFile = path.join(__dirname, "../../../pxpac-chain/TestDev.md");
            const content = fs.readFileSync(testDevFile, 'utf8');

            // 解析合约地址
            const addresses = this.parseAddressesFromTestDev(content);
            
            // 尝试读取wPAT部署信息
            let wpatDeployment = null;
            try {
                const wpatFile = path.join(this.pxpacDeploymentDir, "wpat-deployment.json");
                wpatDeployment = JSON.parse(fs.readFileSync(wpatFile, 'utf8'));
            } catch (e) {
                console.log("⚠️  未找到wPAT部署文件，使用TestToken地址");
            }

            return {
                addresses,
                wpat: wpatDeployment
            };
        } catch (error) {
            throw new Error(`无法读取PXPAC部署信息: ${error.message}`);
        }
    }

    /**
     * 从TestDev.md解析合约地址
     */
    parseAddressesFromTestDev(content) {
        const addresses = {};

        // 正则表达式匹配合约地址（更新模式以匹配实际输出）
        const patterns = {
            BridgeReceiver: /BridgeReceiver部署完成:\s*(0x[a-fA-F0-9]{40})/g,
            ValidatorRegistry: /ValidatorRegistry部署完成:\s*(0x[a-fA-F0-9]{40})/g,
            TestToken: /TestToken部署完成:\s*(0x[a-fA-F0-9]{40})/g,
            TokenFactory: /TokenFactory部署完成:\s*(0x[a-fA-F0-9]{40})/g,
            GasFeeManager: /GasFeeManager部署完成:\s*(0x[a-fA-F0-9]{40})/g
        };

        for (const [name, pattern] of Object.entries(patterns)) {
            // 使用全局匹配找到最后一个（最新的）地址
            const matches = [...content.matchAll(pattern)];
            if (matches.length > 0) {
                // 取最后一个匹配（最新部署的）
                addresses[name] = matches[matches.length - 1][1];
            }
        }

        // 如果没有找到地址，尝试手动设置最新的地址
        if (Object.keys(addresses).length === 0) {
            console.log("⚠️  未能从TestDev.md解析地址，使用最新已知地址");
            addresses.BridgeReceiver = "0x873f92639B4aAf66Aa92b423aCD63dB23ea3FBf5";
            addresses.ValidatorRegistry = "0x6e1c841d81CF649Ed8BaDF2b20541F87DcF9483A";
            addresses.TestToken = "0xb9E6c48A9cf5852723B8CDe2ae27920EE04be60D";
            addresses.TokenFactory = "0xce7B15c700f9fC6652e6a54EE87892D588b48661";
            addresses.GasFeeManager = "0x8C6CF43Ce88e659D30Bed9db3a4a716B198c03Dd";
        }

        return addresses;
    }

    /**
     * 获取BSC链合约地址
     */
    getBSCAddresses() {
        const deployment = this.getBSCDeployment();
        
        return {
            // 核心代币
            PAToken: deployment.core.contracts.PAToken.address,
            PXToken: deployment.core.contracts.PXToken.address,
            TokenRegistry: deployment.core.contracts.TokenRegistry.address,
            
            // 跨链桥
            TokenBridge: deployment.bridge.contracts.TokenBridge.address,
            
            // 资金池
            pools: deployment.core.pools,
            
            // 跨链池地址
            crossChainPool: deployment.core.pools.crossChainPool.address,
            crossChainPoolPrivateKey: deployment.core.pools.crossChainPool.privateKey
        };
    }

    /**
     * 获取PXPAC链合约地址
     */
    getPXPACAddresses() {
        const deployment = this.getPXPACDeployment();
        
        return {
            BridgeReceiver: deployment.addresses.BridgeReceiver,
            ValidatorRegistry: deployment.addresses.ValidatorRegistry,
            TestToken: deployment.addresses.TestToken,
            TokenFactory: deployment.addresses.TokenFactory,
            GasFeeManager: deployment.addresses.GasFeeManager,
            
            // wPAT地址（如果已部署）
            wPAT: deployment.wpat?.contracts?.wPAT?.address || deployment.addresses.TestToken
        };
    }

    /**
     * 验证合约是否存在
     */
    async validateContract(address, chainName = "当前链") {
        try {
            const [signer] = await ethers.getSigners();
            const code = await signer.provider.getCode(address);
            if (code === "0x") {
                throw new Error(`合约不存在: ${address} (${chainName})`);
            }
            return true;
        } catch (error) {
            throw new Error(`验证合约失败: ${error.message}`);
        }
    }

    /**
     * 验证PXPAC链合约（跨链验证）
     */
    async validatePXPACContract(address, rpcUrl = "http://127.0.0.1:8545") {
        try {
            const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
            const code = await provider.getCode(address);
            if (code === "0x") {
                throw new Error(`PXPAC链合约不存在: ${address}`);
            }
            return true;
        } catch (error) {
            throw new Error(`验证PXPAC链合约失败: ${error.message}`);
        }
    }

    /**
     * 获取所有地址信息
     */
    getAllAddresses() {
        return {
            bsc: this.getBSCAddresses(),
            pxpac: this.getPXPACAddresses()
        };
    }

    /**
     * 打印地址信息
     */
    printAddresses() {
        const addresses = this.getAllAddresses();
        
        console.log("📋 合约地址信息");
        console.log("================================================");
        
        console.log("\n🔗 BSC链合约:");
        console.log("- PAT代币:", addresses.bsc.PAToken);
        console.log("- PXT代币:", addresses.bsc.PXToken);
        console.log("- TokenBridge:", addresses.bsc.TokenBridge);
        console.log("- 跨链池:", addresses.bsc.crossChainPool);
        
        console.log("\n🎯 PXPAC链合约:");
        console.log("- BridgeReceiver:", addresses.pxpac.BridgeReceiver);
        console.log("- ValidatorRegistry:", addresses.pxpac.ValidatorRegistry);
        console.log("- TestToken/wPAT:", addresses.pxpac.wPAT);
        
        return addresses;
    }

    /**
     * 同步地址到其他脚本（生成配置文件）
     */
    syncAddresses() {
        const addresses = this.getAllAddresses();
        
        // 生成配置文件
        const configFile = path.join(__dirname, "../config/addresses.json");
        const configDir = path.dirname(configFile);
        
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        
        const config = {
            network: this.network,
            timestamp: new Date().toISOString(),
            addresses
        };
        
        fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
        console.log("✅ 地址配置已同步:", configFile);
        
        return config;
    }
}

module.exports = AddressManager;
