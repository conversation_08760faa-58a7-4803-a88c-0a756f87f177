const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("💰 转账PAT代币到跨链池");
    console.log("================================================");
    console.log("网络:", network.name);

    const [deployer] = await ethers.getSigners();
    console.log("操作账户:", deployer.address);

    // 直接读取配置文件
    const configFile = path.join(__dirname, "../config/addresses.json");
    const addresses = JSON.parse(fs.readFileSync(configFile, 'utf8')).addresses;

    const crossChainPoolAddress = addresses.bsc.crossChainPool;
    const patTokenAddress = addresses.bsc.PAToken;
    
    console.log("跨链池地址:", crossChainPoolAddress);
    console.log("PAT代币地址:", patTokenAddress);
    
    // 获取PAT代币合约
    const PAToken = await ethers.getContractFactory("PAToken");
    const patoken = PAToken.attach(patTokenAddress);
    
    console.log("\n=== 1. 检查当前余额 ===");
    
    const deployerBalance = await patoken.balanceOf(deployer.address);
    const poolBalance = await patoken.balanceOf(crossChainPoolAddress);

    console.log("部署者PAT余额:", ethers.utils.formatEther(deployerBalance), "PAT");
    console.log("跨链池PAT余额:", ethers.utils.formatEther(poolBalance), "PAT");

    // 转账金额：50,000,000 PAT
    const transferAmount = ethers.utils.parseEther("50000000");

    console.log("计划转账:", ethers.utils.formatEther(transferAmount), "PAT");
    
    if (deployerBalance < transferAmount) {
        console.error("❌ 部署者PAT余额不足");
        console.log("需要:", ethers.utils.formatEther(transferAmount), "PAT");
        console.log("拥有:", ethers.utils.formatEther(deployerBalance), "PAT");
        process.exit(1);
    }

    console.log("\n=== 2. 执行转账 ===");

    try {
        console.log("正在转账", ethers.utils.formatEther(transferAmount), "PAT 到跨链池...");
        
        const transferTx = await patoken.transfer(crossChainPoolAddress, transferAmount);
        console.log("转账交易哈希:", transferTx.hash);
        
        console.log("等待交易确认...");
        const receipt = await transferTx.wait();
        console.log("✅ 转账成功！");
        console.log("区块号:", receipt.blockNumber);
        console.log("Gas使用:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.error("❌ 转账失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 3. 验证转账结果 ===");
    
    const newDeployerBalance = await patoken.balanceOf(deployer.address);
    const newPoolBalance = await patoken.balanceOf(crossChainPoolAddress);

    console.log("部署者新PAT余额:", ethers.utils.formatEther(newDeployerBalance), "PAT");
    console.log("跨链池新PAT余额:", ethers.utils.formatEther(newPoolBalance), "PAT");

    const balanceIncrease = newPoolBalance - poolBalance;
    console.log("跨链池余额增加:", ethers.utils.formatEther(balanceIncrease), "PAT");
    
    if (balanceIncrease === transferAmount) {
        console.log("✅ 转账金额正确");
    } else {
        console.log("❌ 转账金额不匹配");
    }
    
    console.log("\n💰 转账完成！");
    console.log("================================================");
    console.log("✅ 跨链池地址:", crossChainPoolAddress);
    console.log("✅ 当前PAT余额:", ethers.utils.formatEther(newPoolBalance), "PAT");
    console.log("✅ 现在可以进行跨链操作");
    
    console.log("\n🔧 下一步操作:");
    console.log("1. 检查BNB余额: npx hardhat run scripts/utils/fund-cross-chain-pool.js --network localhost");
    console.log("2. 授权TokenBridge: npx hardhat run scripts/utils/approve-bridge-from-pool.js --network localhost");
    console.log("3. 执行跨链: npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network localhost");
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("转账失败:", error);
            process.exit(1);
        });
}

module.exports = main;
