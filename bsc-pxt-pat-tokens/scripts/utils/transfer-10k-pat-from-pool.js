// 从跨链池转10,000 PAT代币给部署者用于跨链测试
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n💰 从跨链池转10,000 PAT代币给部署者");
    console.log("=====================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 部署者地址:", deployer.address);
    
    // 从部署记录加载地址和私钥
    let coreDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        console.log("✅ 已加载核心部署信息");
    } catch (error) {
        console.error("❌ 未找到核心部署文件");
        process.exit(1);
    }
    
    const patTokenAddress = coreDeployment.contracts.PAToken.address;
    const crossChainPoolAddress = coreDeployment.pools.crossChainPool.address;
    const crossChainPoolPrivateKey = coreDeployment.pools.crossChainPool.privateKey;
    
    console.log("📝 合约信息:");
    console.log("- PAT代币:", patTokenAddress);
    console.log("- 跨链池:", crossChainPoolAddress);
    
    // 创建跨链池钱包
    const provider = ethers.provider;
    const poolWallet = new ethers.Wallet(crossChainPoolPrivateKey, provider);
    
    console.log("- 跨链池钱包:", poolWallet.address);
    
    // 验证跨链池地址匹配
    if (poolWallet.address.toLowerCase() !== crossChainPoolAddress.toLowerCase()) {
        console.error("❌ 跨链池私钥不匹配!");
        console.error("期望地址:", crossChainPoolAddress);
        console.error("实际地址:", poolWallet.address);
        process.exit(1);
    }
    
    // 连接PAT代币合约
    const patToken = await ethers.getContractAt("PAToken", patTokenAddress);
    
    console.log("\n💰 检查余额:");
    
    // 检查余额
    const poolBalance = await patToken.balanceOf(poolWallet.address);
    const deployerBalance = await patToken.balanceOf(deployer.address);
    
    console.log("- 跨链池PAT余额:", ethers.formatEther(poolBalance), "PAT");
    console.log("- 部署者PAT余额:", ethers.formatEther(deployerBalance), "PAT");
    
    if (poolBalance === 0n) {
        console.error("❌ 跨链池没有PAT代币!");
        process.exit(1);
    }
    
    // 转账金额 (10,000 PAT)
    const transferAmount = ethers.parseEther("10000");
    
    if (poolBalance < transferAmount) {
        console.log("⚠️ 跨链池余额不足10,000 PAT");
        console.log("- 需要:", ethers.formatEther(transferAmount), "PAT");
        console.log("- 可用:", ethers.formatEther(poolBalance), "PAT");
        console.log("- 使用全部可用余额");
        transferAmount = poolBalance;
    }
    
    console.log("\n🚀 执行转账:");
    console.log("- 转账金额:", ethers.formatEther(transferAmount), "PAT");
    console.log("- 从:", poolWallet.address);
    console.log("- 到:", deployer.address);
    
    try {
        // 使用跨链池钱包连接合约并转账
        const patTokenFromPool = patToken.connect(poolWallet);
        const transferTx = await patTokenFromPool.transfer(deployer.address, transferAmount);
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", transferTx.hash);
        
        const receipt = await transferTx.wait();
        
        console.log("✅ 转账成功!");
        console.log("- 区块号:", receipt.blockNumber);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 检查转账后余额
        const newPoolBalance = await patToken.balanceOf(poolWallet.address);
        const newDeployerBalance = await patToken.balanceOf(deployer.address);
        
        console.log("\n💰 转账后余额:");
        console.log("- 跨链池PAT余额:", ethers.formatEther(newPoolBalance), "PAT");
        console.log("- 部署者PAT余额:", ethers.formatEther(newDeployerBalance), "PAT");
        
        console.log("\n🎉 转账完成!");
        console.log("=====================================");
        console.log("✅ 部署者现在有", ethers.formatEther(newDeployerBalance), "PAT");
        console.log("✅ 可以执行跨链操作:");
        console.log("   npx hardhat run scripts/bridge/bridge-10k-pat.js --network localhost");
        
        return {
            success: true,
            transferAmount: ethers.formatEther(transferAmount),
            newDeployerBalance: ethers.formatEther(newDeployerBalance),
            txHash: transferTx.hash
        };
        
    } catch (error) {
        console.error("❌ 转账失败:", error.message);
        process.exit(1);
    }
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}

module.exports = main;
