# BSC PXPAC代币系统完整部署指南

## 🎯 项目概述
**PXPAC代币系统**：基于BSC的双代币经济模型，支持原创内容上链、质押治理、跨链桥接等功能。

- **PXT代币**：治理代币，总量1亿，用于DAO投票和质押奖励
- **PAT代币**：功能代币，总量3亿，用于内容发布、奖励分配和跨链操作

## 📋 部署环境选择

### 🏠 本地开发环境
适用于：开发测试、功能验证、快速迭代

### 🌐 BSC测试网环境
适用于：生产前测试、公开演示、合约验证

---

# 🏠 本地环境部署指南

## 🚀 本地部署流程

### 第1步：清理环境（重新部署时）
```bash
# 清理旧数据
cd local-bsc-chain
./clean-bsc-chain.sh

# 清理编译缓存
cd ../
npx hardhat clean
rm -rf deployments/localhost
```

### 第2步：启动BSC本地链

#### 方式A：交互式启动（推荐学习）
```bash
cd local-bsc-chain
./bsc-chain-setup.sh
```

#### 方式B：后台启动（推荐开发）
```bash
cd local-bsc-chain
./start-background.sh
```

**自动完成功能**：
1. 🧹 清理旧数据
2. 🔑 生成验证者节点密钥
3. 📜 创建创世文件（包含预分配余额）
4. 🔥 启动所有验证者节点
5. 🧪 测试链状态和余额
6. 💰 显示MetaMask配置信息

### 第3步：验证链启动
```bash
# 等待10-15秒后测试
curl -X POST http://127.0.0.1:18485 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'

# 应该返回类似: {"jsonrpc":"2.0","id":1,"result":"0x1"}
```

### 第4步：编译合约
```bash
cd ../
npx hardhat clean
npx hardhat compile
```

## 📋 本地环境核心合约部署（按顺序执行）

### 第5步：部署核心代币系统
```bash
# 部署核心代币系统（PAT + PXT + 国库）
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost
```

### 第6步：部署质押系统
```bash
# 部署质押奖励系统
npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost
```

### 第7步：部署治理系统
```bash
# 部署治理投票系统
npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost
```

### 第8步：部署内容上链系统
```bash
# 部署内容系统（ContentRegistry + ContentCharacter + ContentMint）
npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost
```

### 第9步：部署跨链桥系统
```bash
# 部署TokenBridge
npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost
```

### 第10步：配置跨链桥连接
```bash
# 配置跨链桥连接到PXPAC链
npx hardhat run scripts/bridge/setup-bridge-connection.js --network localhost
```

## 🧪 本地环境功能测试

### 第11步：基础功能测试
```bash
# 测试基础代币功能
npx hardhat run scripts/test/01-basic-function-test.js --network localhost
```

### 第12步：质押系统测试
```bash
# 测试质押奖励系统
npx hardhat run scripts/test/02-reward-system-test.js --network localhost
```

### 第13步：治理系统测试
```bash
# 测试治理投票功能
npx hardhat run scripts/test/03-governance-test.js --network localhost
npx hardhat run scripts/test/03-governance-system-test.js --network localhost
```

### 第14步：测试原创内容IPFS上链
```bash
# 测试真实IPFS原创内容上链（推荐）
node scripts/standalone/bsc-ipfs-upload.js

# 或使用hardhat版本（如果依赖正常）
npx hardhat run scripts/test/04-real-ipfs-content-upload.js --network localhost
```

### 第15步：测试跨链桥功能
```bash
# 测试跨链桥基础功能
npx hardhat run scripts/test/05-bridge-test.js --network localhost

# 检查跨链池状态
npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost

# 执行跨链操作
npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network localhost
```

### 第16步：测试内容铸造NFT
```bash
# 测试内容铸造（90/10分成）
npx hardhat run scripts/test/06-content-mint-test.js --network localhost
```

### 第17步：验证PAT分配
```bash
# 验证PAT代币分配情况
npx hardhat run scripts/utils/verify-pat-allocation.js --network localhost
```

---

# 🌐 BSC测试网部署指南

## 🎯 BSC测试网部署目标
基于成功的本地部署，将PXPAC代币系统部署到BSC测试网进行生产前测试。

## � BSC测试网部署前准备

### 1. 检查网络连接
```bash
# 测试BSC测试网连接
curl -X POST https://data-seed-prebsc-1-s1.binance.org:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'
```

### 2. 检查账户余额
```bash
# 检查BSC测试网账户余额
node scripts/test/01-basic-function-test-safe.js
```

**最低余额要求**：
- **部署者账户**: 0.5 tBNB
- **国库账户**: 0.1 tBNB
- **操作员账户**: 0.1 tBNB

### 3. 获取测试网BNB
- 访问: https://testnet.binance.org/faucet-smart
- 每24小时领取 0.1 tBNB
- 建议为部署者多领取几次

## 🚀 BSC测试网手动部署流程

### 第1步：编译合约
```bash
# 清理和编译
npx hardhat clean
npx hardhat compile
```

### 第2步：部署核心代币系统
```bash
# 部署PXT、PAT代币和注册表
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network bscTestnet
```

**会得到**：
- PXT代币地址
- PAT代币地址
- 代币注册表地址
- PAT自动分配到4个池子（中国大陆池、国际池、质押池、跨链池）

### 第3步：部署质押系统
```bash
# 部署质押相关合约
npx hardhat run scripts/deploy/02-deploy-staking-system.js --network bscTestnet
```

**会得到**：
- 质押工厂地址
- 质押池地址
- 奖励分配器地址

### 第4步：部署治理系统
```bash
# 部署DAO治理合约
npx hardhat run scripts/deploy/03-deploy-governance.js --network bscTestnet
```

**会得到**：
- DAO主合约地址
- 提案管理器地址
- 投票合约地址
- 国库合约地址

### 第5步：部署内容系统
```bash
# 部署内容上链合约
npx hardhat run scripts/deploy/04-deploy-content-system.js --network bscTestnet
```

**会得到**：
- ContentRegistry地址（内容注册）
- ContentCharacter地址（用户角色NFT）
- ContentMint地址（内容NFT铸造）

### 第6步：部署跨链桥
```bash
# 部署跨链桥合约
npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network bscTestnet
```

**会得到**：
- TokenBridge地址
- 自动配置PXPAC链支持

## 🧪 BSC测试网功能测试（可选）

### 第7步：测试基础功能
```bash
# 测试代币转账、授权（安全版本，避免RPC限制）
npx hardhat run scripts/test/01-basic-function-test-safe.js --network bscTestnet

# 或使用原版本（可能遇到RPC限制）
npx hardhat run scripts/test/01-basic-function-test.js --network bscTestnet
```

### 第8步：测试质押功能
```bash
# 测试质押和奖励
npx hardhat run scripts/test/02-reward-system-test.js --network bscTestnet
```

### 第9步：测试治理功能
```bash
# 测试提案和投票
npx hardhat run scripts/test/03-governance-test.js --network bscTestnet
```

### 第10步：测试内容上链
```bash
# 方法1：使用独立脚本测试内容注册（推荐）
node scripts/test/content-registry-test.js

# 方法2：测试Character创建
node scripts/test/simple-character-test.js

# 方法3：系统状态总结
node scripts/test/final-test-summary.js

# 方法4：原始IPFS上链测试（可能遇到依赖问题）
npx hardhat run scripts/test/bsc-ipfs-content-upload.js --network bscTestnet

# 方法5：如果需要IPFS上传，先检查Pinata配置
node scripts/test/standalone-ipfs-test.js
```

**注意事项**：
- 如果遇到 `ERR_REQUIRE_ESM` 错误，使用方法1-3的独立脚本
- 确保 `.env` 文件中配置了有效的 `PINATA_JWT`
- 测试账户需要有足够的 BNB 支付 gas 费用（至少 0.01 BNB）
- 测试账户需要有足够的 PAT 代币支付内容注册费用（至少 5 PAT）

### 第11步：测试跨链桥
```bash
# 方法1：使用修复版跨链桥测试（推荐）
node scripts/test/05-bridge-test-fixed.js

# 方法2：如果跨链池BNB余额不足，先充值
node scripts/test/fund-bridge-accounts.js

# 方法3：原始跨链桥测试（可能遇到配置问题）
npx hardhat run scripts/test/05-bridge-test.js --network bscTestnet
```

**注意事项**：
- 跨链池需要有足够的 BNB 支付跨链费用（至少 0.15 BNB）
- 跨链池需要有足够的 PAT 代币进行锁定（至少 100 PAT）
- 确保 PXPAC 链已配置并且验证者正常运行
- 跨链费用 = 基础费用(0.001 BNB) + 百分比费用(0.5%) + 验证者费用(0.1 BNB)

### 第12步：测试NFT铸造
```bash
# 测试内容NFT铸造
npx hardhat run scripts/test/06-content-mint-test.js --network bscTestnet
```

### 第13步：查看PAT增发统计
```bash
# 查看PAT代币增发统计和经济模型验证
npx hardhat run scripts/query/pat-inflation-stats.js --network bscTestnet
```

## 🔍 BSC测试网合约验证

### 第14步：验证合约源码
部署完成后，在BSCScan测试网上验证合约：

```bash
# 验证示例（替换为实际地址）
npx hardhat verify --network bscTestnet <PXT地址> "Platform Governance Token" "PXT" 18 "100000000000000000000000000"
npx hardhat verify --network bscTestnet <PAT地址> "PX Activity Token" "PAT" 18 "300000000000000000000000000"
# ... 其他合约
```

**注意**：需要在 `.env` 中设置 `BSCSCAN_API_KEY`

---

# 📊 系统配置信息

## 🌐 网络参数

### 本地开发环境
- **链ID**: 97
- **RPC URL**: http://127.0.0.1:18485
- **Gas价格**: 20 gwei
- **区块时间**: 3秒

### BSC测试网环境
- **链ID**: 97
- **RPC URL**: https://data-seed-prebsc-1-s1.binance.org:8545
- **区块浏览器**: https://testnet.bscscan.com
- **Gas价格**: 20 gwei
- **Gas限制**: 8,000,000

### 备用RPC节点
```
https://data-seed-prebsc-2-s1.binance.org:8545
https://data-seed-prebsc-1-s2.binance.org:8545
https://data-seed-prebsc-2-s2.binance.org:8545
```

## 💰 代币经济模型

### PXT代币（治理代币）
- **总量**: 1亿PXT
- **用途**: DAO治理投票、质押奖励
- **分配**: 部署时全部分配给部署者

### PAT代币（功能代币）
- **总量**: 3亿PAT
- **用途**: 内容发布费用、奖励分配、跨链操作

### PAT分配方案
- 🇨🇳 **中国大陆池**：1亿PAT
- 🌍 **国际池**：1亿PAT
- 🏦 **质押池**：0PAT（通过PXT质押增发）
- 🌉 **跨链池**：1亿PAT

### 验证PAT分配
```bash
# 本地环境
npx hardhat run scripts/utils/verify-pat-allocation.js --network localhost

# BSC测试网
npx hardhat run scripts/utils/verify-pat-allocation.js --network bscTestnet
```

### PAT增发机制
- **增发限制**: 季度0.5%，年化2%
- **增发条件**: 只能通过质押奖励增发
- **销毁机制**: 内容发布费用的10%销毁

## 🎨 内容上链配置

### 内容类型费用
- **video**: 1.0 PAT
- **article**: 0.05 PAT
- **其他类型**: 根据配置

### 收益分配
- **创作者**: 90%
- **平台**: 10%

### IPFS内容元数据结构
```json
{
  "name": "原创作品标题",
  "description": "详细描述",
  "contentType": "original",
  "originalityProof": {
    "author": "0x...",
    "createdAt": "2025-07-31T...",
    "signature": "original_content_signature_..."
  },
  "license": "All Rights Reserved",
  "copyright": "© 2025 作者"
}
```

## 🌉 跨链桥配置

### 跨链费用结构
- **基础费用**: 0.001 BNB
- **百分比费用**: 0.5%
- **验证者费用**: 0.1 BNB
- **总费用**: 基础费用 + 百分比费用 + 验证者费用

### 支持的链
- **源链**: BSC (链ID: 97)
- **目标链**: PXPAC (链ID: 待配置)

---

# 🔧 管理和维护命令

## 🏠 本地环境管理

### 激活随机奖励系统
```bash
# 激活质押奖励系统
npx hardhat run scripts/admin/01-activate-random-rewards.js --network localhost
```

### 其他管理功能
```bash
# 管理治理提案
npx hardhat run scripts/admin/manage-governance-proposals.js --network localhost

# 管理质押池
npx hardhat run scripts/admin/manage-staking-pools.js --network localhost
```

### 状态查询和调试
```bash
# 查看TokenBridge状态
npx hardhat run scripts/query/bridge-status.js --network localhost

# 查看跨链池余额
npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost

# 检查PAT余额
npx hardhat run scripts/debug/check-pat-balance.js --network localhost

# 调试内容注册
npx hardhat run scripts/debug/debug-content-registration.js --network localhost
```

### 代币分发工具
```bash
# 分发测试代币
npx hardhat run scripts/utils/distribute-tokens.js --network localhost

# 验证PAT分配情况
npx hardhat run scripts/utils/verify-pat-allocation.js --network localhost
```

### 跨链桥操作
```bash
# 执行PAT跨链到PXPAC链
npx hardhat run scripts/bridge/bridge-pat-tokens.js --network localhost
```

## 🌐 BSC测试网管理

### 部署后统计查询
```bash
# 查看完整的PAT增发统计
npx hardhat run scripts/query/pat-inflation-stats.js --network bscTestnet
```

**统计内容包括：**
- 📈 总增发量、总销毁量、净增发量
- 💰 各池子余额分配验证
- 🔒 增发权限检查
- 📊 增发限制验证（季度0.5%，年化2%）
- ✅ 经济模型完整性验证

### 其他有用的查询命令
```bash
# 检查账户余额
node scripts/test/check-balances.js

# 给账户充值BNB（从部署者转账）
node scripts/test/fund-accounts.js

# 查看系统完整状态
node scripts/test/final-test-summary.js

# 查看质押状态
npx hardhat run scripts/admin/check-staking-status.js --network bscTestnet

# 查看治理状态
npx hardhat run scripts/admin/check-governance-status.js --network bscTestnet

# 查看内容统计
npx hardhat run scripts/query/content-stats.js --network bscTestnet
```

### 内容上链专用工具
```bash
# 检查所有账户BNB余额
node scripts/test/check-balances.js

# 测试Character创建功能
node scripts/test/simple-character-test.js

# 测试内容注册功能（无IPFS）
node scripts/test/content-registry-test.js

# 测试IPFS上传功能（需要有效的Pinata JWT）
node scripts/test/standalone-ipfs-test.js

# 查看系统完整状态和测试结果
node scripts/test/final-test-summary.js
```

### 跨链桥专用工具
```bash
# 给跨链池充值BNB（从部署者转账）
node scripts/test/fund-bridge-accounts.js

# 测试跨链桥功能（BSC → PXPAC）
node scripts/test/05-bridge-test-fixed.js

# 检查跨链桥状态和配置
# （需要创建专门的状态检查脚本）
```
---

# 🔧 故障排除

## 🏠 本地环境常见问题

### 1. 链启动问题
```bash
# 问题：链无法启动或连接失败
# 解决：重新清理和启动
cd local-bsc-chain
./clean-bsc-chain.sh
./start-background.sh

# 等待10-15秒后测试连接
curl -X POST http://127.0.0.1:18485 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'
```

### 2. 依赖冲突问题
```bash
# 问题：ERR_REQUIRE_ESM 错误
# 解决：使用独立脚本
node scripts/standalone/bsc-ipfs-upload.js

# 或使用内容注册测试
node scripts/test/content-registry-test.js
```

### 3. 余额不足问题
```bash
# 问题：INSUFFICIENT_FUNDS 错误
# 解决：检查跨链池PAT余额
npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost

# 分发测试代币
npx hardhat run scripts/utils/distribute-tokens.js --network localhost
```

### 4. Gas估算失败
```bash
# 问题：Gas estimation failed
# 解决：检查链是否正常运行
curl -X POST http://127.0.0.1:18485 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'

# 重新启动链
cd local-bsc-chain
./clean-bsc-chain.sh
./start-background.sh
```

### 5. IPFS上传失败
```bash
# 问题：IPFS upload failed
# 解决：检查PINATA_JWT配置
node scripts/test/standalone-ipfs-test.js

# 或使用模拟哈希测试
node scripts/test/content-registry-test.js
```

### 完全重新部署
```bash
# 完全重新开始
cd local-bsc-chain
./clean-bsc-chain.sh
cd ../
npx hardhat clean
rm -rf deployments/localhost

# 然后从第2步重新开始
```

## 🌐 BSC测试网常见问题

### 1. 网络连接问题
```bash
# 问题：网络超时或连接失败
# 解决：切换到备用RPC节点
# 在 hardhat.config.js 中更换 RPC URL：
# https://data-seed-prebsc-2-s1.binance.org:8545
# https://data-seed-prebsc-1-s2.binance.org:8545
```

### 2. Gas估算失败
```bash
# 问题：Gas estimation failed
# 解决：增加gasLimit或降低gasPrice
# 在部署脚本中添加：
# gasLimit: 8000000,
# gasPrice: ethers.utils.parseUnits('20', 'gwei')
```

### 3. 余额不足问题
```bash
# 问题：余额不足
# 解决：从水龙头获取更多tBNB
# 访问: https://testnet.binance.org/faucet-smart

# 给测试账户充值 BNB
node scripts/test/fund-accounts.js

# 检查账户余额
node scripts/test/check-balances.js
```

### 4. 合约验证失败
```bash
# 问题：合约验证失败
# 解决：检查构造函数参数是否正确
# 确保 .env 中设置了 BSCSCAN_API_KEY

# 验证示例
npx hardhat verify --network bscTestnet <合约地址> <构造函数参数>
```

### 5. 内容上链测试问题
```bash
# ERR_REQUIRE_ESM 错误
# 解决：使用独立脚本
node scripts/test/content-registry-test.js

# INSUFFICIENT_FUNDS 错误
# 解决：给测试账户充值
node scripts/test/fund-accounts.js

# API_KEY_REVOKED 错误
# 解决：更新 .env 中的 PINATA_JWT

# Character创建失败
# 解决：检查账户是否已有Character，或使用不同账户
```

### 6. 跨链桥测试问题
```bash
# INSUFFICIENT_FUNDS 错误
# 解决：给跨链池充值 BNB
node scripts/test/fund-bridge-accounts.js

# unsupported chain 错误
# 解决：检查 PXPAC 链是否已配置

# insufficient allowance 错误
# 解决：检查 PAT 代币授权，重新运行授权步骤

# 余额变化异常
# 解决：等待更长时间让区块链状态更新
```

---

# 🌟 环境对比

## 本地环境 vs BSC测试网

| 项目 | 本地环境 | BSC测试网 |
|------|----------|-----------|
| **网络** | 本地BSC链 | BSC测试网 |
| **Gas费用** | 免费 | 需要tBNB |
| **区块确认** | 即时 | 3秒 |
| **合约验证** | 不需要 | 需要API密钥 |
| **IPFS上传** | 真实 | 真实 |
| **功能完整性** | 100% | 100% |
| **适用场景** | 开发测试 | 生产前测试 |
| **网络稳定性** | 依赖本地 | 依赖公网 |
| **调试便利性** | 高 | 中 |

## BSC链 vs PXPAC链

| 功能 | BSC链 | PXPAC链 |
|------|-------|---------|
| **定位** | 原创内容 | 分享内容 |
| **权威性** | 高（大链） | 中（侧链） |
| **Gas费用** | 较高（BNB） | 较低（PXPAC） |
| **内容保护** | 版权保护 | 传播友好 |
| **适用场景** | 原创发布 | 内容分享 |
| **收益分配** | 90/10 | 90/10 |

---

# 📝 部署记录模板

## 🏠 本地环境部署记录

部署完成后，记录以下信息到 `BSCDev.md`：

```markdown
# BSC本地环境部署记录

## 部署信息
- 部署时间: 2025-07-31
- 网络: 本地BSC链 (链ID: 97)
- RPC URL: http://127.0.0.1:18485
- 部署者: 0x...

## 合约地址
- PXT代币: 0x...
- PAT代币: 0x...
- 代币注册表: 0x...
- 质押工厂: 0x...
- 质押池: 0x...
- 奖励分配器: 0x...
- DAO合约: 0x...
- 提案管理器: 0x...
- 投票合约: 0x...
- 国库合约: 0x...
- ContentRegistry: 0x...
- ContentCharacter: 0x...
- ContentMint: 0x...
- TokenBridge: 0x...

## 测试结果
- [x] 基础功能测试
- [x] 质押系统测试
- [x] 治理系统测试
- [x] 原创内容上链测试
- [x] 跨链桥测试
- [x] 内容铸造测试
- [x] PAT分配验证

## 重要配置
- 收益分配: 90/10
- 内容费用: video=1.0 PAT, article=0.05 PAT
- 跨链池余额: X PAT
- IPFS哈希: Qm...
```

## 🌐 BSC测试网部署记录

部署完成后，记录以下信息：

```markdown
# BSC测试网部署记录

## 部署信息
- 部署时间: 2025-07-31
- 网络: BSC测试网 (链ID: 97)
- 部署者: 0x...

## 合约地址
- PXT代币: 0x...
- PAT代币: 0x...
- 代币注册表: 0x...
- 质押工厂: 0x...
- 质押池: 0x...
- 奖励分配器: 0x...
- DAO合约: 0x...
- 提案管理器: 0x...
- 投票合约: 0x...
- 国库合约: 0x...
- ContentRegistry: 0x...
- ContentCharacter: 0x...
- ContentMint: 0x...
- TokenBridge: 0x...

## 测试结果
- [ ] 基础功能测试
- [ ] 质押系统测试
- [ ] 治理系统测试
- [x] 内容上链测试
  - [x] Character创建测试
  - [x] 内容注册测试
  - [ ] IPFS上传测试（可选）
  - [x] 系统状态验证
- [x] 跨链桥测试
  - [x] BSC → PXPAC 跨链锁定
  - [x] PAT代币锁定验证
  - [x] 跨链费用支付
  - [x] 验证者配置检查
  - [ ] PXPAC链接收验证（需要PXPAC链环境）
- [ ] 内容铸造测试
- [ ] PAT增发统计查询

## PAT经济模型验证
- [ ] 中国大陆池：1亿PAT
- [ ] 国际池：1亿PAT
- [ ] 跨链池：1亿PAT
- [ ] 质押池：0PAT（通过增发获得）
- [ ] 总供应量：3亿PAT
- [ ] 增发限制：季度0.5%，年化2%

## 验证状态
- [ ] 所有合约已在BSCScan验证
```

---

# 🎉 总结

## ✅ 完整功能清单

### 核心功能
- ✅ **双代币系统**: PXT治理代币 + PAT功能代币
- ✅ **质押奖励**: PXT质押获得PAT奖励
- ✅ **DAO治理**: 提案投票系统
- ✅ **内容上链**: IPFS + 区块链双重存储
- ✅ **跨链桥接**: BSC ↔ PXPAC 代币转移
- ✅ **NFT铸造**: 内容NFT化，90/10收益分配

### 经济模型
- ✅ **PAT分配**: 4个池子合理分配
- ✅ **增发控制**: 季度0.5%，年化2%限制
- ✅ **销毁机制**: 内容费用10%销毁
- ✅ **收益分配**: 创作者90%，平台10%

### 部署环境
- ✅ **本地开发**: 快速迭代，免费测试
- ✅ **BSC测试网**: 生产前验证，真实环境

## 🚀 快速开始

### 本地开发
```bash
# 1. 启动本地链
cd local-bsc-chain && ./start-background.sh

# 2. 部署合约
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost
# ... 继续其他部署步骤

# 3. 测试功能
npx hardhat run scripts/test/01-basic-function-test.js --network localhost
```

### BSC测试网
```bash
# 1. 获取测试币
# 访问: https://testnet.binance.org/faucet-smart

# 2. 部署合约
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network bscTestnet
# ... 继续其他部署步骤

# 3. 验证合约
npx hardhat verify --network bscTestnet <合约地址> <参数>
```

这样您就拥有了完整的BSC PXPAC代币系统部署和管理指南！🎉