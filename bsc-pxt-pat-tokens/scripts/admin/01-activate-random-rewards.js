const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🎲 激活随机奖励系统");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("操作账户:", deployer.address);
    
    // 加载部署信息
    let deploymentInfo;
    try {
        const stakingFile = path.join(__dirname, "../../deployments", network.name, "staking-deployment.json");
        deploymentInfo = JSON.parse(fs.readFileSync(stakingFile, 'utf8'));
        console.log("✅ 已加载质押系统部署信息");
    } catch (error) {
        console.error("❌ 未找到质押系统部署信息，请先运行质押系统部署脚本");
        process.exit(1);
    }
    
    // 获取奖励计算器合约
    const RewardCalculator = await ethers.getContractFactory("RewardCalculator");
    const rewardCalculator = RewardCalculator.attach(deploymentInfo.contracts.RewardCalculator.address);
    
    console.log("奖励计算器地址:", rewardCalculator.address);
    
    console.log("\n=== 1. 检查当前状态 ===");
    
    const currentStatus = await rewardCalculator.randomRewardEnabled();
    console.log("当前随机奖励状态:", currentStatus ? "✅ 已启用" : "❌ 未启用");
    
    if (currentStatus) {
        console.log("✅ 随机奖励系统已经启用，无需重复激活");
        
        // 显示当前配置
        console.log("\n=== 当前随机奖励配置 ===");
        for (let level = 1; level <= 7; level++) {
            try {
                const levelConfig = await rewardCalculator.getLevelConfig(level);
                const levelNames = ["", "丁级", "丙级", "乙级", "甲级", "十绝", "双十绝", "至尊"];
                
                console.log(`${levelNames[level]} (等级${level}):`);
                console.log("  固定倍数:", levelConfig.multiplier.toString() / 100, "x");
                console.log("  随机范围:", levelConfig.randomMin.toString() / 100, "x -", levelConfig.randomMax.toString() / 100, "x");
            } catch (error) {
                console.log(`等级${level}配置获取失败:`, error.message);
            }
        }
        return;
    }
    
    console.log("\n=== 2. 激活随机奖励系统 ===");
    
    try {
        const tx = await rewardCalculator.enableRandomReward();
        await tx.wait();
        console.log("✅ 随机奖励系统激活成功");
        console.log("交易哈希:", tx.hash);
    } catch (error) {
        console.error("❌ 激活失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 3. 验证激活结果 ===");
    
    const newStatus = await rewardCalculator.randomRewardEnabled();
    console.log("新的随机奖励状态:", newStatus ? "✅ 已启用" : "❌ 未启用");
    
    if (newStatus) {
        console.log("🎉 随机奖励系统激活成功！");
        
        // 显示随机奖励配置
        console.log("\n=== 随机奖励配置详情 ===");
        const levelNames = ["", "丁级", "丙级", "乙级", "甲级", "十绝", "双十绝", "至尊"];
        
        for (let level = 1; level <= 7; level++) {
            try {
                const levelConfig = await rewardCalculator.getLevelConfig(level);
                
                console.log(`\n${levelNames[level]} (等级${level}):`);
                console.log("  最小质押:", ethers.utils.formatEther(levelConfig.minAmount), "PXT");
                console.log("  固定倍数:", levelConfig.multiplier.toString() / 100, "x");
                console.log("  随机范围:", levelConfig.randomMin.toString() / 100, "x -", levelConfig.randomMax.toString() / 100, "x");
                
                // 计算随机奖励的额外收益
                const extraMin = (levelConfig.randomMin.toNumber() - levelConfig.multiplier.toNumber()) / 100;
                const extraMax = (levelConfig.randomMax.toNumber() - levelConfig.multiplier.toNumber()) / 100;
                
                if (extraMax > 0) {
                    console.log("  额外收益:", extraMin.toFixed(2) + "x -", extraMax.toFixed(2) + "x");
                }
            } catch (error) {
                console.log(`等级${level}配置获取失败:`, error.message);
            }
        }
        
        console.log("\n=== 随机奖励机制说明 ===");
        console.log("🎲 每次领取奖励时，系统会在随机范围内生成一个倍数");
        console.log("📈 倍数越高的等级，随机奖励的潜在收益越大");
        console.log("🔒 随机奖励基于质押等级和时间计算");
        console.log("⚡ 随机奖励会在每次claim时重新计算");
        
    } else {
        console.error("❌ 随机奖励系统激活失败");
        process.exit(1);
    }
    
    // 保存激活记录
    const activationRecord = {
        network: network.name,
        timestamp: new Date().toISOString(),
        operator: deployer.address,
        rewardCalculatorAddress: rewardCalculator.address,
        activationStatus: newStatus,
        transactionHash: tx?.hash || null
    };
    
    const recordDir = path.join(__dirname, "../../admin-records");
    if (!fs.existsSync(recordDir)) {
        fs.mkdirSync(recordDir, { recursive: true });
    }
    
    const recordFile = path.join(recordDir, `random-reward-activation-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(recordFile, JSON.stringify(activationRecord, null, 2));
    
    console.log("\n📄 激活记录已保存:", recordFile);
    console.log("\n🔧 下一步:");
    console.log("1. 测试随机奖励: npx hardhat run scripts/test/02-reward-system-test.js --network", network.name);
    console.log("2. 调整奖励参数: npx hardhat run scripts/admin/02-adjust-reward-ranges.js --network", network.name);
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("激活失败:", error);
            process.exit(1);
        });
}

module.exports = main;
