const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔍 查看BSC链TokenBridge状态");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("查询账户:", deployer.address);
    
    console.log("\n=== 1. 加载TokenBridge地址 ===");
    
    // 加载TokenBridge部署信息
    let bridgeDeployment;
    try {
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载TokenBridge部署信息");
        console.log("TokenBridge地址:", bridgeDeployment.contracts.TokenBridge.address);
    } catch (error) {
        console.error("❌ 未找到TokenBridge部署文件:", error.message);
        console.log("请先部署TokenBridge:");
        console.log("npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost");
        process.exit(1);
    }
    
    console.log("\n=== 2. 连接TokenBridge合约 ===");
    
    let tokenBridge;
    try {
        const TokenBridge = await ethers.getContractFactory("TokenBridge");
        tokenBridge = TokenBridge.attach(bridgeDeployment.contracts.TokenBridge.address);
        
        // 测试合约连接
        const owner = await tokenBridge.owner();
        console.log("✅ TokenBridge合约连接成功");
        console.log("合约Owner:", owner);
    } catch (error) {
        console.error("❌ TokenBridge合约连接失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 3. 检查权限状态 ===");
    
    try {
        // 检查管理员状态
        const isAdmin = await tokenBridge.administrators(deployer.address);
        console.log("部署者管理员权限:", isAdmin ? "✅" : "❌");
        
        // 检查验证者状态
        const isValidator = await tokenBridge.bridgeValidators(deployer.address);
        console.log("部署者验证者权限:", isValidator ? "✅" : "❌");
        
    } catch (error) {
        console.error("❌ 权限检查失败:", error.message);
    }
    
    console.log("\n=== 4. 检查PXPAC链配置 ===");
    
    const PXPAC_CHAIN_ID = 9;
    
    try {
        // 检查链支持状态
        const isPxpacSupported = await tokenBridge.supportedChainIds(PXPAC_CHAIN_ID);
        console.log("PXPAC链支持状态:", isPxpacSupported ? "✅ 支持" : "❌ 不支持");
        
        if (isPxpacSupported) {
            // 获取链费用配置
            const chainFee = await tokenBridge.chainFees(PXPAC_CHAIN_ID);
            console.log("PXPAC链费用配置:");
            console.log("- 基础费用:", ethers.utils.formatEther(chainFee.baseFee), "BNB");
            console.log("- 百分比费用:", (Number(chainFee.percentFee) / 100).toFixed(2), "%");
            console.log("- 最小费用:", ethers.utils.formatEther(chainFee.minFee), "BNB");
            console.log("- 最大费用:", ethers.utils.formatEther(chainFee.maxFee), "BNB");
            console.log("- 最后更新:", new Date(Number(chainFee.lastUpdated) * 1000).toLocaleString());

            // 获取需要确认数
            const requiredConfirmations = await tokenBridge.requiredConfirmations(PXPAC_CHAIN_ID);
            console.log("- 需要确认数:", requiredConfirmations.toString());

            // 获取验证者列表
            try {
                const validators = await tokenBridge.getChainValidators(PXPAC_CHAIN_ID);
                console.log("- 验证者数量:", validators.length);
                if (validators.length > 0) {
                    console.log("- 验证者列表:", validators.slice(0, 3).join(", ") + (validators.length > 3 ? "..." : ""));
                }
            } catch (error) {
                console.log("- 验证者列表: 查询失败");
            }
        }
        
    } catch (error) {
        console.error("❌ PXPAC链配置检查失败:", error.message);
    }
    
    console.log("\n=== 5. 检查跨链池状态 ===");
    
    try {
        // 加载地址配置
        const addressFile = path.join(__dirname, "../config/addresses.json");
        const addresses = JSON.parse(fs.readFileSync(addressFile, 'utf8'));
        
        const crossChainPoolAddress = addresses.addresses.bsc.crossChainPool;
        console.log("跨链池地址:", crossChainPoolAddress);
        
        // 检查跨链池BNB余额
        const poolBNBBalance = await ethers.provider.getBalance(crossChainPoolAddress);
        console.log("跨链池BNB余额:", ethers.utils.formatEther(poolBNBBalance), "BNB");
        
        // 检查跨链池PAT余额
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        const patTokenAddress = coreDeployment.contracts.PAToken.address;
        
        const PAToken = await ethers.getContractFactory("PAToken");
        const patToken = PAToken.attach(patTokenAddress);
        
        const poolPATBalance = await patToken.balanceOf(crossChainPoolAddress);
        console.log("跨链池PAT余额:", ethers.utils.formatEther(poolPATBalance), "PAT");
        
    } catch (error) {
        console.error("❌ 跨链池状态检查失败:", error.message);
    }
    
    console.log("\n=== 6. 检查合约统计 ===");
    
    try {
        // 检查跨链统计
        const lockedPXT = await tokenBridge.lockedPXT();
        const lockedPAT = await tokenBridge.lockedPAT();
        const requestCount = await tokenBridge.requestCount();

        console.log("跨链统计:");
        console.log("- 锁定的PXT:", ethers.utils.formatEther(lockedPXT), "PXT");
        console.log("- 锁定的PAT:", ethers.utils.formatEther(lockedPAT), "PAT");
        console.log("- 跨链请求数量:", requestCount.toString());

    } catch (error) {
        console.error("❌ 合约统计查询失败:", error.message);
    }
    
    console.log("\n📊 TokenBridge状态检查完成!");
    console.log("================================================");
    
    // 生成状态摘要
    try {
        const isPxpacSupported = await tokenBridge.supportedChainIds(PXPAC_CHAIN_ID);
        const isAdmin = await tokenBridge.administrators(deployer.address);
        
        console.log("✅ 状态摘要:");
        console.log(`- TokenBridge地址: ${bridgeDeployment.contracts.TokenBridge.address}`);
        console.log(`- PXPAC链支持: ${isPxpacSupported ? "是" : "否"}`);
        console.log(`- 管理员权限: ${isAdmin ? "是" : "否"}`);
        
        if (!isPxpacSupported) {
            console.log("\n🔧 修复建议:");
            console.log("1. 配置PXPAC链支持:");
            console.log("   npx hardhat run scripts/bridge/setup-bridge-connection.js --network localhost");
        }
        
        console.log("\n🔧 相关命令:");
        console.log("- 检查跨链池: npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost");
        console.log("- 执行跨链: npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network localhost");
        
    } catch (error) {
        console.log("⚠️  无法生成完整状态摘要");
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("查询失败:", error);
            process.exit(1);
        });
}

module.exports = main;
