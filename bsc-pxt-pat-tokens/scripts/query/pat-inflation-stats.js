// PAT代币增发统计查询脚本
const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("📊 PAT代币增发统计查询");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    // 加载部署信息
    const deploymentPath = path.join(__dirname, `../../deployments/${network.name}/core-deployment.json`);
    if (!fs.existsSync(deploymentPath)) {
        console.error("❌ 未找到部署信息文件:", deploymentPath);
        process.exit(1);
    }
    
    const deployment = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));
    const patAddress = deployment.contracts.PAToken.address;
    
    console.log("PAT代币地址:", patAddress);
    
    // 连接PAT合约
    const PAToken = await ethers.getContractFactory("PAToken");
    const patoken = PAToken.attach(patAddress);
    
    console.log("\n=== 基础代币信息 ===");
    const name = await patoken.name();
    const symbol = await patoken.symbol();
    const decimals = await patoken.decimals();
    const totalSupply = await patoken.totalSupply();
    
    console.log("代币名称:", name);
    console.log("代币符号:", symbol);
    console.log("精度:", decimals);
    console.log("当前总供应量:", ethers.utils.formatEther(totalSupply), "PAT");
    
    console.log("\n=== 增发统计 ===");
    const totalMinted = await patoken.totalMinted();
    const totalBurned = await patoken.totalBurned();
    const netInflation = totalMinted.sub(totalBurned);
    
    console.log("总增发量:", ethers.utils.formatEther(totalMinted), "PAT");
    console.log("总销毁量:", ethers.utils.formatEther(totalBurned), "PAT");
    console.log("净增发量:", ethers.utils.formatEther(netInflation), "PAT");
    
    // 计算增发率
    const initialSupply = ethers.utils.parseEther("300000000"); // 3亿初始供应量
    const inflationRate = netInflation.mul(10000).div(initialSupply);
    console.log("累计增发率:", inflationRate.toString() / 100, "%");
    
    console.log("\n=== 池子余额分配 ===");
    
    // 获取池子地址
    const chinaPool = await patoken.getChinaMainlandPool();
    const globalPool = await patoken.getGlobalPool();
    const crossChainPool = await patoken.getCrossChainPool();
    
    console.log("🇨🇳 中国大陆池:");
    console.log("  地址:", chinaPool);
    const chinaBalance = await patoken.balanceOf(chinaPool);
    console.log("  余额:", ethers.utils.formatEther(chinaBalance), "PAT");
    
    console.log("🌍 国际池:");
    console.log("  地址:", globalPool);
    const globalBalance = await patoken.balanceOf(globalPool);
    console.log("  余额:", ethers.utils.formatEther(globalBalance), "PAT");
    
    console.log("🌉 跨链池:");
    console.log("  地址:", crossChainPool);
    const crossChainBalance = await patoken.balanceOf(crossChainPool);
    console.log("  余额:", ethers.utils.formatEther(crossChainBalance), "PAT");
    
    // 计算池子总余额
    const totalPoolBalance = chinaBalance.add(globalBalance).add(crossChainBalance);
    console.log("\n池子总余额:", ethers.utils.formatEther(totalPoolBalance), "PAT");
    console.log("流通中余额:", ethers.utils.formatEther(totalSupply.sub(totalPoolBalance)), "PAT");
    
    console.log("\n=== 增发权限检查 ===");
    const signers = await ethers.getSigners();
    const deployer = signers[0];
    
    const isMinter = await patoken.isMinter(deployer.address);
    console.log("部署者是否为铸币者:", isMinter ? "✅ 是" : "❌ 否");
    
    // 检查其他可能的铸币者
    console.log("\n检查其他合约的铸币权限...");
    
    // 如果有质押系统部署信息，检查相关合约
    const stakingDeploymentPath = path.join(__dirname, `../../deployments/${network.name}/staking-deployment.json`);
    if (fs.existsSync(stakingDeploymentPath)) {
        const stakingDeployment = JSON.parse(fs.readFileSync(stakingDeploymentPath, 'utf8'));
        
        if (stakingDeployment.contracts.RewardDistributor) {
            const rewardDistributorMinter = await patoken.isMinter(stakingDeployment.contracts.RewardDistributor);
            console.log("奖励分配器铸币权限:", rewardDistributorMinter ? "✅ 有" : "❌ 无");
        }
    }
    
    console.log("\n=== 增发限制检查 ===");
    
    // 计算当前季度增发上限
    const quarterlyInflationCap = totalSupply.mul(50).div(10000); // 0.5%
    console.log("当前季度增发上限:", ethers.utils.formatEther(quarterlyInflationCap), "PAT");
    console.log("年化增发上限:", ethers.utils.formatEther(quarterlyInflationCap.mul(4)), "PAT");
    
    const yearlyInflationRate = quarterlyInflationCap.mul(4).mul(10000).div(totalSupply);
    console.log("年化增发率上限:", yearlyInflationRate.toString() / 100, "%");
    
    console.log("\n=== 经济模型验证 ===");
    
    // 验证初始分配是否正确
    const expectedChinaAllocation = ethers.utils.parseEther("100000000"); // 1亿
    const expectedGlobalAllocation = ethers.utils.parseEther("100000000"); // 1亿
    const expectedCrossChainAllocation = ethers.utils.parseEther("100000000"); // 1亿
    
    console.log("中国大陆池分配验证:", chinaBalance.eq(expectedChinaAllocation) ? "✅ 正确" : "❌ 错误");
    console.log("国际池分配验证:", globalBalance.eq(expectedGlobalAllocation) ? "✅ 正确" : "❌ 错误");
    console.log("跨链池分配验证:", crossChainBalance.eq(expectedCrossChainAllocation) ? "✅ 正确" : "❌ 错误");
    
    // 验证总供应量
    const expectedTotalSupply = ethers.utils.parseEther("300000000"); // 3亿
    console.log("总供应量验证:", totalSupply.eq(expectedTotalSupply) ? "✅ 正确" : "❌ 错误");
    
    console.log("\n=== 质押增发机制说明 ===");
    console.log("💡 质押池不预分配PAT代币");
    console.log("💡 用户质押PXT时，通过mint函数增发PAT作为奖励");
    console.log("💡 增发受到季度0.5%的严格限制");
    console.log("💡 所有增发都会被totalMinted()统计追踪");
    
    console.log("\n================================================");
    console.log("🎉 PAT增发统计查询完成！");
    
    // 保存统计报告
    const report = {
        network: network.name,
        timestamp: new Date().toISOString(),
        patAddress: patAddress,
        basicInfo: {
            name,
            symbol,
            decimals,
            totalSupply: ethers.utils.formatEther(totalSupply)
        },
        inflationStats: {
            totalMinted: ethers.utils.formatEther(totalMinted),
            totalBurned: ethers.utils.formatEther(totalBurned),
            netInflation: ethers.utils.formatEther(netInflation),
            inflationRate: (inflationRate.toString() / 100) + "%"
        },
        poolBalances: {
            chinaMainland: ethers.utils.formatEther(chinaBalance),
            global: ethers.utils.formatEther(globalBalance),
            crossChain: ethers.utils.formatEther(crossChainBalance),
            totalInPools: ethers.utils.formatEther(totalPoolBalance),
            circulating: ethers.utils.formatEther(totalSupply.sub(totalPoolBalance))
        },
        inflationLimits: {
            quarterlyCapPAT: ethers.utils.formatEther(quarterlyInflationCap),
            yearlyCapPAT: ethers.utils.formatEther(quarterlyInflationCap.mul(4)),
            yearlyCapPercentage: (yearlyInflationRate.toString() / 100) + "%"
        }
    };
    
    const reportPath = path.join(__dirname, `../../reports/pat-inflation-stats-${network.name}-${Date.now()}.json`);
    
    // 确保reports目录存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log("📄 统计报告已保存:", reportPath);
}

// 运行脚本
main().catch((error) => {
    console.error("❌ 脚本执行失败:", error);
    process.exit(1);
});
