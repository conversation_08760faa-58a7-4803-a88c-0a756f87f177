const { ethers } = require("ethers");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔍 BSC链跨链桥状态查询");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 直接使用已知的合约地址
    const deployment = {
        contracts: {
            TokenBridge: { address: "******************************************" },
            PAT: { address: "******************************************" }
        }
    };

    console.log("✅ 使用已知合约地址");
    console.log("- TokenBridge:", deployment.contracts.TokenBridge.address);
    console.log("- PAT代币:", deployment.contracts.PAT.address);
    
    // 连接BSC网络
    const provider = new ethers.providers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545");
    
    // 合约ABI
    const erc20ABI = [
        "function balanceOf(address owner) view returns (uint256)",
        "function totalSupply() view returns (uint256)",
        "function name() view returns (string)",
        "function symbol() view returns (string)"
    ];
    
    // 连接合约
    const patToken = new ethers.Contract(deployment.contracts.PAT.address, erc20ABI, provider);
    const tokenBridgeAddress = deployment.contracts.TokenBridge.address;
    const crossChainPoolAddress = "******************************************";
    
    console.log("\n=== 1. PAT代币基本信息 ===");
    
    try {
        const name = await patToken.name();
        const symbol = await patToken.symbol();
        const totalSupply = await patToken.totalSupply();
        
        console.log("PAT代币信息:");
        console.log("- 名称:", name);
        console.log("- 符号:", symbol);
        console.log("- 总供应量:", ethers.formatEther(totalSupply), "PAT");
        
    } catch (error) {
        console.error("❌ 获取PAT信息失败:", error.message);
    }
    
    console.log("\n=== 2. 跨链池状态 ===");
    
    try {
        // 跨链池余额
        const poolBalance = await patToken.balanceOf(crossChainPoolAddress);
        console.log("跨链池当前余额:", ethers.utils.formatEther(poolBalance), "PAT");

        // 估算初始余额（假设是1亿）
        const estimatedInitialBalance = ethers.utils.parseEther("100000000");
        const usedAmount = estimatedInitialBalance.sub(poolBalance);
        console.log("估算已使用额度:", ethers.utils.formatEther(usedAmount), "PAT");
        
    } catch (error) {
        console.error("❌ 查询跨链池失败:", error.message);
    }
    
    console.log("\n=== 3. TokenBridge锁定状态 ===");
    
    try {
        // TokenBridge合约中锁定的PAT
        const bridgeBalance = await patToken.balanceOf(tokenBridgeAddress);
        console.log("TokenBridge锁定总量:", ethers.utils.formatEther(bridgeBalance), "PAT");
        
        // 这个数量应该等于PXPAC链上铸造的wPAT总量
        console.log("💡 此数量应等于PXPAC链wPAT总供应量");
        
    } catch (error) {
        console.error("❌ 查询TokenBridge失败:", error.message);
    }
    
    console.log("\n=== 4. 关键账户余额 ===");
    
    const keyAccounts = [
        { name: "跨链池", address: crossChainPoolAddress },
        { name: "TokenBridge", address: tokenBridgeAddress }
    ];
    
    for (const account of keyAccounts) {
        try {
            const balance = await patToken.balanceOf(account.address);
            console.log(`${account.name} (${account.address}):`);
            console.log(`  余额: ${ethers.utils.formatEther(balance)} PAT`);
        } catch (error) {
            console.log(`${account.name}: 查询失败`);
        }
    }
    
    console.log("\n=== 5. 跨链统计总结 ===");
    
    try {
        const poolBalance = await patToken.balanceOf(crossChainPoolAddress);
        const bridgeBalance = await patToken.balanceOf(tokenBridgeAddress);
        
        console.log("BSC链跨链统计:");
        console.log("- 跨链池剩余:", ethers.utils.formatEther(poolBalance), "PAT");
        console.log("- 已锁定总量:", ethers.utils.formatEther(bridgeBalance), "PAT");
        console.log("- 可用跨链额度:", ethers.utils.formatEther(poolBalance), "PAT");
        
        // 基于已知的跨链记录
        console.log("\n已知跨链记录:");
        console.log("- 第1次: 1000 PAT (交易: 0xcf7f...)");
        console.log("- 第2次: 1000 PAT (交易: 0x9944...)");
        console.log("- 总计: 2000 PAT");
        
    } catch (error) {
        console.error("❌ 统计失败:", error.message);
    }
}

main()
    .then(() => {
        console.log("\n🎉 BSC链状态查询完成！");
        console.log("💡 接下来查询PXPAC链状态进行对账");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 查询失败:", error);
        process.exit(1);
    });
