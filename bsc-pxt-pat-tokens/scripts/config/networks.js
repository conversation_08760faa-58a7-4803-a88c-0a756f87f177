/**
 * 网络配置文件 - 统一管理网络参数
 */

module.exports = {
    localhost: {
        bsc: {
            name: "BSC Local",
            url: "http://127.0.0.1:18485",
            chainId: 97,
            description: "BSC本地测试链"
        },
        pxpac: {
            name: "PXPAC Local",
            url: "http://127.0.0.1:8545",
            chainId: 11,
            description: "PXPAC本地测试链"
        }
    },
    
    testnet: {
        bsc: {
            name: "BSC Testnet",
            url: "https://data-seed-prebsc-1-s1.binance.org:8545",
            chainId: 97,
            description: "BSC测试网"
        },
        pxpac: {
            name: "PXPAC Testnet",
            url: "https://testrpc.pxpac.com",
            chainId: 11,
            description: "PXPAC测试网"
        }
    },
    
    mainnet: {
        bsc: {
            name: "BSC Mainnet",
            url: "https://bsc-dataseed1.binance.org",
            chainId: 56,
            description: "BSC主网"
        },
        pxpac: {
            name: "PXPAC Mainnet",
            url: "https://rpc.pxpac.com", // 示例URL
            chainId: 11,
            description: "PXPAC主网"
        }
    }
};

/**
 * 获取网络配置
 */
function getNetworkConfig(environment = "localhost") {
    const config = module.exports[environment];
    if (!config) {
        throw new Error(`未找到环境配置: ${environment}`);
    }
    return config;
}

/**
 * 获取跨链配置
 */
function getCrossChainConfig(environment = "localhost") {
    const config = getNetworkConfig(environment);
    
    return {
        source: config.bsc,
        target: config.pxpac,
        bridgeParams: {
            requiredConfirmations: 2,
            validatorCount: 3,
            baseFee: "0.001", // BNB
            percentFee: 0.5,   // 0.5%
            minFee: "0.0005",  // BNB
            maxFee: "0.1"      // BNB
        }
    };
}

module.exports.getNetworkConfig = getNetworkConfig;
module.exports.getCrossChainConfig = getCrossChainConfig;
