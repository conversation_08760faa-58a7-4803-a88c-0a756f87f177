const { ethers } = require("ethers");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 测试双向跨链桥功能");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 连接BSC网络
    const provider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545");
    
    // 读取部署信息
    const deploymentsDir = path.join(__dirname, "../../deployments");
    const deploymentFiles = fs.readdirSync(deploymentsDir);
    const latestDeployment = deploymentFiles
        .filter(file => file.startsWith('complete-deployment-') && file.endsWith('.json'))
        .sort()
        .pop();
    
    if (!latestDeployment) {
        console.error("❌ 找不到部署文件");
        return;
    }
    
    const deploymentPath = path.join(deploymentsDir, latestDeployment);
    const deployment = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));
    
    console.log("✅ 已加载部署信息");
    console.log("- TokenBridge:", deployment.contracts.TokenBridge.address);
    console.log("- PAT代币:", deployment.contracts.PAT.address);
    
    // 合约ABI
    const tokenBridgeABI = [
        "function getLockedTokenAmount(address tokenAddress) view returns (uint256)",
        "function createUnlockRequest(address tokenAddress, address recipient, uint256 amount, uint256 sourceChainId, bytes32 sourceBurnTxHash) returns (bytes32)",
        "function isUnlockRequestProcessed(bytes32 unlockRequestId) view returns (bool)",
        "function addValidator(address validator)",
        "function bridgeValidators(address) view returns (bool)",
        "event TokensUnlocked(bytes32 indexed unlockRequestId, address indexed recipient, uint256 amount, address tokenAddress, bytes32 sourceBurnTxHash)"
    ];
    
    const erc20ABI = [
        "function balanceOf(address owner) view returns (uint256)",
        "function transfer(address to, uint256 amount) returns (bool)"
    ];
    
    // 连接合约
    const tokenBridge = new ethers.Contract(deployment.contracts.TokenBridge.address, tokenBridgeABI, provider);
    const patToken = new ethers.Contract(deployment.contracts.PAT.address, erc20ABI, provider);
    
    console.log("\n=== 1. 检查TokenBridge新功能 ===");
    
    try {
        // 检查PAT锁定数量
        const lockedPAT = await tokenBridge.getLockedTokenAmount(deployment.contracts.PAT.address);
        console.log("当前锁定的PAT数量:", ethers.formatEther(lockedPAT), "PAT");

        // 检查TokenBridge的PAT余额
        const bridgeBalance = await patToken.balanceOf(deployment.contracts.TokenBridge.address);
        console.log("TokenBridge PAT余额:", ethers.formatEther(bridgeBalance), "PAT");
        
        if (lockedPAT.toString() === bridgeBalance.toString()) {
            console.log("✅ 锁定数量与余额一致");
        } else {
            console.log("⚠️  锁定数量与余额不一致");
        }
        
    } catch (error) {
        console.error("❌ 检查失败:", error.message);
    }
    
    console.log("\n=== 2. 模拟解锁请求 ===");
    
    // 模拟参数
    const mockUnlockParams = {
        tokenAddress: deployment.contracts.PAT.address,
        recipient: "******************************************", // 跨链池地址
        amount: ethers.utils.parseEther("100"), // 解锁100 PAT
        sourceChainId: 11, // PXPAC链ID
        sourceBurnTxHash: ethers.utils.keccak256(ethers.utils.toUtf8Bytes("mock-burn-tx-" + Date.now()))
    };
    
    console.log("模拟解锁参数:");
    console.log("- 代币地址:", mockUnlockParams.tokenAddress);
    console.log("- 接收者:", mockUnlockParams.recipient);
    console.log("- 解锁数量:", ethers.utils.formatEther(mockUnlockParams.amount), "PAT");
    console.log("- 源链ID:", mockUnlockParams.sourceChainId);
    console.log("- 源交易哈希:", mockUnlockParams.sourceBurnTxHash);
    
    console.log("\n=== 3. 验证解锁逻辑 ===");
    
    try {
        // 检查是否有足够的锁定代币
        const lockedAmount = await tokenBridge.getLockedTokenAmount(mockUnlockParams.tokenAddress);
        console.log("可解锁数量:", ethers.utils.formatEther(lockedAmount), "PAT");
        
        if (lockedAmount.gte(mockUnlockParams.amount)) {
            console.log("✅ 有足够的锁定代币可以解锁");
        } else {
            console.log("❌ 锁定代币不足，无法解锁");
        }
        
        // 检查接收者当前余额
        const recipientBalance = await patToken.balanceOf(mockUnlockParams.recipient);
        console.log("接收者当前PAT余额:", ethers.utils.formatEther(recipientBalance), "PAT");
        
    } catch (error) {
        console.error("❌ 验证失败:", error.message);
    }
    
    console.log("\n=== 4. 双向跨链流程说明 ===");
    
    console.log("完整的双向跨链流程:");
    console.log("📤 正向跨链 (BSC → PXPAC):");
    console.log("  1. 用户在BSC调用 lockTokens() 锁定PAT");
    console.log("  2. 验证者在PXPAC调用 processMintRequest() 铸造wPAT");
    console.log("");
    console.log("📥 反向跨链 (PXPAC → BSC):");
    console.log("  1. 用户在PXPAC调用 createBurnRequest() 销毁wPAT");
    console.log("  2. 验证者监听销毁事件");
    console.log("  3. 验证者在BSC调用 createUnlockRequest() 解锁PAT");
    console.log("");
    console.log("🔧 新增的TokenBridge功能:");
    console.log("  ✅ getLockedTokenAmount() - 查询锁定数量");
    console.log("  ✅ createUnlockRequest() - 创建解锁请求");
    console.log("  ✅ isUnlockRequestProcessed() - 检查解锁状态");
    console.log("  ✅ addValidator() - 添加验证者");
    console.log("  ✅ removeValidator() - 移除验证者");
    
    console.log("\n=== 5. 使用建议 ===");
    
    console.log("部署后的操作步骤:");
    console.log("1. 重新编译和部署更新后的TokenBridge");
    console.log("2. 添加验证者地址到TokenBridge");
    console.log("3. 在PXPAC端部署BidirectionalBridge");
    console.log("4. 配置验证者监听服务");
    console.log("5. 测试完整的双向跨链流程");
    
    console.log("\n💡 注意事项:");
    console.log("- 确保验证者有足够的权限");
    console.log("- 监听PXPAC的销毁事件");
    console.log("- 验证源链交易的真实性");
    console.log("- 防止重放攻击");
    
} 

main()
    .then(() => {
        console.log("\n🎉 双向跨链桥功能测试完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 测试失败:", error);
        process.exit(1);
    });
