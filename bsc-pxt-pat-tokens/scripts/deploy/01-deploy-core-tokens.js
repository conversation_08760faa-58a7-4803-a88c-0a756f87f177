const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署核心代币系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    
    const signers = await ethers.getSigners();
    const deployer = signers[0];
    const treasury = signers[1] || deployer;
    const operator = signers[2] || deployer;
    
    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasury.address);
    console.log("操作员账户:", operator.address);
    
    const deployerBalance = await deployer.provider.getBalance(deployer.address);
    console.log("部署账户余额:", ethers.formatEther(deployerBalance), "ETH");

    // 部署配置参数
    const INITIAL_PXT_SUPPLY = ethers.parseEther("100000000"); // 1亿 PXT
    
    // 创建池子账户
    const chinaMainlandPool = ethers.Wallet.createRandom().connect(ethers.provider);
    const globalPool = ethers.Wallet.createRandom().connect(ethers.provider);
    const stakingPool = ethers.Wallet.createRandom().connect(ethers.provider);
    const crossChainPool = ethers.Wallet.createRandom().connect(ethers.provider);

    console.log("\n=== 创建池子账户 ===");
    console.log("中国大陆池子地址:", chinaMainlandPool.address);
    console.log("国际池子地址:", globalPool.address);
    console.log("质押池地址:", stakingPool.address);
    console.log("跨链池地址:", crossChainPool.address);
    
    // 给池子账户转ETH
    await deployer.sendTransaction({
        to: chinaMainlandPool.address,
        value: ethers.parseEther("0.01")
    });
    await deployer.sendTransaction({
        to: globalPool.address,
        value: ethers.parseEther("0.01")
    });
    await deployer.sendTransaction({
        to: stakingPool.address,
        value: ethers.parseEther("0.01")
    });
    await deployer.sendTransaction({
        to: crossChainPool.address,
        value: ethers.parseEther("0.01")
    });
    
    console.log("\n=== 1. 部署 PXT 治理代币 ===");
    const PXToken = await ethers.getContractFactory("PXToken");
    const pxtoken = await PXToken.deploy(
        "Paper x Token",
        "PXT",
        INITIAL_PXT_SUPPLY,
        deployer.address,      // 社区挖矿与激励
        operator.address,      // 团队持有
        treasury.address,      // 平台生态建设
        deployer.address,      // 私募投资者
        deployer.address,      // 战略合作伙伴
        deployer.address,      // 市场营销
        deployer.address,      // 安全储备
        chinaMainlandPool.address  // 中国大陆池子
    );
    await pxtoken.waitForDeployment();
    console.log("✅ PXT代币部署成功:", await pxtoken.getAddress());
    
    console.log("\n=== 2. 部署 PAT 功能代币 ===");
    const PAToken = await ethers.getContractFactory("PAToken");
    const patoken = await PAToken.deploy(
        chinaMainlandPool.address,  // 中国大陆池：1亿PAT
        globalPool.address,         // 国际池：1亿PAT
        stakingPool.address,        // 质押池：0PAT（通过PXT质押增发）
        crossChainPool.address,     // 跨链池：1亿PAT
        deployer.address            // 铸币者
    );
    await patoken.waitForDeployment();
    console.log("✅ PAT代币部署成功:", await patoken.getAddress());
    
    console.log("\n=== 3. 部署代币注册表 ===");
    const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
    const tokenRegistry = await TokenRegistry.deploy();
    await tokenRegistry.waitForDeployment();
    console.log("✅ 代币注册表部署成功:", await tokenRegistry.getAddress());

    console.log("\n=== 4. 配置代币注册表 ===");

    // 验证合约owner
    const registryOwner = await tokenRegistry.owner();
    console.log("注册表合约owner:", registryOwner);
    console.log("部署者地址:", deployer.address);
    console.log("是否为owner:", registryOwner.toLowerCase() === deployer.address.toLowerCase());

    // 将部署者添加为工厂，以便注册代币
    try {
        console.log("正在添加工厂权限...");
        const addFactoryTx = await tokenRegistry.addFactory(deployer.address);
        await addFactoryTx.wait();
        console.log("✅ 部署者已添加为工厂");

        // 验证工厂权限
        const isFactory = await tokenRegistry.isFactory(deployer.address);
        console.log("验证工厂权限:", isFactory);

        if (!isFactory) {
            throw new Error("添加工厂权限失败");
        }
    } catch (error) {
        console.error("添加工厂权限失败:", error);
        console.error("错误详情:", error.message);
        if (error.data) {
            console.error("错误数据:", error.data);
        }
        throw error;
    }

    // 注册PXT代币
    try {
        const pxtName = await pxtoken.name();
        const pxtSymbol = await pxtoken.symbol();
        const pxtDecimals = await pxtoken.decimals();
        const pxtTotalSupply = await pxtoken.totalSupply();

        console.log("准备注册PXT代币:");
        console.log("- 地址:", await pxtoken.getAddress());
        console.log("- 名称:", pxtName);
        console.log("- 符号:", pxtSymbol);
        console.log("- 小数位:", pxtDecimals);
        console.log("- 总供应量:", ethers.formatEther(pxtTotalSupply));

        const registerPxtTx = await tokenRegistry.registerToken(
            await pxtoken.getAddress(),
            pxtName,
            pxtSymbol,
            pxtDecimals,
            pxtTotalSupply,
            deployer.address,
            true
        );
        await registerPxtTx.wait();
        console.log("✅ PXT代币已注册到注册表");
    } catch (error) {
        console.error("注册PXT代币失败:", error);
        throw error;
    }

    // 注册PAT代币
    try {
        const patName = await patoken.name();
        const patSymbol = await patoken.symbol();
        const patDecimals = await patoken.decimals();
        const patTotalSupply = await patoken.totalSupply();

        console.log("准备注册PAT代币:");
        console.log("- 地址:", await patoken.getAddress());
        console.log("- 名称:", patName);
        console.log("- 符号:", patSymbol);
        console.log("- 小数位:", patDecimals);
        console.log("- 总供应量:", ethers.formatEther(patTotalSupply));

        const registerPatTx = await tokenRegistry.registerToken(
            await patoken.getAddress(),
            patName,
            patSymbol,
            patDecimals,
            patTotalSupply,
            deployer.address,
            false  // PAT不是PXT代币
        );
        await registerPatTx.wait();
        console.log("✅ PAT代币已注册到注册表");
    } catch (error) {
        console.error("注册PAT代币失败:", error);
        throw error;
    }
    
    // 验证部署
    console.log("\n=== 5. 验证部署结果 ===");
    const pxtBalance = await pxtoken.balanceOf(deployer.address);
    const patBalance = await patoken.balanceOf(deployer.address);
    
    console.log("PXT总供应量:", ethers.formatEther(await pxtoken.totalSupply()));
    console.log("PAT总供应量:", ethers.formatEther(await patoken.totalSupply()));
    console.log("部署者PXT余额:", ethers.formatEther(pxtBalance));
    console.log("部署者PAT余额:", ethers.formatEther(patBalance));
    
    // 保存部署信息
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        operator: operator.address,
        contracts: {
            PXToken: {
                address: await pxtoken.getAddress(),
                name: "Paper x Token",
                symbol: "PXT",
                totalSupply: ethers.formatEther(await pxtoken.totalSupply())
            },
            PAToken: {
                address: await patoken.getAddress(),
                name: "Paper Author Token",
                symbol: "PAT",
                totalSupply: ethers.formatEther(await patoken.totalSupply())
            },
            TokenRegistry: {
                address: await tokenRegistry.getAddress(),
                description: "代币注册表"
            }
        },
        pools: {
            chinaMainlandPool: {
                address: chinaMainlandPool.address,
                privateKey: chinaMainlandPool.privateKey,
                description: "中国大陆池",
                allocation: "100,000,000 PAT"
            },
            globalPool: {
                address: globalPool.address,
                privateKey: globalPool.privateKey,
                description: "国际池",
                allocation: "100,000,000 PAT"
            },
            stakingPool: {
                address: stakingPool.address,
                privateKey: stakingPool.privateKey,
                description: "质押池",
                allocation: "50,000,000 PAT"
            },
            crossChainPool: {
                address: crossChainPool.address,
                privateKey: crossChainPool.privateKey,
                description: "跨链池（到PXPAC）",
                allocation: "50,000,000 PAT"
            }
        }
    };
    
    // 确保部署目录存在
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }
    
    // 保存部署文件
    const deploymentFile = path.join(deploymentDir, "core-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n=== 部署完成 ===");
    console.log("部署信息已保存到:", deploymentFile);
    console.log("\n下一步:");
    console.log("1. 部署质押系统: npx hardhat run scripts/deploy/02-deploy-staking-system.js --network", network.name);
    console.log("2. 部署治理系统: npx hardhat run scripts/deploy/03-deploy-governance.js --network", network.name);
    
    return {
        pxtoken: await pxtoken.getAddress(),
        patoken: await patoken.getAddress(),
        tokenRegistry: await tokenRegistry.getAddress(),
        chinaMainlandPool: chinaMainlandPool.address,
        globalPool: globalPool.address,
        stakingPool: stakingPool.address,
        crossChainPool: crossChainPool.address
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
