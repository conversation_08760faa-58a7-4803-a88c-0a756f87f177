const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署内容上链系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    
    const [deployer, treasury, operator] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    
    // 检查部署者余额
    const deployerBalance = await ethers.provider.getBalance(deployer.address);
    console.log("部署者余额:", ethers.formatEther(deployerBalance), "ETH");

    if (deployerBalance < ethers.parseEther("0.1")) {
        console.warn("⚠️  部署者余额较低，建议至少 0.1 ETH");
    }
    
    // 获取已部署的合约地址
    let patTokenAddress, treasuryAddress;
    
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const governanceFile = path.join(__dirname, "../../deployments", network.name, "governance-deployment.json");
        
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        const governanceDeployment = JSON.parse(fs.readFileSync(governanceFile, 'utf8'));
        
        patTokenAddress = coreDeployment.contracts.PAToken.address;
        treasuryAddress = governanceDeployment.contracts.Treasury.address;
        
        console.log("✅ 已加载依赖合约地址");
        console.log("- PAT代币:", patTokenAddress);
        console.log("- 国库合约:", treasuryAddress);
    } catch (error) {
        console.error("❌ 未找到依赖的部署文件，请先运行:");
        console.error("1. npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        console.error("2. npx hardhat run scripts/deploy/02-deploy-staking-system.js --network", network.name);
        console.error("3. npx hardhat run scripts/deploy/03-deploy-governance.js --network", network.name);
        process.exit(1);
    }
    
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        operator: operator.address,
        dependencies: {
            patToken: patTokenAddress,
            treasuryContract: treasuryAddress
        },
        contracts: {},
        gasUsed: {},
        verificationCommands: []
    };
    
    try {
        console.log("\n=== 1. 部署ContentRegistry合约 ===");
        
        const ContentRegistry = await ethers.getContractFactory("ContentRegistry");
        console.log("正在部署ContentRegistry...");
        
        const contentRegistry = await ContentRegistry.deploy(
            patTokenAddress,    // PAT代币地址
            treasuryAddress     // 国库地址
        );

        await contentRegistry.waitForDeployment();
        console.log("✅ ContentRegistry部署成功:", await contentRegistry.getAddress());
        
        // 记录gas使用
        const deployTx = await ethers.provider.getTransactionReceipt(contentRegistry.deploymentTransaction().hash);
        deploymentInfo.gasUsed.ContentRegistry = deployTx.gasUsed.toString();
        deploymentInfo.contracts.ContentRegistry = {
            address: await contentRegistry.getAddress(),
            description: "内容注册合约"
        };
        
        // 验证合约参数
        console.log("验证ContentRegistry配置...");
        const registryPATToken = await contentRegistry.patToken();
        const registryTreasury = await contentRegistry.treasuryAddress();
        
        console.log("  PAT代币地址:", registryPATToken);
        console.log("  国库地址:", registryTreasury);
        
        if (registryPATToken !== patTokenAddress) {
            throw new Error("ContentRegistry PAT代币地址配置错误");
        }
        
        if (registryTreasury !== treasuryAddress) {
            throw new Error("ContentRegistry 国库地址配置错误");
        }
        
        console.log("\n=== 2. 部署ContentCharacter合约 ===");

        const ContentCharacter = await ethers.getContractFactory("ContentCharacter");
        console.log("正在部署ContentCharacter...");

        const contentCharacter = await ContentCharacter.deploy();

        await contentCharacter.waitForDeployment();
        console.log("✅ ContentCharacter部署成功:", await contentCharacter.getAddress());

        // 记录gas使用
        const characterDeployTx = await ethers.provider.getTransactionReceipt(contentCharacter.deploymentTransaction().hash);
        deploymentInfo.gasUsed.ContentCharacter = characterDeployTx.gasUsed.toString();
        deploymentInfo.contracts.ContentCharacter = {
            address: await contentCharacter.getAddress(),
            description: "内容角色NFT合约"
        };

        console.log("\n=== 3. 部署ContentMint合约 ===");

        const ContentMint = await ethers.getContractFactory("ContentMint");
        console.log("正在部署ContentMint...");

        const contentMint = await ContentMint.deploy(
            await contentRegistry.getAddress(),  // ContentRegistry地址
            treasuryAddress           // 国库地址
        );

        await contentMint.waitForDeployment();
        console.log("✅ ContentMint部署成功:", await contentMint.getAddress());

        // 记录gas使用
        const mintDeployTx = await ethers.provider.getTransactionReceipt(contentMint.deploymentTransaction().hash);
        deploymentInfo.gasUsed.ContentMint = mintDeployTx.gasUsed.toString();
        deploymentInfo.contracts.ContentMint = {
            address: await contentMint.getAddress(),
            description: "内容铸造NFT合约"
        };
        
        // 验证合约参数
        console.log("验证ContentMint配置...");
        const mintRegistry = await contentMint.contentRegistry();
        const mintTreasury = await contentMint.treasuryAddress();
        
        console.log("  ContentRegistry地址:", mintRegistry);
        console.log("  国库地址:", mintTreasury);
        
        if (mintRegistry !== await contentRegistry.getAddress()) {
            throw new Error("ContentMint ContentRegistry地址配置错误");
        }
        
        if (mintTreasury !== treasuryAddress) {
            throw new Error("ContentMint 国库地址配置错误");
        }
        
        console.log("\n=== 4. 测试基础功能 ===");
        
        // 测试内容类型费用查询
        console.log("测试内容类型费用...");
        try {
            const videoFee = await contentRegistry.getContentFee("video");
            const articleFee = await contentRegistry.getContentFee("article");
            
            console.log("  视频费用:", ethers.formatEther(videoFee), "PAT");
            console.log("  文章费用:", ethers.formatEther(articleFee), "PAT");
        } catch (error) {
            console.log("  费用查询测试跳过:", error.message);
        }
        
        // 测试统计信息
        console.log("测试统计信息...");
        try {
            const stats = await contentRegistry.getContentStats();
            console.log("  总内容数:", stats.totalContents.toString());
            console.log("  活跃内容数:", stats.activeContents.toString());
            console.log("  总PAT消耗:", ethers.formatEther(stats.totalPATConsumed), "PAT");
        } catch (error) {
            console.log("  统计信息测试跳过:", error.message);
        }
        
        console.log("\n=== 5. 生成验证命令 ===");

        // 生成验证命令
        const networkName = network.name === "localhost" ? "bscTestnet" : network.name;
        deploymentInfo.verificationCommands = [
            `npx hardhat verify --network ${networkName} ${await contentRegistry.getAddress()} "${patTokenAddress}" "${treasuryAddress}"`,
            `npx hardhat verify --network ${networkName} ${await contentCharacter.getAddress()}`,
            `npx hardhat verify --network ${networkName} ${await contentMint.getAddress()} "${await contentRegistry.getAddress()}" "${treasuryAddress}"`
        ];
        
        console.log("ContentRegistry验证命令:");
        console.log(`  ${deploymentInfo.verificationCommands[0]}`);
        console.log("ContentCharacter验证命令:");
        console.log(`  ${deploymentInfo.verificationCommands[1]}`);
        console.log("ContentMint验证命令:");
        console.log(`  ${deploymentInfo.verificationCommands[2]}`);

        console.log("\n=== 6. 保存部署信息 ===");
        
        // 保存部署信息
        const deploymentDir = path.join(__dirname, "../../deployments", network.name);
        if (!fs.existsSync(deploymentDir)) {
            fs.mkdirSync(deploymentDir, { recursive: true });
        }
        
        const deploymentFile = path.join(deploymentDir, "content-deployment.json");
        fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
        
        console.log("✅ 部署信息已保存到:", deploymentFile);
        
        console.log("\n=== 内容上链系统部署完成 ===");
        console.log("ContentRegistry:", await contentRegistry.getAddress());
        console.log("ContentCharacter:", await contentCharacter.getAddress());
        console.log("ContentMint:", await contentMint.getAddress());
        
        console.log("\n下一步:");
        console.log("1. 在区块链浏览器上验证合约");
        console.log("2. 集成IPFS存储功能");
        console.log("3. 运行完整功能测试");
        console.log("4. 配置前端应用集成");
        
        return {
            contentRegistry: await contentRegistry.getAddress(),
            contentCharacter: await contentCharacter.getAddress(),
            contentMint: await contentMint.getAddress()
        };
        
    } catch (error) {
        console.error("❌ 内容系统部署失败:", error.message);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("内容系统部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
