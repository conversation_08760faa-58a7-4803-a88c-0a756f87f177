const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署质押系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);
    
    const [deployer, treasury, operator] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    
    // 质押系统配置参数
    const MIN_STAKE_AMOUNT = ethers.parseEther("100"); // 最小质押 100 PXT (丁级)
    const BASE_APY = 500; // 5% 基础年化收益率(基点)
    const LOCK_PERIODS = [
        30 * 24 * 60 * 60,   // 30天 - 丁级
        90 * 24 * 60 * 60,   // 90天 - 丙级
        180 * 24 * 60 * 60,  // 180天 - 乙级
        270 * 24 * 60 * 60,  // 270天 - 甲级
        365 * 24 * 60 * 60,  // 365天 - 十绝
        540 * 24 * 60 * 60,  // 540天 - 双十绝
        730 * 24 * 60 * 60   // 730天 - 至尊
    ];

    // 质押等级配置
    const STAKING_LEVELS = [
        { name: "丁级", minAmount: ethers.parseEther("100"), multiplier: 130, randomMin: 120, randomMax: 130 },
        { name: "丙级", minAmount: ethers.parseEther("1000"), multiplier: 140, randomMin: 130, randomMax: 140 },
        { name: "乙级", minAmount: ethers.parseEther("5000"), multiplier: 160, randomMin: 150, randomMax: 160 },
        { name: "甲级", minAmount: ethers.parseEther("20000"), multiplier: 200, randomMin: 190, randomMax: 200 },
        { name: "十绝", minAmount: ethers.parseEther("100000"), multiplier: 250, randomMin: 200, randomMax: 250 },
        { name: "双十绝", minAmount: ethers.parseEther("250000"), multiplier: 300, randomMin: 300, randomMax: 400 },
        { name: "至尊", minAmount: ethers.parseEther("500000"), multiplier: 500, randomMin: 400, randomMax: 600 }
    ];
    
    // 获取核心合约地址
    let pxtokenAddress, patokenAddress, tokenRegistryAddress;

    try {
        const deploymentFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const deploymentData = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
        pxtokenAddress = deploymentData.contracts.PXToken.address;
        patokenAddress = deploymentData.contracts.PAToken.address;
        tokenRegistryAddress = deploymentData.contracts.TokenRegistry.address;

        console.log("✅ 已加载核心合约地址");
        console.log("- PXT代币:", pxtokenAddress);
        console.log("- PAT代币:", patokenAddress);
        console.log("- 代币注册表:", tokenRegistryAddress);
    } catch (error) {
        console.error("❌ 未找到核心合约部署信息，请先运行:");
        console.error("npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        process.exit(1);
    }
    
    console.log("PXT代币地址:", pxtokenAddress);
    console.log("PAT代币地址:", patokenAddress);
    
    console.log("\n=== 1. 部署质押工厂 ===");
    const StakingFactory = await ethers.getContractFactory("StakingFactory");
    const stakingFactory = await StakingFactory.deploy(
        pxtokenAddress,
        patokenAddress
    );
    await stakingFactory.waitForDeployment();
    console.log("✅ 质押工厂部署成功:", await stakingFactory.getAddress());

    console.log("\n=== 2. 部署质押池 ===");
    const StakingPool = await ethers.getContractFactory("StakingPool");
    const stakingPool = await StakingPool.deploy();
    await stakingPool.waitForDeployment();
    console.log("✅ 质押池部署成功:", await stakingPool.getAddress());

    console.log("\n=== 3. 初始化质押池 ===");
    await stakingPool.initialize(
        pxtokenAddress,
        patokenAddress,
        BASE_APY
    );
    console.log("✅ 质押池初始化完成");

    console.log("\n=== 4. 部署奖励分配器 ===");
    const RewardDistributor = await ethers.getContractFactory("RewardDistributor");
    const rewardDistributor = await RewardDistributor.deploy(
        patokenAddress
    );
    await rewardDistributor.waitForDeployment();
    console.log("✅ 奖励分配器部署成功:", await rewardDistributor.getAddress());

    console.log("\n=== 5. 配置质押系统关系 ===");

    // 将质押池添加到奖励分配器
    await rewardDistributor.addRewardPool(await stakingPool.getAddress(), 1000); // 分配权重1000
    console.log("✅ 质押池已添加到奖励分配器");

    // 配置质押工厂的奖励分配器
    await stakingFactory.updateRewardDistributor(await rewardDistributor.getAddress());
    console.log("✅ 已设置奖励分配器");

    // 设置质押池实现合约
    await stakingFactory.updateStandardImplementation(await stakingPool.getAddress());
    console.log("✅ 已设置质押池实现合约");

    console.log("\n=== 6. 质押等级已预配置 ===");
    console.log("质押等级在合约构造函数中已预设：");
    console.log("- 丁级: 100 PXT");
    console.log("- 丙级: 1,000 PXT");
    console.log("- 乙级: 5,000 PXT");
    console.log("- 甲级: 20,000 PXT");
    console.log("- 十绝: 100,000 PXT");
    console.log("- 双十绝: 250,000 PXT");
    console.log("- 至尊: 500,000 PXT");

    console.log("\n=== 7. 验证质押系统 ===");
    const minStake = await stakingPool.minStakeAmount();
    const baseAPY = await stakingPool.baseAPR(); // 注意：合约中实际变量名是baseAPR，不是baseAPY

    console.log("最小质押金额:", ethers.formatEther(minStake), "PXT");
    console.log("基础年化收益率:", baseAPY.toString(), "基点");

    // 保存部署信息
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        contracts: {
            StakingFactory: await stakingFactory.getAddress(),
            StakingPool: await stakingPool.getAddress(),
            RewardDistributor: await rewardDistributor.getAddress()
        },
        configuration: {
            minStakeAmount: minStake.toString(),
            baseAPY: baseAPY.toString(),
            stakeLevels: 7 // 假设质押等级数量为7
        }
    };
    
    // 保存部署文件
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }

    const deploymentFile = path.join(deploymentDir, "staking-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n=== 质押系统部署完成 ===");
    console.log("部署信息已保存到:", deploymentFile);
    console.log("\n下一步:");
    console.log("1. 部署治理系统: npx hardhat run scripts/deploy/03-deploy-governance.js --network", network.name);
    console.log("2. 测试质押功能: npx hardhat run scripts/test/02-reward-system-test.js --network", network.name);

    return deploymentInfo;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
