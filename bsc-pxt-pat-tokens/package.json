{"name": "pxpat-tokens", "version": "0.1.0", "description": "PXT和PAT代币系统实现", "main": "index.js", "scripts": {"compile": "npx hardhat compile", "test": "npx hardhat test", "test:coverage": "npx hardhat coverage", "node:start": "npx hardhat node", "quick-start": "node scripts/quick-start.js", "deploy:core": "npx hardhat run scripts/local/deploy/01-deploy-core-tokens.js --network localhost", "deploy:staking": "npx hardhat run scripts/local/deploy/02-deploy-staking-system.js --network localhost", "deploy:governance": "npx hardhat run scripts/local/deploy/03-deploy-governance.js --network localhost", "deploy:local": "npx hardhat run scripts/local/deploy/deploy-all.js --network localhost", "deploy:testnet": "npx hardhat run scripts/local/deploy/deploy-testnet.js --network bscTestnet", "deploy:testnet:core": "npx hardhat run scripts/local/deploy/01-deploy-core-tokens.js --network bscTestnet", "deploy:testnet:staking": "npx hardhat run scripts/local/deploy/02-deploy-staking-system.js --network bscTestnet", "deploy:testnet:governance": "npx hardhat run scripts/local/deploy/03-deploy-governance.js --network bscTestnet", "check:balances": "npx hardhat check-balances --network bscTestnet", "generate:keys": "node scripts/utils/generate-test-keys.js", "setup:testnet": "node scripts/utils/generate-test-keys.js > testnet-setup.txt && echo '设置信息已保存到 testnet-setup.txt'", "test:basic": "npx hardhat run scripts/local/test/01-basic-function-test.js --network localhost", "test:rewards": "npx hardhat run scripts/local/test/02-reward-system-test.js --network localhost", "test:governance": "npx hardhat run scripts/local/test/03-governance-test.js --network localhost", "test:governance-comprehensive": "npx hardhat run scripts/local/test/04-comprehensive-governance-test.js --network localhost", "test:random-demo": "npx hardhat run scripts/local/demos/05-probability-claim-test.js --network localhost", "admin:activate-random": "npx hardhat run scripts/local/admin/01-activate-random-rewards.js --network localhost", "admin:adjust-ranges": "npx hardhat run scripts/local/admin/02-adjust-reward-ranges.js --network localhost", "admin:reset-min-stake": "npx hardhat run scripts/local/admin/03-reset-min-stake.js --network localhost", "admin:cleanup-stakes": "npx hardhat run scripts/local/admin/04-cleanup-test-stakes.js --network localhost", "bsc:check": "node scripts/bsc-testnet/utils/check-requirements.js", "bsc:deploy:core": "npx hardhat run scripts/bsc-testnet/deploy/01-deploy-core-tokens.js --network bscTestnet", "bsc:deploy:staking": "npx hardhat run scripts/bsc-testnet/deploy/02-deploy-staking-system.js --network bscTestnet", "bsc:deploy:governance": "npx hardhat run scripts/bsc-testnet/deploy/03-deploy-governance.js --network bscTestnet", "bsc:deploy:all": "npm run bsc:check && npm run bsc:deploy:core && npm run bsc:deploy:staking && npm run bsc:deploy:governance", "bsc:test:basic": "npx hardhat run scripts/bsc-testnet/test/01-basic-function-test.js --network bscTestnet", "bsc:test:rewards": "npx hardhat run scripts/bsc-testnet/test/02-reward-system-test.js --network bscTestnet", "bsc:test:governance": "npx hardhat run scripts/bsc-testnet/test/03-governance-test.js --network bscTestnet", "bsc:verify": "npx hardhat verify --network bscTestnet", "bsc:balances": "npx hardhat check-balances --network bscTestnet", "bsc:generate-accounts": "node scripts/bsc-testnet/utils/generate-accounts.js", "bsc:quick-start": "node scripts/bsc-testnet/bsc-quick-start.js", "test:backend-sync": "node scripts/test/run-backend-sync-tests.js", "test:multi-transfer": "node scripts/test/multi-transfer-test.js", "test:staking-ops": "node scripts/test/staking-operations-test.js", "test:stress": "node scripts/test/mixed-transactions-stress-test.js"}, "keywords": ["blockchain", "tokens", "bsc", "defi"], "author": "PX团队", "license": "UNLICENSED", "private": true, "devDependencies": {"@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-verify": "^2.0.11", "@nomicfoundation/hardhat-chai-matchers": "^2.0.8", "@openzeppelin/contracts": "^4.9.3", "chai": "^4.5.0", "dotenv": "^16.6.1", "ethers": "^6.13.4", "hardhat": "^2.22.15", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.4"}, "dependencies": {"@chainlink/contracts": "^0.6.1", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "axios": "^1.10.0", "form-data": "^4.0.3"}}