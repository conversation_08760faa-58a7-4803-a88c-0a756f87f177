# 池子管理操作手册

## 📋 概述

本手册详细说明如何管理和操作PXT-PAT系统中的中国大陆池子和全球池子。

## 🔑 池子信息

### 中国大陆池子
- **地址**: `0x1Aa6dA1b4cD18A0fC0B2A513f96209a39A261E6f`
- **私钥**: 保存在 `deployments/bscTestnet/pool-private-keys.json`
- **资产**: 100,000,000 PAT + 19,998,000 PXT + 0.21 tBNB
- **功能**: 地区性奖励分发、治理参与

### 全球池子
- **地址**: `0xf8CfA57C19C010A40064fcc801419D52Ce658A43`
- **私钥**: 保存在 `deployments/bscTestnet/pool-private-keys.json`
- **资产**: 100,000,000 PAT + 0.21 tBNB
- **功能**: 全球奖励分发、国际化运营

## 🛠️ 基础操作

### 1. 查看池子状态
```bash
# 使用管理工具查看
npx hardhat run scripts/utils/pool-manager.js --network bscTestnet

# 或使用检查工具
npx hardhat run scripts/utils/check-deployment.js --network bscTestnet
```

### 2. 创建池子钱包实例
```javascript
const { ethers } = require("hardhat");

// 读取私钥
const deploymentData = require("../deployments/bscTestnet/core-deployment.json");

// 创建钱包实例
const chinaPool = new ethers.Wallet(
    deploymentData.pools.chinaMainlandPoolPrivateKey,
    ethers.provider
);

const globalPool = new ethers.Wallet(
    deploymentData.pools.globalPoolPrivateKey,
    ethers.provider
);
```

### 3. 检查余额
```javascript
// 连接到代币合约
const patoken = await ethers.getContractAt("PAToken", deploymentData.contracts.PAToken);

// 检查余额
const chinaPAT = await patoken.balanceOf(chinaPool.address);
const globalPAT = await patoken.balanceOf(globalPool.address);
const chinaETH = await chinaPool.getBalance();

console.log(`中国大陆池子PAT: ${ethers.utils.formatEther(chinaPAT)}`);
console.log(`全球池子PAT: ${ethers.utils.formatEther(globalPAT)}`);
console.log(`中国大陆池子tBNB: ${ethers.utils.formatEther(chinaETH)}`);
```

## 💸 转账操作

### 1. 从池子转账PAT到指定地址
```javascript
async function transferFromPool(poolWallet, recipient, amount) {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    // 检查余额
    const balance = await patoken.balanceOf(poolWallet.address);
    if (balance.lt(amount)) {
        throw new Error("余额不足");
    }
    
    // 执行转账
    const tx = await patoken.connect(poolWallet).transfer(recipient, amount);
    const receipt = await tx.wait();
    
    console.log(`转账成功: ${tx.hash}`);
    console.log(`Gas使用: ${receipt.gasUsed.toString()}`);
    
    return receipt;
}

// 使用示例
const amount = ethers.utils.parseEther("1000"); // 1000 PAT
await transferFromPool(chinaPool, "0x接收地址", amount);
```

### 2. 池子间转账
```javascript
async function transferBetweenPools(fromPool, toPool, amount) {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    const tx = await patoken.connect(fromPool).transfer(toPool.address, amount);
    const receipt = await tx.wait();
    
    console.log(`池子间转账成功: ${tx.hash}`);
    return receipt;
}

// 从中国大陆池子转到全球池子
const amount = ethers.utils.parseEther("5000");
await transferBetweenPools(chinaPool, globalPool, amount);
```

### 3. 批量转账
```javascript
async function batchTransfer(poolWallet, recipients, amounts) {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    const results = [];
    
    for (let i = 0; i < recipients.length; i++) {
        const tx = await patoken.connect(poolWallet).transfer(recipients[i], amounts[i]);
        const receipt = await tx.wait();
        results.push({
            recipient: recipients[i],
            amount: ethers.utils.formatEther(amounts[i]),
            txHash: tx.hash
        });
        
        // 避免nonce冲突，等待确认
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
}
```

## 🔐 授权操作

### 1. 授权代币使用
```javascript
async function approveToken(poolWallet, spender, amount) {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    const tx = await patoken.connect(poolWallet).approve(spender, amount);
    const receipt = await tx.wait();
    
    console.log(`授权成功: ${tx.hash}`);
    
    // 验证授权额度
    const allowance = await patoken.allowance(poolWallet.address, spender);
    console.log(`授权额度: ${ethers.utils.formatEther(allowance)} PAT`);
    
    return receipt;
}
```

### 2. 清除授权
```javascript
async function clearApproval(poolWallet, spender) {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    const tx = await patoken.connect(poolWallet).approve(spender, 0);
    await tx.wait();
    
    console.log("授权已清除");
}
```

## 🎁 奖励分发

### 1. 为质押池注资
```javascript
async function fundStakingPool(poolWallet, amount) {
    const stakingPoolAddress = "******************************************";
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    const tx = await patoken.connect(poolWallet).transfer(stakingPoolAddress, amount);
    const receipt = await tx.wait();
    
    console.log(`质押池注资成功: ${ethers.utils.formatEther(amount)} PAT`);
    console.log(`交易哈希: ${tx.hash}`);
    
    return receipt;
}

// 从中国大陆池子为质押池注资100万PAT
const fundAmount = ethers.utils.parseEther("1000000");
await fundStakingPool(chinaPool, fundAmount);
```

### 2. 活动奖励分发
```javascript
async function distributeActivityRewards(poolWallet, participants, rewards) {
    const results = [];
    
    for (let i = 0; i < participants.length; i++) {
        const result = await transferFromPool(poolWallet, participants[i], rewards[i]);
        results.push({
            participant: participants[i],
            reward: ethers.utils.formatEther(rewards[i]),
            txHash: result.transactionHash
        });
    }
    
    console.log(`活动奖励分发完成，共 ${participants.length} 人`);
    return results;
}
```

## 🗳️ 治理参与

### 1. 检查治理能力
```javascript
async function checkGovernancePower(poolWallet) {
    const pxtoken = await ethers.getContractAt("PXToken", PXT_ADDRESS);
    
    const pxtBalance = await pxtoken.balanceOf(poolWallet.address);
    const votingPower = pxtBalance; // PXT即为治理代币
    
    console.log(`治理代币余额: ${ethers.utils.formatEther(pxtBalance)} PXT`);
    console.log(`投票权重: ${ethers.utils.formatEther(votingPower)}`);
    
    return votingPower;
}

// 中国大陆池子具备治理能力
await checkGovernancePower(chinaPool);
```

### 2. 参与治理投票
```javascript
async function voteOnProposal(poolWallet, proposalId, support) {
    // 这里需要根据实际的治理合约接口调整
    const governance = await ethers.getContractAt("Governance", GOVERNANCE_ADDRESS);
    
    const tx = await governance.connect(poolWallet).vote(proposalId, support);
    const receipt = await tx.wait();
    
    console.log(`投票成功: 提案${proposalId}, 支持度${support}`);
    return receipt;
}
```

## 🔧 维护操作

### 1. 为池子充值Gas费用
```javascript
async function fundPoolGas(fromWallet, poolAddress, amount) {
    const tx = await fromWallet.sendTransaction({
        to: poolAddress,
        value: amount
    });
    
    await tx.wait();
    console.log(`Gas费用充值成功: ${ethers.utils.formatEther(amount)} tBNB`);
}

// 为池子充值0.1 tBNB
const gasAmount = ethers.utils.parseEther("0.1");
await fundPoolGas(deployerWallet, chinaPool.address, gasAmount);
```

### 2. 备份池子私钥
```bash
# 使用管理工具备份
npx hardhat run scripts/utils/pool-manager.js backup --network bscTestnet
```

### 3. 监控池子状态
```javascript
async function monitorPools() {
    const patoken = await ethers.getContractAt("PAToken", PAT_ADDRESS);
    
    const chinaPAT = await patoken.balanceOf(chinaPool.address);
    const globalPAT = await patoken.balanceOf(globalPool.address);
    const chinaETH = await chinaPool.getBalance();
    const globalETH = await globalPool.getBalance();
    
    const status = {
        chinaPool: {
            address: chinaPool.address,
            pat: ethers.utils.formatEther(chinaPAT),
            eth: ethers.utils.formatEther(chinaETH),
            gasLow: chinaETH.lt(ethers.utils.parseEther("0.01"))
        },
        globalPool: {
            address: globalPool.address,
            pat: ethers.utils.formatEther(globalPAT),
            eth: ethers.utils.formatEther(globalETH),
            gasLow: globalETH.lt(ethers.utils.parseEther("0.01"))
        }
    };
    
    console.log("池子状态监控:", JSON.stringify(status, null, 2));
    return status;
}
```

## ⚠️ 安全注意事项

### 1. 私钥安全
- 🔒 私钥文件已添加到 `.gitignore`，不会被提交到Git
- 💾 定期备份私钥文件到安全位置
- 🔐 生产环境建议使用硬件钱包或多重签名

### 2. 操作安全
- 🧪 重要操作前先在测试环境验证
- 💰 大额转账前先进行小额测试
- 📊 操作后及时检查余额和状态
- 📝 重要操作保留交易哈希记录

### 3. Gas管理
- ⛽ 确保池子有足够的tBNB支付Gas费用
- 📈 监控Gas价格，选择合适的时机操作
- 🔄 批量操作时注意nonce管理

### 4. 应急处理
- 🚨 如发现异常，立即停止操作并检查
- 📞 重要问题及时联系技术团队
- 🔄 必要时可以重新部署测试网环境

---

**最后更新**: 2025-01-04  
**版本**: v1.0  
**适用环境**: BSC测试网
