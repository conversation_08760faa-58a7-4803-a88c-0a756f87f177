# 快速参考 - BSC测试网部署

## 🚀 部署信息

### 网络配置
- **网络**: BSC Testnet
- **Chain ID**: 97
- **RPC**: https://data-seed-prebsc-1-s1.binance.org:8545/
- **浏览器**: https://testnet.bscscan.com/

### 核心合约地址
```
PAT Token: 0xf4AA9F162800A2E636065B3D0898f01D09A59699
质押池: 0xa552aB6cCD13E1E7bfD54e3ADe110076Ca32F03b
奖励分配器: 0x0EDA5b2df075980B47C5577DA12F9CbC4b447b50
```

### 账户角色
```
部署者: 0xc4A114182ddbaE513BCFD6976B8856a845f7bB57
国库: 0x2566916DD0E0E8B4c10B228F92D7418a337df2cb
操作员: 0x458ceE5679866a1873B129275bDe34dd8AA8180e
```

### 池子地址
```
中国大陆池子: 0x1Aa6dA1b4cD18A0fC0B2A513f96209a39A261E6f
全球池子: 0xf8CfA57C19C010A40064fcc801419D52Ce658A43
```

## 💰 PAT代币分配

### 新分配方案 (总计: 300,000,000 PAT)
```
中国大陆池子: 100,000,000 PAT (33.3%)
全球池子: 100,000,000 PAT (33.3%)
国库: 100,000,000 PAT (33.3%)
```

### 当前余额状态
```
中国大陆池子: 100,000,000 PAT + 19,998,000 PXT + 0.21 tBNB
全球池子: 100,000,000 PAT + 0.21 tBNB
国库: 100,000,000 PAT + 45,011,000 PXT
质押池: 1,000,000 PAT (奖励资金)
```

## 🔧 常用命令

### 部署命令
```bash
# 清理旧部署
rm -rf deployments/bscTestnet

# 重新部署
npm run bsc:quick-start
```

### 测试命令
```bash
# 验证分配方案
npx hardhat run scripts/test/verify-new-allocation.js --network bscTestnet

# 质押系统测试
npx hardhat run scripts/test/staking-system-test.js --network bscTestnet

# 池子管理测试
npx hardhat run scripts/test/pool-management-test.js --network bscTestnet

# 池子转账测试
npx hardhat run scripts/test/pool-transfer-test.js --network bscTestnet
```

### 管理命令
```bash
# 查看池子信息
npx hardhat run scripts/utils/pool-manager.js --network bscTestnet

# 检查部署状态
npx hardhat run scripts/utils/check-deployment.js --network bscTestnet

# 备份池子私钥
npx hardhat run scripts/utils/pool-manager.js backup --network bscTestnet
```

## 🔐 私钥管理

### 私钥文件位置
```
deployments/bscTestnet/pool-private-keys.json
```

### 私钥安全
- ✅ 已添加到 `.gitignore`
- ✅ 包含完整的地址和私钥信息
- ✅ 支持备份功能
- ✅ 包含安全警告

### 池子钱包使用示例
```javascript
// 创建池子钱包
const chinaPool = new ethers.Wallet(
    "0x75975bae...", // 中国大陆池子私钥
    ethers.provider
);

const globalPool = new ethers.Wallet(
    "0x9b1ddf82...", // 全球池子私钥
    ethers.provider
);

// 转账示例
await patoken.connect(chinaPool).transfer(recipient, amount);
```

## 🧪 测试结果

### ✅ 功能验证
- PAT分配: 每个池子1亿PAT ✅
- 私钥保存: 完整且可用 ✅
- 质押系统: 正常运行 ✅
- 随机奖励: 已启用 ✅
- 池子转账: 功能正常 ✅
- 治理能力: 中国大陆池子具备 ✅

### Gas消耗参考
```
普通转账: ~39,241 Gas (~0.0001 tBNB)
授权操作: ~46,070 Gas (~0.00012 tBNB)
质押操作: ~50,000 Gas (~0.00013 tBNB)
```

## 🎯 关键特性

### 系统优势
1. **均衡分配**: 每个池子和国库都有1亿PAT
2. **私钥安全**: 池子私钥完整保存且功能正常
3. **灵活管理**: 国库可以直接为奖励系统注资
4. **完整工具**: 提供全套管理和测试工具

### 运营能力
1. **资金可控**: 所有池子资金都可以正常使用
2. **奖励分发**: 质押池有充足的奖励资金
3. **治理参与**: 中国大陆池子可以参与DAO治理
4. **应急响应**: 可以快速转移和调配资金

## 🔗 重要链接

### BSCScan链接
- [PAT Token](https://testnet.bscscan.com/address/0xf4AA9F162800A2E636065B3D0898f01D09A59699)
- [质押池](https://testnet.bscscan.com/address/0xa552aB6cCD13E1E7bfD54e3ADe110076Ca32F03b)
- [奖励分配器](https://testnet.bscscan.com/address/0x0EDA5b2df075980B47C5577DA12F9CbC4b447b50)

### 文档链接
- [完整部署记录](./PAT代币分配重构与BSC测试网部署记录.md)
- [本地测试网部署指南](./本地测试网部署指南.md)
- [BSC测试网部署指南](./BSC测试网部署指南.md)

## ⚠️ 注意事项

### 安全提醒
1. **私钥保护**: 池子私钥文件包含敏感信息，请妥善保管
2. **测试网环境**: 当前为测试网部署，代币无实际价值
3. **Gas费用**: 池子操作需要消耗tBNB作为Gas费用
4. **备份重要**: 定期备份私钥和部署信息

### 操作建议
1. **测试先行**: 重要操作前先在测试环境验证
2. **小额测试**: 大额转账前先进行小额测试
3. **状态检查**: 操作后及时检查余额和状态
4. **记录保存**: 重要操作保留交易哈希记录

---

**最后更新**: 2025-01-04  
**版本**: v1.0  
**状态**: 测试网部署完成 ✅
