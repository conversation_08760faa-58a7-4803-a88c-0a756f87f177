const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("📝 部署PXPAC链内容系统");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const signers = await ethers.getSigners();
    const deployer = signers[0];
    const treasury = signers[1] || deployer;  // 如果没有第二个账户，使用部署者账户
    const operator = signers[2] || deployer;  // 如果没有第三个账户，使用部署者账户

    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasury.address);
    console.log("操作员账户:", operator.address);
    
    // 使用测试代币作为支付代币（从你的测试记录中获取）
    let paymentTokenAddress;
    let tokenName = "未知代币";
    let tokenSymbol = "UNKNOWN";

    // 选项1: 使用你在测试记录中创建的Test Token (推荐用于测试)
    const testTokenAddress = "******************************************"; // 来自测试记录的Test Token

    // 选项2: 使用wPAT代币 (如果已部署)
    const wpatDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
        .filter(file => file.startsWith("wpat-deployment-") && file.endsWith(".json"))
        .sort()
        .reverse(); // 最新的在前

    // 优先使用Test Token，因为它有充足的供应量用于测试
    paymentTokenAddress = testTokenAddress;
    tokenName = "Test Token";
    tokenSymbol = "TEST";
    console.log("✅ 使用Test Token作为支付代币:", paymentTokenAddress);
    console.log("✅ 代币名称:", tokenName, "(" + tokenSymbol + ")");
    console.log("💡 这个代币有充足的供应量，适合测试内容系统");

    // 如果想使用wPAT代币，可以取消下面的注释
    /*
    if (wpatDeploymentFiles.length > 0) {
        const wpatDeploymentPath = path.join(__dirname, "../../deployments", wpatDeploymentFiles[0]);
        const wpatDeployment = JSON.parse(fs.readFileSync(wpatDeploymentPath, "utf8"));
        paymentTokenAddress = wpatDeployment.contracts.wPAT.address;
        tokenName = "Wrapped PX Activity Token";
        tokenSymbol = "wPAT";
        console.log("✅ 找到wPAT代币部署:", paymentTokenAddress);
        console.log("✅ 部署文件:", wpatDeploymentFiles[0]);
    }
    */
    
    console.log("\n=== 1. 部署ContentCharacter合约 ===");
    
    const ContentCharacter = await ethers.getContractFactory("ContentCharacter");
    console.log("正在部署ContentCharacter...");
    
    const contentCharacter = await ContentCharacter.deploy();
    await contentCharacter.waitForDeployment();
    
    const contentCharacterAddress = await contentCharacter.getAddress();
    console.log("✅ ContentCharacter部署成功:", contentCharacterAddress);
    
    console.log("\n=== 2. 部署ContentRegistry合约 ===");
    
    const ContentRegistry = await ethers.getContractFactory("ContentRegistry");
    console.log("正在部署ContentRegistry...");
    
    const contentRegistry = await ContentRegistry.deploy(
        paymentTokenAddress,        // 支付代币地址 (TEST代币)
        contentCharacterAddress,    // ContentCharacter地址
        treasury.address           // 国库地址
    );
    await contentRegistry.waitForDeployment();
    
    const contentRegistryAddress = await contentRegistry.getAddress();
    console.log("✅ ContentRegistry部署成功:", contentRegistryAddress);
    
    console.log("\n=== 3. 配置内容类型和费用 ===");
    
    // 配置内容类型和费用（使用TEST代币，创作者友好的费用结构）
    const contentTypes = [
        { type: "article", fee: ethers.parseEther("0.05") },   // 0.05 TEST (小说章节)
        { type: "video", fee: ethers.parseEther("1") },        // 1 TEST (视频内容)
        { type: "audio", fee: ethers.parseEther("0.25") },     // 0.25 TEST (音乐)
        { type: "image", fee: ethers.parseEther("0.01") },     // 0.01 TEST (图片/漫画)
        { type: "document", fee: ethers.parseEther("0.05") }   // 0.05 TEST (文档)
    ];
    
    console.log("配置内容类型和费用...");
    for (const { type, fee } of contentTypes) {
        console.log(`- 添加类型: ${type}, 费用: ${ethers.formatEther(fee)} ${tokenSymbol}`);

        const addTypeTx = await contentRegistry.addContentType(type, fee);
        await addTypeTx.wait();

        const activateTx = await contentRegistry.activateContentType(type);
        await activateTx.wait();
    }
    
    console.log("✅ 内容类型配置完成");
    
    console.log("\n=== 4. 配置角色权限 ===");
    
    // 配置内容审核角色
    const roles = [
        "content_moderator",
        "education_expert",
        "technical_reviewer",
        "community_manager",
        "creator"  // 添加creator角色供测试使用
    ];
    
    console.log("配置内容角色...");
    for (const role of roles) {
        console.log(`- 添加角色: ${role}`);
        const addRoleTx = await contentRegistry.addRole(role);
        await addRoleTx.wait();
    }
    
    console.log("✅ 角色配置完成");
    
    console.log("\n=== 5. 部署ContentMint合约 ===");

    const ContentMint = await ethers.getContractFactory("ContentMint");
    console.log("正在部署ContentMint...");

    const contentMint = await ContentMint.deploy(
        contentRegistryAddress,    // ContentRegistry地址
        paymentTokenAddress,       // 支付代币地址 (TEST代币)
        contentCharacterAddress,   // ContentCharacter地址
        treasury.address          // 国库地址
    );
    await contentMint.waitForDeployment();

    const contentMintAddress = await contentMint.getAddress();
    console.log("✅ ContentMint部署成功:", contentMintAddress);

    console.log("\n=== 6. 配置ContentMint价格 ===");

    // 配置铸造价格（使用TEST代币，创作者友好的价格结构）
    const mintPrices = [
        { type: "article", basePrice: ethers.parseEther("0.01"), increment: ethers.parseEther("0.005"), maxPrice: ethers.parseEther("0.5") },
        { type: "video", basePrice: ethers.parseEther("0.1"), increment: ethers.parseEther("0.05"), maxPrice: ethers.parseEther("2") },
        { type: "audio", basePrice: ethers.parseEther("0.05"), increment: ethers.parseEther("0.01"), maxPrice: ethers.parseEther("1") },
        { type: "image", basePrice: ethers.parseEther("0.002"), increment: ethers.parseEther("0.001"), maxPrice: ethers.parseEther("0.1") },
        { type: "document", basePrice: ethers.parseEther("0.01"), increment: ethers.parseEther("0.005"), maxPrice: ethers.parseEther("0.5") }
    ];

    console.log("配置铸造价格...");
    for (const { type, basePrice, increment, maxPrice } of mintPrices) {
        console.log(`- 配置 ${type}: 基础 ${ethers.formatEther(basePrice)} ${tokenSymbol}, 递增 ${ethers.formatEther(increment)} ${tokenSymbol}, 最大 ${ethers.formatEther(maxPrice)} ${tokenSymbol}`);

        const configureTx = await contentMint.configureMintPrice(type, basePrice, increment, maxPrice);
        await configureTx.wait();
    }

    console.log("✅ 铸造价格配置完成");

    console.log("\n=== 7. 设置权限 ===");

    // 设置ContentRegistry为ContentCharacter的铸造者
    console.log("设置ContentRegistry为ContentCharacter的铸造者...");
    const setRegistryMinterTx = await contentCharacter.setMinter(contentRegistryAddress, true);
    await setRegistryMinterTx.wait();

    // 设置ContentMint为ContentCharacter的铸造者（用于更新收益）
    console.log("设置ContentMint为ContentCharacter的铸造者...");
    const setMintMinterTx = await contentCharacter.setMinter(contentMintAddress, true);
    await setMintMinterTx.wait();

    console.log("✅ 权限设置完成");
    
    console.log("\n=== 8. 验证部署 ===");

    // 验证配置
    const activeTypes = await contentRegistry.getActiveContentTypes();
    console.log("激活的内容类型:", activeTypes);

    const videoFee = await contentRegistry.getContentFee("video");
    console.log("video内容费用:", ethers.formatEther(videoFee), tokenSymbol);

    const videoMintPrice = await contentMint.calculateMintPrice("video", 1);
    console.log("video铸造基础价格:", ethers.formatEther(videoMintPrice), tokenSymbol);
    
    console.log("\n=== 9. 保存部署信息 ===");

    const deploymentInfo = {
        network: network.name,
        chainId: Number((await ethers.provider.getNetwork()).chainId),
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        operator: operator.address,
        contracts: {
            PaymentToken: {
                address: paymentTokenAddress,
                name: tokenName,
                symbol: tokenSymbol,
                description: `支付代币（${tokenName}）`
            },
            // 保持向后兼容，测试脚本期望wPAT字段
            wPAT: {
                address: paymentTokenAddress,
                name: tokenName,
                symbol: tokenSymbol,
                description: `支付代币（${tokenName}）- 兼容字段`
            },
            ContentCharacter: {
                address: contentCharacterAddress,
                description: "内容创作者角色NFT"
            },
            ContentRegistry: {
                address: contentRegistryAddress,
                description: "内容注册表"
            },
            ContentMint: {
                address: contentMintAddress,
                description: "内容铸造NFT"
            }
        },
        contentTypes: contentTypes.map(ct => ({
            type: ct.type,
            fee: ethers.formatEther(ct.fee) + " " + tokenSymbol
        })),
        mintPrices: mintPrices.map(mp => ({
            type: mp.type,
            basePrice: ethers.formatEther(mp.basePrice) + " " + tokenSymbol,
            increment: ethers.formatEther(mp.increment) + " " + tokenSymbol,
            maxPrice: ethers.formatEther(mp.maxPrice) + " " + tokenSymbol
        })),
        roles: roles
    };
    
    // 确保部署目录存在
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }
    
    // 保存部署文件（处理BigInt序列化）
    const deploymentFile = path.join(deploymentDir, "content-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, (key, value) => {
        return typeof value === 'bigint' ? value.toString() : value;
    }, 2));
    
    console.log("✅ 部署信息已保存:", deploymentFile);
    
    console.log("\n🎉 PXA链内容系统部署完成！");
    console.log("================================================");
    console.log("✅ 支付代币:", paymentTokenAddress, `(${tokenName})`);
    console.log("✅ ContentCharacter:", contentCharacterAddress);
    console.log("✅ ContentRegistry:", contentRegistryAddress);
    console.log("✅ ContentMint:", contentMintAddress);
    console.log("✅ 支持内容类型:", activeTypes.join(", "));
    console.log("✅ 铸造功能: 已配置");

    console.log("\n🔧 下一步:");
    console.log("1. 测试IPFS上链 (部署者已有充足的TEST代币):");
    console.log(`   npx hardhat run scripts/test/pxa-ipfs-content-upload.js --network ${network.name}`);
    console.log("2. 测试内容铸造:");
    console.log(`   npx hardhat run scripts/test/test-content-mint.js --network ${network.name}`);
    console.log("3. 查看内容系统统计:");
    console.log(`   npx hardhat run scripts/query/content-stats.js --network ${network.name}`);
    
    return deploymentInfo;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("内容系统部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
