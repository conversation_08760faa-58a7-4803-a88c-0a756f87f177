const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("📊 检查BSC本地链跨PXA本地链状态");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 获取当前账户
    const [deployer] = await ethers.getSigners();
    console.log("查询账户:", deployer.address);
    
    // 读取跨链桥配置
    const deploymentsDir = path.join(__dirname, "../../deployments");
    const configPath = path.join(deploymentsDir, "bsc-pxa-local-bridge-config.json");
    
    let bridgeConfig;
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        console.log("💡 请先运行: npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost");
        process.exit(1);
    }
    
    // 连接合约
    const BridgeReceiver = await ethers.getContractFactory("BridgeReceiver");
    const bridgeReceiver = BridgeReceiver.attach(bridgeConfig.pxaLocalChain.bridgeReceiver);
    
    const WrappedToken = await ethers.getContractFactory("WrappedToken");
    const wpatToken = WrappedToken.attach(bridgeConfig.pxaLocalChain.wpatToken);
    
    console.log("\n=== 1. 跨链桥配置状态 ===");
    
    try {
        const totalValidators = await bridgeReceiver.totalValidators();
        const requiredValidators = await bridgeReceiver.requiredValidators();
        const sourceChainId = await bridgeReceiver.sourceChainId();
        const isSupported = await bridgeReceiver.supportedTokens(bridgeConfig.bscLocalChain.patToken);
        const mappedToken = await bridgeReceiver.tokenMapping(bridgeConfig.bscLocalChain.patToken);
        
        console.log("跨链桥配置:");
        console.log("- 总验证者数量:", totalValidators.toString());
        console.log("- 需要验证者数量:", requiredValidators.toString());
        console.log("- 源链ID:", sourceChainId.toString());
        console.log("- PAT代币支持:", isSupported ? "✅ 是" : "❌ 否");
        console.log("- PAT映射地址:", mappedToken);
        console.log("- 映射正确:", mappedToken.toLowerCase() === bridgeConfig.pxaLocalChain.wpatToken.toLowerCase() ? "✅ 是" : "❌ 否");
        
        const configComplete = (
            totalValidators >= requiredValidators &&
            sourceChainId.toString() === bridgeConfig.bscLocalChain.chainId.toString() &&
            isSupported &&
            mappedToken.toLowerCase() === bridgeConfig.pxaLocalChain.wpatToken.toLowerCase()
        );
        
        console.log("配置状态:", configComplete ? "✅ 完整" : "❌ 不完整");
        
    } catch (error) {
        console.error("❌ 检查跨链桥配置失败:", error.message);
    }
    
    console.log("\n=== 2. wPAT代币状态 ===");
    
    try {
        const name = await wpatToken.name();
        const symbol = await wpatToken.symbol();
        const decimals = await wpatToken.decimals();
        const totalSupply = await wpatToken.totalSupply();
        
        console.log("wPAT代币信息:");
        console.log("- 名称:", name);
        console.log("- 符号:", symbol);
        console.log("- 精度:", decimals.toString());
        console.log("- 总供应量:", ethers.formatEther(totalSupply), "wPAT");
        
        // 检查权限
        const BRIDGE_ROLE = await wpatToken.BRIDGE_ROLE();
        const hasBridgeRole = await wpatToken.hasRole(BRIDGE_ROLE, bridgeConfig.pxaLocalChain.bridgeReceiver);
        console.log("- 桥接权限:", hasBridgeRole ? "✅ 正常" : "❌ 缺失");
        
    } catch (error) {
        console.error("❌ 检查wPAT代币状态失败:", error.message);
    }
    
    console.log("\n=== 3. 主要账户余额 ===");
    
    // 主要账户列表
    const accounts = [
        { name: "部署者", address: "******************************************" },
        { name: "BSC部署者", address: "******************************************" },
        { name: "BSC国库", address: "******************************************" },
        { name: "BSC操作员", address: "******************************************" },
        { name: "BSC跨链池", address: "******************************************" }
    ];
    
    console.log("账户wPAT余额:");
    for (const account of accounts) {
        try {
            const balance = await wpatToken.balanceOf(account.address);
            console.log(`- ${account.name}: ${ethers.formatEther(balance)} wPAT`);
        } catch (error) {
            console.log(`- ${account.name}: 查询失败`);
        }
    }
    
    console.log("\n=== 4. 跨链历史记录 ===");
    
    try {
        // 查询最近的跨链事件
        const currentBlock = await deployer.provider.getBlockNumber();
        const fromBlock = Math.max(0, currentBlock - 1000); // 查询最近1000个区块
        
        console.log(`查询区块范围: ${fromBlock} - ${currentBlock}`);
        
        const filter = bridgeReceiver.filters.CrossChainTransferProcessed();
        const events = await bridgeReceiver.queryFilter(filter, fromBlock, currentBlock);
        
        console.log(`找到 ${events.length} 个跨链事件:`);
        
        for (let i = 0; i < events.length; i++) {
            const event = events[i];
            const args = event.args;
            
            console.log(`事件 ${i + 1}:`);
            console.log(`  - 转移ID: ${args.transferId}`);
            console.log(`  - 源代币: ${args.sourceToken}`);
            console.log(`  - 接收者: ${args.recipient}`);
            console.log(`  - 数量: ${ethers.formatEther(args.amount)} PAT`);
            console.log(`  - 区块号: ${event.blockNumber}`);
            console.log(`  - 交易哈希: ${event.transactionHash}`);
        }
        
        if (events.length === 0) {
            console.log("  暂无跨链记录");
        }
        
    } catch (error) {
        console.error("❌ 查询跨链历史失败:", error.message);
    }
    
    console.log("\n=== 5. 验证者状态 ===");
    
    try {
        const VALIDATOR_ROLE = await bridgeReceiver.VALIDATOR_ROLE();
        
        console.log("验证者状态:");
        for (const validator of bridgeConfig.bscLocalChain.validators) {
            const hasRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validator);
            console.log(`- ${validator}: ${hasRole ? "✅ 活跃" : "❌ 未激活"}`);
        }
        
    } catch (error) {
        console.error("❌ 检查验证者状态失败:", error.message);
    }
    
    console.log("\n=== 6. 网络连接状态 ===");
    
    try {
        // 检查PXA链状态
        const pxaBlock = await deployer.provider.getBlockNumber();
        const pxaBalance = await deployer.provider.getBalance(deployer.address);
        
        console.log("PXA本地链状态:");
        console.log("- RPC URL:", bridgeConfig.pxaLocalChain.rpcUrl);
        console.log("- 当前区块:", pxaBlock);
        console.log("- 查询账户余额:", ethers.formatEther(pxaBalance), "PXA");
        console.log("- 连接状态: ✅ 正常");
        
        // 尝试连接BSC链（这里只是显示配置，实际连接需要切换网络）
        console.log("\nBSC本地链配置:");
        console.log("- RPC URL:", bridgeConfig.bscLocalChain.rpcUrl);
        console.log("- 链ID:", bridgeConfig.bscLocalChain.chainId);
        console.log("- TokenBridge:", bridgeConfig.bscLocalChain.tokenBridge);
        console.log("- PAT代币:", bridgeConfig.bscLocalChain.patToken);
        
    } catch (error) {
        console.error("❌ 检查网络状态失败:", error.message);
    }
    
    console.log("\n=== 7. 跨链统计总结 ===");
    
    try {
        const totalSupply = await wpatToken.totalSupply();
        const totalValidators = await bridgeReceiver.totalValidators();
        
        console.log("📊 跨链统计:");
        console.log("- wPAT总铸造量:", ethers.formatEther(totalSupply), "wPAT");
        console.log("- 活跃验证者数:", totalValidators.toString());
        console.log("- 配置状态: ✅ 就绪");
        
        if (totalSupply > 0) {
            console.log("- 跨链状态: ✅ 已有跨链记录");
        } else {
            console.log("- 跨链状态: ⚠️  暂无跨链记录");
        }
        
        console.log("\n💡 使用说明:");
        console.log("1. 配置跨链桥:");
        console.log("   npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost");
        console.log("2. 处理跨链请求:");
        console.log("   npx hardhat run scripts/bridge/process-bsc-local-bridge-request.js --network localhost");
        console.log("3. 检查状态:");
        console.log("   npx hardhat run scripts/bridge/check-bsc-pxa-bridge-status.js --network localhost");
        
    } catch (error) {
        console.error("❌ 生成统计总结失败:", error.message);
    }
}

main()
    .then(() => {
        console.log("\n🎉 BSC本地链跨PXA本地链状态检查完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 检查失败:", error);
        process.exit(1);
    });
