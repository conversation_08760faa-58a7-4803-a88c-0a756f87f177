// 检查跨链桥权限问题
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔍 检查跨链桥权限问题");
    console.log("========================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 检查账户:", deployer.address);
    
    // 加载合约地址
    let bridgeReceiverAddress, wPATAddress;
    
    try {
        const completeDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("complete-deployment-") && file.endsWith(".json"));
        
        if (completeDeploymentFiles.length > 0) {
            const completeFile = path.join(__dirname, "../../deployments", completeDeploymentFiles[0]);
            const completeDeployment = JSON.parse(fs.readFileSync(completeFile, 'utf8'));
            bridgeReceiverAddress = completeDeployment.contracts.bridgeReceiver;
        }
        
        const wpatDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("wpat-deployment-") && file.endsWith(".json"));
        
        if (wpatDeploymentFiles.length > 0) {
            const wpatFile = path.join(__dirname, "../../deployments", wpatDeploymentFiles[0]);
            const wpatDeployment = JSON.parse(fs.readFileSync(wpatFile, 'utf8'));
            wPATAddress = wpatDeployment.contracts.wPAT.address;
        }
        
    } catch (error) {
        console.error("❌ 加载部署文件失败:", error.message);
        process.exit(1);
    }
    
    console.log("📝 合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- wPAT代币:", wPATAddress);
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    const wPAT = await ethers.getContractAt("WrappedToken", wPATAddress);
    
    console.log("\n=== 1. 检查基本状态 ===");
    
    try {
        // 检查合约是否暂停
        const isPaused = await bridgeReceiver.paused();
        console.log("BridgeReceiver暂停状态:", isPaused ? "❌ 已暂停" : "✅ 正常");
        
        // 检查验证者状态
        const isValidator = await bridgeReceiver.validators(deployer.address);
        console.log("验证者状态:", isValidator ? "✅ 已注册" : "❌ 未注册");
        
        // 检查所需验证者数量
        const requiredValidators = await bridgeReceiver.requiredValidators();
        const totalValidators = await bridgeReceiver.totalValidators();
        console.log("所需验证者数量:", requiredValidators.toString());
        console.log("总验证者数量:", totalValidators.toString());
        
    } catch (error) {
        console.error("❌ 检查基本状态失败:", error.message);
    }
    
    console.log("\n=== 2. 检查代币映射 ===");
    
    const sourceToken = "******************************************"; // BSC PAT
    
    try {
        // 检查代币支持状态
        const isSupported = await bridgeReceiver.supportedTokens(sourceToken);
        console.log("BSC PAT支持状态:", isSupported ? "✅ 已支持" : "❌ 未支持");
        
        // 检查代币映射
        const mappedToken = await bridgeReceiver.tokenMapping(sourceToken);
        console.log("映射目标地址:", mappedToken);
        console.log("映射正确性:", mappedToken.toLowerCase() === wPATAddress.toLowerCase() ? "✅ 正确" : "❌ 错误");
        
    } catch (error) {
        console.error("❌ 检查代币映射失败:", error.message);
    }
    
    console.log("\n=== 3. 检查关键权限 ===");
    
    try {
        // 检查BridgeReceiver是否有wPAT的BRIDGE_ROLE权限
        const BRIDGE_ROLE = await wPAT.BRIDGE_ROLE();
        const hasBridgeRole = await wPAT.hasRole(BRIDGE_ROLE, bridgeReceiverAddress);
        
        console.log("🔑 关键权限检查:");
        console.log("- BRIDGE_ROLE:", BRIDGE_ROLE);
        console.log("- BridgeReceiver有BRIDGE_ROLE:", hasBridgeRole ? "✅ 有权限" : "❌ 无权限");
        
        if (!hasBridgeRole) {
            console.log("\n🚨 发现问题！");
            console.log("BridgeReceiver合约没有wPAT的BRIDGE_ROLE权限");
            console.log("这就是跨链失败的原因！");
            
            // 检查当前账户是否有权限授予BRIDGE_ROLE
            const DEFAULT_ADMIN_ROLE = await wPAT.DEFAULT_ADMIN_ROLE();
            const hasAdminRole = await wPAT.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
            
            console.log("\n💡 解决方案:");
            console.log("- 当前账户有管理员权限:", hasAdminRole ? "✅ 有权限" : "❌ 无权限");
            
            if (hasAdminRole) {
                console.log("✅ 可以授予BRIDGE_ROLE权限");
                console.log("运行命令: npx hardhat run scripts/bridge/grant-bridge-role.js --network localhost");
            } else {
                console.log("❌ 需要使用有管理员权限的账户");
            }
        } else {
            console.log("✅ 权限正常，问题可能在其他地方");
        }
        
    } catch (error) {
        console.error("❌ 检查权限失败:", error.message);
    }
    
    console.log("\n=== 4. 检查转移状态 ===");
    
    try {
        // 生成转移ID
        const bscBridgeRequest = {
            sourceToken: "******************************************",
            recipient: "******************************************",
            amount: ethers.parseEther("10000"),
            blockNumber: 2095,
            txHash: "0xe63d17d9347231f2a05548a610962c215e69ea2ee42b9b16d2449cb8e455fb5f",
            sourceChainId: 97
        };
        
        const transferId = ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
                ["address", "address", "uint256", "uint256", "bytes32", "uint256"],
                [
                    bscBridgeRequest.sourceToken,
                    bscBridgeRequest.recipient,
                    bscBridgeRequest.amount,
                    bscBridgeRequest.blockNumber,
                    bscBridgeRequest.txHash,
                    bscBridgeRequest.sourceChainId
                ]
            )
        );
        
        console.log("转移ID:", transferId);
        
        // 检查转移状态
        const transfer = await bridgeReceiver.transfers(transferId);
        const transferExists = transfer.transferId !== "0x0000000000000000000000000000000000000000000000000000000000000000";
        
        console.log("转移记录存在:", transferExists ? "✅ 是" : "❌ 否");
        
        if (transferExists) {
            console.log("转移详情:");
            console.log("- 完成状态:", transfer.completed ? "✅ 已完成" : "❌ 未完成");
            console.log("- 验证者签名数:", transfer.validatorCount.toString());
            console.log("- 所需签名数:", (await bridgeReceiver.requiredValidators()).toString());
        }
        
    } catch (error) {
        console.error("❌ 检查转移状态失败:", error.message);
    }
    
    console.log("\n=== 总结 ===");
    console.log("如果BridgeReceiver没有wPAT的BRIDGE_ROLE权限，");
    console.log("需要先授予权限，然后重新尝试跨链操作。");
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}
