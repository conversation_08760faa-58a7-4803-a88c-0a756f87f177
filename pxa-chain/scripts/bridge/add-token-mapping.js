const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔗 添加BSC PAT → PXA wPAT代币映射");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者账户:", deployer.address);
    
    // 读取跨链桥配置
    const configPath = path.join(__dirname, "../../deployments/bsc-pxa-local-bridge-config.json");
    let bridgeConfig;
    
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        process.exit(1);
    }
    
    const bridgeReceiverAddress = bridgeConfig.pxaLocalChain.bridgeReceiver;
    const wpatTokenAddress = bridgeConfig.pxaLocalChain.wpatToken;
    
    // BSC PAT代币地址（从跨链记录中获取）
    const bscPATAddress = "******************************************";
    
    console.log("合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- BSC PAT代币:", bscPATAddress);
    console.log("- PXA wPAT代币:", wpatTokenAddress);
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    
    console.log("\n=== 1. 检查当前代币映射状态 ===");
    
    // 检查代币是否已支持
    const isTokenSupported = await bridgeReceiver.supportedTokens(bscPATAddress);
    console.log("BSC PAT代币支持状态:", isTokenSupported ? "✅ 已支持" : "❌ 不支持");
    
    // 检查代币映射
    const currentMapping = await bridgeReceiver.tokenMapping(bscPATAddress);
    console.log("当前映射地址:", currentMapping);
    console.log("映射是否正确:", currentMapping.toLowerCase() === wpatTokenAddress.toLowerCase() ? "✅ 正确" : "❌ 错误");
    
    if (isTokenSupported && currentMapping.toLowerCase() === wpatTokenAddress.toLowerCase()) {
        console.log("\n✅ 代币映射已正确配置，无需修改");
        return;
    }
    
    console.log("\n=== 2. 添加代币映射 ===");
    
    try {
        console.log("正在添加代币映射...");
        console.log("- 源代币 (BSC PAT):", bscPATAddress);
        console.log("- 目标代币 (PXA wPAT):", wpatTokenAddress);
        
        const addMappingTx = await bridgeReceiver.addTokenMapping(
            bscPATAddress,
            wpatTokenAddress
        );
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", addMappingTx.hash);
        
        const receipt = await addMappingTx.wait();
        
        if (receipt.status === 1) {
            console.log("✅ 代币映射添加成功!");
            console.log("- Gas使用:", receipt.gasUsed.toString());
            console.log("- 区块号:", receipt.blockNumber);
        } else {
            console.error("❌ 交易失败，状态:", receipt.status);
            process.exit(1);
        }
        
    } catch (error) {
        console.error("❌ 添加代币映射失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 3. 验证映射结果 ===");
    
    // 重新检查代币支持状态
    const finalIsTokenSupported = await bridgeReceiver.supportedTokens(bscPATAddress);
    console.log("最终代币支持状态:", finalIsTokenSupported ? "✅ 已支持" : "❌ 不支持");
    
    // 重新检查代币映射
    const finalMapping = await bridgeReceiver.tokenMapping(bscPATAddress);
    console.log("最终映射地址:", finalMapping);
    console.log("最终映射是否正确:", finalMapping.toLowerCase() === wpatTokenAddress.toLowerCase() ? "✅ 正确" : "❌ 错误");
    
    if (finalIsTokenSupported && finalMapping.toLowerCase() === wpatTokenAddress.toLowerCase()) {
        console.log("\n🎉 代币映射配置完成!");
        console.log("=====================================");
        console.log("✅ BSC PAT代币已支持跨链");
        console.log("✅ 映射到PXA wPAT代币");
        console.log("✅ 现在可以处理跨链请求");
        
        console.log("\n📋 下一步操作:");
        console.log("npx hardhat run scripts/bridge/process-bsc-local-with-validator.js --network localhost");
    } else {
        console.error("❌ 代币映射配置可能失败，请检查");
    }
}

main()
    .then(() => {
        console.log("\n🎉 代币映射配置完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 配置失败:", error);
        process.exit(1);
    });
