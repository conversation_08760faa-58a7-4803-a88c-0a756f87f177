// 修复验证者计数问题
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔧 修复验证者计数问题");
    console.log("========================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 管理员账户:", deployer.address);
    
    // 加载合约地址
    let bridgeReceiverAddress;
    
    try {
        const completeDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("complete-deployment-") && file.endsWith(".json"));
        
        if (completeDeploymentFiles.length > 0) {
            const completeFile = path.join(__dirname, "../../deployments", completeDeploymentFiles[0]);
            const completeDeployment = JSON.parse(fs.readFileSync(completeFile, 'utf8'));
            bridgeReceiverAddress = completeDeployment.contracts.bridgeReceiver;
        }
        
    } catch (error) {
        console.error("❌ 加载部署文件失败:", error.message);
        process.exit(1);
    }
    
    console.log("📝 BridgeReceiver地址:", bridgeReceiverAddress);
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    
    console.log("\n=== 1. 检查当前状态 ===");
    
    try {
        const validatorInfo = await bridgeReceiver.validators(deployer.address);
        const totalValidators = await bridgeReceiver.totalValidators();
        const requiredValidators = await bridgeReceiver.requiredValidators();
        
        console.log("验证者激活状态:", validatorInfo.isActive ? "✅ 已激活" : "❌ 未激活");
        console.log("当前验证者数量:", totalValidators.toString());
        console.log("所需验证者数量:", requiredValidators.toString());
        
        if (validatorInfo.isActive && totalValidators.toString() === "0") {
            console.log("🚨 发现问题: 验证者已激活但计数为0");
            console.log("💡 需要通过addValidator函数正确添加验证者");
        }
        
    } catch (error) {
        console.error("❌ 检查状态失败:", error.message);
    }
    
    console.log("\n=== 2. 添加验证者到列表 ===");
    
    try {
        console.log("🚀 正确添加验证者:", deployer.address);
        
        const addValidatorTx = await bridgeReceiver.addValidator(deployer.address);
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", addValidatorTx.hash);
        
        const receipt = await addValidatorTx.wait();
        console.log("✅ 验证者添加成功!");
        console.log("- 区块号:", receipt.blockNumber);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.log("❌ 添加验证者失败:", error.message);
        
        if (error.message.includes("Validator already active")) {
            console.log("💡 验证者已经在列表中，这很奇怪...");
            
            // 尝试降低所需验证者数量
            console.log("\n=== 3. 尝试降低所需验证者数量 ===");
            try {
                console.log("🔧 将所需验证者数量设置为1...");
                
                const setRequiredTx = await bridgeReceiver.setRequiredValidators(1);
                await setRequiredTx.wait();
                
                console.log("✅ 所需验证者数量已设置为1");
                
            } catch (setError) {
                console.log("❌ 设置所需验证者数量失败:", setError.message);
            }
        }
    }
    
    console.log("\n=== 4. 最终验证 ===");
    
    try {
        const validatorInfo = await bridgeReceiver.validators(deployer.address);
        const totalValidators = await bridgeReceiver.totalValidators();
        const requiredValidators = await bridgeReceiver.requiredValidators();
        
        console.log("最终状态:");
        console.log("- 验证者激活:", validatorInfo.isActive ? "✅ 已激活" : "❌ 未激活");
        console.log("- 当前验证者数量:", totalValidators.toString());
        console.log("- 所需验证者数量:", requiredValidators.toString());
        console.log("- 验证者数量足够:", totalValidators >= requiredValidators ? "✅ 足够" : "❌ 不足");
        
        if (validatorInfo.isActive && totalValidators >= requiredValidators) {
            console.log("\n🎉 验证者问题已修复！");
            console.log("=====================================");
            console.log("✅ 验证者已正确添加到列表");
            console.log("✅ 验证者数量满足要求");
            console.log("✅ 现在可以处理跨链请求了");
            
            console.log("\n🚀 下一步:");
            console.log("运行跨链处理脚本:");
            console.log("npx hardhat run scripts/bridge/process-bsc-bridge-request.js --network localhost");
        } else {
            console.log("❌ 验证者问题未完全解决");
            
            if (totalValidators.toString() === "0") {
                console.log("💡 建议: 检查BridgeReceiver合约的addValidator函数实现");
                console.log("💡 或者尝试重新部署BridgeReceiver合约");
            }
        }
        
    } catch (error) {
        console.error("❌ 最终验证失败:", error.message);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}
