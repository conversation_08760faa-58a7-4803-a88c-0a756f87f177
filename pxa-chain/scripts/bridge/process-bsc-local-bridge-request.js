const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 处理BSC本地链跨链请求");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 获取当前账户
    const [deployer] = await ethers.getSigners();
    console.log("验证者账户:", deployer.address);
    console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "PXA");
    
    // 读取跨链桥配置
    const deploymentsDir = path.join(__dirname, "../../deployments");
    const configPath = path.join(deploymentsDir, "bsc-pxa-local-bridge-config.json");
    
    let bridgeConfig;
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        console.log("💡 请先运行: npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost");
        process.exit(1);
    }
    
    // 连接合约
    const BridgeReceiver = await ethers.getContractFactory("BridgeReceiver");
    const bridgeReceiver = BridgeReceiver.attach(bridgeConfig.pxaLocalChain.bridgeReceiver);
    
    const WrappedToken = await ethers.getContractFactory("WrappedToken");
    const wpatToken = WrappedToken.attach(bridgeConfig.pxaLocalChain.wpatToken);
    
    console.log("合约地址:");
    console.log("- BridgeReceiver:", bridgeConfig.pxaLocalChain.bridgeReceiver);
    console.log("- wPAT代币:", bridgeConfig.pxaLocalChain.wpatToken);
    
    console.log("\n=== 1. 使用BSC跨链记录中的真实数据 ===");
    
    // 从你的BSC跨链记录中获取的真实数据
    const bscCrossChainRequest = {
        sourceToken: "******************************************", // BSC PAT地址
        recipient: "******************************************",   // 接收地址（部署者）
        amount: ethers.parseEther("10000"),                        // 10000 PAT
        blockNumber: 2095,                                         // BSC区块号
        txHash: "0xe63d17d9347231f2a05548a610962c215e69ea2ee42b9b16d2449cb8e455fb5f" // BSC交易哈希
    };
    
    console.log("BSC跨链请求信息:");
    console.log("- 源代币:", bscCrossChainRequest.sourceToken);
    console.log("- 接收者:", bscCrossChainRequest.recipient);
    console.log("- 数量:", ethers.formatEther(bscCrossChainRequest.amount), "PAT");
    console.log("- BSC区块号:", bscCrossChainRequest.blockNumber);
    console.log("- BSC交易哈希:", bscCrossChainRequest.txHash);
    
    console.log("\n=== 2. 检查接收者余额（处理前） ===");
    
    const balanceBefore = await wpatToken.balanceOf(bscCrossChainRequest.recipient);
    console.log("接收者当前wPAT余额:", ethers.formatEther(balanceBefore), "wPAT");
    
    console.log("\n=== 3. 生成转移ID并检查状态 ===");

    // 手动生成转移ID（与合约中的逻辑一致）
    const sourceChainId = await bridgeReceiver.sourceChainId();
    const transferId = ethers.keccak256(
        ethers.AbiCoder.defaultAbiCoder().encode(
            ["address", "address", "uint256", "uint256", "bytes32", "uint256"],
            [
                bscCrossChainRequest.sourceToken,
                bscCrossChainRequest.recipient,
                bscCrossChainRequest.amount,
                bscCrossChainRequest.blockNumber,
                bscCrossChainRequest.txHash,
                sourceChainId
            ]
        )
    );

    console.log("转移ID:", transferId);

    // 检查转移是否已存在
    try {
        const transfer = await bridgeReceiver.transfers(transferId);
        const isProcessed = transfer.completed;
        console.log("转移是否已处理:", isProcessed ? "✅ 已处理" : "❌ 未处理");

        if (isProcessed) {
            console.log("⚠️  此转移已经被处理过了");
            console.log("💡 如果需要重新测试，请使用不同的交易哈希或区块号");

            // 显示当前余额
            const currentBalance = await wpatToken.balanceOf(bscCrossChainRequest.recipient);
            console.log("当前wPAT余额:", ethers.formatEther(currentBalance), "wPAT");
            return;
        }

        // 检查转移是否已存在但未完成
        if (transfer.transferId !== "0x0000000000000000000000000000000000000000000000000000000000000000") {
            console.log("⚠️  此转移已存在但未完成");
            console.log("- 验证者确认数:", transfer.validatorCount.toString());
            console.log("- 需要确认数:", await bridgeReceiver.requiredValidators());
            console.log("💡 可能需要更多验证者签名");
        }
    } catch (error) {
        console.log("检查转移状态时出错:", error.message);
    }
    
    console.log("\n=== 4. 检查验证者权限 ===");
    
    try {
        const VALIDATOR_ROLE = await bridgeReceiver.VALIDATOR_ROLE();
        const hasValidatorRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, deployer.address);
        
        console.log("验证者权限检查:");
        console.log("- 当前账户:", deployer.address);
        console.log("- 是否为验证者:", hasValidatorRole ? "✅ 是" : "❌ 否");
        
        if (!hasValidatorRole) {
            console.log("❌ 当前账户不是验证者，无法处理跨链请求");
            console.log("💡 请确保当前账户在BSC验证者列表中");
            return;
        }
        
    } catch (error) {
        console.error("❌ 检查验证者权限失败:", error.message);
        return;
    }
    
    console.log("\n=== 5. 检查代币映射 ===");
    
    try {
        const mappedToken = await bridgeReceiver.tokenMapping(bscCrossChainRequest.sourceToken);
        const isSupported = await bridgeReceiver.supportedTokens(bscCrossChainRequest.sourceToken);
        
        console.log("代币映射检查:");
        console.log("- BSC PAT:", bscCrossChainRequest.sourceToken);
        console.log("- 映射到:", mappedToken);
        console.log("- 预期wPAT:", bridgeConfig.pxaLocalChain.wpatToken);
        console.log("- 映射正确:", mappedToken.toLowerCase() === bridgeConfig.pxaLocalChain.wpatToken.toLowerCase() ? "✅ 是" : "❌ 否");
        console.log("- 代币支持:", isSupported ? "✅ 是" : "❌ 否");
        
        if (!isSupported || mappedToken.toLowerCase() !== bridgeConfig.pxaLocalChain.wpatToken.toLowerCase()) {
            console.log("❌ 代币映射配置不正确");
            console.log("💡 请先运行配置脚本修复映射");
            return;
        }
        
    } catch (error) {
        console.error("❌ 检查代币映射失败:", error.message);
        return;
    }
    
    console.log("\n=== 6. 执行跨链处理 ===");

    try {
        console.log("🚀 提交跨链转移...");

        // 估算Gas费用
        const gasEstimate = await bridgeReceiver.submitCrossChainTransfer.estimateGas(
            bscCrossChainRequest.sourceToken,
            bscCrossChainRequest.recipient,
            bscCrossChainRequest.amount,
            bscCrossChainRequest.blockNumber,
            bscCrossChainRequest.txHash
        );

        console.log("预估Gas费用:", gasEstimate.toString());

        const processTx = await bridgeReceiver.submitCrossChainTransfer(
            bscCrossChainRequest.sourceToken,
            bscCrossChainRequest.recipient,
            bscCrossChainRequest.amount,
            bscCrossChainRequest.blockNumber,
            bscCrossChainRequest.txHash,
            {
                gasLimit: gasEstimate * 120n / 100n // 增加20%的Gas缓冲
            }
        );
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", processTx.hash);
        
        const receipt = await processTx.wait();
        
        if (receipt.status === 1) {
            console.log("✅ 跨链处理成功!");
            console.log("- Gas使用:", receipt.gasUsed.toString());
            console.log("- 区块号:", receipt.blockNumber);
            console.log("- Gas费用:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "PXA");
            
            // 解析事件
            if (receipt.logs && receipt.logs.length > 0) {
                console.log("- 事件数量:", receipt.logs.length);
                
                for (const log of receipt.logs) {
                    try {
                        const parsedLog = bridgeReceiver.interface.parseLog(log);
                        if (parsedLog && (parsedLog.name === "CrossChainTransferReceived" || parsedLog.name === "TransferCompleted" || parsedLog.name === "ValidatorSigned")) {
                            console.log(`${parsedLog.name} 事件:`);
                            if (parsedLog.name === "CrossChainTransferReceived") {
                                console.log("  - 转移ID:", parsedLog.args.transferId);
                                console.log("  - 源代币:", parsedLog.args.sourceToken);
                                console.log("  - 目标代币:", parsedLog.args.targetToken);
                                console.log("  - 接收者:", parsedLog.args.recipient);
                                console.log("  - 数量:", ethers.formatEther(parsedLog.args.amount), "PAT");
                            } else if (parsedLog.name === "TransferCompleted") {
                                console.log("  - 转移ID:", parsedLog.args.transferId);
                                console.log("  - 接收者:", parsedLog.args.recipient);
                                console.log("  - 净数量:", ethers.formatEther(parsedLog.args.netAmount), "wPAT");
                                console.log("  - 手续费:", ethers.formatEther(parsedLog.args.fee), "wPAT");
                            } else if (parsedLog.name === "ValidatorSigned") {
                                console.log("  - 转移ID:", parsedLog.args.transferId);
                                console.log("  - 验证者:", parsedLog.args.validator);
                                console.log("  - 签名数:", parsedLog.args.signatureCount.toString());
                            }
                        }
                    } catch (error) {
                        // 忽略解析错误
                    }
                }
            }
            
        } else {
            console.log("❌ 交易失败，状态:", receipt.status);
            return;
        }
        
    } catch (error) {
        console.error("❌ 跨链处理失败:", error.message);
        
        if (error.message.includes("Transaction already processed")) {
            console.log("💡 错误原因: 此交易已经被处理过");
        } else if (error.message.includes("Not a validator")) {
            console.log("💡 错误原因: 不是验证者");
        } else if (error.message.includes("Token not supported")) {
            console.log("💡 错误原因: 代币不支持");
        } else {
            console.log("💡 其他错误，请检查合约状态和参数");
        }
        return;
    }
    
    console.log("\n=== 7. 验证铸造结果 ===");
    
    // 等待状态更新
    console.log("⏳ 等待状态更新...");
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    try {
        const balanceAfter = await wpatToken.balanceOf(bscCrossChainRequest.recipient);
        const balanceChange = balanceAfter - balanceBefore;
        
        console.log("接收者wPAT余额变化:");
        console.log("- 处理前:", ethers.formatEther(balanceBefore), "wPAT");
        console.log("- 处理后:", ethers.formatEther(balanceAfter), "wPAT");
        console.log("- 增加数量:", ethers.formatEther(balanceChange), "wPAT");
        
        if (balanceChange > 0) {
            console.log("🎉 跨链铸造成功完成!");
            console.log(`✅ ${ethers.formatEther(bscCrossChainRequest.amount)} PAT 已成功从BSC跨链到PXA并铸造为 wPAT`);
            
            // 检查总供应量
            const totalSupply = await wpatToken.totalSupply();
            console.log("wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
            
        } else {
            console.log("⚠️  余额没有变化，请检查铸造逻辑");
        }
        
    } catch (error) {
        console.error("❌ 验证铸造结果失败:", error.message);
    }
    
    console.log("\n=== 8. 跨链状态总结 ===");
    
    try {
        const totalSupply = await wpatToken.totalSupply();
        const recipientBalance = await wpatToken.balanceOf(bscCrossChainRequest.recipient);
        
        console.log("PXA链状态:");
        console.log("- wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
        console.log("- 接收者余额:", ethers.formatEther(recipientBalance), "wPAT");
        
        console.log("\nBSC链状态（根据记录）:");
        console.log("- TokenBridge锁定: 10000+ PAT");
        console.log("- 跨链池剩余: 99990000 PAT");
        
        console.log("\n✅ BSC本地链 → PXA本地链跨链成功!");
        
    } catch (error) {
        console.log("无法获取状态总结");
    }
}

main()
    .then(() => {
        console.log("\n🎉 BSC本地链跨链请求处理完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 处理失败:", error);
        process.exit(1);
    });
