// 处理来自BSC的跨链请求
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🌉 处理来自BSC的跨链请求");
    console.log("================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 验证者地址:", deployer.address);
    console.log("⏰ 时间:", new Date().toISOString());
    
    // BSC跨链交易信息（从你的执行记录中获取）
    const bscBridgeRequest = {
        txHash: "0xe63d17d9347231f2a05548a610962c215e69ea2ee42b9b16d2449cb8e455fb5f",
        blockNumber: 2095,
        sourceToken: "******************************************", // BSC PAT地址
        recipient: "******************************************", // 接收者（你的地址）
        amount: ethers.parseEther("10000"), // 10,000 PAT
        sourceChainId: 97 // BSC链ID
    };
    
    console.log("🎯 BSC跨链请求信息:");
    console.log("- 交易哈希:", bscBridgeRequest.txHash);
    console.log("- 区块号:", bscBridgeRequest.blockNumber);
    console.log("- 源代币:", bscBridgeRequest.sourceToken);
    console.log("- 接收者:", bscBridgeRequest.recipient);
    console.log("- 数量:", ethers.formatEther(bscBridgeRequest.amount), "PAT");
    console.log("- 源链ID:", bscBridgeRequest.sourceChainId);
    
    // 从部署记录加载合约地址
    let bridgeReceiverAddress, wPATAddress;
    
    try {
        // 加载完整部署记录（包含BridgeReceiver）
        const completeDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("complete-deployment-") && file.endsWith(".json"));
        
        if (completeDeploymentFiles.length > 0) {
            const completeFile = path.join(__dirname, "../../deployments", completeDeploymentFiles[0]);
            const completeDeployment = JSON.parse(fs.readFileSync(completeFile, 'utf8'));
            bridgeReceiverAddress = completeDeployment.contracts.bridgeReceiver;
        }
        
        // 加载wPAT部署记录
        const wpatDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("wpat-deployment-") && file.endsWith(".json"));
        
        if (wpatDeploymentFiles.length > 0) {
            const wpatFile = path.join(__dirname, "../../deployments", wpatDeploymentFiles[0]);
            const wpatDeployment = JSON.parse(fs.readFileSync(wpatFile, 'utf8'));
            wPATAddress = wpatDeployment.contracts.wPAT.address;
        }
        
        console.log("✅ 已加载部署信息");
        
    } catch (error) {
        console.error("❌ 加载部署文件失败:", error.message);
        process.exit(1);
    }
    
    console.log("📝 PXA链合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- wPAT代币:", wPATAddress);
    
    if (!bridgeReceiverAddress || !wPATAddress) {
        console.error("❌ 缺少必要的合约地址");
        process.exit(1);
    }
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    const wPAT = await ethers.getContractAt("WrappedToken", wPATAddress);
    
    console.log("\n=== 1. 检查验证者状态 ===");
    
    try {
        // 检查是否是验证者
        const isValidator = await bridgeReceiver.validators(deployer.address);
        console.log("验证者状态:", isValidator ? "✅ 已注册" : "❌ 未注册");
        
        if (!isValidator) {
            console.log("⚠️  当前账户不是验证者，尝试注册...");
            
            // 注册为验证者（如果需要）
            try {
                const registerTx = await bridgeReceiver.addValidator(deployer.address);
                await registerTx.wait();
                console.log("✅ 验证者注册成功");
            } catch (error) {
                console.log("❌ 验证者注册失败:", error.message);
                console.log("💡 可能需要管理员权限或使用其他方法");
            }
        }
        
    } catch (error) {
        console.log("⚠️  无法检查验证者状态:", error.message);
    }
    
    console.log("\n=== 2. 生成转移ID ===");
    
    // 生成转移ID（与BridgeReceiver合约中的逻辑一致）
    const transferId = ethers.keccak256(
        ethers.AbiCoder.defaultAbiCoder().encode(
            ["address", "address", "uint256", "uint256", "bytes32", "uint256"],
            [
                bscBridgeRequest.sourceToken,
                bscBridgeRequest.recipient,
                bscBridgeRequest.amount,
                bscBridgeRequest.blockNumber,
                bscBridgeRequest.txHash,
                bscBridgeRequest.sourceChainId
            ]
        )
    );
    
    console.log("转移ID:", transferId);
    
    console.log("\n=== 3. 检查转移状态 ===");
    
    try {
        // 检查转移是否已存在
        const transfer = await bridgeReceiver.transfers(transferId);
        const isCompleted = transfer.completed;
        
        console.log("转移状态:", isCompleted ? "✅ 已完成" : "❌ 未完成");
        
        if (isCompleted) {
            console.log("🎉 此跨链请求已经处理完成！");
            
            // 检查wPAT余额
            const wPATBalance = await wPAT.balanceOf(bscBridgeRequest.recipient);
            console.log("接收者wPAT余额:", ethers.formatEther(wPATBalance), "wPAT");
            return;
        }
        
    } catch (error) {
        console.log("⚠️  无法检查转移状态:", error.message);
    }
    
    console.log("\n=== 4. 提交跨链转移 ===");
    
    try {
        console.log("🚀 提交跨链转移请求...");
        
        const submitTx = await bridgeReceiver.submitCrossChainTransfer(
            bscBridgeRequest.sourceToken,
            bscBridgeRequest.recipient,
            bscBridgeRequest.amount,
            bscBridgeRequest.blockNumber,
            bscBridgeRequest.txHash
        );
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", submitTx.hash);
        
        const receipt = await submitTx.wait();
        console.log("✅ 跨链转移提交成功!");
        console.log("- 区块号:", receipt.blockNumber);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 解析事件
        const events = receipt.logs;
        console.log("📋 事件数量:", events.length);
        
        for (const event of events) {
            try {
                const parsedEvent = bridgeReceiver.interface.parseLog(event);
                console.log("事件:", parsedEvent.name, parsedEvent.args);
            } catch (e) {
                // 忽略无法解析的事件
            }
        }
        
    } catch (error) {
        console.error("❌ 提交跨链转移失败:", error.message);
        
        if (error.message.includes("Already signed")) {
            console.log("💡 此转移已经被当前验证者签名过了");
        } else if (error.message.includes("Not a validator")) {
            console.log("💡 当前账户不是验证者，无法提交转移");
        }
        
        // 尝试直接铸造wPAT（如果有权限）
        console.log("\n=== 5. 尝试直接铸造wPAT ===");
        await tryDirectMint(wPAT, bscBridgeRequest);
        return;
    }
    
    console.log("\n=== 5. 验证结果 ===");
    
    // 等待一下让交易生效
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
        // 检查wPAT余额
        const wPATBalance = await wPAT.balanceOf(bscBridgeRequest.recipient);
        console.log("接收者wPAT余额:", ethers.formatEther(wPATBalance), "wPAT");
        
        const totalSupply = await wPAT.totalSupply();
        console.log("wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
        
        if (wPATBalance > 0) {
            console.log("\n🎉 跨链处理成功！");
            console.log("✅ BSC → PXA 跨链已完成");
            console.log("✅ 收到wPAT:", ethers.formatEther(wPATBalance));
            console.log("✅ 双链系统运行正常");
        } else {
            console.log("\n⚠️  wPAT余额仍为0，可能需要更多验证者签名");
            console.log("💡 检查所需验证者数量和当前签名数量");
        }
        
    } catch (error) {
        console.error("❌ 验证结果失败:", error.message);
    }
}

// 尝试直接铸造wPAT（如果有权限）
async function tryDirectMint(wPAT, bridgeRequest) {
    console.log("尝试直接铸造wPAT...");
    
    try {
        // 检查是否有铸造权限
        const MINTER_ROLE = await wPAT.MINTER_ROLE();
        const [deployer] = await ethers.getSigners();
        const hasMinterRole = await wPAT.hasRole(MINTER_ROLE, deployer.address);
        
        console.log("铸造权限:", hasMinterRole ? "✅ 有权限" : "❌ 无权限");
        
        if (hasMinterRole) {
            const mintTx = await wPAT.mint(bridgeRequest.recipient, bridgeRequest.amount);
            await mintTx.wait();
            
            console.log("✅ 直接铸造成功!");
            
            const newBalance = await wPAT.balanceOf(bridgeRequest.recipient);
            console.log("新余额:", ethers.formatEther(newBalance), "wPAT");
        } else {
            console.log("💡 无铸造权限，需要通过正常的跨链流程");
        }
        
    } catch (error) {
        console.log("❌ 直接铸造失败:", error.message);
    }
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}

module.exports = main;
