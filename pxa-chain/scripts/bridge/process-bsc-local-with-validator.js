const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 使用BSC验证者处理跨链请求");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 使用验证者列表中的第一个验证者私钥
    // 验证者地址: ******************************************
    const VALIDATOR_PRIVATE_KEY = "6a1754654caf01700ca97030350cac87b305231768dd9e0f06413029761694af";
    const provider = ethers.provider;
    const validatorWallet = new ethers.Wallet(VALIDATOR_PRIVATE_KEY, provider);
    
    console.log("验证者账户:", validatorWallet.address);
    
    // 检查账户余额
    const balance = await provider.getBalance(validatorWallet.address);
    console.log("账户余额:", ethers.formatEther(balance), "PXA");
    
    // 如果余额不足，从默认账户转一些PXA
    if (balance < ethers.parseEther("0.1")) {
        console.log("💰 验证者账户余额不足，正在转账...");
        const [defaultAccount] = await ethers.getSigners();
        
        const transferTx = await defaultAccount.sendTransaction({
            to: validatorWallet.address,
            value: ethers.parseEther("1.0") // 转1个PXA
        });
        await transferTx.wait();
        
        const newBalance = await provider.getBalance(validatorWallet.address);
        console.log("✅ 转账成功，新余额:", ethers.formatEther(newBalance), "PXA");
    }
    
    // 读取跨链桥配置
    const configPath = path.join(__dirname, "../../deployments/bsc-pxa-local-bridge-config.json");
    let bridgeConfig;
    
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        process.exit(1);
    }
    
    const bridgeReceiverAddress = bridgeConfig.pxaLocalChain.bridgeReceiver;
    const wpatTokenAddress = bridgeConfig.pxaLocalChain.wpatToken;
    
    console.log("合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- wPAT代币:", wpatTokenAddress);
    
    // 连接合约（使用验证者钱包）
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress, validatorWallet);
    const wpatToken = await ethers.getContractAt("WrappedToken", wpatTokenAddress);
    
    console.log("\n=== 1. 使用BSC跨链记录中的真实数据 ===");
    
    // 使用真实的跨链数据（从BSC跨链记录中获取）
    const crossChainData = {
        sourceToken: "******************************************", // BSC PAT地址
        recipient: "******************************************", // 接收者地址
        amount: ethers.parseEther("10000"), // 10000 PAT
        sourceChainId: 97, // BSC链ID
        sourceTxHash: "0x4b7c181f7b83c7bf7f2e2ee32b7e5881a485cb6136e03e960c6dbebfbdbf64b9" // BSC交易哈希
    };
    
    console.log("BSC跨链请求信息:");
    console.log("- 源代币:", crossChainData.sourceToken);
    console.log("- 接收者:", crossChainData.recipient);
    console.log("- 数量:", ethers.formatEther(crossChainData.amount), "PAT");
    console.log("- 源链ID:", crossChainData.sourceChainId);
    console.log("- BSC交易哈希:", crossChainData.sourceTxHash);
    
    console.log("\n=== 2. 检查接收者余额（处理前） ===");
    
    const recipientBalanceBefore = await wpatToken.balanceOf(crossChainData.recipient);
    console.log("接收者当前wPAT余额:", ethers.formatEther(recipientBalanceBefore), "wPAT");
    
    console.log("\n=== 3. 生成转移ID并检查状态 ===");
    
    // 生成转移ID
    const transferId = ethers.keccak256(
        ethers.AbiCoder.defaultAbiCoder().encode(
            ["address", "address", "uint256", "uint256", "bytes32"],
            [
                crossChainData.sourceToken,
                crossChainData.recipient,
                crossChainData.amount,
                crossChainData.sourceChainId,
                crossChainData.sourceTxHash
            ]
        )
    );
    
    console.log("转移ID:", transferId);
    
    // 检查是否已处理（尝试两种方法）
    let isProcessed = false;
    try {
        // 尝试简化版本的方法
        isProcessed = await bridgeReceiver.processedTransfers(transferId);
        console.log("转移是否已处理 (简化版):", isProcessed ? "✅ 已处理" : "❌ 未处理");
    } catch (error) {
        try {
            // 尝试复杂版本的方法
            const transferDetails = await bridgeReceiver.getTransferDetails(transferId);
            isProcessed = transferDetails.completed;
            console.log("转移是否已处理 (复杂版):", isProcessed ? "✅ 已处理" : "❌ 未处理");
        } catch (error2) {
            console.log("转移状态:", "❌ 未找到记录（新转移）");
        }
    }

    if (isProcessed) {
        console.log("⚠️ 此跨链请求已经处理过了");
        return;
    }
    
    console.log("\n=== 4. 检查验证者权限 ===");
    
    // 检查验证者权限
    const totalValidators = await bridgeReceiver.totalValidators();
    const requiredValidators = await bridgeReceiver.requiredValidators();
    
    console.log("验证者权限检查:");
    console.log("- 当前账户:", validatorWallet.address);
    console.log("- 总验证者数量:", totalValidators.toString());
    console.log("- 需要验证者数量:", requiredValidators.toString());
    
    // 检查是否为验证者
    let isValidator = false;
    try {
        // 尝试使用hasRole方法检查验证者权限
        const VALIDATOR_ROLE = ethers.keccak256(ethers.toUtf8Bytes("VALIDATOR_ROLE"));
        isValidator = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validatorWallet.address);
        console.log("- 是否为验证者 (hasRole):", isValidator ? "✅ 是" : "❌ 否");
    } catch (error) {
        console.log("hasRole方法失败，尝试其他方法...");

        // 如果hasRole失败，尝试遍历验证者列表
        try {
            for (let i = 0; i < totalValidators; i++) {
                try {
                    const validator = await bridgeReceiver.validators(i);
                    if (validator && validator.toLowerCase() === validatorWallet.address.toLowerCase()) {
                        isValidator = true;
                        break;
                    }
                } catch (validatorError) {
                    console.log(`跳过验证者索引 ${i}:`, validatorError.message);
                    continue;
                }
            }
            console.log("- 是否为验证者 (遍历):", isValidator ? "✅ 是" : "❌ 否");
        } catch (listError) {
            console.log("遍历验证者列表失败，假设当前账户有权限...");
            isValidator = true; // 假设有权限，让后续调用来验证
        }
    }

    if (!isValidator) {
        console.warn("⚠️ 无法确认验证者权限，尝试继续执行...");
        console.log("💡 如果后续调用失败，说明确实没有权限");
    }
    
    console.log("\n=== 5. 处理跨链转移 ===");
    
    try {
        console.log("🚀 开始处理跨链转移...");

        // 尝试简化版本的处理方法
        let processTx;
        try {
            console.log("尝试使用简化版本处理...");
            processTx = await bridgeReceiver.processCrossChainTransfer(
                crossChainData.sourceToken,
                crossChainData.recipient,
                crossChainData.amount,
                97, // BSC链ID
                crossChainData.sourceTxHash
            );
        } catch (error) {
            console.log("简化版本失败，尝试复杂版本...");
            // 尝试复杂版本的提交方法
            processTx = await bridgeReceiver.submitCrossChainTransfer(
                crossChainData.sourceToken,
                crossChainData.recipient,
                crossChainData.amount,
                1710, // BSC区块号（从之前的输出获取）
                crossChainData.sourceTxHash
            );
        }

        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", processTx.hash);

        const receipt = await processTx.wait();

        if (receipt.status === 1) {
            console.log("✅ 跨链转移处理成功!");
            console.log("- Gas使用:", receipt.gasUsed.toString());
            console.log("- 区块号:", receipt.blockNumber);
        } else {
            console.error("❌ 交易失败，状态:", receipt.status);
            process.exit(1);
        }

    } catch (error) {
        console.error("❌ 处理跨链转移失败:", error.message);
        console.error("错误详情:", error);
        process.exit(1);
    }
    
    console.log("\n=== 6. 验证处理结果 ===");
    
    // 检查接收者余额变化
    const recipientBalanceAfter = await wpatToken.balanceOf(crossChainData.recipient);
    const balanceIncrease = recipientBalanceAfter - recipientBalanceBefore;
    
    console.log("处理结果:");
    console.log("- 接收者处理前余额:", ethers.formatEther(recipientBalanceBefore), "wPAT");
    console.log("- 接收者处理后余额:", ethers.formatEther(recipientBalanceAfter), "wPAT");
    console.log("- 余额增加:", ethers.formatEther(balanceIncrease), "wPAT");
    
    // 检查转移状态
    let finalIsProcessed = false;
    try {
        finalIsProcessed = await bridgeReceiver.processedTransfers(transferId);
        console.log("- 转移最终状态 (简化版):", finalIsProcessed ? "✅ 已处理" : "❌ 未处理");
    } catch (error) {
        try {
            const transferDetails = await bridgeReceiver.getTransferDetails(transferId);
            finalIsProcessed = transferDetails.completed;
            console.log("- 转移最终状态 (复杂版):", finalIsProcessed ? "✅ 已处理" : "❌ 未处理");
        } catch (error2) {
            console.log("- 转移最终状态:", "❌ 无法确定");
        }
    }
    
    // 检查wPAT总供应量
    const totalSupply = await wpatToken.totalSupply();
    console.log("- wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
    
    if (finalIsProcessed && balanceIncrease > 0) {
        console.log("\n🎉 跨链处理完成!");
        console.log("=====================================");
        console.log("✅ 成功处理10,000 PAT跨链");
        console.log("✅ 接收者获得:", ethers.formatEther(balanceIncrease), "wPAT");
        console.log("✅ 手续费已扣除");
        console.log("✅ 跨链状态: 已完成");
    } else {
        console.error("❌ 跨链处理可能失败，请检查结果");
    }
}

main()
    .then(() => {
        console.log("\n🎉 BSC本地链跨链请求处理完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 处理失败:", error);
        process.exit(1);
    });
