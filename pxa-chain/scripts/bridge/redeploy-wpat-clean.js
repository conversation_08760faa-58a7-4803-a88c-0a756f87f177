// 重新部署wPAT合约并正确铸造
const { ethers } = require("hardhat");
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔄 重新部署wPAT合约并正确铸造");
    console.log("=====================================");
    
    const network = hre.network;
    console.log("🌐 网络:", network.name);
    
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者:", deployer.address);
    
    console.log("\n=== 1. 查询BSC真实锁定数量 ===");
    
    let totalLockedOnBSC = 0n;
    try {
        // 连接BSC测试网查询真实锁定数量
        const bscProvider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545");
        const tokenBridgeAddress = "******************************************";
        const patTokenAddress = "******************************************";
        
        // 查询TokenBridge合约中锁定的PAT数量
        const patToken = new ethers.Contract(
            patTokenAddress,
            ["function balanceOf(address) view returns (uint256)"],
            bscProvider
        );
        
        totalLockedOnBSC = await patToken.balanceOf(tokenBridgeAddress);
        console.log("✅ BSC TokenBridge中锁定的PAT:", ethers.formatEther(totalLockedOnBSC), "PAT");
        
    } catch (error) {
        console.log("⚠️ 无法查询BSC锁定数量:", error.message);
        totalLockedOnBSC = ethers.parseEther("4100"); // 根据重置脚本的结果
        console.log("📝 使用已知的BSC锁定数量:", ethers.formatEther(totalLockedOnBSC), "PAT");
    }
    
    console.log("\n=== 2. 重新部署wPAT合约 ===");
    
    // 读取BSC部署信息
    let bscDeployment;
    try {
        const bscDeploymentFile = path.join(__dirname, "../../../bsc-pxt-pat-tokens/deployments/bscTestnet/core-deployment.json");
        bscDeployment = JSON.parse(fs.readFileSync(bscDeploymentFile, 'utf8'));
        console.log("✅ 已读取BSC部署信息");
    } catch (error) {
        console.error("❌ 无法读取BSC部署信息:", error.message);
        process.exit(1);
    }
    
    // 读取PXA桥接合约地址
    let bridgeReceiverAddress;
    try {
        const deploymentDir = path.join(__dirname, "../../deployments");
        const deploymentFiles = fs.readdirSync(deploymentDir)
            .filter(file => file.startsWith('complete-deployment-') && file.endsWith('.json'))
            .sort();
        
        if (deploymentFiles.length > 0) {
            const latestFile = deploymentFiles[deploymentFiles.length - 1];
            const deployment = JSON.parse(fs.readFileSync(path.join(deploymentDir, latestFile), 'utf8'));
            bridgeReceiverAddress = deployment.contracts.bridgeReceiver;
        }
        
        if (!bridgeReceiverAddress) {
            bridgeReceiverAddress = "******************************************"; // 默认地址
        }
        
        console.log("✅ 桥接合约地址:", bridgeReceiverAddress);
        
    } catch (error) {
        bridgeReceiverAddress = "******************************************"; // 默认地址
        console.log("⚠️ 使用默认桥接合约地址:", bridgeReceiverAddress);
    }
    
    // 部署新的wPAT合约
    console.log("🚀 部署新的wPAT合约...");
    
    const WrappedToken = await ethers.getContractFactory("WrappedToken");
    const wpatToken = await WrappedToken.deploy(
        "Wrapped PX Activity Token",           // 名称
        "wPAT",                               // 符号
        18,                                   // 精度
        bscDeployment.contracts.PAToken.address, // 原生合约地址
        97,                                   // BSC测试网链ID
        "PAT",                               // 原生符号
        bridgeReceiverAddress,               // 桥接合约
        deployer.address                     // 管理员
    );
    
    await wpatToken.waitForDeployment();
    const wpatAddress = await wpatToken.getAddress();
    
    console.log("✅ 新wPAT合约部署成功!");
    console.log("- 合约地址:", wpatAddress);
    
    console.log("\n=== 3. 铸造正确数量的wPAT ===");
    
    console.log("🪙 铸造", ethers.formatEther(totalLockedOnBSC), "wPAT给跨链池...");
    
    // 使用emergencyMint铸造正确数量
    const reason = `初始铸造 - 对应BSC锁定的${ethers.formatEther(totalLockedOnBSC)}PAT`;
    const mintTx = await wpatToken.emergencyMint(bridgeReceiverAddress, totalLockedOnBSC, reason);
    
    console.log("⏳ 等待铸造交易确认...");
    const receipt = await mintTx.wait();
    
    console.log("✅ 铸造成功!");
    console.log("- 交易哈希:", receipt.hash);
    console.log("- Gas使用:", receipt.gasUsed.toString());
    
    console.log("\n=== 4. 验证部署结果 ===");
    
    // 验证合约状态
    const name = await wpatToken.name();
    const symbol = await wpatToken.symbol();
    const totalSupply = await wpatToken.totalSupply();
    const poolBalance = await wpatToken.balanceOf(bridgeReceiverAddress);
    
    console.log("📊 合约验证:");
    console.log("- 名称:", name);
    console.log("- 符号:", symbol);
    console.log("- 总供应量:", ethers.formatEther(totalSupply), "wPAT");
    console.log("- 跨链池余额:", ethers.formatEther(poolBalance), "wPAT");
    console.log("- BSC锁定数量:", ethers.formatEther(totalLockedOnBSC), "PAT");
    
    // 检查是否完美匹配
    if (totalSupply === totalLockedOnBSC && poolBalance === totalLockedOnBSC) {
        console.log("✅ 完美匹配! 1:1映射关系正确");
    } else {
        console.log("❌ 数量不匹配，请检查");
    }
    
    console.log("\n=== 5. 保存新的部署信息 ===");
    
    // 保存新的部署信息
    const deploymentInfo = {
        network: network.name,
        chainId: (await ethers.provider.getNetwork()).chainId.toString(),
        timestamp: Date.now(),
        deployer: deployer.address,
        contracts: {
            wPAT: {
                address: wpatAddress,
                name: "Wrapped Paper Author Token",
                symbol: "wPAT",
                decimals: 18,
                totalSupply: ethers.formatEther(totalSupply),
                nativeContract: bscDeployment.contracts.PAToken.address,
                nativeChainId: 97,
                nativeSymbol: "PAT",
                bridgeContract: bridgeReceiverAddress,
                admin: deployer.address
            }
        },
        verification: {
            bscLockedAmount: ethers.formatEther(totalLockedOnBSC),
            wpatTotalSupply: ethers.formatEther(totalSupply),
            isMatched: totalSupply === totalLockedOnBSC
        }
    };
    
    const deploymentDir = path.join(__dirname, "../../deployments");
    const filename = `wpat-deployment-clean-${Date.now()}.json`;
    const filepath = path.join(deploymentDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(deploymentInfo, null, 2));
    console.log("✅ 部署信息已保存:", filename);
    
    console.log("\n🎉 wPAT合约重新部署完成!");
    console.log("=====================================");
    console.log("✅ 新合约地址:", wpatAddress);
    console.log("✅ 正确铸造数量:", ethers.formatEther(totalSupply), "wPAT");
    console.log("✅ 1:1映射关系:", totalSupply === totalLockedOnBSC ? "正确" : "错误");
    console.log("✅ 无历史遗留问题");
    
    console.log("\n💡 下一步:");
    console.log("1. 更新其他脚本使用新的wPAT合约地址");
    console.log("2. 测试跨链功能");
    console.log("3. 验证防重复铸造机制");
}

// 错误处理
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 脚本执行失败:", error);
        process.exit(1);
    });
