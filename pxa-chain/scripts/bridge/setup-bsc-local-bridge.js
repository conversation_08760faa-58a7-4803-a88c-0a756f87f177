const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 配置BSC本地链跨PXA本地链");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 获取签名者
    const [deployer] = await ethers.getSigners();
    console.log("部署者账户:", deployer.address);
    console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "PXA");
    
    // BSC本地链配置（从你的部署记录获取）
    const BSC_CHAIN_ID = 97;
    const BSC_TOKEN_BRIDGE = "******************************************";
    const BSC_PAT_TOKEN = "******************************************";
    
    // BSC验证者地址（从BSC部署记录获取）
    const BSC_VALIDATORS = [
        "******************************************", // 部署者
        "******************************************", // 国库
        "******************************************"  // 操作员
    ];
    
    console.log("BSC本地链配置:");
    console.log("- 链ID:", BSC_CHAIN_ID);
    console.log("- TokenBridge:", BSC_TOKEN_BRIDGE);
    console.log("- PAT代币:", BSC_PAT_TOKEN);
    console.log("- 验证者数量:", BSC_VALIDATORS.length);
    
    // 读取PXA链部署信息
    const deploymentsDir = path.join(__dirname, "../../deployments");
    const deploymentFiles = fs.readdirSync(deploymentsDir).filter(f => f.startsWith('complete-deployment-'));
    const latestDeploymentFile = deploymentFiles.sort().pop();
    const deploymentPath = path.join(deploymentsDir, latestDeploymentFile);
    
    let deployment;
    try {
        deployment = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));
        console.log("\n✅ 已加载PXA链部署信息");
        console.log("- BridgeReceiver:", deployment.contracts.bridgeReceiver);
    } catch (error) {
        console.error("❌ 读取部署信息失败:", error.message);
        process.exit(1);
    }
    
    // 读取wPAT代币地址
    const wpatFiles = fs.readdirSync(deploymentsDir).filter(f => f.startsWith('wpat-deployment-'));
    const latestWpatFile = wpatFiles.sort().pop();
    const wpatPath = path.join(deploymentsDir, latestWpatFile);
    
    let wpatAddress;
    try {
        const wpatDeployment = JSON.parse(fs.readFileSync(wpatPath, 'utf8'));
        wpatAddress = wpatDeployment.contracts.wPAT.address;
        console.log("- wPAT代币:", wpatAddress);
    } catch (error) {
        console.error("❌ 读取wPAT部署信息失败:", error.message);
        process.exit(1);
    }
    
    // 连接合约
    const BridgeReceiver = await ethers.getContractFactory("BridgeReceiver");
    const bridgeReceiver = BridgeReceiver.attach(deployment.contracts.bridgeReceiver);
    
    const WrappedToken = await ethers.getContractFactory("WrappedToken");
    const wpatToken = WrappedToken.attach(wpatAddress);
    
    console.log("\n=== 1. 检查当前配置状态 ===");
    
    try {
        const totalValidators = await bridgeReceiver.totalValidators();
        const requiredValidators = await bridgeReceiver.requiredValidators();
        const sourceChainId = await bridgeReceiver.sourceChainId();
        const isSupported = await bridgeReceiver.supportedTokens(BSC_PAT_TOKEN);
        const mappedToken = await bridgeReceiver.tokenMapping(BSC_PAT_TOKEN);
        
        console.log("当前配置:");
        console.log("- 总验证者数量:", totalValidators.toString());
        console.log("- 需要验证者数量:", requiredValidators.toString());
        console.log("- 源链ID:", sourceChainId.toString());
        console.log("- PAT代币支持:", isSupported ? "是" : "否");
        console.log("- PAT映射地址:", mappedToken);
        console.log("- 映射是否正确:", mappedToken.toLowerCase() === wpatAddress.toLowerCase() ? "是" : "否");
        
    } catch (error) {
        console.error("❌ 检查配置状态失败:", error.message);
    }
    
    console.log("\n=== 2. 配置源链ID ===");
    
    try {
        const currentSourceChainId = await bridgeReceiver.sourceChainId();
        if (currentSourceChainId.toString() !== BSC_CHAIN_ID.toString()) {
            console.log("更新源链ID从", currentSourceChainId.toString(), "到", BSC_CHAIN_ID);
            const setSourceChainTx = await bridgeReceiver.setSourceChainId(BSC_CHAIN_ID);
            await setSourceChainTx.wait();
            console.log("✅ 源链ID更新完成");
        } else {
            console.log("✅ 源链ID配置正确");
        }
    } catch (error) {
        console.error("❌ 配置源链ID失败:", error.message);
    }
    
    console.log("\n=== 3. 配置验证者 ===");
    
    try {
        const VALIDATOR_ROLE = await bridgeReceiver.VALIDATOR_ROLE();
        
        for (const validator of BSC_VALIDATORS) {
            try {
                const hasRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validator);
                
                if (!hasRole) {
                    console.log(`添加验证者: ${validator}`);
                    const addValidatorTx = await bridgeReceiver.addValidator(validator);
                    await addValidatorTx.wait();
                    console.log(`✅ 验证者 ${validator} 已添加`);
                } else {
                    console.log(`✅ 验证者 ${validator} 已存在`);
                }
            } catch (error) {
                console.log(`⚠️  处理验证者 ${validator} 失败:`, error.message);
            }
        }
        
        // 检查最终验证者状态
        const finalTotalValidators = await bridgeReceiver.totalValidators();
        console.log("最终验证者数量:", finalTotalValidators.toString());
        
    } catch (error) {
        console.error("❌ 配置验证者失败:", error.message);
    }
    
    console.log("\n=== 4. 配置代币映射 ===");
    
    try {
        const currentMapping = await bridgeReceiver.tokenMapping(BSC_PAT_TOKEN);
        
        if (currentMapping === "0x0000000000000000000000000000000000000000") {
            console.log("设置新的代币映射...");
            console.log("- BSC PAT:", BSC_PAT_TOKEN);
            console.log("- PXA wPAT:", wpatAddress);
            
            const addTokenMappingTx = await bridgeReceiver.addTokenMapping(
                BSC_PAT_TOKEN,
                wpatAddress
            );
            await addTokenMappingTx.wait();
            console.log("✅ 代币映射配置完成");
        } else if (currentMapping.toLowerCase() === wpatAddress.toLowerCase()) {
            console.log("✅ 代币映射已正确配置");
        } else {
            console.log("⚠️  代币映射不正确，当前映射:", currentMapping);
            console.log("正在更新映射...");
            
            // 如果需要更新映射，可能需要先移除再添加
            try {
                const updateMappingTx = await bridgeReceiver.addTokenMapping(
                    BSC_PAT_TOKEN,
                    wpatAddress
                );
                await updateMappingTx.wait();
                console.log("✅ 代币映射更新完成");
            } catch (error) {
                console.log("⚠️  更新映射失败:", error.message);
            }
        }
        
        // 验证最终映射状态
        const finalMapping = await bridgeReceiver.tokenMapping(BSC_PAT_TOKEN);
        const isSupported = await bridgeReceiver.supportedTokens(BSC_PAT_TOKEN);
        console.log("最终映射状态:");
        console.log("- 映射地址:", finalMapping);
        console.log("- 代币支持:", isSupported ? "是" : "否");
        
    } catch (error) {
        console.error("❌ 配置代币映射失败:", error.message);
    }
    
    console.log("\n=== 5. 检查wPAT权限 ===");
    
    try {
        const BRIDGE_ROLE = await wpatToken.BRIDGE_ROLE();
        const hasBridgeRole = await wpatToken.hasRole(BRIDGE_ROLE, deployment.contracts.bridgeReceiver);
        
        console.log("wPAT权限检查:");
        console.log("- BridgeReceiver地址:", deployment.contracts.bridgeReceiver);
        console.log("- 是否有桥接权限:", hasBridgeRole ? "是" : "否");
        
        if (!hasBridgeRole) {
            console.log("⚠️  BridgeReceiver没有wPAT的桥接权限");
            console.log("💡 这可能会导致跨链铸造失败");
        }
        
    } catch (error) {
        console.error("❌ 检查wPAT权限失败:", error.message);
    }
    
    console.log("\n=== 6. 最终配置验证 ===");
    
    try {
        const totalValidators = await bridgeReceiver.totalValidators();
        const requiredValidators = await bridgeReceiver.requiredValidators();
        const sourceChainId = await bridgeReceiver.sourceChainId();
        const mappedToken = await bridgeReceiver.tokenMapping(BSC_PAT_TOKEN);
        const isSupported = await bridgeReceiver.supportedTokens(BSC_PAT_TOKEN);
        
        console.log("✅ 最终配置状态:");
        console.log("- 总验证者数量:", totalValidators.toString());
        console.log("- 需要验证者数量:", requiredValidators.toString());
        console.log("- 源链ID:", sourceChainId.toString(), sourceChainId.toString() === BSC_CHAIN_ID.toString() ? "✅" : "❌");
        console.log("- PAT代币支持:", isSupported ? "✅" : "❌");
        console.log("- PAT映射正确:", mappedToken.toLowerCase() === wpatAddress.toLowerCase() ? "✅" : "❌");
        
        const configComplete = (
            totalValidators >= requiredValidators &&
            sourceChainId.toString() === BSC_CHAIN_ID.toString() &&
            isSupported &&
            mappedToken.toLowerCase() === wpatAddress.toLowerCase()
        );
        
        if (configComplete) {
            console.log("\n🎉 跨链桥配置完成！可以开始跨链测试");
        } else {
            console.log("\n⚠️  配置不完整，请检查上述问题");
        }
        
    } catch (error) {
        console.error("❌ 最终验证失败:", error.message);
    }
    
    console.log("\n=== 7. 保存配置信息 ===");
    
    const bridgeConfig = {
        timestamp: new Date().toISOString(),
        bscLocalChain: {
            chainId: BSC_CHAIN_ID,
            rpcUrl: "http://127.0.0.1:18485",
            tokenBridge: BSC_TOKEN_BRIDGE,
            patToken: BSC_PAT_TOKEN,
            validators: BSC_VALIDATORS
        },
        pxaLocalChain: {
            chainId: 327,
            rpcUrl: "http://127.0.0.1:8545",
            bridgeReceiver: deployment.contracts.bridgeReceiver,
            wpatToken: wpatAddress
        },
        configuration: {
            ready: true,
            lastUpdated: new Date().toISOString()
        }
    };
    
    const configPath = path.join(deploymentsDir, "bsc-pxa-local-bridge-config.json");
    fs.writeFileSync(configPath, JSON.stringify(bridgeConfig, null, 2));
    console.log("✅ 跨链桥配置已保存:", configPath);
    
    console.log("\n🎯 下一步操作:");
    console.log("1. 测试BSC到PXA跨链:");
    console.log("   npx hardhat run scripts/bridge/test-bsc-to-pxa-bridge.js --network localhost");
    console.log("2. 处理BSC跨链请求:");
    console.log("   npx hardhat run scripts/bridge/process-bsc-bridge-request.js --network localhost");
    console.log("3. 检查wPAT余额:");
    console.log("   npx hardhat run scripts/bridge/check-wPAT-balance.js --network localhost");
    
    console.log("\n📊 跨链流程说明:");
    console.log("BSC本地链: PAT锁定 → 事件监听 → PXA本地链: wPAT铸造");
    console.log("费用: BSC Gas + 跨链费用 + PXA处理费用");
}

main()
    .then(() => {
        console.log("\n🎉 BSC本地链跨PXA本地链配置完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 配置失败:", error);
        process.exit(1);
    });
