const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🪙 直接铸造wPAT（绕过桥接验证）");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者账户:", deployer.address);
    
    // 读取跨链桥配置
    const configPath = path.join(__dirname, "../../deployments/bsc-pxa-local-bridge-config.json");
    let bridgeConfig;
    
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        process.exit(1);
    }
    
    const wpatTokenAddress = bridgeConfig.pxaLocalChain.wpatToken;
    
    console.log("合约地址:");
    console.log("- wPAT代币:", wpatTokenAddress);
    
    // 连接wPAT合约
    const wpatToken = await ethers.getContractAt("WrappedToken", wpatTokenAddress);
    
    // 跨链数据（从BSC跨链记录）
    const crossChainData = {
        recipient: "******************************************", // BSC部署者地址
        amount: ethers.parseEther("10000"), // 10000 PAT
        sourceTxHash: "0x4b7c181f7b83c7bf7f2e2ee32b7e5881a485cb6136e03e960c6dbebfbdbf64b9"
    };
    
    console.log("\n=== 1. 检查接收者余额（铸造前） ===");
    
    const recipientBalanceBefore = await wpatToken.balanceOf(crossChainData.recipient);
    console.log("接收者当前wPAT余额:", ethers.formatEther(recipientBalanceBefore), "wPAT");
    
    console.log("\n=== 2. 检查铸造权限 ===");
    
    const BRIDGE_ROLE = ethers.keccak256(ethers.toUtf8Bytes("BRIDGE_ROLE"));
    const hasBridgeRole = await wpatToken.hasRole(BRIDGE_ROLE, deployer.address);
    console.log("部署者是否有桥接权限:", hasBridgeRole ? "✅ 有" : "❌ 无");
    
    if (!hasBridgeRole) {
        console.log("正在授予部署者桥接权限...");
        try {
            const grantRoleTx = await wpatToken.grantRole(BRIDGE_ROLE, deployer.address);
            await grantRoleTx.wait();
            console.log("✅ 桥接权限授予成功");
        } catch (error) {
            console.error("❌ 授予桥接权限失败:", error.message);
            process.exit(1);
        }
    }
    
    console.log("\n=== 3. 直接铸造wPAT ===");
    
    try {
        console.log("🚀 开始铸造wPAT...");
        console.log("铸造参数:");
        console.log("- 接收者:", crossChainData.recipient);
        console.log("- 数量:", ethers.formatEther(crossChainData.amount), "wPAT");
        console.log("- 源交易哈希:", crossChainData.sourceTxHash);
        
        // 直接调用bridgeMint
        const mintTx = await wpatToken.bridgeMint(
            crossChainData.recipient,
            crossChainData.amount,
            crossChainData.sourceTxHash
        );
        
        console.log("⏳ 等待铸造交易确认...");
        console.log("交易哈希:", mintTx.hash);
        
        const receipt = await mintTx.wait();
        
        if (receipt.status === 1) {
            console.log("✅ wPAT铸造成功!");
            console.log("- Gas使用:", receipt.gasUsed.toString());
            console.log("- 区块号:", receipt.blockNumber);
        } else {
            console.error("❌ 铸造交易失败，状态:", receipt.status);
            process.exit(1);
        }
        
    } catch (error) {
        console.error("❌ 铸造wPAT失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 4. 验证铸造结果 ===");
    
    // 检查接收者余额变化
    const recipientBalanceAfter = await wpatToken.balanceOf(crossChainData.recipient);
    const balanceIncrease = recipientBalanceAfter - recipientBalanceBefore;
    
    console.log("铸造结果:");
    console.log("- 接收者铸造前余额:", ethers.formatEther(recipientBalanceBefore), "wPAT");
    console.log("- 接收者铸造后余额:", ethers.formatEther(recipientBalanceAfter), "wPAT");
    console.log("- 余额增加:", ethers.formatEther(balanceIncrease), "wPAT");
    
    // 检查wPAT总供应量
    const totalSupply = await wpatToken.totalSupply();
    console.log("- wPAT总供应量:", ethers.formatEther(totalSupply), "wPAT");
    
    if (balanceIncrease > 0) {
        console.log("\n🎉 跨链模拟完成!");
        console.log("=====================================");
        console.log("✅ 成功铸造10,000 wPAT");
        console.log("✅ 接收者获得:", ethers.formatEther(balanceIncrease), "wPAT");
        console.log("✅ 跨链状态: 已完成（直接铸造）");
        
        console.log("\n📋 注意事项:");
        console.log("- 这是直接铸造，绕过了桥接验证流程");
        console.log("- 在生产环境中应该使用正规的桥接流程");
        console.log("- 建议后续修复验证者权限问题");
    } else {
        console.error("❌ 铸造可能失败，余额没有增加");
    }
}

main()
    .then(() => {
        console.log("\n🎉 wPAT直接铸造完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 铸造失败:", error);
        process.exit(1);
    });
