const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔧 添加BSC验证者到PXA链BridgeReceiver");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者账户:", deployer.address);
    
    // 读取跨链桥配置
    const configPath = path.join(__dirname, "../../deployments/bsc-pxa-local-bridge-config.json");
    let bridgeConfig;
    
    try {
        bridgeConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log("✅ 已加载跨链桥配置");
    } catch (error) {
        console.error("❌ 读取跨链桥配置失败:", error.message);
        process.exit(1);
    }
    
    const bridgeReceiverAddress = bridgeConfig.pxaLocalChain.bridgeReceiver;
    const bscValidators = bridgeConfig.bscLocalChain.validators;
    
    console.log("合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("BSC验证者列表:");
    bscValidators.forEach((validator, index) => {
        console.log(`  ${index + 1}. ${validator}`);
    });
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    
    console.log("\n=== 1. 检查当前验证者状态 ===");
    
    const VALIDATOR_ROLE = ethers.keccak256(ethers.toUtf8Bytes("VALIDATOR_ROLE"));
    const totalValidators = await bridgeReceiver.totalValidators();
    const requiredValidators = await bridgeReceiver.requiredValidators();
    
    console.log("当前验证者配置:");
    console.log("- 总验证者数量:", totalValidators.toString());
    console.log("- 需要验证者数量:", requiredValidators.toString());
    
    // 检查每个BSC验证者的状态
    console.log("\nBSC验证者在PXA链的状态:");
    for (let i = 0; i < bscValidators.length; i++) {
        const validator = bscValidators[i];
        const hasRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validator);
        console.log(`  ${validator}: ${hasRole ? "✅ 已是验证者" : "❌ 不是验证者"}`);
    }
    
    console.log("\n=== 2. 添加BSC验证者到PXA链 ===");
    
    let addedCount = 0;
    
    for (let i = 0; i < bscValidators.length; i++) {
        const validator = bscValidators[i];
        const hasRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validator);
        
        if (!hasRole) {
            try {
                console.log(`正在添加验证者: ${validator}`);
                
                // 添加验证者角色
                const grantRoleTx = await bridgeReceiver.grantRole(VALIDATOR_ROLE, validator);
                await grantRoleTx.wait();
                
                // 添加到验证者列表
                const addValidatorTx = await bridgeReceiver.addValidator(validator);
                await addValidatorTx.wait();
                
                console.log(`✅ 验证者 ${validator} 添加成功`);
                addedCount++;
                
            } catch (error) {
                console.error(`❌ 添加验证者 ${validator} 失败:`, error.message);
            }
        } else {
            console.log(`⏭️ 验证者 ${validator} 已存在，跳过`);
        }
    }
    
    console.log("\n=== 3. 验证添加结果 ===");
    
    const finalTotalValidators = await bridgeReceiver.totalValidators();
    console.log("最终验证者数量:", finalTotalValidators.toString());
    
    console.log("最终BSC验证者状态:");
    for (let i = 0; i < bscValidators.length; i++) {
        const validator = bscValidators[i];
        const hasRole = await bridgeReceiver.hasRole(VALIDATOR_ROLE, validator);
        console.log(`  ${validator}: ${hasRole ? "✅ 已是验证者" : "❌ 不是验证者"}`);
    }
    
    if (addedCount > 0) {
        console.log("\n🎉 验证者添加完成!");
        console.log("=====================================");
        console.log(`✅ 成功添加 ${addedCount} 个BSC验证者到PXA链`);
        console.log("✅ 现在可以使用BSC验证者处理跨链请求");
        
        console.log("\n📋 下一步操作:");
        console.log("npx hardhat run scripts/bridge/process-bsc-local-with-validator.js --network localhost");
    } else {
        console.log("\n✅ 所有BSC验证者都已经在PXA链中");
        console.log("可以直接处理跨链请求");
    }
}

main()
    .then(() => {
        console.log("\n🎉 BSC验证者添加完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 添加失败:", error);
        process.exit(1);
    });
