// 设置BSC PAT到PXA wPAT的代币映射
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("\n🔗 设置BSC PAT到PXA wPAT的代币映射");
    console.log("=====================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("🌐 当前网络:", network.name);
    console.log("📝 管理员地址:", deployer.address);
    console.log("⏰ 时间:", new Date().toISOString());
    
    // 代币映射信息
    const tokenMapping = {
        sourceToken: "******************************************", // BSC PAT地址
        sourceChainId: 97, // BSC链ID
        sourceSymbol: "PAT"
    };
    
    console.log("🎯 代币映射信息:");
    console.log("- 源代币地址:", tokenMapping.sourceToken);
    console.log("- 源链ID:", tokenMapping.sourceChainId);
    console.log("- 源代币符号:", tokenMapping.sourceSymbol);
    
    // 从部署记录加载合约地址
    let bridgeReceiverAddress, wPATAddress;
    
    try {
        // 加载完整部署记录（包含BridgeReceiver）
        const completeDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("complete-deployment-") && file.endsWith(".json"));
        
        if (completeDeploymentFiles.length > 0) {
            const completeFile = path.join(__dirname, "../../deployments", completeDeploymentFiles[0]);
            const completeDeployment = JSON.parse(fs.readFileSync(completeFile, 'utf8'));
            bridgeReceiverAddress = completeDeployment.contracts.bridgeReceiver;
        }
        
        // 加载wPAT部署记录
        const wpatDeploymentFiles = fs.readdirSync(path.join(__dirname, "../../deployments"))
            .filter(file => file.startsWith("wpat-deployment-") && file.endsWith(".json"));
        
        if (wpatDeploymentFiles.length > 0) {
            const wpatFile = path.join(__dirname, "../../deployments", wpatDeploymentFiles[0]);
            const wpatDeployment = JSON.parse(fs.readFileSync(wpatFile, 'utf8'));
            wPATAddress = wpatDeployment.contracts.wPAT.address;
        }
        
        console.log("✅ 已加载部署信息");
        
    } catch (error) {
        console.error("❌ 加载部署文件失败:", error.message);
        process.exit(1);
    }
    
    console.log("📝 PXA链合约地址:");
    console.log("- BridgeReceiver:", bridgeReceiverAddress);
    console.log("- wPAT代币:", wPATAddress);
    
    if (!bridgeReceiverAddress || !wPATAddress) {
        console.error("❌ 缺少必要的合约地址");
        process.exit(1);
    }
    
    // 连接合约
    const bridgeReceiver = await ethers.getContractAt("BridgeReceiver", bridgeReceiverAddress);
    
    console.log("\n=== 1. 检查管理员权限 ===");
    
    try {
        // 检查是否有管理员权限
        const DEFAULT_ADMIN_ROLE = await bridgeReceiver.DEFAULT_ADMIN_ROLE();
        const hasAdminRole = await bridgeReceiver.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
        
        console.log("管理员权限:", hasAdminRole ? "✅ 有权限" : "❌ 无权限");
        
        if (!hasAdminRole) {
            console.error("❌ 当前账户没有管理员权限，无法设置代币映射");
            console.log("💡 需要使用部署合约时的管理员账户");
            process.exit(1);
        }
        
    } catch (error) {
        console.error("❌ 检查管理员权限失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 2. 检查当前映射状态 ===");
    
    try {
        // 检查代币是否已支持
        const isSupported = await bridgeReceiver.supportedTokens(tokenMapping.sourceToken);
        console.log("代币支持状态:", isSupported ? "✅ 已支持" : "❌ 未支持");
        
        if (isSupported) {
            // 检查当前映射
            const currentMapping = await bridgeReceiver.tokenMapping(tokenMapping.sourceToken);
            console.log("当前映射目标:", currentMapping);
            
            if (currentMapping.toLowerCase() === wPATAddress.toLowerCase()) {
                console.log("✅ 代币映射已正确设置");
                console.log("🎉 无需重复设置，可以直接进行跨链操作");
                return;
            } else {
                console.log("⚠️  映射目标不正确，需要更新");
            }
        }
        
    } catch (error) {
        console.log("⚠️  检查映射状态失败:", error.message);
    }
    
    console.log("\n=== 3. 设置代币映射 ===");
    
    try {
        console.log("🚀 添加代币映射...");
        console.log("- 源代币:", tokenMapping.sourceToken);
        console.log("- 目标代币:", wPATAddress);
        
        const addMappingTx = await bridgeReceiver.addTokenMapping(
            tokenMapping.sourceToken,
            wPATAddress
        );
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", addMappingTx.hash);
        
        const receipt = await addMappingTx.wait();
        console.log("✅ 代币映射设置成功!");
        console.log("- 区块号:", receipt.blockNumber);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 解析事件
        const events = receipt.logs;
        console.log("📋 事件数量:", events.length);
        
        for (const event of events) {
            try {
                const parsedEvent = bridgeReceiver.interface.parseLog(event);
                console.log("事件:", parsedEvent.name, parsedEvent.args);
            } catch (e) {
                // 忽略无法解析的事件
            }
        }
        
    } catch (error) {
        console.error("❌ 设置代币映射失败:", error.message);
        
        if (error.message.includes("Token already supported")) {
            console.log("💡 代币已经支持，可能只需要更新映射");
        } else if (error.message.includes("AccessControl")) {
            console.log("💡 权限不足，需要管理员权限");
        }
        
        process.exit(1);
    }
    
    console.log("\n=== 4. 验证映射设置 ===");
    
    try {
        // 验证设置结果
        const isSupported = await bridgeReceiver.supportedTokens(tokenMapping.sourceToken);
        const mappedToken = await bridgeReceiver.tokenMapping(tokenMapping.sourceToken);
        
        console.log("验证结果:");
        console.log("- 代币支持状态:", isSupported ? "✅ 已支持" : "❌ 未支持");
        console.log("- 映射目标地址:", mappedToken);
        console.log("- 映射是否正确:", mappedToken.toLowerCase() === wPATAddress.toLowerCase() ? "✅ 正确" : "❌ 错误");
        
        if (isSupported && mappedToken.toLowerCase() === wPATAddress.toLowerCase()) {
            console.log("\n🎉 代币映射设置完成！");
            console.log("=====================================");
            console.log("✅ BSC PAT → PXA wPAT 映射已建立");
            console.log("✅ 现在可以处理跨链请求了");
            
            console.log("\n🚀 下一步:");
            console.log("运行跨链处理脚本:");
            console.log("npx hardhat run scripts/bridge/process-bsc-bridge-request.js --network localhost");
        } else {
            console.log("❌ 映射设置验证失败");
        }
        
    } catch (error) {
        console.error("❌ 验证映射设置失败:", error.message);
    }
}

// 错误处理
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ 脚本执行失败:", error);
            process.exit(1);
        });
}

module.exports = main;
