const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🪙 部署PXPAC链wPAT代币合约");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());

    const [deployer] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);

    // 获取部署账户余额
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log("部署账户余额:", ethers.formatEther(balance), "ETH");

    console.log("\n=== 1. 读取BSC链PAT代币信息 ===");

    // 读取BSC链的PAT代币信息
    let bscPATInfo;
    try {
        const bscConfigFile = path.join(__dirname, "../../../bsc-pxt-pat-tokens/scripts/config/addresses.json");
        const bscConfig = JSON.parse(fs.readFileSync(bscConfigFile, 'utf8'));

        bscPATInfo = {
            address: bscConfig.addresses.bsc.PAToken,
            chainId: 97, // BSC测试链ID
            symbol: "PAT",
            decimals: 18
        };

        console.log("✅ BSC链PAT代币信息:");
        console.log("   地址:", bscPATInfo.address);
        console.log("   链ID:", bscPATInfo.chainId);
        console.log("   符号:", bscPATInfo.symbol);
        console.log("   精度:", bscPATInfo.decimals);

    } catch (error) {
        console.error("❌ 无法读取BSC链PAT代币信息:", error.message);
        console.log("使用默认配置...");

        bscPATInfo = {
            address: "0x0000000000000000000000000000000000000000", // 需要手动更新
            chainId: 97,
            symbol: "PAT",
            decimals: 18
        };
    }

    console.log("\n=== 2. 读取PXPAC链桥接合约信息 ===");

    // 读取PXPAC链的桥接合约地址
    let bridgeReceiverAddress;
    try {
        const deploymentsDir = path.join(__dirname, "../../deployments");
        const deploymentFiles = fs.readdirSync(deploymentsDir)
            .filter(file => file.startsWith('complete-deployment-') && file.endsWith('.json'))
            .sort();

        if (deploymentFiles.length === 0) {
            throw new Error("未找到PXPAC链部署文件");
        }

        const latestDeploymentFile = deploymentFiles[deploymentFiles.length - 1];
        const deploymentPath = path.join(deploymentsDir, latestDeploymentFile);
        const deployment = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));

        bridgeReceiverAddress = deployment.contracts.bridgeReceiver;

        if (!bridgeReceiverAddress) {
            throw new Error("部署文件中未找到BridgeReceiver地址");
        }

        console.log("✅ PXPAC链桥接合约:");
        console.log("   BridgeReceiver:", bridgeReceiverAddress);
        console.log("   部署文件:", latestDeploymentFile);

    } catch (error) {
        console.error("❌ 无法读取PXPAC链桥接合约信息:", error.message);
        process.exit(1);
    }

    console.log("\n=== 3. 部署wPAT代币合约 ===");

    // wPAT代币参数
    const wpatParams = {
        name: "Wrapped Paper Author Token",
        symbol: "wPAT",
        decimals: 18,
        originalContract: bscPATInfo.address,
        originalChainId: bscPATInfo.chainId,
        originalSymbol: bscPATInfo.symbol,
        bridge: bridgeReceiverAddress,
        admin: deployer.address
    };

    console.log("wPAT代币参数:");
    console.log("   名称:", wpatParams.name);
    console.log("   符号:", wpatParams.symbol);
    console.log("   精度:", wpatParams.decimals);
    console.log("   原生合约:", wpatParams.originalContract);
    console.log("   原生链ID:", wpatParams.originalChainId);
    console.log("   原生符号:", wpatParams.originalSymbol);
    console.log("   桥接合约:", wpatParams.bridge);
    console.log("   管理员:", wpatParams.admin);

    try {
        console.log("\n正在部署wPAT代币合约...");

        const WrappedToken = await ethers.getContractFactory("WrappedToken");
        const wpatToken = await WrappedToken.deploy(
            wpatParams.name,
            wpatParams.symbol,
            wpatParams.decimals,
            wpatParams.originalContract,
            wpatParams.originalChainId,
            wpatParams.originalSymbol,
            wpatParams.bridge,
            wpatParams.admin
        );

        await wpatToken.waitForDeployment();
        const wpatAddress = await wpatToken.getAddress();

        console.log("✅ wPAT代币部署成功!");
        console.log("   合约地址:", wpatAddress);
        console.log("   交易哈希:", wpatToken.deploymentTransaction().hash);

        console.log("\n=== 4. 验证部署结果 ===");

        // 验证合约信息
        const name = await wpatToken.name();
        const symbol = await wpatToken.symbol();
        const decimals = await wpatToken.decimals();
        const totalSupply = await wpatToken.totalSupply();

        console.log("合约验证:");
        console.log("   名称:", name);
        console.log("   符号:", symbol);
        console.log("   精度:", decimals);
        console.log("   总供应量:", ethers.formatEther(totalSupply));

        // 验证原生代币信息
        const originalTokenInfo = await wpatToken.getOriginalTokenInfo();
        console.log("原生代币信息:");
        console.log("   原生合约:", originalTokenInfo.originalContract);
        console.log("   原生链ID:", originalTokenInfo.originalChainId.toString());
        console.log("   原生符号:", originalTokenInfo.originalSymbol);
        console.log("   原生精度:", originalTokenInfo.originalDecimals);

        // 验证角色权限
        const BRIDGE_ROLE = await wpatToken.BRIDGE_ROLE();
        const MINTER_ROLE = await wpatToken.MINTER_ROLE();
        const hasBridgeRole = await wpatToken.hasRole(BRIDGE_ROLE, bridgeReceiverAddress);
        const hasMinterRole = await wpatToken.hasRole(MINTER_ROLE, bridgeReceiverAddress);

        console.log("权限验证:");
        console.log("   桥接权限:", hasBridgeRole ? "✅" : "❌");
        console.log("   铸造权限:", hasMinterRole ? "✅" : "❌");

        console.log("\n=== 5. 保存部署信息 ===");

        // 保存部署信息
        const deploymentInfo = {
            timestamp: Date.now(),
            network: network.name,
            chainId: (await ethers.provider.getNetwork()).chainId.toString(),
            deployer: deployer.address,
            contracts: {
                wPAT: {
                    address: wpatAddress,
                    name: name,
                    symbol: symbol,
                    decimals: decimals,
                    originalContract: wpatParams.originalContract,
                    originalChainId: wpatParams.originalChainId,
                    originalSymbol: wpatParams.originalSymbol,
                    bridge: wpatParams.bridge,
                    admin: wpatParams.admin,
                    transactionHash: wpatToken.deploymentTransaction().hash
                }
            },
            verification: {
                bridgeRole: hasBridgeRole,
                minterRole: hasMinterRole,
                totalSupply: ethers.formatEther(totalSupply)
            }
        };

        // 保存到文件
        const deploymentsDir = path.join(__dirname, "../../deployments");
        if (!fs.existsSync(deploymentsDir)) {
            fs.mkdirSync(deploymentsDir, { recursive: true });
        }

        const deploymentFile = path.join(deploymentsDir, `wpat-deployment-${Date.now()}.json`);
        fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, (key, value) => {
            return typeof value === 'bigint' ? value.toString() : value;
        }, 2));

        console.log("✅ 部署信息已保存:", deploymentFile);

        console.log("\n🎉 wPAT代币部署完成!");
        console.log("================================================");
        console.log("✅ 合约地址:", wpatAddress);
        console.log("✅ 代币名称:", name);
        console.log("✅ 代币符号:", symbol);
        console.log("✅ 桥接功能:", hasBridgeRole && hasMinterRole ? "已启用" : "需要配置");
        console.log("✅ 跨链映射: BSC PAT ↔ PXPAC wPAT");

        console.log("\n🔧 下一步操作:");
        console.log("1. 更新跨链桥配置使用新的wPAT合约");
        console.log("2. 测试跨链铸造功能");
        console.log("3. 验证跨链统计功能");

        return {
            wpatAddress,
            deploymentInfo
        };

    } catch (error) {
        console.error("❌ wPAT代币部署失败:", error.message);

        if (error.message.includes("insufficient funds")) {
            console.error("原因: 部署账户余额不足");
        } else if (error.message.includes("Invalid original contract")) {
            console.error("原因: BSC PAT合约地址无效");
        }

        process.exit(1);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
