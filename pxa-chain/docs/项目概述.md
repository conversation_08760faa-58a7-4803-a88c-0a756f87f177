# 🚀 PXA Chain 项目概述

### 项目简介

本平台致力于打造基于Web3技术的去中心化内容分享与激励生态，融合链上确权、三币激励、用户多维等级等创新机制，赋能内容创作者、分享者、审核员和广大用户，推动内容经济的公平、透明与可持续发展。

---

### 平台成长体系总览

平台采用双重成长体系：
- **用户等级体系**：衡量成长、活跃与贡献，解锁平台功能与荣誉
- **个人中心等级**：反映用户综合成长表现，提升社区归属感
两者互不冲突，分别赋能不同场景，提升平台生态多样性与用户粘性。

---

### 1. 角色权限体系

#### 1.1 角色类型与功能映射
- 分享者、创作者、审核员、全能员、其他功能性身份（可扩展）
> 每种角色与平台具体功能（如内容发布、审核、治理等）对应，用户需持有相应角色权限方可解锁对应功能。
> 全能员是特殊的角色类型，同时拥有创作者、分享者和审核员的权限，可执行多种功能。

#### 1.2 角色等级与升级
- 丁级、丙级、乙级、甲级、十绝、双十绝、至尊（稀有度递增）
- 通过服务包、任务奖励等方式获得角色权限
- 角色可通过活跃、质押、贡献等方式升级

#### 1.3 角色专属权益
- 内容发布、审核、分红、治理等链上专属权益
- 角色等级越高，权益越多，治理权重越大

#### 1.7 知名角色晋升机制
- 只有达到青提等级的创作者、分享者、审核员、全能员，且消耗一定数量的PXT代币，才有资格晋升为"知名创作者""知名分享者""知名审核员""知名全能员"
- 平台设定明确的晋升条件（如内容数量、好评率、审核准确率等），用户达标后可申请晋升
- 晋升流程包括自动检测、社区/平台审核、授予知名身份及专属权益
- 知名身份动态调整，长期不活跃或违规可降级
- 不同知名角色拥有相应的治理权重和平台分红比例

---

### 2. 用户等级体系

#### 2.1 等级划分
平台用户等级共10级，分别为：
1. 青铜
2. 赤铁
3. 白银
4. 黄金
5. 紫晶
6. 青提
7. 红枣
8. 白荔
9. 黄杏
10. 股东

#### 2.2 成长路径与权益
- 等级提升依据：活跃天数、内容贡献、互动行为、平台任务等多维度成长指标
- 每个等级对应不同的成长权益、展示标识、部分功能解锁等
- 用户等级与角色体系关系说明：
  - 两者是独立但互相影响的系统：用户等级反映个人成长轨迹，角色反映专业身份
  - 用户等级达到特定门槛（如青提等级）是升级某些高级角色的前提条件
  - 角色等级决定用户的专业角色权限，而用户等级决定个人的综合成长权益
  - 用户可以同时在两条路径上发展，获得不同维度的权益与收益
  - 例如：一个黄金等级的用户可以持有丙级分享者角色，同时拥有相应的用户等级权益和角色权限
- 用户等级与角色体系独立，部分角色升级可参考用户等级，但两者不强绑定，用户可分别在两条成长路径上获得不同成就与权益

---

### 3. 个人中心等级

- 个人中心等级反映用户在平台的综合成长表现，包括但不限于活跃度、声望、任务完成度等
- 该等级与平台用户等级、角色体系并行存在，三者共同构建用户的多维成长与身份体系
- 个人中心等级划分为：初学者、探索者、进阶者、专家、大师和宗师六个等级
- 个人中心等级主要用于解锁个人页面展示功能、个性化设置和社区特权
> 个人中心等级可用于解锁专属展示、参与社区活动、获得特殊徽章等，提升用户在社区的归属感和荣誉感

---

### 4. 典型业务流程

1. 用户注册与实名认证
2. 用户通过法币充值获得平台积分或兑换券
3. 用户用积分/PXT购买角色服务包
4. 用户获得不同等级/类型的角色权限
5. 用户获得角色后，自动获得平台赋予的角色权限、收益分成、治理权重等
6. 用户通过活跃、质押、贡献等方式升级角色，提升等级和权益
7. 用户可通过内容创作、审核、任务等获得PAT等激励
8. 角色持有者可参与平台治理，投票权重与角色等级挂钩
9. 用户成长同步提升平台用户等级和个人中心等级
> 建议后续补充业务流程可视化图示，便于团队和合作方快速理解整体链路

---

### 5. 双币激励机制

#### 5.1 治理代币 PXT (Paper x Token)
- 平台治理、质押、升级、广告竞价、创作者身份认证等
- 固定总量，初期基于BSC发行，后续迁移至专属内容公链

#### 5.2 功能代币 PAT (Paper Author Token)
- 通过质押PXT挖矿、内容创作、审核任务、平台任务等方式获得
- 用于创作者等级升级、内容上传、广告投放、评论申诉等功能消耗
- 动态通胀机制，季度调节，PAT销毁与通胀率反向挂钩

#### 5.3 通胀机制
- PXT：固定总量,无通胀
- PAT：动态调节
  - 设定1-2%的年通胀率区间（基于当前流通量计算）
  - 每季度通胀上限0.5%
  - 首年增发按季度分批：
    - Q1：200万枚
    - Q2：300万枚
    - Q3：300万枚
    - Q4：200万枚
    - 首年总计1000万枚（约占初始流通量的1.5%）
  - 负通胀触发：
    - 当PAT总销毁量>当季增发量时，自动进入负通胀周期
    - 负通胀期间暂停季度增发
    - 持续到市场供需重新平衡  

#### 5.4 通胀决策会议机制
- 链上数据仪表盘：
  - PAT流通量/销毁量比值
  - PXT质押率/持币地址集中度
  - 平台内容上传量/广告消耗量
- 会议频率：
  - 每季度一次例会
  - 必要时召开临时会议
  - 年度大会确定全年目标
- 会议流程：
  - 平台数据分析报告
  - 经济模型讨论
  - 通胀率投票
  - 政策声明发布
- 信息披露机制：
  - 会前 30 天发布会议预告
  - 会议当天发布决议声明和通胀率
  - 会后一周发布详细会议纪要
- 信息发布渠道：
  - 官方网站
  - 社交媒体
  - 社区论坛
  - 项目公告
  - 邮件订阅  
- 决策委员会构成：
  - 核心团队代表
  - DAO社区代表
  - 知名创作者代表
  - 知名分享者代表
  - 独立经济学家
- 社区否决权：
  - 反对率超40%触发二次审计
  - 第三方机构重新评估

#### 5.5 双币风险隔离机制
- 价格脱钩触发器：
  - PAT价格7天低于初始价50%时激活
  - 暂停PXT质押挖PAT功能
  - 开放PAT与稳定币1:1兑换通道
- 独立价值锚定：
  - PXT：平台广告收入25%用于回购和销毁
  - PAT：支持外部合作方直接使用
   
#### 5.6 双币经济模型调节机制
- PAT销毁比例与通胀率反向挂钩：
  - 通胀率3%时，销毁50%功能费用
  - 通胀率0.5%时，销毁10%功能费用
- 双币组合支付：
  - 特权服务可用PAT+PXT组合支付
  - 同时销毁两种代币
- 组合支付方案：
  - 基础支付：100% PAT
  - 优惠方案1：70% PAT + 30% PXT（享受5%折扣）
  - 优惠方案2：50% PAT + 50% PXT（享受10%折扣）
  - 优惠方案3：30% PAT + 70% PXT（享受15%折扣）  
- 设置合理的PAT产出上限

#### 5.7 多重代币销毁机制
- PXT销毁：
  - 平台回购后50%直接销毁
  - 广告竞价消耗部分销毁
  - 违规惩罚销毁
- PAT销毁：
  - 视频分级功能销毁
  - 等级升级销毁
  - 加速推广销毁
  - 内容上传销毁
  - 广告投放销毁

#### 5.8 代币回购与危机响应
- 常规回购：
  - 平台广告收入的25%用于PXT回购
  - 功能费用的20%用于PAT回购并销毁
- 应急机制：
  - PXT暴跌超30%启动循环拍卖
  - 开放PAT销毁换取平台数字凭证通道

---

### 6. 中国大陆区域运营方案

#### 6.1 概述
- 中国大陆区域采用符合当地法规的积分系统与服务包机制
- 与国际版本保持业务协同，共享内容创作生态，但采用不同的激励机制
- 运营资源由国际版授权，获得2000万PXT额度支持中国大陆区域运营
- 所有机制严格遵守中国大陆相关法律法规，不涉及数字货币、加密资产等

#### 6.2 平台点数系统
- **PXT平台点数**（替代国际版PXT代币）：
  - 用户通过法币直接购买PXT平台点数
  - 用途：购买平台服务包、功能解锁、内容推广等
  - 严格限制：平台点数仅限平台内使用，不可转赠、交易或兑换为法币
  - 定价透明：以法币固定价格进行充值，如1元=10PXT点数

- **未来展望与过渡准备 (基于内部账本机制)**：
  - 为确保中国大陆用户在未来政策允许的情况下，能够将其通过法币购买获得的PXT平台点数转换为真实的PXT治理代币，平台将通过内部账本机制精确记录PXT平台点数的获取与消耗。
  - 此账本机制是实现未来"和平过渡"的关键技术支撑，与国际版预留的2000万PXT代币额度相结合，保障用户在合规前提下的潜在兑换权益。

- **PAT打赏点**（替代国际版PAT代币）：
  - 通过使用服务包功能、内容创作、完成任务等方式获得
  - 用途：用于平台内打赏创作者、解锁特定功能等
  - 获取途径：
    - 使用服务包功能时获得打赏点奖励
    - 创作/分享优质内容获得社区打赏
    - 完成平台日常任务和活动
  - 合规保障：
    - PAT打赏点不可兑换为法币或PXT点数
    - 设置每日获取上限，防止过度积累
    - 仅限平台内部流通使用

#### 6.3 服务包机制
- **服务包类型**：
  - 创作者服务包：提升内容创作权限、获得创作工具等
  - 分享者服务包：增加内容分享额度、提高分享权重等
  - 审核员服务包：开通内容审核权限、参与社区治理等
  - 通用服务包：通用功能增强，如额外存储空间、个性化设置等

- **服务包等级**：
  - 基础服务包：开通基本功能
  - 进阶服务包：增强功能，提供额外特权
  - 高级服务包：全功能解锁，获得专属标识
  - 专业服务包：获得定制化服务和最高级别权益

- **获取方式**：
  - 直接使用PXT点数购买对应服务包
  - 完成特定任务获得服务包奖励
  - 参与平台活动赢取服务包

- **权益内容**：
  - 功能性权益：扩展上传数量、提高活动参与优先级等
  - 体验性权益：界面个性化、专属标识和动效等
  - 社区性权益：专属社区身份、发言权重提升等

#### 6.4 激励与奖励机制
- **内容创作激励**：
  - 基于内容质量、用户互动情况获得PAT打赏点
  - 根据创作者等级和贡献提供额外PAT打赏点奖励
  - 优质创作者可获得平台推荐资源和流量扶持

- **社区贡献奖励**：
  - 参与内容审核获得PAT打赏点
  - 社区治理贡献获得平台荣誉和激励
  - 用户互助与分享行为的积分奖励

- **合规设计**：
  - 所有奖励基于实际贡献，非投资回报
  - 建立实名认证系统确保用户身份可追溯
  - PAT打赏点获取设有每日上限
  - 创作者通过官方计划获得收益，不直接兑换打赏点

#### 6.5 与国际版的关系
- 中国大陆版作为国际版生态的一部分，采用符合当地法规的运营方式
- 内容创作和分享机制与国际版协同，但经济模型相互独立
- 中国大陆版不涉及区块链资产交易，仅作为内容平台运营
- 国际版的2000万PXT支持转化为中国大陆版平台运营资源

#### 6.6 角色能力体系
- 替代国际版角色NFT系统，采用角色能力体系：
  - 角色能力通过使用服务包获得和提升
  - 角色级别:丁级、丙级、乙级、甲级、十绝、双十绝、至尊（能力递增）
  - 角色能力决定用户可使用的平台功能范围和权限
  - 能力提升依赖于活跃度、贡献度和使用服务包

- **能力点机制**：
  - 各级别角色提供不同的能力点数（2-10点不等）
  - 能力点用于平台核心功能的使用和解锁
  - 每日能力点有使用上限，次日自动恢复
  - 通过提升角色等级获得更多的能力点

- **艺术家荣誉机制**：
  - 优秀创作者长期无活动可获得"艺术家"荣誉称号
  - 需社区提名和投票，使用PXT点数参与
  - 获得称号后作品获专属展示和永久纪念

---

### 7. 收益分配与治理机制

- 所有平台激励、分红、治理权重等均与角色绑定
- 用户等级体系用于成长激励、功能解锁、展示荣誉
- 个人中心等级反映综合成长表现
- 平台采用DAO治理，角色持有者可参与提案、投票、重大决策

---

### 8. 技术架构与合规保障

#### 8.1 整体技术架构

- 采用现代化前后端分离架构，基于微服务设计
- 支持链上存储与链下计算混合模式
- 数据流与价值流分离，提高系统安全性和可扩展性
- 采用事件驱动模型，支持系统异步通信与实时更新
- 多层次缓存策略，优化用户体验和系统性能

#### 8.2 前端技术栈

- **核心框架**：React 19 + Next.js 15
- **状态管理**：Redux Toolkit + React Query
- **样式解决方案**：Tailwind CSS + Styled Components
- **UI组件库**：shadcn/ui + 自定义组件
- **Web3集成**：ethers.js + wagmi
- **国际化**：next-intl
- **媒体处理**：HLS.js（视频流）+ FFmpeg.wasm（客户端处理）
- **性能优化**：
  - React Server Components
  - 增量静态再生成(ISR)
  - 图像自动优化
  - 代码拆分与懒加载
  - Service Worker缓存策略
- **前端测试**：Jest + React Testing Library + Cypress

#### 8.3 后端架构

- **开发语言**：Golang
- **Web框架**：Gin
- **微服务通信**：gRPC + Protocol Buffers
- **API网关**：自定义Gateway + Kong
- **服务发现**：Consul
- **配置中心**：Nacos
- **数据存储**：
  - 关系型数据库：PostgreSQL（主数据库）
  - 文档数据库：MongoDB（日志、分析数据）
  - 缓存：Redis（多级缓存、分布式锁）
  - 全文搜索：Elasticsearch
  - 时序数据库：InfluxDB（性能监控）
- **消息队列**：Kafka（事件驱动）+ NSQ（实时消息）
- **区块链集成**：
  - 智能合约：Solidity v0.8.30 文档链接 https://docs.soliditylang.org/en/v0.8.30/
  - 链交互：Web3.js v4.15.0 文档链接 https://docs.web3js.org/ + go-ethereum
  - 多链支持：BSC（初期）+ 自建链（后期）
- **存储方案**：
  - IPFS（去中心化内容存储）
  - Arweave（永久存储）
  - Cloudflare R2（CDN与大规模内容分发）
  - MinIO（私有对象存储,如用户头像,用户主页背景）
  - backblaze (视频,漫画,小说,短剧,动漫,音乐储存方案)
- **监控告警**：Prometheus + Grafana + Jaeger
- **CI/CD**：GitHub Actions + ArgoCD
- **容器化**：Docker + Kubernetes

#### 8.4 微服务划分详情

1. **基础服务集群**：
   - **API网关服务** (api-gateway)：统一入口、路由分发、限流熔断、日志追踪
   - **用户服务** (user-service)：认证授权、用户管理、角色权限
   - **通知服务** (notify-service)：消息推送、邮件通知、系统公告

2. **业务服务集群**：
   - **内容服务** (content-service)：内容CRUD、分类管理、推荐算法
   - **审核服务** (audit-service)：内容审核流程、自动审核、人工审核
   - **数字凭证服务** (digital-certificate-service)：数字凭证发放、属性管理、升级机制
   - **市场服务** (market-service)：交易、服务包购买

3. **金融服务集群**：
   - **代币服务** (token-service)：代币发行、交易、分配
   - **钱包服务** (wallet-service)：钱包管理、交易签名、资产管理
   - **分红服务** (dividend-service)：收益计算、分配执行

4. **支撑服务集群**：
   - **存储服务** (storage-service)：文件上传、分发、转码
   - **搜索服务** (search-service)：全文搜索、内容索引
   - **分析服务** (analytics-service)：用户行为、内容热度、市场趋势

5. **管理服务集群**：
   - **管理后台服务** (admin-service)：平台运营、系统配置
   - **监控服务** (monitor-service)：系统性能、安全监控
   - **治理服务** (governance-service)：DAO提案、投票系统

#### 8.5 微服务通信机制

1. **同步通信**：
   - 服务间直接调用：gRPC（高性能二进制协议）
   - 外部API：RESTful API + GraphQL（灵活查询）

2. **异步通信**：
   - 事件驱动模式：Kafka作为事件总线
   - 消息订阅/发布：基于NSQ的实时消息通知
   - 任务调度：分布式定时任务（基于Asynq）

3. **服务网格**：
   - Istio管理服务通信
   - 支持流量控制、安全策略、可观测性

#### 8.6 数据一致性策略

1. **分布式事务**：
   - Saga模式处理跨服务事务
   - TCC（Try-Confirm-Cancel）保证最终一致性

2. **状态管理**：
   - CQRS模式分离读写操作
   - 事件溯源记录状态变更

3. **缓存策略**：
   - 多级缓存：本地缓存 + Redis分布式缓存
   - 缓存一致性：写穿 + 异步失效 + 定时刷新

#### 8.7 安全与合规

- 多因素认证与授权
- API安全（签名、加密、令牌）
- 数据加密（传输中加密、静态数据加密）
- 平台严格执行实名制、反洗钱、内容合规等监管要求
> 平台所有数字凭证仅作为身份和荣誉证明，不具备投资、分红、理财等金融属性，严格遵守中国大陆相关法律法规

#### 8.8 系统容量与性能优化

1. **扩展性设计**：
   - 水平扩展：服务无状态化，支持动态扩缩容
   - 数据库分库分表：按用户ID、内容ID进行分片

2. **性能优化**：
   - 内容CDN全球分发
   - 视频分级转码与自适应码率
   - 资源预加载与智能缓存
   - 按需加载与渐进式渲染

3. **高可用设计**：
   - 多区域部署与灾备
   - 服务熔断与降级策略
   - 自动恢复机制

---

### 9. 未来发展规划

- 主网迁移、数字资产管理、跨平台生态、全球市场推广等
- 持续优化双重成长体系，丰富用户等级的互动玩法

---

### 10. 补充说明/FAQ

> 如需详细的角色升级机制、用户等级成长规则、业务流程图等，可在后续文档中补充。

### 项目核心功能与目标

1. **分级展示机制**：

   - 内容分级为 A、B、C 三个级别：
     - **A 级**：正常内容
     - **B 级**：隐私内容
     - **C 级**：全部内容（包含 A 和 B）
   - 用户共通,用户唯一,可以查看不同分级下的内容
   - ABC 选项均包含支持视频、短视频、动漫、漫画、小说、音乐、文章板块

2. **双币激励机制**：

   A. 治理代币 PXT (Paper x Token) :
   - 初期基于BSC发行
   - 后续将构建专属内容公链，实现平滑迁移
   - 支持内容创作者在新公链上发行自己的代币
   - 固定总量：1亿枚代币 
   - 初始代币分配方案：
     - 平台生态基金（25%）：2500万枚，首期释放5%
     - 团队和顾问（15%）：1500万枚，5年递进解锁（首年5%，次年10%，第三年15%，第四年20%，第五年50%）
     - 社区激励（25%）：2500万枚
     - 中国大陆团队内部测试以及激励（20%）：2000万枚 团队在中国大陆运营 测试平台模式 类似于 抖音 和 tiktok
     - 流动性供应（15%）：1500万枚
   - 初期流通量：
     - 团队代币首次解锁：75万枚（5%）
     - 初始流动性：1500万枚
     - 生态基金首期释放：125万枚（5%）
     - 初期实际流通量约1700万枚
   - 主要用途：
     - 平台治理投票权 平台新增方案可以参与投票 也可以发起更新方案
     - 质押获取平台身份权限 用户质押代币升级权限
     - 广告位竞价排名
     - 创作者代币发行质押
     - 获得平台广告分红
     - 参与重大决策
     - 创作者改艺术家身份 用户消耗代币 参与给原创者铸造艺术家身份 然后用户列表可以 显示铸造的粉丝牌 
     - 艺术家身份铸造：铸造费用：1000 PXT 粉丝投票：最少需要50位持币用户投票 投票质押：每票需质押10 PXT（14天锁定期）

   B. 功能代币 PAT (Paper Author Token):
   - 初期基于BSC发行
   - 后续将随PXT迁移至自有公链
   - 无上限供应代币
   - 获取方式：
     - 质押PXT挖矿（前两年每年减产10%，第三年起减产20%）
     - 创作优质内容奖励（前6个月3倍奖励，50%锁仓6个月）
     - 审核任务奖励
     - 完成平台任务
     - 创作者特别激励：
       - 连续30天日更：额外30% PAT奖励
       - 月度最佳创作者：获得PXT空投
       - 季度爆款作品：作者获得平台角色权限
   - 刚性消耗场景：
     - 创作者等级升级
     - 上传内容支付
     - 投放广告费用
     - 评论申诉费用
     - 发起视频企划
   
   C. 通胀机制：
     - 详见前文5.3节通胀机制，采用动态调节与负通胀触发的模式，保持代币经济平衡
     
   D. 通胀决策会议机制：
   - 链上数据仪表盘：
     - PAT流通量/销毁量比值
     - PXT质押率/持币地址集中度
     - 平台内容上传量/广告消耗量
   - 会议频率：
     - 每季度一次例会
     - 必要时召开临时会议
     - 年度大会确定全年目标
   - 会议流程：
     - 平台数据分析报告
     - 经济模型讨论
     - 通胀率投票
     - 政策声明发布
   - 信息披露机制：
     - 会前 30 天发布会议预告
     - 会议当天发布决议声明和通胀率
     - 会后一周发布详细会议纪要
   - 信息发布渠道：
     - 官方网站
     - 社交媒体
     - 社区论坛
     - 项目公告
     - 邮件订阅  
   - 决策委员会构成：
     - 核心团队代表
     - DAO社区代表
     - 知名创作者代表
     - 知名分享者代表
     - 独立经济学家
   - 社区否决权：
     - 反对率超40%触发二次审计
     - 第三方机构重新评估

   E. 双币风险隔离机制：
   - 价格脱钩触发器：
     - PAT价格7天低于初始价50%时激活
     - 暂停PXT质押挖PAT功能
     - 开放PAT与稳定币1:1兑换通道
   - 独立价值锚定：
     - PXT：平台广告收入25%用于回购和销毁
     - PAT：支持外部合作方直接使用
   
   F. 双币经济模型调节机制：
   - PAT销毁比例与通胀率反向挂钩：
     - 通胀率3%时，销毁50%功能费用
     - 通胀率0.5%时，销毁10%功能费用
   - 双币组合支付：
     - 特权服务可用PAT+PXT组合支付
     - 同时销毁两种代币
   - 组合支付方案：
     - 基础支付：100% PAT
     - 优惠方案1：70% PAT + 30% PXT（享受5%折扣）
     - 优惠方案2：50% PAT + 50% PXT（享受10%折扣）
     - 优惠方案3：30% PAT + 70% PXT（享受15%折扣）  
   - 设置合理的PAT产出上限

   G. 多重代币销毁机制：
   - PXT销毁：
     - 平台回购后50%直接销毁
     - 广告竞价消耗部分销毁
     - 违规惩罚销毁
   - PAT销毁：
     - 视频分级功能销毁
     - 等级升级销毁
     - 加速推广销毁
     - 内容上传销毁
     - 广告投放销毁

   H. 代币回购与危机响应：
   - 常规回购：
     - 平台广告收入的25%用于PXT回购
     - 功能费用的20%用于PAT回购并销毁
   - 应急机制：
     - PXT暴跌超30%启动循环拍卖
     - 开放PAT销毁换取平台数字凭证通道

3. **分布式存储与支付**：

   - 使用 Backblaze B2 和 Cloudflare CDN 进行内容分发和存储
   - 支持 IPFS 和 Arweave 提供永久存储
   - 智能合约管理交易和分红

4. **审核与投票机制**：

   - 用户上传内容需经至少 10 位投票员审核
   - 投票通过率高于不通过率可发布到主站
   - 未通过内容可由知名创作者或知名分享者再次审核,最终决定发布与否
   - 再次未通过的话 需要股东出场参与审核内容,一票就可以通过是否发布与否
   - 知名创作者和知名分享者的内容无需审核直接发布

5. **双数据库存储模式**：

   - **PostgreSQL**：存储结构化数据（用户信息、系统配置等）
   - **MongoDB**：存储非结构化数据（日志、观看统计等）
   - **去中心化存储**：IPFS 和 Arweave 存储数字内容

6. **用户成长体系**：

   - 经验值系统（与代币分开计算）
   - 成就系统（首次上传、获得 100 个赞等）
   - 社交声望值（影响收益倍数）
   - 用户等级制度（升级获得额外权益）

7. **质押机制**：

   - 角色质押要求：

     - 审核员：最低质押 10000 代币
     - 分享者：最低质押 5000 代币
     - 创作者：最低质押 8000 代币
     - 质押量越高，获得的权益越多

   - 质押等级制度：

     - 质押等级制度（丁级、丙级、乙级、甲级、十绝、双十绝、至尊）


   - 质押与角色等级协同关系：
   
     - 质押等级是获取和升级角色的必要条件：用户必须达到相应质押等级才能获得对应等级的角色
     - 角色等级与权益直接相关，而质押等级决定获取角色的资格和升级路径
     - 例如：升级至甲级角色需要黄金级质押等级作为前提条件
     - 两者相辅相成：更高的质押等级解锁更高级的角色，更高级的角色带来更多平台权益

   - 质押收益机制：

     - 质押本金：始终属于质押者，可按规则解质押取回，金额不变
     - 工作收益：通过审核、创作、分享等工作获得的额外代币奖励
     - 质押奖励：连续好评每 30 天返还 1%质押代币作为奖励
     - 示例：
       - 质押 10000 代币（本金）
       - 30 天好评可得 100 代币（质押奖励）
       - 审核一个视频可得 100 代币（基础工作收益）
       - 质押本金 10000 代币按规则原额取回

   - 质押权益（仅影响工作收益，不影响质押本金）：

     - 质押等级制度（丁级、丙级、乙级、甲级、十绝、双十绝、至尊）
     - 注意：这些倍数只针对工作收益，质押本金始终是原额返还

   - 惩罚机制：

     - 轻微违规：警告，燃烧质押量的 5%
     - 中度违规：暂停权限 7 天，燃烧质押量的 10%
     - 严重违规：永久封禁，燃烧全部质押量
     - 违规燃烧的代币将永久销毁

   - 解质押规则（取回质押本金）：

     - 最短锁定期：30 天（首次质押必须锁定 30 天）
     - 解质押预约：提前 7 天申请（防止大量集中解质押）
     - 分批解质押：每次最多取回 50%质押量
     - 解质押冷却期：30 天后才能取回剩余质押量
     - 示例流程：
       - 用户质押 10000 代币
       - 30 天后可申请首次解质押
       - 最多可取回 5000 代币
       - 剩余 5000 代币需再等 30 天
       - 期间继续享受剩余质押量的权益

   - 额外权益：
     - 优先审核权
     - 更高的收益分成
     - 专属身份标识
     - 社区投票权重提升

8. **内容激励系统**：
   - 热度算法：
     - 观看时长权重（40%）
     - 互动率权重（30%）
     - 分享转发权重（20%）
     - 收藏率权重（10%）
   - 差异化奖励：
     - 原创内容额外 20%奖励
     - 高质量内容（AI 识别）额外 10%奖励
     - 首发内容额外 15%奖励

---

### 用户角色与权限

### 平台双币制度 治理代币 PXT (Paper X Token) 功能/奖励代币 PAT (Paper Activity Token)

1. **游客**：无需登录,可观看所有分级内容,使用设备以及 IP 地址进行自动创建用户,例如 IOS 游客 XXX,Android 游客 XXX,电脑游客 XXX 等,可记录历史观看和评论
2. **用户**：邮件接码注册登录后,等待管理员通过审核,或者输入邀请码直接完成注册,可记录历史观看和评论
3. **分享者**：用户质押PXT代币,升级成为分享者,消耗质押代币获得的PAT代币,开始发布非原创的内容,赚取PAT代币
4. **知名分享者**：分享者分享大量资源,达到等级要求,可选择消耗PXT代币晋升为知名分享者,继续获得PAT代币奖励,额外参与平台分红
5. **创作者**：用户质押PXT代币,升级成为创作者,消耗质押代币获得的PAT代币,开始发布原创或非原创内容,赚取PAT代币
6. **知名创作者**：创作者创作大量资源,达到等级要求,可选择消耗PXT代币晋升为知名创作者,继续获得PAT代币奖励,额外参与平台分红
7. **艺术家**：知名创作者停更180+天,平台可选择消耗PXT代币,即可开始为知名创作者设立艺术家身份,享受永久代币分红
   - 停更保护期：知名创作者，180天不活跃后自动进入候选名单
   - 认证条件：
     - 粉丝投票需来自至少50个独立地址
     - 投票质押代币锁定周期延长至14天
   - 权益升级：
     + 获得历史作品1%的永久版税
     + 可创建粉丝专属数字纪念品
8. **审核员**：用户质押PXT代币,升级成为审核员,消耗代币获得的PAT代币,开始审核内容赚取PAT代币
9. **知名审核员**：审核员审核大量内容,达到等级要求,可选择消耗PXT代币晋升为知名审核员,继续获得PAT代币奖励,额外参与平台分红
10. **全能员**：用户质押大量PXT代币,升级成为全能员,同时拥有创作者、分享者和审核员的所有权限,可以执行所有相关功能
11. **知名全能员**：全能员长期活跃且贡献突出,达到等级要求,可选择消耗PXT代币晋升为知名全能员,获得全平台最高权益,参与平台重要决策与分红
12. **股东**：初期平台创建参与者, 参与平台分红,可多位股东
13. **创始人**：平台创建者,系统初始化时指定,唯一性

---

### 收益分配机制

平台采用动态收益分配机制，通过季度会议调整具体参数

1. **基础收益分配**：

   - 分享者收益分配：
     - 基础分配：50%上传者，30%销毁，20%相关角色
     - 质押加成：根据质押量获得最高 20%的额外收益
     - 信用评级：根据历史表现获得最高 10%的额外收益
   - 创作者收益分配：
     - 基础分配：40%上传者，30%销毁，30%相关角色
     - 原创加成：原创内容可获得最高 20%的额外收益
     - 质量加成：高质量内容可获得最高 15%的额外收益

2. **平台收益分配**：

   - 广告收益分配：
     - 基础分红池：25%（用于知名角色分红）
     - 平台发展基金：45%
     - 平台维护费用：15%
     - 社区奖励池：10%
     - 市场回购：5%
     - 注意：基础分红池25%专门用于支付知名角色分红机制中的各项分成，包括创始人3%、股东10%、知名审核员2%、知名分享者4%、知名创作者6%，总计25%
   - 交易手续费分配：
     - 销毁：40%
     - 流动性池：30%
     - 社区奖励：30%

3. **知名角色分红机制**：

   - 分红周期：每季度结算一次
   - 基础分红比例：
     - 创始人：3%
     - 股东：10%
     - 知名审核员：2%
     - 知名分享者：4%
     - 知名创作者：6%
     - 知名全能员：3%
   - 动态调节机制：
     - 每季度通过决策会议审查调整
     - 根据贡献度和平台发展情况浮动 ±20%
     - 调整需经过 DAO 投票通过

4. **收益调节机制**：

   - 调节周期：
     - 与通胀决策会议同步（季度例会）
     - 特殊情况可召开临时会议
   - 调节范围：
     - 基础分配比例：±10%浮动
     - 额外收益比例：±20%浮动
     - 平台收益分配：±15%浮动
   - 调节流程：
     - 决策委员会提出建议方案
     - 经过 DAO 社区投票
     - 公示期 7 天
     - 正式实施

5. **透明度保障**：

   - 收益数据实时上链
   - 每日发布收益报告
   - 每周发布数据分析
   - 每月发布综合报告
   - 每季度发布战略调整报告

6. **特殊情况处理**：
   - 市场异常波动：可触发临时调节机制
   - 重大事件影响：可启动应急预案
   - 社区重大提案：可进行专项调整
   - 技术升级过渡：可实施过渡期方案

---

### 治理机制

1. **DAO 治理**：

   - 提案投票系统
   - 代币持有者可以：
     - 提议修改收益分配比例
     - 投票决定新功能开发方向
     - 参与平台重大决策
   - 投票权重与质押时长/数量挂钩

2. **社区建设**：

   - DAO 组织架构
   - 专项基金支持社区发展
   - 社区贡献积分制度
   - 社区大使计划

3. **数字凭证系统**：
   - 优质内容可获得数字凭证
   - 数字凭证持有者享受该内容收益分成
   - 数字凭证交易平台
   - 数字凭证质押机制

---

### 核心理念

- **公平透明**：通过 Web3 实现去中心化管理和社区自治
- **激励机制**：促进创作者经济发展,保障内容创作和分配的公平性
- **用户隐私保护**：采用区块链和加密技术,确保用户数据安全
- **全球化与本地化**：支持多语言和本地化运营
- **社区自治/内容审核**：内容审核和发布由社区决定,提高平台透明度和民主性

---

### 未来发展规划

- **主网迁移**：从 BSC 平滑迁移至主网
- **数字资产管理**：支持内容数字化,增加创作者收益模式
- **跨平台生态**：与其他 Web3 应用对接,拓展生态圈
- **全球市场推广**：扩大国际市场影响力,打造全球化平台

---

### 1. 后端技术栈选择

- 开发语言：Golang
- Web 框架：Gin
- ORM：GORM
- 数据库：PostgreSQL + MongoDB
- 区块链：BSC + Web3.js
- 存储：IPFS + Arweave + Cloudflare R2
- 缓存：Redis
- 消息队列：RabbitMQ/Kafka

### 2. 后端系统架构

- 微服务架构
- API 网关模式
- 分布式存储
- 事件驱动
- 消息队列

### 3. 后端服务端口分配

基础服务端口段：10000-10999

- API Gateway: 10000
- User Service: 10100
- Notify Service: 10200

业务服务端口段：11000-11999

- Content Service: 11000
- Audit Service: 11100
- Digital Certificate Service: 11200
- Market Service: 11300
- Chain Service: 11400

金融服务端口段：12000-12999

- Token Service: 12000
- Wallet Service: 12100
- Dividend Service: 12200

支撑服务端口段：13000-13999

- Storage Service: 13000
- Search Service: 13100
- Analytics Service: 13200

管理服务端口段：14000-14999

- Admin Service: 14000
- Monitor Service: 14100
- Governance Service: 14200

### 角色权限体系

#### 角色等级体系
- 丁级：基础等级，初始获得
- 丙级：进阶等级，具备基本特权
- 乙级：高级，拥有较多平台权益
- 甲级：精英级，显著提升收益和权限
- 十绝：专家级，获得稀有特权和高额分成
- 双十绝：大师级，拥有平台核心权益
- 至尊：顶级存在，独特身份与最高权益

#### 角色等级提升机制
- 丁→丙：累计活跃30天+质押1000 PXT
- 丙→乙：累计活跃90天+质押3000 PXT+完成50次贡献
- 乙→甲：累计活跃180天+质押8000 PXT+获得100次好评
- 甲→十绝：持续一年活跃+质押15000 PXT+内容被收藏500次
- 十绝→双十绝：持续两年活跃+质押30000 PXT+培养3个甲级以上用户
- 双十绝→至尊：平台投票选举+质押50000 PXT+特殊贡献认证

#### 角色属性与权益
- 共享属性：
  - 经验值积累速度
  - 声望影响力
  - 收益分成比例
  - 质押奖励倍数
- 分享者角色特性：
  - 丁级：基础分享权限
  - 丙级：额外5%PAT奖励
  - 乙级：内容推荐权重+10%
  - 甲级：每月免费推广机会×3
  - 十绝：平台广告收益分成2%
  - 双十绝：可创建专属内容频道
  - 至尊：独家平台活动主办权
- 创作者角色特性：
  - 丁级：基础创作权限
  - 丙级：额外8%PAT奖励
  - 乙级：内容版权保护增强
  - 甲级：独立作品页面展示
  - 十绝：专属粉丝社区权限
  - 双十绝：发行限定创作数字凭证
  - 至尊：作品永久收益分成
- 审核员角色特性：
  - 丁级：基础审核权限
  - 丙级：审核速度提升10%
  - 乙级：特定分类审核专家
  - 甲级：审核决策权重加倍
  - 十绝：内容质量评定权
  - 双十绝：审核标准制定参与
  - 至尊：终审否决权
- 全能员角色特性：
  - 丁级：创作者+分享者+审核员基础权限
  - 丙级：额外10%PAT总奖励
  - 乙级：全平台功能优先体验
  - 甲级：跨角色特权组合使用
  - 十绝：参与平台核心决策
  - 双十绝：平台专属功能定制权
  - 至尊：战略发展投票一票双倍权重

#### 能量点机制
- 平台引入能量点机制，根据角色等级和活跃度计算：
  - 丁级角色：每天提供2点能量
  - 丙级角色：每天提供3点能量
  - 乙级角色：每天提供4点能量
  - 甲级角色：每天提供5点能量
  - 十绝角色：每天提供6点能量
  - 双十绝角色：每天提供8点能量
  - 至尊角色：每天提供10点能量
  - 全能员额外奖励：每天额外提供2点能量
- 角色等级直接影响能量点贡献：等级越高提供的能量点越多，从而使用户能够执行更多平台操作
- 能量点每天上限为20点，初始用户每天可使用5点能量
- 能量点用于内容发布、审核、收益领取等，每消耗一次减少一点，次日自动重置
- 能量点机制鼓励用户升级角色，提升平台活跃度和经济健康度

#### 知名角色晋升机制
- 只有达到青提等级的创作者、分享者、审核员，且消耗一定数量的PXT代币，才有资格晋升为"知名创作者""知名分享者""知名审核员"
- 平台设定明确的晋升条件（如内容数量、好评率、审核准确率等），用户达标后可申请晋升
- 晋升流程包括自动检测、社区/平台审核、授予知名身份及专属权益
- 知名身份动态调整，长期不活跃或违规可降级