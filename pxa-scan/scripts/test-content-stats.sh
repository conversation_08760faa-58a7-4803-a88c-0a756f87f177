#!/bin/bash

# 测试内容统计修复脚本
# 用于验证前端统计数据是否与实际内容记录一致

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "${BLUE}🔄 $1${NC}"
}

# 配置
API_BASE_URL="http://localhost:8080"
FRONTEND_URL="http://localhost:3000"

echo "📊 内容统计修复测试脚本"
echo "=================================="

# 检查后端服务
print_step "检查后端服务状态..."
if curl -s "${API_BASE_URL}/health" > /dev/null 2>&1; then
    print_success "后端服务运行正常"
else
    print_error "后端服务未运行，请先启动后端服务"
    exit 1
fi

# 1. 获取链上内容记录
print_step "获取链上内容记录..."
ONCHAIN_RESPONSE=$(curl -s "${API_BASE_URL}/api/v1/content/on-chain?limit=100")
ONCHAIN_COUNT=$(echo "$ONCHAIN_RESPONSE" | jq -r '.data.contents | length' 2>/dev/null || echo "0")
echo "链上内容记录数: $ONCHAIN_COUNT"

# 2. 获取IPFS内容记录
print_step "获取IPFS内容记录..."
IPFS_RESPONSE=$(curl -s "${API_BASE_URL}/api/v1/content/ipfs?limit=100")
IPFS_COUNT=$(echo "$IPFS_RESPONSE" | jq -r '.data.contents | length' 2>/dev/null || echo "0")
echo "IPFS内容记录数: $IPFS_COUNT"

# 3. 获取链上内容统计
print_step "获取链上内容统计..."
ONCHAIN_STATS_RESPONSE=$(curl -s "${API_BASE_URL}/api/v1/content/on-chain/stats")
ONCHAIN_STATS_TOTAL=$(echo "$ONCHAIN_STATS_RESPONSE" | jq -r '.data.total_count' 2>/dev/null || echo "0")
echo "链上内容统计总数: $ONCHAIN_STATS_TOTAL"

# 4. 获取IPFS内容统计
print_step "获取IPFS内容统计..."
IPFS_STATS_RESPONSE=$(curl -s "${API_BASE_URL}/api/v1/content/ipfs/stats")
IPFS_STATS_TOTAL=$(echo "$IPFS_STATS_RESPONSE" | jq -r '.data.total_count' 2>/dev/null || echo "0")
echo "IPFS内容统计总数: $IPFS_STATS_TOTAL"

# 5. 分析数据一致性
print_step "分析数据一致性..."

echo ""
echo "📋 数据对比结果:"
echo "=================================="
echo "链上内容记录数:     $ONCHAIN_COUNT"
echo "链上内容统计总数:   $ONCHAIN_STATS_TOTAL"
echo "IPFS内容记录数:     $IPFS_COUNT"
echo "IPFS内容统计总数:   $IPFS_STATS_TOTAL"

# 计算总记录数（去重）
TOTAL_UNIQUE_RECORDS=$((ONCHAIN_COUNT + IPFS_COUNT))
echo "总记录数(简单相加): $TOTAL_UNIQUE_RECORDS"

# 检查一致性
ONCHAIN_CONSISTENT=false
IPFS_CONSISTENT=false

if [ "$ONCHAIN_COUNT" -eq "$ONCHAIN_STATS_TOTAL" ]; then
    print_success "链上内容记录与统计一致"
    ONCHAIN_CONSISTENT=true
else
    print_warning "链上内容记录与统计不一致 (记录:$ONCHAIN_COUNT vs 统计:$ONCHAIN_STATS_TOTAL)"
fi

if [ "$IPFS_COUNT" -eq "$IPFS_STATS_TOTAL" ]; then
    print_success "IPFS内容记录与统计一致"
    IPFS_CONSISTENT=true
else
    print_warning "IPFS内容记录与统计不一致 (记录:$IPFS_COUNT vs 统计:$IPFS_STATS_TOTAL)"
fi

# 6. 测试前端统计修复
print_step "测试前端统计修复..."

if command -v jq > /dev/null 2>&1; then
    # 分析记录状态分布
    print_info "分析记录状态分布..."
    
    if [ "$ONCHAIN_COUNT" -gt 0 ]; then
        echo "链上内容状态分布:"
        echo "$ONCHAIN_RESPONSE" | jq -r '.data.contents[] | .status' | sort | uniq -c | while read count status; do
            echo "  $status: $count"
        done
    fi
    
    if [ "$IPFS_COUNT" -gt 0 ]; then
        echo "IPFS内容状态分布:"
        echo "$IPFS_RESPONSE" | jq -r '.data.contents[] | .status' | sort | uniq -c | while read count status; do
            echo "  $status: $count"
        done
    fi
else
    print_warning "jq未安装，跳过详细分析"
fi

# 7. 生成修复建议
echo ""
echo "🔧 修复建议:"
echo "=================================="

if [ "$ONCHAIN_CONSISTENT" = false ] || [ "$IPFS_CONSISTENT" = false ]; then
    print_warning "发现统计数据不一致问题"
    echo "建议操作:"
    echo "1. 重启后端服务以加载最新的统计修复代码"
    echo "2. 刷新前端页面，新的统计逻辑会基于实际记录计算统计"
    echo "3. 检查异步上链任务是否正常完成"
    echo "4. 如果问题持续，可能需要手动同步数据库记录"
else
    print_success "统计数据一致性良好"
fi

# 8. 提供有用的链接
echo ""
echo "🔗 相关链接:"
echo "=================================="
echo "- 前端内容页面: ${FRONTEND_URL}/zh-TW/content"
echo "- 链上内容API: ${API_BASE_URL}/api/v1/content/on-chain"
echo "- IPFS内容API: ${API_BASE_URL}/api/v1/content/ipfs"
echo "- 链上统计API: ${API_BASE_URL}/api/v1/content/on-chain/stats"
echo "- IPFS统计API: ${API_BASE_URL}/api/v1/content/ipfs/stats"

print_success "内容统计测试完成！"

# 9. 返回适当的退出码
if [ "$ONCHAIN_CONSISTENT" = true ] && [ "$IPFS_CONSISTENT" = true ]; then
    exit 0
else
    exit 1
fi
