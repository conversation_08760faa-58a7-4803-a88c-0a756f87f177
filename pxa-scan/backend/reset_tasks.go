package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	// 连接MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI("******************************************************************************************"))
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}
	defer client.Disconnect(ctx)

	db := client.Database("pxpac_explorer")
	collection := db.Collection("async_upload_tasks")

	// 删除所有任务
	result, err := collection.DeleteMany(ctx, bson.M{})
	if err != nil {
		log.Fatal("Failed to delete tasks:", err)
	}

	fmt.Printf("Deleted %d tasks\n", result.DeletedCount)
	fmt.Println("All tasks have been cleared from the database")
}
