package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type AsyncUploadTask struct {
	ID          string      `bson:"_id" json:"id"`
	ContentID   interface{} `bson:"content_id" json:"content_id"`
	CreatorID   interface{} `bson:"creator_id" json:"creator_id"`
	Title       string      `bson:"title" json:"title"`
	ContentType string      `bson:"content_type" json:"content_type"`
	Status      string      `bson:"status" json:"status"`
	CreatedAt   time.Time   `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time   `bson:"updated_at" json:"updated_at"`
}

func main() {
	// 连接MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI("******************************************************************************************"))
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}
	defer client.Disconnect(ctx)

	db := client.Database("pxpac_explorer")
	collection := db.Collection("async_upload_tasks")

	// 查询所有任务
	cursor, err := collection.Find(ctx, bson.M{})
	if err != nil {
		log.Fatal("Failed to find tasks:", err)
	}
	defer cursor.Close(ctx)

	fmt.Println("=== All async upload tasks ===")
	count := 0
	for cursor.Next(ctx) {
		var task AsyncUploadTask
		if err := cursor.Decode(&task); err != nil {
			log.Printf("Failed to decode task: %v", err)
			continue
		}
		count++
		fmt.Printf("Task %d:\n", count)
		fmt.Printf("  ID: %s\n", task.ID)
		fmt.Printf("  ContentID: %v\n", task.ContentID)
		fmt.Printf("  CreatorID: %v\n", task.CreatorID)
		fmt.Printf("  Title: %s\n", task.Title)
		fmt.Printf("  ContentType: %s\n", task.ContentType)
		fmt.Printf("  Status: %s\n", task.Status)
		fmt.Printf("  CreatedAt: %s\n", task.CreatedAt.Format(time.RFC3339))
		fmt.Printf("  UpdatedAt: %s\n", task.UpdatedAt.Format(time.RFC3339))
		fmt.Println()
	}

	if count == 0 {
		fmt.Println("No tasks found in the database")
	} else {
		fmt.Printf("Total tasks found: %d\n", count)
	}

	// 查询pending任务
	fmt.Println("=== Pending tasks ===")
	cursor2, err := collection.Find(ctx, bson.M{"status": "pending"})
	if err != nil {
		log.Fatal("Failed to find pending tasks:", err)
	}
	defer cursor2.Close(ctx)

	pendingCount := 0
	for cursor2.Next(ctx) {
		var task AsyncUploadTask
		if err := cursor2.Decode(&task); err != nil {
			log.Printf("Failed to decode pending task: %v", err)
			continue
		}
		pendingCount++
		fmt.Printf("Pending Task %d: ID=%s, Status=%s\n", pendingCount, task.ID, task.Status)
	}

	if pendingCount == 0 {
		fmt.Println("No pending tasks found")
	} else {
		fmt.Printf("Total pending tasks: %d\n", pendingCount)
	}
}
