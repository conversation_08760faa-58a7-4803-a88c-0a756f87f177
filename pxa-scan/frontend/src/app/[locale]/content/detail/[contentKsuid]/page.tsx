import ContentDetailPageClient from '@/components/Content/ContentDetailPageClient';
import { isValidLocale } from '@/lib/i18n';
import { getContentDictionary } from '@/lib/modularDictionary';
import { notFound } from 'next/navigation';

interface ContentDetailPageProps {
  params: Promise<{
    locale: string;
    contentKsuid: string;
  }>;
}

export default async function ContentDetailPage({ params }: ContentDetailPageProps) {
  const { locale, contentKsuid } = await params;

  if (!isValidLocale(locale)) {
    notFound();
  }

  // 现在 contentKsuid 就是 content_ksuid，也是 on_chain_id
  const onChainId = contentKsuid; // 现在两者相同

  // 在服务器端预加载翻译字典
  const dict = await getContentDictionary(locale);

  return (
    <ContentDetailPageClient
      locale={locale}
      onChainId={onChainId}
      contentKsuid={contentKsuid}
      dict={dict}
    />
  );
}
