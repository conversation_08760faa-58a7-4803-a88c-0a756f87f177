const { ethers, network } = require("hardhat");

async function main() {
    console.log("💰 给跨链池充值BNB手续费");
    console.log("================================================");
    
    const [deployer] = await ethers.getSigners();
    console.log("操作账户:", deployer.address);
    
    // 从配置文件获取跨链池地址
    const fs = require("fs");
    const path = require("path");
    const configFile = path.join(__dirname, "../config/addresses.json");
    const addresses = JSON.parse(fs.readFileSync(configFile, 'utf8')).addresses;
    const CROSS_CHAIN_POOL = addresses.bsc.crossChainPool;
    console.log("跨链池地址:", CROSS_CHAIN_POOL);
    
    console.log("\n=== 1. 检查当前余额 ===");
    
    const deployerBalance = await deployer.provider.getBalance(deployer.address);
    const poolBalance = await deployer.provider.getBalance(CROSS_CHAIN_POOL);

    console.log("部署者BNB余额:", ethers.formatEther(deployerBalance), "BNB");
    console.log("跨链池BNB余额:", ethers.formatEther(poolBalance), "BNB");

    // 计算需要转账的金额
    const transferAmount = ethers.parseEther("1.0"); // 转账1 BNB
    const minRequiredBalance = ethers.parseEther("0.1"); // 跨链池至少需要0.1 BNB

    console.log("计划转账:", ethers.formatEther(transferAmount), "BNB");
    console.log("目标余额:", ethers.formatEther(minRequiredBalance + transferAmount), "BNB");

    if (deployerBalance < (transferAmount + ethers.parseEther("0.1"))) {
        console.error("❌ 部署者BNB余额不足");
        console.error("需要至少:", ethers.formatEther(transferAmount + ethers.parseEther("0.1")), "BNB");
        process.exit(1);
    }
    
    console.log("\n=== 2. 执行转账 ===");
    
    try {
        console.log("正在转账", ethers.formatEther(transferAmount), "BNB 给跨链池...");

        const transferTx = await deployer.sendTransaction({
            to: CROSS_CHAIN_POOL,
            value: transferAmount,
            gasLimit: 21000 // 标准转账Gas限制
        });
        
        console.log("转账交易哈希:", transferTx.hash);
        console.log("等待交易确认...");
        
        const receipt = await transferTx.wait();
        console.log("✅ 转账成功！");
        console.log("区块号:", receipt.blockNumber);
        console.log("Gas使用:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.error("❌ 转账失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 3. 验证转账结果 ===");
    
    const newDeployerBalance = await deployer.provider.getBalance(deployer.address);
    const newPoolBalance = await deployer.provider.getBalance(CROSS_CHAIN_POOL);

    console.log("部署者新BNB余额:", ethers.formatEther(newDeployerBalance), "BNB");
    console.log("跨链池新BNB余额:", ethers.formatEther(newPoolBalance), "BNB");

    const balanceIncrease = newPoolBalance - poolBalance;
    console.log("跨链池余额增加:", ethers.formatEther(balanceIncrease), "BNB");

    if (balanceIncrease === transferAmount) {
        console.log("✅ 转账金额正确");
    } else {
        console.log("⚠️  转账金额不匹配");
    }
    
    console.log("\n=== 4. 估算可执行的跨链次数 ===");
    
    // 估算单次跨链的Gas费用
    const estimatedGasPerBridge = ethers.parseEther("0.002"); // 估算每次跨链需要0.002 BNB
    const possibleBridges = newPoolBalance / estimatedGasPerBridge;

    console.log("估算单次跨链Gas费:", ethers.formatEther(estimatedGasPerBridge), "BNB");
    console.log("可执行跨链次数:", Math.floor(Number(possibleBridges)).toString(), "次");

    if (newPoolBalance >= ethers.parseEther("0.1")) {
        console.log("✅ 跨链池BNB余额充足，可以进行跨链操作");

        console.log("\n🚀 现在可以执行跨链:");
        console.log("npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network", network.name);

    } else {
        console.log("⚠️  跨链池BNB余额仍然不足，建议再转入更多BNB");
    }

    console.log("\n💰 充值完成！");
    console.log("================================================");
    console.log("✅ 跨链池地址:", CROSS_CHAIN_POOL);
    console.log("✅ 当前BNB余额:", ethers.formatEther(newPoolBalance), "BNB");
    console.log("✅ PAT代币余额: 50,000,000 PAT");
    console.log("✅ 已准备好进行大额跨链操作");
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("充值失败:", error);
            process.exit(1);
        });
}

module.exports = main;
