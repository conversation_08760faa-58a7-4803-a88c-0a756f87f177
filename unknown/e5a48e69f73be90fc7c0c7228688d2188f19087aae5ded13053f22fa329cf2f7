// 检查BSC测试网和PXA本地链的连接状态
const { ethers } = require("hardhat");

async function main() {
    console.log("\n🔍 检查BSC测试网和PXA本地链连接状态");
    console.log("=====================================");
    
    console.log("\n=== 1. 检查BSC测试网连接 ===");
    
    try {
        // BSC测试网连接
        const bscRpc = "https://data-seed-prebsc-1-s1.binance.org:8545";
        const bscProvider = new ethers.JsonRpcProvider(bscRpc);

        const bscNetwork = await bscProvider.getNetwork();
        const bscBlockNumber = await bscProvider.getBlockNumber();
        const bscFeeData = await bscProvider.getFeeData();

        console.log("✅ BSC测试网连接正常");
        console.log("- RPC URL:", bscRpc);
        console.log("- 链ID:", bscNetwork.chainId.toString());
        console.log("- 当前区块:", bscBlockNumber);
        console.log("- Gas价格:", ethers.formatUnits(bscFeeData.gasPrice || 0n, "gwei"), "Gwei");
        
    } catch (error) {
        console.error("❌ BSC测试网连接失败:", error.message);
    }
    
    console.log("\n=== 2. 检查PXA本地链连接 ===");
    
    try {
        // PXA本地链连接
        const pxaRpc = "http://127.0.0.1:8545";
        const pxaProvider = new ethers.JsonRpcProvider(pxaRpc);

        const pxaNetwork = await pxaProvider.getNetwork();
        const pxaBlockNumber = await pxaProvider.getBlockNumber();
        const pxaFeeData = await pxaProvider.getFeeData();

        console.log("✅ PXA本地链连接正常");
        console.log("- RPC URL:", pxaRpc);
        console.log("- 链ID:", pxaNetwork.chainId.toString());
        console.log("- 当前区块:", pxaBlockNumber);
        console.log("- Gas价格:", ethers.formatUnits(pxaFeeData.gasPrice || 0n, "gwei"), "Gwei");
        
        // 检查链ID是否正确
        if (pxaNetwork.chainId !== 327) {
            console.log("⚠️ PXA链ID不是327，当前:", pxaNetwork.chainId.toString());
        }
        
    } catch (error) {
        console.error("❌ PXA本地链连接失败:", error.message);
        console.log("💡 请确保PXA本地链正在运行:");
        console.log("   cd ../pxa-chain/local-chain && ./local-chain-setup.sh");
    }
    
    console.log("\n=== 3. 检查账户余额 ===");
    
    try {
        const [deployer] = await ethers.getSigners();
        console.log("📝 部署者地址:", deployer.address);
        
        // BSC测试网余额
        const bscProvider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545");
        const bscBalance = await bscProvider.getBalance(deployer.address);
        console.log("💰 BSC测试网余额:", ethers.formatEther(bscBalance), "BNB");

        // PXA本地链余额
        try {
            const pxaProvider = new ethers.JsonRpcProvider("http://127.0.0.1:8545");
            const pxaBalance = await pxaProvider.getBalance(deployer.address);
            console.log("💰 PXA本地链余额:", ethers.formatEther(pxaBalance), "PXA");
        } catch (error) {
            console.log("⚠️ 无法获取PXA本地链余额:", error.message);
        }
        
    } catch (error) {
        console.error("❌ 账户余额检查失败:", error.message);
    }
    
    console.log("\n=== 4. 网络配置验证 ===");
    
    console.log("📋 Hardhat网络配置:");
    console.log("- bscTestnet: ✅ 已配置");
    console.log("- pxaLocal: ✅ 已配置 (新增)");
    console.log("- localhost: ✅ 已配置");
    
    console.log("\n💡 使用建议:");
    console.log("1. BSC测试网操作使用: --network bscTestnet");
    console.log("2. PXA本地链操作使用: --network pxaLocal 或 --network localhost");
    console.log("3. 跨链测试流程:");
    console.log("   a) BSC发起跨链: npx hardhat run scripts/cross-chain/bridge-to-pxa-local.js --network bscTestnet");
    console.log("   b) PXA接收跨链: cd ../pxa-chain && npx hardhat run scripts/bridge/mint-bridged-tokens.js --network localhost");
    
    console.log("\n🎊 连接状态检查完成!");
    console.log("=====================================");
}

// 错误处理
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 脚本执行失败:", error);
        process.exit(1);
    });
