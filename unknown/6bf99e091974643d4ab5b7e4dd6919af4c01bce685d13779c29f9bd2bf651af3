# 治理提案指南

## 📋 概述

治理提案是PXT-PAT代币系统的核心治理机制，允许代币持有者通过投票来决定系统参数的调整和重要决策。

## 🎯 提案类型

### 1. 参数调整提案 (Parameter)
用于调整系统的各种参数设置。

#### 1.1 质押系统参数
- **最小质押金额**: 调整用户质押的最低门槛
- **质押等级阈值**: 修改各个质押等级的门槛要求
- **解锁期间范围**: 调整最小/最大解锁等待时间
- **紧急提取手续费**: 设置紧急提取的手续费率
- **早期解锁惩罚**: 调整提前解锁的惩罚比例

#### 1.2 代币系统参数
- **PAT通胀率**: 调整PAT代币的年通胀率
- **最大供应量**: 修改PAT代币的最大发行量
- **质押奖励池**: 更换质押奖励池合约地址

#### 1.3 治理系统参数
- **最小提案者等级**: 调整创建提案的最低质押等级要求
- **投票锁定期**: 设置投票后的代币锁定时间
- **社区否决阈值**: 调整触发社区否决的反对票比例
- **最小参与率**: 设置提案通过的最低参与率要求
- **执行延迟**: 调整提案通过后的执行等待时间
- **质押等级权重**: 修改不同质押等级的投票权重

### 2. 资金管理提案 (Funding)
- **国库资金分配**: 决定国库资金的使用方向
- **开发资金申请**: 为开发项目申请资金支持
- **营销推广预算**: 分配营销活动的预算
- **合作伙伴激励**: 为合作伙伴提供激励资金

### 3. 系统升级提案 (Upgrade)
- **合约升级**: 升级智能合约的功能
- **新功能添加**: 为系统添加新的功能模块
- **安全补丁**: 修复发现的安全漏洞
- **性能优化**: 优化系统性能和gas消耗

### 4. 成员管理提案 (Membership)
- **审计员任命**: 任命或移除系统审计员
- **多签成员调整**: 调整多重签名的成员组成
- **权限角色分配**: 分配或撤销特殊权限角色

### 5. 紧急提案 (Emergency)
- **紧急暂停**: 在发现严重问题时紧急暂停系统
- **紧急修复**: 快速修复关键安全问题
- **危机响应**: 应对市场或技术危机

## 🔧 提案创建示例

### 示例1: 调整最小质押金额

```javascript
// 目标合约: StakingPool
// 函数: setMinStakeAmount(uint256 _minStakeAmount)
// 参数: 500 PXT (500 * 10^18 wei)

const targets = [stakingPoolAddress];
const values = [0];
const calldatas = [
    stakingPool.interface.encodeFunctionData("setMinStakeAmount", [
        ethers.utils.parseEther("500")
    ])
];

await proposalManager.createProposal(
    0, // ProposalType.Parameter
    startTime,
    endTime,
    "调整最小质押金额",
    "将最小质押金额从1 PXT提高到500 PXT，以提高参与门槛",
    targets,
    values,
    calldatas
);
```

### 示例2: 修改PAT通胀率

```javascript
// 目标合约: PAToken
// 函数: setInflationRate(uint256 _newRate)
// 参数: 200 基点 (2%)

const targets = [paTokenAddress];
const values = [0];
const calldatas = [
    paToken.interface.encodeFunctionData("setInflationRate", [200])
];

await proposalManager.createProposal(
    0, // ProposalType.Parameter
    startTime,
    endTime,
    "调整PAT通胀率",
    "将PAT年通胀率从1.5%调整为2%，以增加流动性",
    targets,
    values,
    calldatas
);
```

### 示例3: 修改治理参数

```javascript
// 目标合约: ProposalManager
// 函数: setMinParticipationRate(uint256 _rate)
// 参数: 15 (15%)

const targets = [proposalManagerAddress];
const values = [0];
const calldatas = [
    proposalManager.interface.encodeFunctionData("setMinParticipationRate", [15])
];

await proposalManager.createProposal(
    0, // ProposalType.Parameter
    startTime,
    endTime,
    "提高最小参与率",
    "将治理提案的最小参与率从10%提高到15%，确保更广泛的社区参与",
    targets,
    values,
    calldatas
);
```

## 📊 提案流程

1. **创建阶段**: 符合条件的用户创建提案
2. **讨论阶段**: 社区讨论提案内容（可选）
3. **投票阶段**: 代币持有者进行投票
4. **执行阶段**: 通过的提案在延迟期后自动执行

## ⚖️ 投票权重计算

投票权重 = 质押金额 × 质押等级权重 ÷ 100

质押等级权重:
- 丁级: 100%
- 丙级: 150%
- 乙级: 200%
- 甲级: 250%
- 十绝: 300%
- 双十绝: 350%
- 至尊: 400%

## 🎯 提案通过条件

1. **参与率要求**: 参与投票的总权重 ≥ 最小参与率
2. **多数支持**: 赞成票 > 反对票
3. **社区否决**: 反对票比例 < 社区否决阈值

## 🚀 最佳实践

1. **详细描述**: 提案应包含详细的背景、目标和影响分析
2. **社区讨论**: 在正式投票前进行充分的社区讨论
3. **渐进调整**: 参数调整应该是渐进的，避免剧烈变化
4. **风险评估**: 评估提案可能带来的风险和副作用
5. **执行验证**: 提案执行后验证结果是否符合预期

## 📝 注意事项

- 只有达到最低质押等级的用户才能创建提案
- 投票期间用户的代币会被锁定
- 提案执行有延迟期，给社区时间做最后检查
- 紧急提案有特殊的快速通道机制
