#!/bin/bash

# 激活image内容类型的脚本

echo "🔧 激活image内容类型..."

# 检查是否在正确的目录
if [ ! -f "hardhat.config.js" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 创建临时激活脚本
cat > temp_activate_image_type.js << 'EOF'
const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔧 激活image内容类型...");
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者:", deployer.address);
    
    try {
        // 1. 加载合约
        const deploymentDir = path.join(__dirname, "deployments", network.name);
        const contentFile = path.join(deploymentDir, "content-deployment.json");
        
        const contentDeployment = JSON.parse(fs.readFileSync(contentFile, 'utf8'));
        const contentRegistryAddr = contentDeployment.contracts.ContentRegistry.address;
        
        const ContentRegistry = await ethers.getContractFactory("ContentRegistry");
        const contentRegistry = ContentRegistry.attach(contentRegistryAddr);
        
        console.log("✅ 合约加载成功");
        console.log("ContentRegistry地址:", contentRegistry.address);
        
        // 2. 检查当前状态
        console.log("\n=== 当前内容类型状态 ===");
        const allTypes = await contentRegistry.getAllContentTypes();
        const activeTypes = await contentRegistry.getActiveContentTypes();
        
        console.log("所有内容类型:", allTypes);
        console.log("激活的内容类型:", activeTypes);
        console.log("image类型是否激活:", activeTypes.includes("image"));
        
        // 3. 检查image类型是否存在
        if (!allTypes.includes("image")) {
            console.log("❌ image类型不存在，需要先添加");
            
            // 尝试添加image类型
            console.log("添加image内容类型...");
            try {
                const addTx = await contentRegistry.connect(deployer).addContentType(
                    "image",
                    ethers.utils.parseEther("0.1"), // 0.1 PAT费用
                    true // 立即激活
                );
                await addTx.wait();
                console.log("✅ image类型添加成功");
                console.log("交易哈希:", addTx.hash);
            } catch (addError) {
                console.log("❌ 添加image类型失败:", addError.message);
                
                // 如果添加失败，可能是权限问题
                if (addError.message.includes("Ownable")) {
                    console.log("💡 可能原因: 只有合约所有者可以添加内容类型");
                    console.log("💡 解决方案: 使用合约所有者账户运行此脚本");
                }
                return;
            }
        } else if (!activeTypes.includes("image")) {
            console.log("image类型存在但未激活，尝试激活...");
            
            try {
                // 尝试激活image类型
                const activateTx = await contentRegistry.connect(deployer).setContentTypeActive("image", true);
                await activateTx.wait();
                console.log("✅ image类型激活成功");
                console.log("交易哈希:", activateTx.hash);
            } catch (activateError) {
                console.log("❌ 激活image类型失败:", activateError.message);
                
                if (activateError.message.includes("Ownable")) {
                    console.log("💡 可能原因: 只有合约所有者可以激活内容类型");
                    console.log("💡 解决方案: 使用合约所有者账户运行此脚本");
                }
                return;
            }
        } else {
            console.log("✅ image类型已经激活");
        }
        
        // 4. 验证激活结果
        console.log("\n=== 验证激活结果 ===");
        const newActiveTypes = await contentRegistry.getActiveContentTypes();
        console.log("新的激活类型列表:", newActiveTypes);
        console.log("image类型是否激活:", newActiveTypes.includes("image"));
        
        if (newActiveTypes.includes("image")) {
            console.log("🎉 image类型激活成功！");
            
            // 检查费用
            const imageFee = await contentRegistry.getContentFee("image");
            console.log("image内容费用:", ethers.utils.formatEther(imageFee), "PAT");
            
            console.log("\n✅ 现在可以注册image类型的内容了！");
        } else {
            console.log("❌ image类型激活失败");
        }
        
        console.log("\n🎉 激活完成！");
        
    } catch (error) {
        console.log("❌ 激活失败:", error.message);
        console.log("错误详情:", error);
        
        // 提供解决建议
        console.log("\n💡 可能的解决方案:");
        console.log("1. 确保使用合约所有者账户");
        console.log("2. 检查合约是否支持addContentType方法");
        console.log("3. 检查合约是否支持setContentTypeActive方法");
        console.log("4. 可能需要重新部署内容系统");
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("激活脚本失败:", error);
        process.exit(1);
    });
EOF

# 运行激活脚本
echo "📝 执行激活..."
npx hardhat run temp_activate_image_type.js --network localhost

# 清理临时文件
rm -f temp_activate_image_type.js

echo ""
echo "🔧 激活完成后，可以重新运行内容上链测试:"
echo "npx hardhat run scripts/test/04-content-system-test.js --network localhost"
