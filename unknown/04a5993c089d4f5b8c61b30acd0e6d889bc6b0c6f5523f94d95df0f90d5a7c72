// 简化版BSC到PXA跨链测试脚本
const { ethers } = require("hardhat");
const hre = require("hardhat");

async function main() {
    console.log("\n🌉 BSC测试网 → PXA本地链跨链测试 (简化版)");
    console.log("=====================================");
    
    const network = hre.network;
    console.log("🌐 当前网络:", network.name);
    
    if (network.name !== "bscTestnet") {
        console.error("❌ 请在BSC测试网上运行此脚本:");
        console.error("npx hardhat run scripts/cross-chain/bridge-to-pxa-simple.js --network bscTestnet");
        process.exit(1);
    }
    
    // 检查PXA本地链是否运行
    console.log("\n=== 1. 检查PXA本地链状态 ===");
    
    try {
        const pxaProvider = new ethers.providers.JsonRpcProvider("http://127.0.0.1:8545");
        const pxaNetwork = await pxaProvider.getNetwork();
        const pxaBlockNumber = await pxaProvider.getBlockNumber();
        
        console.log("✅ PXA本地链运行正常");
        console.log("- 链ID:", pxaNetwork.chainId.toString());
        console.log("- 当前区块:", pxaBlockNumber);
        
        if (pxaNetwork.chainId !== 327) {
            console.error("❌ PXA链ID不正确，期望327，实际:", pxaNetwork.chainId.toString());
            process.exit(1);
        }
        
    } catch (error) {
        console.error("❌ PXA本地链未运行，请先启动:");
        console.error("cd ../pxa-chain/local-chain && ./local-chain-setup.sh");
        process.exit(1);
    }
    
    console.log("\n=== 2. 使用已知的BSC合约地址 ===");
    
    // 直接使用已知的合约地址
    const contractAddresses = {
        PAToken: "******************************************",
        TokenBridge: "******************************************",
        crossChainPool: "******************************************"
    };
    
    console.log("✅ 使用已部署的合约地址:");
    console.log("- PAT代币:", contractAddresses.PAToken);
    console.log("- TokenBridge:", contractAddresses.TokenBridge);
    console.log("- 跨链池:", contractAddresses.crossChainPool);
    
    console.log("\n=== 3. 连接合约 ===");
    
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者地址:", deployer.address);
    
    // 连接合约
    const patToken = await ethers.getContractAt("PAToken", contractAddresses.PAToken);
    const tokenBridge = await ethers.getContractAt("TokenBridge", contractAddresses.TokenBridge);
    
    // 检查PAT余额
    const patBalance = await patToken.balanceOf(deployer.address);
    console.log("💰 部署者PAT余额:", ethers.utils.formatEther(patBalance), "PAT");
    
    // 检查跨链池余额
    const poolBalance = await patToken.balanceOf(contractAddresses.crossChainPool);
    console.log("💰 跨链池PAT余额:", ethers.utils.formatEther(poolBalance), "PAT");
    
    if (patBalance.eq(0) && poolBalance.eq(0)) {
        console.error("❌ 没有PAT代币可用于跨链测试");
        process.exit(1);
    }
    
    console.log("\n=== 4. 检查TokenBridge配置 ===");
    
    try {
        // 检查是否支持PXA链 (链ID 327)
        const supportsPXA = await tokenBridge.supportedChains(327);
        console.log("PXA链(327)支持状态:", supportsPXA ? "✅ 支持" : "❌ 不支持");
        
        if (!supportsPXA) {
            console.log("⚠️ TokenBridge尚未配置支持PXA链(327)");
            console.log("💡 当前配置的是PXPAC链(11)，需要管理员添加PXA链支持");
            
            // 尝试添加PXA链支持 (如果是管理员)
            try {
                console.log("🔧 尝试添加PXA链支持...");
                const addChainTx = await tokenBridge.addSupportedChain(
                    327,                    // PXA链ID
                    [deployer.address],     // 验证者列表
                    1,                      // 需要确认数
                    ethers.utils.parseEther("0.001"), // 基础费用
                    50,                     // 百分比费用 (0.5%)
                    ethers.utils.parseEther("0.0005"), // 最小费用
                    ethers.utils.parseEther("0.1")     // 最大费用
                );
                await addChainTx.wait();
                console.log("✅ 已添加PXA链支持");
            } catch (error) {
                console.log("⚠️ 无法添加PXA链支持:", error.message);
                console.log("💡 请联系管理员添加PXA链支持");
            }
        }
        
    } catch (error) {
        console.log("⚠️ 无法检查链支持状态:", error.message);
    }
    
    console.log("\n=== 5. 执行跨链测试 ===");
    
    // 使用较小的金额进行测试
    const bridgeAmount = ethers.utils.parseEther("10"); // 10 PAT
    
    // 选择资金来源
    let fromAddress = deployer.address;
    let fromBalance = patBalance;
    
    if (patBalance.lt(bridgeAmount) && poolBalance.gte(bridgeAmount)) {
        console.log("💡 部署者余额不足，尝试从跨链池转账...");
        // 这里需要跨链池的私钥，暂时跳过
        console.log("⚠️ 需要跨链池私钥才能从池中转账");
        console.log("💡 建议: 先给部署者账户转一些PAT代币");
        process.exit(1);
    }
    
    if (fromBalance.lt(bridgeAmount)) {
        console.log("⚠️ 余额不足，使用全部余额进行测试");
        bridgeAmount = fromBalance;
    }
    
    console.log("🌉 准备跨链:", ethers.utils.formatEther(bridgeAmount), "PAT");
    console.log("- 从地址:", fromAddress);
    console.log("- 到PXA链(327)");
    console.log("- 接收地址:", deployer.address);
    
    // 检查授权
    const allowance = await patToken.allowance(fromAddress, tokenBridge.address);
    if (allowance.lt(bridgeAmount)) {
        console.log("🔐 授权PAT代币给TokenBridge...");
        const approveTx = await patToken.approve(tokenBridge.address, bridgeAmount);
        await approveTx.wait();
        console.log("✅ 授权完成");
    }
    
    // 计算跨链费用
    let bridgeFee = ethers.utils.parseEther("0.001"); // 默认费用
    try {
        bridgeFee = await tokenBridge.calculateFee(bridgeAmount, 327);
        console.log("💰 跨链费用:", ethers.utils.formatEther(bridgeFee), "BNB");
    } catch (error) {
        console.log("⚠️ 无法计算费用，使用默认值:", ethers.utils.formatEther(bridgeFee), "BNB");
    }
    
    // 执行跨链
    try {
        console.log("🚀 发起跨链交易...");
        const lockTx = await tokenBridge.lockTokens(
            contractAddresses.PAToken,  // PAT代币地址
            bridgeAmount,               // 跨链金额
            327,                        // PXA链ID
            deployer.address,           // 接收地址
            { value: bridgeFee }        // 支付费用
        );
        
        console.log("⏳ 等待交易确认...");
        const receipt = await lockTx.wait();
        
        console.log("✅ 跨链交易成功!");
        console.log("- 交易哈希:", receipt.transactionHash);
        console.log("- Gas使用:", receipt.gasUsed.toString());
        
        // 查找跨链事件
        const lockEvents = receipt.logs.filter(log => {
            try {
                const parsed = tokenBridge.interface.parseLog(log);
                return parsed.name === "TokensLocked";
            } catch {
                return false;
            }
        });
        
        if (lockEvents.length > 0) {
            const event = tokenBridge.interface.parseLog(lockEvents[0]);
            console.log("📋 跨链事件详情:");
            console.log("- 代币:", event.args.token);
            console.log("- 金额:", ethers.utils.formatEther(event.args.amount), "PAT");
            console.log("- 目标链:", event.args.targetChain.toString());
            console.log("- 接收者:", event.args.recipient);
            console.log("- 锁定ID:", event.args.lockId?.toString() || "N/A");
        }
        
        console.log("\n=== 6. 下一步操作 ===");
        
        console.log("🎯 现在需要在PXA链上接收跨链代币:");
        console.log("1. 切换到PXA链目录:");
        console.log("   cd ../pxa-chain");
        console.log("2. 执行跨链接收:");
        console.log("   npx hardhat run scripts/bridge/mint-bridged-tokens.js --network localhost");
        console.log("3. 查询跨链统计:");
        console.log("   npx hardhat run scripts/query/cross-chain-stats.js --network localhost");
        
        console.log("\n🎊 BSC → PXA跨链发起成功!");
        console.log("=====================================");
        console.log("✅ 跨链交易已提交到BSC测试网");
        console.log("✅ 等待PXA链处理跨链请求");
        console.log("✅ 跨链完成后将获得wPAT代币");
        
    } catch (error) {
        console.error("❌ 跨链交易失败:", error.message);
        
        if (error.message.includes("Unsupported target chain")) {
            console.log("💡 解决方案: TokenBridge需要添加PXA链(327)支持");
        } else if (error.message.includes("insufficient funds")) {
            console.log("💡 解决方案: 账户BNB余额不足支付跨链费用");
        }
        
        process.exit(1);
    }
}

// 错误处理
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ 跨链脚本执行失败:", error);
        process.exit(1);
    });
