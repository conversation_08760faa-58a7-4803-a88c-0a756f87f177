// 检查BSC测试网账户余额
const { ethers } = require("ethers");
require("dotenv").config();

async function checkBalances() {
    console.log("🔍 检查BSC测试网账户余额");
    console.log("================================================");
    
    // 连接到BSC测试网
    const provider = new ethers.providers.JsonRpcProvider(
        process.env.BSC_TESTNET_RPC || "https://data-seed-prebsc-1-s1.binance.org:8545"
    );
    
    // 检查网络连接
    try {
        const network = await provider.getNetwork();
        console.log(`✅ 连接到网络: ${network.name} (链ID: ${network.chainId})`);
        
        const blockNumber = await provider.getBlockNumber();
        console.log(`📦 当前区块高度: ${blockNumber}`);
    } catch (error) {
        console.error("❌ 网络连接失败:", error.message);
        return;
    }
    
    // 账户配置
    const accounts = [
        {
            name: "部署者",
            privateKey: process.env.DEPLOYER_PRIVATE_KEY,
            required: "0.5 tBNB"
        },
        {
            name: "国库",
            privateKey: process.env.TREASURY_PRIVATE_KEY,
            required: "0.1 tBNB"
        },
        {
            name: "操作员",
            privateKey: process.env.OPERATOR_PRIVATE_KEY,
            required: "0.1 tBNB"
        },
        {
            name: "测试用户1",
            privateKey: process.env.TEST_USER_1_PRIVATE_KEY,
            required: "0.05 tBNB"
        },
        {
            name: "测试用户2",
            privateKey: process.env.TEST_USER_2_PRIVATE_KEY,
            required: "0.05 tBNB"
        },
        {
            name: "测试用户3",
            privateKey: process.env.TEST_USER_3_PRIVATE_KEY,
            required: "0.05 tBNB"
        }
    ];
    
    console.log("\n💰 账户余额检查:");
    console.log("================================================");
    
    let totalBalance = 0;
    let insufficientAccounts = [];
    
    for (const account of accounts) {
        if (!account.privateKey) {
            console.log(`⚠️  ${account.name}: 私钥未配置`);
            continue;
        }
        
        try {
            const wallet = new ethers.Wallet(account.privateKey, provider);
            const balance = await wallet.getBalance();
            const balanceInBNB = ethers.utils.formatEther(balance);
            const balanceNum = parseFloat(balanceInBNB);
            
            totalBalance += balanceNum;
            
            // 检查余额是否充足
            const requiredBNB = parseFloat(account.required.replace(" tBNB", ""));
            const isInsufficient = balanceNum < requiredBNB;
            
            if (isInsufficient) {
                insufficientAccounts.push({
                    name: account.name,
                    address: wallet.address,
                    current: balanceInBNB,
                    required: account.required
                });
            }
            
            const status = isInsufficient ? "❌ 不足" : "✅ 充足";
            console.log(`${account.name}: ${wallet.address}`);
            console.log(`  余额: ${balanceInBNB} tBNB (需要: ${account.required}) ${status}`);
            console.log("");
            
        } catch (error) {
            console.log(`❌ ${account.name}: 获取余额失败 - ${error.message}`);
        }
    }
    
    console.log("================================================");
    console.log(`📊 总余额: ${totalBalance.toFixed(6)} tBNB`);
    
    if (insufficientAccounts.length > 0) {
        console.log("\n⚠️  余额不足的账户:");
        console.log("================================================");
        
        for (const account of insufficientAccounts) {
            console.log(`${account.name}: ${account.address}`);
            console.log(`  当前: ${account.current} tBNB`);
            console.log(`  需要: ${account.required}`);
            console.log("");
        }
        
        console.log("🚰 获取测试网BNB:");
        console.log("- 访问: https://testnet.binance.org/faucet-smart");
        console.log("- 每24小时可领取 0.1 tBNB");
        console.log("- 建议为部署者账户多领取几次");
        
    } else {
        console.log("✅ 所有账户余额充足，可以开始部署！");
        console.log("\n🚀 开始部署命令:");
        console.log("chmod +x deploy-to-testnet.sh");
        console.log("./deploy-to-testnet.sh");
    }
}

// 运行检查
checkBalances().catch(console.error);
