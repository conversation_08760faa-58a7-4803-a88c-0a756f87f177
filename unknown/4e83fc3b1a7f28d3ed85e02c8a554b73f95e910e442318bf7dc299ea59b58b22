# BSC测试网手动部署检查清单

## ✅ 部署前检查

### 环境准备
- [ ] `.env` 文件配置正确
- [ ] 部署者私钥已设置：`DEPLOYER_PRIVATE_KEY=0xd11995ba...`
- [ ] BSCScan API密钥已设置：`BSCSCAN_API_KEY=...`
- [ ] IPFS配置已设置：`PINATA_JWT=...`

### 账户余额检查
```bash
# 快速检查余额
node check-testnet-balance.js
```

- [ ] 部署者账户：≥ 0.5 tBNB
- [ ] 国库账户：≥ 0.1 tBNB
- [ ] 操作员账户：≥ 0.1 tBNB

### 网络连接测试
```bash
# 测试BSC测试网连接
curl -X POST https://data-seed-prebsc-1-s1.binance.org:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'
```

- [ ] 网络连接正常

## 🚀 部署步骤

### 第1步：编译合约
```bash
npx hardhat clean
npx hardhat compile
```
- [ ] 编译成功，无错误

### 第2步：部署核心代币
```bash
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network bscTestnet
```
- [ ] PXT代币部署成功
- [ ] PAT代币部署成功
- [ ] 代币注册表部署成功
- [ ] PAT分配到4个池子完成

**记录地址**：
- PXT代币：`0x...`
- PAT代币：`0x...`
- 代币注册表：`0x...`

### 第3步：部署质押系统
```bash
npx hardhat run scripts/deploy/02-deploy-staking-system.js --network bscTestnet
```
- [ ] 质押工厂部署成功
- [ ] 质押池部署成功
- [ ] 奖励分配器部署成功

**记录地址**：
- 质押工厂：`0x...`
- 质押池：`0x...`
- 奖励分配器：`0x...`

### 第4步：部署治理系统
```bash
npx hardhat run scripts/deploy/03-deploy-governance.js --network bscTestnet
```
- [ ] DAO合约部署成功
- [ ] 提案管理器部署成功
- [ ] 投票合约部署成功
- [ ] 国库合约部署成功

**记录地址**：
- DAO合约：`0x...`
- 提案管理器：`0x...`
- 投票合约：`0x...`
- 国库合约：`0x...`

### 第5步：部署内容系统
```bash
npx hardhat run scripts/deploy/04-deploy-content-system.js --network bscTestnet
```
- [ ] ContentRegistry部署成功
- [ ] ContentCharacter部署成功
- [ ] ContentMint部署成功

**记录地址**：
- ContentRegistry：`0x...`
- ContentCharacter：`0x...`
- ContentMint：`0x...`

### 第6步：部署跨链桥
```bash
npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network bscTestnet
```
- [ ] TokenBridge部署成功
- [ ] PXPAC链支持配置完成

**记录地址**：
- TokenBridge：`0x...`

## 🧪 功能测试（可选）

### 基础测试
```bash
npx hardhat run scripts/test/01-basic-function-test.js --network bscTestnet
```
- [ ] 代币转账测试通过
- [ ] 代币授权测试通过

### 质押测试
```bash
npx hardhat run scripts/test/02-reward-system-test.js --network bscTestnet
```
- [ ] 质押功能测试通过
- [ ] 奖励计算测试通过

### 治理测试
```bash
npx hardhat run scripts/test/03-governance-test.js --network bscTestnet
```
- [ ] 提案创建测试通过
- [ ] 投票功能测试通过

### 内容上链测试
```bash
npx hardhat run scripts/test/bsc-ipfs-content-upload.js --network bscTestnet
```
- [ ] IPFS上传测试通过
- [ ] 内容注册测试通过

### 跨链桥测试
```bash
npx hardhat run scripts/test/05-bridge-test.js --network bscTestnet
```
- [ ] 跨链锁定测试通过

### NFT铸造测试
```bash
npx hardhat run scripts/test/06-content-mint-test.js --network bscTestnet
```
- [ ] NFT铸造测试通过
- [ ] 收益分配测试通过

## 🔍 合约验证

### 在BSCScan上验证源码
访问：https://testnet.bscscan.com

- [ ] PXT代币已验证
- [ ] PAT代币已验证
- [ ] 代币注册表已验证
- [ ] 质押工厂已验证
- [ ] 质押池已验证
- [ ] 奖励分配器已验证
- [ ] DAO合约已验证
- [ ] 提案管理器已验证
- [ ] 投票合约已验证
- [ ] 国库合约已验证
- [ ] ContentRegistry已验证
- [ ] ContentCharacter已验证
- [ ] ContentMint已验证
- [ ] TokenBridge已验证

## 📋 部署完成检查

### 系统状态验证
- [ ] 所有合约部署成功
- [ ] 所有合约已验证
- [ ] 基础功能测试通过
- [ ] 部署信息已记录

### 文件检查
- [ ] `deployments/bscTestnet/` 目录存在
- [ ] 合约地址文件已生成
- [ ] 测试报告已生成

### 下一步准备
- [ ] 更新前端合约地址配置
- [ ] 准备用户测试
- [ ] 文档更新完成

## 🎯 重要提醒

1. **保存所有合约地址** - 部署过程中记录每个合约地址
2. **验证合约源码** - 在BSCScan上验证所有合约
3. **测试功能** - 至少运行基础功能测试
4. **备份部署信息** - 保存 `deployments/` 目录

## 🔧 常见问题

### Gas费用不足
- 解决：从水龙头获取更多tBNB

### 网络超时
- 解决：重试部署命令

### 合约验证失败
- 解决：检查构造函数参数

### 依赖冲突
- 解决：使用 `node` 直接运行脚本

---

**部署完成后，你的PXPAC原创内容上链平台就可以在BSC测试网上运行了！** 🎉
