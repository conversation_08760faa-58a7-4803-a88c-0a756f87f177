const { ethers } = require("ethers");
const fs = require("fs");
const path = require("path");

// 手动加载 .env 文件
require("dotenv").config();

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
    console.log("🌉 BSC到PXPAC跨链桥功能测试（修复版）");
    console.log("================================================");
    console.log("时间:", new Date().toISOString());
    
    // 连接到BSC测试网
    const provider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545");
    
    // 读取部署信息
    const deploymentsDir = path.join(__dirname, "../../deployments/bscTestnet");
    const bridgeFile = path.join(deploymentsDir, "bridge-deployment.json");
    const coreFile = path.join(deploymentsDir, "core-deployment.json");
    
    let bridgeDeployment, coreDeployment;
    try {
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        
        console.log("✅ 已加载部署信息");
        console.log("- TokenBridge:", bridgeDeployment.contracts.TokenBridge.address);
        console.log("- PAT代币:", coreDeployment.contracts.PAToken.address);
        console.log("- PXT代币:", coreDeployment.contracts.PXToken.address);
        
    } catch (error) {
        console.error("❌ 未找到部署文件:", error.message);
        console.error("请确保已部署跨链桥合约");
        process.exit(1);
    }
    
    // 创建钱包 - 使用跨链池
    const crossChainPoolPrivateKey = coreDeployment.pools.crossChainPool.privateKey;
    const crossChainPoolSigner = new ethers.Wallet(crossChainPoolPrivateKey, provider);
    
    console.log("测试账户:");
    console.log("- 跨链池地址:", crossChainPoolSigner.address);
    
    // 合约 ABI
    const tokenBridgeABI = [
        "function supportedChainIds(uint256) view returns (bool)",
        "function getChainValidators(uint256) view returns (address[])",
        "function requiredConfirmations(uint256) view returns (uint256)",
        "function chainFees(uint256) view returns (tuple(uint256 baseFee, uint256 percentFee))",
        "function calculateFee(uint256 amount, uint256 targetChainId) view returns (uint256)",
        "function lockTokens(address receiver, uint256 amount, uint256 targetChainId, address tokenAddress) payable returns (bytes32)",
        "function bridgeRequests(bytes32) view returns (tuple(address sender, address receiver, uint256 amount, uint256 fee, uint256 sourceChainId, uint256 targetChainId, address tokenAddress, bool isProcessed, uint256 timestamp))",
        "event BridgeRequestCreated(bytes32 indexed requestHash, uint256 indexed requestId, address indexed sender, address receiver, uint256 amount, uint256 targetChainId, address tokenAddress)"
    ];
    
    const patTokenABI = [
        "function balanceOf(address) view returns (uint256)",
        "function approve(address spender, uint256 amount) returns (bool)",
        "function allowance(address owner, address spender) view returns (uint256)"
    ];
    
    // 连接合约
    const tokenBridge = new ethers.Contract(bridgeDeployment.contracts.TokenBridge.address, tokenBridgeABI, crossChainPoolSigner);
    const patToken = new ethers.Contract(coreDeployment.contracts.PAToken.address, patTokenABI, crossChainPoolSigner);
    
    // 测试配置
    const PXPAC_CHAIN_ID = 11;
    const TEST_AMOUNT = ethers.parseEther("1000"); // 1000 PAT
    const RECEIVER_ADDRESS = crossChainPoolSigner.address; // 接收地址设为跨链池自己
    
    console.log("\n=== 1. 检查跨链桥状态 ===");
    
    try {
        // 检查链支持
        const isPxpacSupported = await tokenBridge.supportedChainIds(PXPAC_CHAIN_ID);
        console.log("PXPAC链支持状态:", isPxpacSupported ? "✅ 支持" : "❌ 不支持");
        
        if (!isPxpacSupported) {
            console.error("❌ PXPAC链未配置，请先运行配置脚本");
            console.log("💡 可能需要运行: npx hardhat run scripts/bridge/setup-bridge-connection.js --network bscTestnet");
            process.exit(1);
        }
        
        // 检查验证者
        const chainValidators = await tokenBridge.getChainValidators(PXPAC_CHAIN_ID);
        const requiredConfirmations = await tokenBridge.requiredConfirmations(PXPAC_CHAIN_ID);
        console.log("验证者数量:", chainValidators.length);
        console.log("验证者地址:", chainValidators);
        console.log("需要确认数:", requiredConfirmations.toString());
        
        // 检查费用配置
        const chainFee = await tokenBridge.chainFees(PXPAC_CHAIN_ID);
        console.log("基础费用:", ethers.utils.formatEther(chainFee.baseFee), "BNB");
        console.log("百分比费用:", chainFee.percentFee.toString() / 100, "%");
        
    } catch (error) {
        console.error("❌ 检查跨链桥状态失败:", error.message);
        console.log("💡 可能的原因:");
        console.log("1. 跨链桥合约未正确部署");
        console.log("2. PXPAC链配置未完成");
        console.log("3. 网络连接问题");
        process.exit(1);
    }
    
    console.log("\n=== 2. 检查账户余额 ===");
    
    // 检查跨链池PAT余额
    const poolPatBalance = await patToken.balanceOf(crossChainPoolSigner.address);
    console.log("跨链池PAT余额:", ethers.utils.formatEther(poolPatBalance), "PAT");
    
    // 检查跨链池BNB余额
    const poolBnbBalance = await crossChainPoolSigner.getBalance();
    console.log("跨链池BNB余额:", ethers.utils.formatEther(poolBnbBalance), "BNB");
    
    // 检查余额是否足够
    if (poolPatBalance.lt(TEST_AMOUNT)) {
        console.error("❌ 跨链池PAT余额不足");
        console.log("当前余额:", ethers.utils.formatEther(poolPatBalance), "PAT");
        console.log("需要余额:", ethers.utils.formatEther(TEST_AMOUNT), "PAT");
        process.exit(1);
    }
    
    console.log("\n=== 3. 计算跨链费用 ===");
    
    let bridgeFee;
    try {
        bridgeFee = await tokenBridge.calculateFee(TEST_AMOUNT, PXPAC_CHAIN_ID);
        console.log("跨链费用:", ethers.utils.formatEther(bridgeFee), "BNB");
        
        if (poolBnbBalance.lt(bridgeFee)) {
            console.error("❌ 跨链池BNB余额不足支付跨链费用");
            console.log("当前BNB余额:", ethers.utils.formatEther(poolBnbBalance), "BNB");
            console.log("需要BNB费用:", ethers.utils.formatEther(bridgeFee), "BNB");
            console.log("💡 解决方案: 给跨链池充值BNB");
            process.exit(1);
        }
    } catch (error) {
        console.error("❌ 费用计算失败:", error.message);
        process.exit(1);
    }
    
    console.log("\n=== 4. 授权TokenBridge使用PAT ===");
    
    // 检查当前授权额度
    const currentAllowance = await patToken.allowance(crossChainPoolSigner.address, tokenBridge.address);
    console.log("当前授权额度:", ethers.utils.formatEther(currentAllowance), "PAT");
    
    if (currentAllowance.lt(TEST_AMOUNT)) {
        console.log("授权额度不足，进行授权...");
        
        try {
            const approveTx = await patToken.approve(tokenBridge.address, TEST_AMOUNT, {
                gasPrice: ethers.utils.parseUnits("10", "gwei"),
                gasLimit: 100000
            });
            await approveTx.wait();
            
            console.log("✅ 已授权TokenBridge使用", ethers.utils.formatEther(TEST_AMOUNT), "PAT");
            
            // 验证授权
            const newAllowance = await patToken.allowance(crossChainPoolSigner.address, tokenBridge.address);
            console.log("新授权额度:", ethers.utils.formatEther(newAllowance), "PAT");
            
        } catch (error) {
            console.error("❌ 授权失败:", error.message);
            process.exit(1);
        }
    } else {
        console.log("✅ 授权额度充足");
    }
    
    await delay(2000);
    
    console.log("\n=== 5. 执行跨链锁定 ===");
    
    // 记录锁定前状态
    const beforeLockBalance = await patToken.balanceOf(crossChainPoolSigner.address);
    const beforeBridgeBalance = await patToken.balanceOf(tokenBridge.address);
    
    console.log("锁定前状态:");
    console.log("- 跨链池PAT余额:", ethers.utils.formatEther(beforeLockBalance), "PAT");
    console.log("- 跨链桥PAT余额:", ethers.utils.formatEther(beforeBridgeBalance), "PAT");

    let lockTx; // 在外部声明变量

    try {
        console.log("🚀 开始执行跨链锁定...");
        console.log("- 锁定数量:", ethers.utils.formatEther(TEST_AMOUNT), "PAT");
        console.log("- 目标链ID:", PXPAC_CHAIN_ID);
        console.log("- 接收地址:", RECEIVER_ADDRESS);
        console.log("- 支付费用:", ethers.utils.formatEther(bridgeFee), "BNB");

        // 执行跨链锁定
        lockTx = await tokenBridge.lockTokens(
            RECEIVER_ADDRESS,    // 接收地址
            TEST_AMOUNT,         // 锁定数量
            PXPAC_CHAIN_ID,      // 目标链ID
            patToken.address,    // 代币地址
            { 
                value: bridgeFee,
                gasPrice: ethers.utils.parseUnits("10", "gwei"),
                gasLimit: 500000
            }
        );
        
        console.log("⏳ 等待交易确认...");
        console.log("交易哈希:", lockTx.hash);
        
        const receipt = await lockTx.wait();
        console.log("✅ 跨链锁定交易成功");
        console.log("Gas使用:", receipt.gasUsed.toString());
        
        // 解析事件
        let requestHash, requestId;
        for (const log of receipt.logs || []) {
            try {
                const parsedLog = tokenBridge.interface.parseLog(log);
                if (parsedLog.name === "BridgeRequestCreated") {
                    requestHash = parsedLog.args.requestHash;
                    requestId = parsedLog.args.requestId;
                    
                    console.log("✅ 跨链请求已创建");
                    console.log("请求哈希:", requestHash);
                    console.log("请求ID:", requestId.toString());
                    break;
                }
            } catch (error) {
                // 忽略解析错误
            }
        }
        
        // 获取请求详情
        if (requestHash) {
            try {
                const request = await tokenBridge.bridgeRequests(requestHash);
                console.log("请求详情:");
                console.log("- 发送者:", request.sender);
                console.log("- 接收者:", request.receiver);
                console.log("- 数量:", ethers.utils.formatEther(request.amount), "PAT");
                console.log("- 费用:", ethers.utils.formatEther(request.fee), "BNB");
                console.log("- 源链ID:", request.sourceChainId.toString());
                console.log("- 目标链ID:", request.targetChainId.toString());
                console.log("- 代币地址:", request.tokenAddress);
                console.log("- 是否已处理:", request.isProcessed);
                console.log("- 时间戳:", new Date(request.timestamp.toNumber() * 1000).toISOString());
            } catch (error) {
                console.log("⚠️  无法获取请求详情:", error.message);
            }
        }
        
    } catch (error) {
        console.error("❌ 跨链锁定失败:", error.message);
        
        // 检查可能的失败原因
        if (error.message.includes("insufficient allowance")) {
            console.error("原因: 授权额度不足");
        } else if (error.message.includes("insufficient balance")) {
            console.error("原因: 代币余额不足");
        } else if (error.message.includes("insufficient fee")) {
            console.error("原因: 跨链费用不足");
        } else if (error.message.includes("unsupported chain")) {
            console.error("原因: 不支持的目标链");
        }
        
        process.exit(1);
    }
    
    console.log("\n=== 6. 验证锁定结果 ===");

    // 等待区块链状态更新
    console.log("⏳ 等待区块链状态更新...");
    await delay(5000);

    // 检查锁定后状态
    const afterLockBalance = await patToken.balanceOf(crossChainPoolSigner.address);
    const afterBridgeBalance = await patToken.balanceOf(tokenBridge.address);
    
    console.log("锁定后状态:");
    console.log("- 跨链池PAT余额:", ethers.utils.formatEther(afterLockBalance), "PAT");
    console.log("- 跨链桥PAT余额:", ethers.utils.formatEther(afterBridgeBalance), "PAT");
    
    // 计算变化
    const poolBalanceChange = beforeLockBalance.sub(afterLockBalance);
    const bridgeBalanceChange = afterBridgeBalance.sub(beforeBridgeBalance);
    
    console.log("余额变化:");
    console.log("- 跨链池减少:", ethers.utils.formatEther(poolBalanceChange), "PAT");
    console.log("- 跨链桥增加:", ethers.utils.formatEther(bridgeBalanceChange), "PAT");
    
    // 验证余额变化是否正确
    const balanceChangeCorrect = poolBalanceChange.eq(TEST_AMOUNT) && bridgeBalanceChange.eq(TEST_AMOUNT);
    if (balanceChangeCorrect) {
        console.log("✅ 余额变化正确");
    } else {
        console.log("⚠️  余额变化异常");
        console.log("预期变化:", ethers.utils.formatEther(TEST_AMOUNT), "PAT");
    }
    
    console.log("\n=== 7. 跨链流程说明 ===");
    
    console.log("💡 跨链流程:");
    console.log("1. ✅ BSC链上锁定PAT代币");
    console.log("2. 🔄 验证者监听并验证锁定事件");
    console.log("3. 🎯 验证者在PXPAC链上确认并铸造wPAT");
    console.log("4. 🎉 用户在PXPAC链上收到对应的wPAT代币");
    
    console.log("\n📊 测试结果汇总:");
    console.log("🎊 BSC到PXPAC跨链桥测试成功！");
    console.log("✅ PAT代币锁定成功");
    console.log("✅ 跨链请求已创建");
    console.log("✅ 余额变化正确");
    console.log("✅ 费用计算正确");
    
    console.log("\n🔗 相关链接:");
    console.log("- 交易哈希:", `https://testnet.bscscan.com/tx/${lockTx.hash}`);
    console.log("- TokenBridge合约:", `https://testnet.bscscan.com/address/${tokenBridge.address}`);
    
    console.log("\n🔧 下一步:");
    console.log("1. 在PXPAC链上验证跨链接收");
    console.log("2. 检查PXPAC链上的wPAT余额");
    console.log("3. 测试反向跨链（PXPAC → BSC）");
}

main()
    .then(() => {
        console.log("\n🎉 跨链桥测试完成！");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 跨链桥测试失败:", error);
        process.exit(1);
    });
