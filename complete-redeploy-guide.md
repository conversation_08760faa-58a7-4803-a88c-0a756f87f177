# 🚀 BSC本地链 ↔ PXA本地链完整重新部署指南

## 🎯 部署目标
基于成功的跨链经验，重新部署两条链并确保跨链功能稳定可靠。

## 📋 部署流程概览

### 🔄 第一阶段：环境清理（5分钟）
1. 停止所有链服务
2. 清理所有数据和缓存
3. 验证环境干净

### 🏗️ 第二阶段：BSC链部署（15分钟）
1. 启动BSC本地链
2. 部署核心代币系统
3. 部署跨链桥系统
4. 配置验证者和跨链池

### 🏗️ 第三阶段：PXA链部署（15分钟）
1. 启动PXA本地链
2. 部署核心合约系统
3. 部署wPAT和跨链接收器
4. 配置内容上链系统

### 🌉 第四阶段：跨链配置（10分钟）
1. 配置BSC→PXA跨链映射
2. 设置验证者权限
3. 执行跨链测试
4. 验证完整流程

---

## 🔄 第一阶段：环境清理

### 1.1 停止所有服务
```bash
# 停止BSC本地链
cd /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/local-bsc-chain
./stop-bsc-chain.sh

# 停止PXA本地链
cd /Users/<USER>/Desktop/PXA/pxa-chain/local-chain
./stop-pxa-chain.sh
```

### 1.2 清理BSC链数据
```bash
cd /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens

# 清理链数据
cd local-bsc-chain
./clean-bsc-chain.sh

# 清理部署记录
cd ../
npx hardhat clean
rm -rf deployments/localhost
rm -rf cache
```

### 1.3 清理PXA链数据
```bash
cd /Users/<USER>/Desktop/PXA/pxa-chain

# 清理链数据
cd local-chain
./clean-pxa-chain.sh

# 清理部署记录
cd ../
npx hardhat clean
rm -rf deployments/localhost
rm -rf cache
```

### 1.4 验证清理完成
```bash
# 检查没有残留进程
ps aux | grep polygon-edge
ps aux | grep geth

# 检查端口释放
lsof -i :18485  # BSC端口
lsof -i :8545   # PXA端口
```

**✅ 验证点：** 无残留进程，端口已释放

---

## 🏗️ 第二阶段：BSC链部署

### 2.1 启动BSC本地链
```bash
cd /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/local-bsc-chain

# 后台启动BSC链
./start-background.sh

# 等待链稳定（15秒）
sleep 15

# 验证链启动
curl -X POST http://127.0.0.1:18485 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'
```

**✅ 验证点：** 返回区块号，链正常运行

### 2.2 部署BSC核心系统
```bash
cd /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens

# 编译合约
npx hardhat clean
npx hardhat compile

# 按顺序部署核心系统
npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost
npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost
npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost
npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost
npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost
```

**✅ 验证点：** 所有合约部署成功，获得合约地址

### 2.3 配置BSC跨链桥
```bash
# 配置跨链桥连接到PXA链
npx hardhat run scripts/bridge/setup-bridge-connection.js --network localhost

# 测试基础功能
npx hardhat run scripts/test/01-basic-function-test.js --network localhost
```

**✅ 验证点：** 跨链桥配置成功，基础功能正常

---

## 🏗️ 第三阶段：PXA链部署

### 3.1 启动PXA本地链
```bash
cd /Users/<USER>/Desktop/PXA/pxa-chain/local-chain

# 后台启动PXA链
./start-background.sh

# 等待链稳定（15秒）
sleep 15

# 验证链启动
curl -X POST http://127.0.0.1:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'
```

**✅ 验证点：** 返回区块号，链正常运行

### 3.2 部署PXA核心系统
```bash
cd /Users/<USER>/Desktop/PXA/pxa-chain

# 编译合约
npx hardhat clean
npx hardhat compile

# 部署核心合约
npx hardhat run scripts/deployment/complete-deployment.js --network localhost

# 修复脚本地址
npx hardhat run scripts/fix-all-scripts.js --network localhost
```

**✅ 验证点：** 核心合约部署成功

### 3.3 部署wPAT跨链系统
```bash
# 部署wPAT代币
npx hardhat run scripts/deploy/deploy-wpat-token.js --network localhost

# 测试wPAT功能
npx hardhat run scripts/test/test-wpat-functionality.js --network localhost
```

**✅ 验证点：** wPAT系统部署成功

### 3.4 部署内容系统
```bash
# 部署内容系统
npx hardhat run scripts/deployment/deploy-content-system.js --network localhost

# 修复内容脚本
npx hardhat run scripts/fix-content-scripts.js --network localhost
```

**✅ 验证点：** 内容系统部署成功

---

## 🌉 第四阶段：跨链配置

### 4.1 配置PXA链跨链接收器
```bash
cd /Users/<USER>/Desktop/PXA/pxa-chain

# 配置BSC→PXA跨链桥
npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost
```

**✅ 验证点：** 跨链桥配置完成，验证者添加成功

### 4.2 执行BSC→PXA跨链测试
```bash
# 在BSC链执行跨链（新终端）
cd /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens
npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network localhost

# 在PXA链处理跨链请求
cd /Users/<USER>/Desktop/PXA/pxa-chain
npx hardhat run scripts/bridge/process-bsc-local-bridge-request.js --network localhost
```

**✅ 验证点：** 跨链成功，wPAT铸造正常

### 4.3 验证完整流程
```bash
# 检查跨链状态
npx hardhat run scripts/bridge/check-bsc-pxa-bridge-status.js --network localhost

# 测试内容上链
npx hardhat run scripts/test/pxa-ipfs-content-upload.js --network localhost
```

**✅ 验证点：** 所有功能正常，跨链流程完整

---

## 📊 部署验证清单

### BSC链验证
- [ ] 链启动成功（端口18485）
- [ ] PXT代币部署成功
- [ ] PAT代币部署成功
- [ ] TokenBridge部署成功
- [ ] 跨链池配置正确
- [ ] 验证者配置正确

### PXA链验证
- [ ] 链启动成功（端口8545）
- [ ] 核心合约部署成功
- [ ] wPAT代币部署成功
- [ ] BridgeReceiver部署成功
- [ ] 内容系统部署成功

### 跨链功能验证
- [ ] BSC→PXA代币映射正确
- [ ] 验证者权限配置正确
- [ ] 跨链铸造功能正常
- [ ] 手续费机制正常
- [ ] 事件监听正常

### 集成功能验证
- [ ] 内容上链功能正常
- [ ] IPFS上传功能正常
- [ ] NFT铸造功能正常
- [ ] 费用分配功能正常

---

## 🔧 优化建议

### 1. 自动化脚本
创建一键部署脚本，减少手动操作错误。

### 2. 配置文件统一
将关键配置写入配置文件，便于管理和修改。

### 3. 监控和日志
增加详细的日志记录，便于问题排查。

### 4. 测试用例完善
增加更多边界情况的测试用例。

---

## 🚨 注意事项

1. **端口冲突**：确保18485和8545端口未被占用
2. **进程清理**：重新部署前务必清理所有残留进程
3. **数据一致性**：确保两条链的配置参数一致
4. **验证者配置**：确保验证者地址在两条链上一致
5. **Gas费用**：确保账户有足够的Gas费用

---

## 🎯 预期结果

完成重新部署后，你将拥有：

1. **稳定的BSC本地链**：支持PAT代币和跨链功能
2. **稳定的PXA本地链**：支持wPAT代币和内容上链
3. **完整的跨链桥**：BSC PAT ↔ PXA wPAT
4. **内容上链系统**：IPFS + 区块链存储
5. **费用管理系统**：多钱包费用分配

**预计总部署时间：45分钟**
**预计跨链测试时间：10分钟**

现在你可以按照这个优化的流程重新部署，确保每个步骤都验证成功后再进行下一步！
