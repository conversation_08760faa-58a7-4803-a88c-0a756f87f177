import { createPublicClient, http, parseAbiItem, decodeEventLog, formatUnits } from 'viem';
import { bscTestnet } from 'viem/chains';

// 备用RPC节点列表 - 按测试结果排序（可用的节点优先）
const RPC_ENDPOINTS = [
  'https://data-seed-prebsc-2-s2.binance.org:8545', // 900ms - 最快
  'https://bsc-testnet-rpc.publicnode.com',          // 909ms
  'https://bsc-testnet.public.blastapi.io',          // 931ms
  // 以下节点在测试中超时，作为最后备选
  'https://data-seed-prebsc-2-s1.binance.org:8545',
  'https://data-seed-prebsc-1-s1.binance.org:8545',
  'https://data-seed-prebsc-1-s2.binance.org:8545',
];

// 创建公共客户端，使用重试机制和故障转移
const createClientWithRetry = () => {
  let currentEndpointIndex = 0;

  const getNextEndpoint = () => {
    const endpoint = RPC_ENDPOINTS[currentEndpointIndex];
    currentEndpointIndex = (currentEndpointIndex + 1) % RPC_ENDPOINTS.length;
    return endpoint;
  };

  return createPublicClient({
    chain: bscTestnet,
    transport: http(getNextEndpoint(), {
      retryCount: 3,
      retryDelay: 2000,
      timeout: 30000, // 30秒超时
    }),
  });
};

// 创建带有故障转移的客户端
const createRobustClient = () => {
  let clientIndex = 0;

  const tryNextClient = async (operation: (client: any) => Promise<any>): Promise<any> => {
    for (let i = 0; i < RPC_ENDPOINTS.length; i++) {
      try {
        const client = createPublicClient({
          chain: bscTestnet,
          transport: http(RPC_ENDPOINTS[clientIndex], {
            retryCount: 1,
            retryDelay: 1000,
            timeout: 15000, // 15秒超时
          }),
        });

        const result = await operation(client);
        return result;
      } catch (error: any) {
        console.warn(`RPC节点 ${RPC_ENDPOINTS[clientIndex]} 失败:`, error.message);
        clientIndex = (clientIndex + 1) % RPC_ENDPOINTS.length;

        // 如果是最后一次尝试，抛出错误
        if (i === RPC_ENDPOINTS.length - 1) {
          throw error;
        }

        // 等待一下再尝试下一个节点
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  };

  return { tryNextClient };
};

const publicClient = createClientWithRetry();

// 合约地址 - 从环境变量获取
const CONTRACTS = {
  PXToken: process.env.NEXT_PUBLIC_PXT_TOKEN_ADDRESS!,
  PAToken: process.env.NEXT_PUBLIC_PAT_TOKEN_ADDRESS!,
};

// BSCScan API 配置
const BSCSCAN_API = {
  baseUrl: 'https://api-testnet.bscscan.com/api',
  apiKey: process.env.NEXT_PUBLIC_BSCSCAN_API_KEY || '**********************************',  // 使用提供的API密钥
  timeout: 8000, // 降低超时时间到8秒
};

// ERC20代币ABI (简化版，仅包含我们需要的函数)
const ERC20_ABI = [
  // 查询代币信息的方法
  {
    "constant": true,
    "inputs": [],
    "name": "name",
    "outputs": [{"name": "", "type": "string"}],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "symbol",
    "outputs": [{"name": "", "type": "string"}],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "totalSupply",
    "outputs": [{"name": "", "type": "uint256"}],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  }
];

// Transfer 事件 ABI
const TRANSFER_EVENT = parseAbiItem(
  'event Transfer(address indexed from, address indexed to, uint256 value)'
);

// BSCScan API获取代币转账事件的函数
async function fetchTokenTransfers(tokenAddress: string, limit = 5) {
  try {
    const url = new URL(BSCSCAN_API.baseUrl);
    url.searchParams.append('module', 'account');
    url.searchParams.append('action', 'tokentx');
    url.searchParams.append('contractaddress', tokenAddress);
    url.searchParams.append('page', '1');
    url.searchParams.append('offset', limit.toString());
    url.searchParams.append('sort', 'desc');

    if (BSCSCAN_API.apiKey) {
      url.searchParams.append('apikey', BSCSCAN_API.apiKey);
    }

    // 创建一个AbortController来处理超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), BSCSCAN_API.timeout);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'TokensScan/1.0'
        },
        cache: 'no-store',
        signal: controller.signal,
      });

      // 清理超时
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`BSCScan API 请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === '1' && Array.isArray(data.result) && data.result.length > 0) {
        return data.result;
      } else {
        // 静默处理，不输出警告日志
        return [];
      }
    } catch (fetchError: any) {
      // 清理超时
      clearTimeout(timeoutId);
      
      // 判断错误类型，静默处理网络错误
      if (fetchError.name === 'AbortError') {
        console.warn('BSCScan API 请求超时，将使用备用数据源');
      } else if (fetchError.message?.includes('Failed to fetch') || 
                 fetchError.message?.includes('CONNECTION_CLOSED')) {
        console.warn('BSCScan API 连接失败，将使用备用数据源');
      } else {
        console.error(`BSCScan API 请求异常:`, fetchError.message);
      }
      
      throw fetchError;
    }
  } catch (error: any) {
    // 静默返回空数组，不输出错误日志
    return [];
  }
}

export interface BlockchainTransaction {
  hash: string;
  from: string;
  to: string | null;
  value: string;
  gasPrice: string;
  gasUsed: string;
  timestamp: number;
  timeAgo: string;
  type: string;
  tokenSymbol?: string;
  tokenAmount?: string;
}

export interface BlockchainBlock {
  height: number;
  hash: string;
  timestamp: number;
  transactionCount: number;
  gasUsed: bigint;
  gasLimit: bigint;
  miner: string;
  timeAgo: string;
}

// 代币信息缓存
interface TokenInfo {
  name: string;
  symbol: string;
  decimals: number;
  lastUpdated: number;
}

class BlockchainService {
  private static instance: BlockchainService;
  private lastFetchTime = 0;
  private tokenInfoCache: Record<string, TokenInfo> = {};
  private blockTimestampCache: Record<string, number> = {};
  private cachedData: {
    blocks: BlockchainBlock[];
    transactions: BlockchainTransaction[];
  } = {
    blocks: [],
    transactions: []
  };

  static getInstance(): BlockchainService {
    if (!BlockchainService.instance) {
      BlockchainService.instance = new BlockchainService();
    }
    return BlockchainService.instance;
  }

  // 指数退避重试函数
  private async retryWithBackoff(operation: () => Promise<any>, maxRetries = 3): Promise<any> {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 500)); // 指数退避
        return await operation();
      } catch (error: any) {
        if (error?.message?.includes('limit exceeded') || error?.message?.includes('too many requests')) {
          if (attempt === maxRetries - 1) {
            return null; // 最后一次尝试失败，静默返回null
          }
          continue; // 重试
        }
        throw error; // 其他错误直接抛出
      }
    }
    return null;
  }

  private formatTimeAgo(timestamp: number): string {
    const now = Math.floor(Date.now() / 1000);
    const diff = now - timestamp;

    if (diff < 60) return `${diff} 秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)} 分钟前`;
    if (diff < 86400) return `${Math.floor(diff / 3600)} 小时前`;
    return `${Math.floor(diff / 86400)} 天前`;
  }

  // 获取区块时间戳（带缓存）
  private async getBlockTimestamp(blockHash: string): Promise<number | null> {
    // 检查缓存
    if (this.blockTimestampCache[blockHash]) {
      return this.blockTimestampCache[blockHash];
    }

    try {
      const block = await this.retryWithBackoff(async () => {
        return await publicClient.getBlock({
          blockHash: blockHash as `0x${string}`,
        });
      });

      if (block) {
        const timestamp = Number(block.timestamp);
        // 缓存时间戳
        this.blockTimestampCache[blockHash] = timestamp;

        // 清理旧缓存，防止内存泄漏（保留最近1000个）
        const cacheKeys = Object.keys(this.blockTimestampCache);
        if (cacheKeys.length > 1000) {
          const keysToDelete = cacheKeys.slice(0, cacheKeys.length - 1000);
          keysToDelete.forEach(key => delete this.blockTimestampCache[key]);
        }

        return timestamp;
      }
      return null;
    } catch (error) {
      console.warn(`获取区块 ${blockHash} 时间戳失败:`, error);
      return null;
    }
  }

  // 获取代币信息
  private async getTokenInfo(tokenAddress: string): Promise<TokenInfo | null> {
    // 检查缓存
    if (this.tokenInfoCache[tokenAddress] && 
        Date.now() - this.tokenInfoCache[tokenAddress].lastUpdated < 3600000) { // 1小时缓存
      return this.tokenInfoCache[tokenAddress];
    }

    try {
      // 使用viem调用合约方法获取代币信息
      const [name, symbol, decimals] = await Promise.all([
        publicClient.readContract({
          address: tokenAddress as `0x${string}`,
          abi: ERC20_ABI,
          functionName: 'name',
        }),
        publicClient.readContract({
          address: tokenAddress as `0x${string}`,
          abi: ERC20_ABI,
          functionName: 'symbol',
        }),
        publicClient.readContract({
          address: tokenAddress as `0x${string}`,
          abi: ERC20_ABI,
          functionName: 'decimals',
        })
      ]);

      const tokenInfo: TokenInfo = {
        name: name as string,
        symbol: symbol as string,
        decimals: Number(decimals),
        lastUpdated: Date.now()
      };

      // 更新缓存
      this.tokenInfoCache[tokenAddress] = tokenInfo;
      
      return tokenInfo;
    } catch (error) {
      console.error(`获取代币信息失败: ${tokenAddress}`, error);
      
      // 返回硬编码的值作为备选方案
      if (tokenAddress === CONTRACTS.PXToken) {
        return {
          name: 'Paper x Token',
          symbol: 'PXT',
          decimals: 18,
          lastUpdated: Date.now()
        };
      } else if (tokenAddress === CONTRACTS.PAToken) {
        return {
          name: 'Paper Author Token',
          symbol: 'PAT',
          decimals: 18,
          lastUpdated: Date.now()
        };
      }
      
      return null;
    }
  }

  // 获取最新区块（优化策略：批量获取减少API调用）
  async getLatestBlocks(): Promise<BlockchainBlock[]> {
    try {
      const latestBlockNumber = await publicClient.getBlockNumber();
      const blocks: BlockchainBlock[] = [];

      // 分批获取区块，每批2个，减少总的API调用时间
      const batchSize = 2;
      const totalBlocks = 5;

      for (let batch = 0; batch < Math.ceil(totalBlocks / batchSize); batch++) {
        const batchPromises = [];
        const startIndex = batch * batchSize;
        const endIndex = Math.min(startIndex + batchSize, totalBlocks);

        for (let i = startIndex; i < endIndex; i++) {
          const blockNumber = latestBlockNumber - BigInt(i);
          batchPromises.push(
            publicClient.getBlock({
              blockNumber,
              includeTransactions: false
            }).catch(() => {
              // 静默处理区块获取失败
              return null;
            })
          );
        }

        // 并行获取当前批次的区块
        const batchResults = await Promise.all(batchPromises);

        batchResults.forEach((block) => {
          if (block) {
            blocks.push({
              height: Number(block.number),
              hash: block.hash,
              timestamp: Number(block.timestamp),
              transactionCount: block.transactions.length,
              gasUsed: block.gasUsed,
              gasLimit: block.gasLimit,
              miner: block.miner || '',
              timeAgo: this.formatTimeAgo(Number(block.timestamp))
            });
          }
        });

        // 批次间延迟，减少延迟时间
        if (batch < Math.ceil(totalBlocks / batchSize) - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒间隔
        }
      }

      return blocks.sort((a, b) => b.height - a.height); // 按高度降序排列
    } catch (error) {
      console.error('获取区块失败:', error);
      return [];
    }
  }

  // 从tokens服务获取最新代币交易
  private async getLatestTransactionsFromService(): Promise<BlockchainTransaction[]> {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_TOKEN_SERVICE_URL}/api/v1/tokens/transfers?limit=5`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        cache: 'no-store',
      });

      if (!response.ok) {
        throw new Error(`Tokens服务请求失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.transfers && Array.isArray(data.transfers)) {
        return data.transfers.map((transfer: any) => ({
          hash: transfer.transaction_hash,
          from: transfer.from_address,
          to: transfer.to_address,
          value: '0',
          gasPrice: '5',
          gasUsed: '50000',
          timestamp: new Date(transfer.timestamp).getTime() / 1000,
          timeAgo: this.formatTimeAgo(new Date(transfer.timestamp).getTime() / 1000),
          type: `${transfer.token_type} Transfer`,
          tokenSymbol: transfer.token_type,
          tokenAmount: transfer.amount,
        }));
      }

      return [];
    } catch (error) {
      console.warn('从tokens服务获取交易失败，使用备用方案:', error);
      return [];
    }
  }

  // 使用多种方法获取最新代币交易
  async getLatestTransactions(): Promise<BlockchainTransaction[]> {
    const transactions: BlockchainTransaction[] = [];

    try {
      // 首先尝试从tokens服务获取
      const serviceTransactions = await this.getLatestTransactionsFromService();
      if (serviceTransactions.length > 0) {
        transactions.push(...serviceTransactions);
        return transactions;
      }

      // 如果tokens服务失败，使用原有的方法
      // 方法1: 使用BSCScan API
      const bscScanTransactions = await this.getTokenTransfersFromBscScan();
      if (bscScanTransactions.length > 0) {
        transactions.push(...bscScanTransactions);
      }

      // 如果BSCScan API没有返回足够的交易，使用方法2: viem的getLogs
      if (transactions.length < 3) {
        const viemTransactions = await this.getTokenTransfersFromViem();
        if (viemTransactions.length > 0) {
          transactions.push(...viemTransactions);
        }
      }

      // 如果没有找到交易，返回空数组
      if (transactions.length === 0) {
        console.log("未找到最近的代币交易");
      }
    } catch (error) {
      console.error('获取最新代币交易失败:', error);
    }

    return transactions;
  }

  // 方法1: 使用BSCScan API获取代币转账
  private async getTokenTransfersFromBscScan(): Promise<BlockchainTransaction[]> {
    const transactions: BlockchainTransaction[] = [];
    
    try {
      // 尝试PXT代币
      try {
        const pxtTransfers = await fetchTokenTransfers(CONTRACTS.PXToken, 3);
        const pxtInfo = await this.getTokenInfo(CONTRACTS.PXToken);
        
        if (pxtTransfers.length > 0 && pxtInfo) {
          for (const transfer of pxtTransfers.slice(0, 2)) { // 最多取2条
            transactions.push({
              hash: transfer.hash,
              from: transfer.from,
              to: transfer.to,
              value: '0',
              gasPrice: transfer.gasPrice || '5',
              gasUsed: transfer.gasUsed || '50000',
              timestamp: parseInt(transfer.timeStamp),
              timeAgo: this.formatTimeAgo(parseInt(transfer.timeStamp)),
              type: 'PXT Transfer',
              tokenSymbol: pxtInfo.symbol,
              tokenAmount: formatUnits(BigInt(transfer.value), pxtInfo.decimals)
            });
          }
        }
      } catch (pxtError) {
        // 静默处理PXT获取错误
      }

      // 尝试PAT代币
      try {
        const patTransfers = await fetchTokenTransfers(CONTRACTS.PAToken, 3);
        const patInfo = await this.getTokenInfo(CONTRACTS.PAToken);
        
        if (patTransfers.length > 0 && patInfo) {
          for (const transfer of patTransfers.slice(0, 2)) { // 最多取2条
            transactions.push({
              hash: transfer.hash,
              from: transfer.from,
              to: transfer.to,
              value: '0',
              gasPrice: transfer.gasPrice || '5',
              gasUsed: transfer.gasUsed || '50000',
              timestamp: parseInt(transfer.timeStamp),
              timeAgo: this.formatTimeAgo(parseInt(transfer.timeStamp)),
              type: 'PAT Transfer',
              tokenSymbol: patInfo.symbol,
              tokenAmount: formatUnits(BigInt(transfer.value), patInfo.decimals)
            });
          }
        }
      } catch (patError) {
        // 静默处理PAT获取错误
      }
    } catch (error) {
      // 静默处理整体错误
    }
    
    return transactions;
  }

  // 方法2: 使用viem的getLogs方法获取代币转账
  private async getTokenTransfersFromViem(): Promise<BlockchainTransaction[]> {
    const transactions: BlockchainTransaction[] = [];

    try {
      // 获取最新区块号
      const latestBlockNumber = await publicClient.getBlockNumber();
      const fromBlock = latestBlockNumber - BigInt(500); // 减少查询范围到最近500个区块

      // 添加延迟以避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 获取PXT转账
      try {
        const pxtInfo = await this.getTokenInfo(CONTRACTS.PXToken);
        
        if (pxtInfo) {
          // 大幅减少批次大小，降低RPC压力
          const batchSize = BigInt(50); // 从200减少到50个区块
          let pxtLogs: any[] = [];
          let batchCount = 0;
          const maxBatches = 5; // 限制最大批次数

          for (let start = fromBlock; start < latestBlockNumber && pxtLogs.length < 5 && batchCount < maxBatches; start += batchSize) {
            const end = start + batchSize > latestBlockNumber ? latestBlockNumber : start + batchSize;
            batchCount++;

            const batchLogs = await this.retryWithBackoff(async () => {
              return await publicClient.getLogs({
                address: CONTRACTS.PXToken as `0x${string}`,
                event: TRANSFER_EVENT,
                fromBlock: start,
                toBlock: end,
              });
            });

            if (batchLogs) {
              pxtLogs.push(...batchLogs);
              // 如果找到足够的日志，停止查询
              if (pxtLogs.length >= 5) break;
            }

            // 增加批次间延迟
            await new Promise(resolve => setTimeout(resolve, 800));
          }
          
          if (pxtLogs.length > 0) {
            // 获取最新的2条记录
            for (const log of pxtLogs.slice(-2)) {
              try {
                const decoded = decodeEventLog({
                  abi: [TRANSFER_EVENT],
                  data: log.data,
                  topics: log.topics,
                });
                
                // 获取交易所在区块的时间戳
                const timestamp = await this.getBlockTimestamp(log.blockHash);
                if (timestamp === null) {
                  // 如果无法获取区块时间戳，跳过这条记录
                  console.warn(`无法获取区块 ${log.blockHash} 的时间戳，跳过交易 ${log.transactionHash}`);
                  continue;
                }
                
                transactions.push({
                  hash: log.transactionHash,
                  from: decoded.args.from as string,
                  to: decoded.args.to as string,
                  value: '0',
                  gasPrice: '5',
                  gasUsed: '50000',
                  timestamp,
                  timeAgo: this.formatTimeAgo(timestamp),
                  type: 'PXT Transfer',
                  tokenSymbol: pxtInfo.symbol,
                  tokenAmount: formatUnits(decoded.args.value as bigint, pxtInfo.decimals),
                });
              } catch (decodeError) {
                // 静默处理解码错误
                continue;
              }
            }
          }
        }
      } catch (error) {
        // 静默处理PXT转账获取失败
      }
      
      // 获取PAT转账
      try {
        const patInfo = await this.getTokenInfo(CONTRACTS.PAToken);
        
        if (patInfo) {
          // 同样的优化策略
          const batchSize = BigInt(50);
          let patLogs: any[] = [];
          let batchCount = 0;
          const maxBatches = 5;

          for (let start = fromBlock; start < latestBlockNumber && patLogs.length < 5 && batchCount < maxBatches; start += batchSize) {
            const end = start + batchSize > latestBlockNumber ? latestBlockNumber : start + batchSize;
            batchCount++;

            const batchLogs = await this.retryWithBackoff(async () => {
              return await publicClient.getLogs({
                address: CONTRACTS.PAToken as `0x${string}`,
                event: TRANSFER_EVENT,
                fromBlock: start,
                toBlock: end,
              });
            });

            if (batchLogs) {
              patLogs.push(...batchLogs);
              if (patLogs.length >= 5) break;
            }

            await new Promise(resolve => setTimeout(resolve, 800));
          }
          
          if (patLogs.length > 0) {
            // 获取最新的2条记录
            for (const log of patLogs.slice(-2)) {
              try {
                const decoded = decodeEventLog({
                  abi: [TRANSFER_EVENT],
                  data: log.data,
                  topics: log.topics,
                });
                
                // 获取交易所在区块的时间戳
                const timestamp = await this.getBlockTimestamp(log.blockHash);
                if (timestamp === null) {
                  // 如果无法获取区块时间戳，跳过这条记录
                  console.warn(`无法获取区块 ${log.blockHash} 的时间戳，跳过交易 ${log.transactionHash}`);
                  continue;
                }
                
                transactions.push({
                  hash: log.transactionHash,
                  from: decoded.args.from as string,
                  to: decoded.args.to as string,
                  value: '0',
                  gasPrice: '5',
                  gasUsed: '50000',
                  timestamp,
                  timeAgo: this.formatTimeAgo(timestamp),
                  type: 'PAT Transfer',
                  tokenSymbol: patInfo.symbol,
                  tokenAmount: formatUnits(decoded.args.value as bigint, patInfo.decimals),
                });
              } catch (decodeError) {
                // 静默处理解码错误
                continue;
              }
            }
          }
        }
      } catch (error) {
        // 静默处理PAT转账获取失败
      }
      
    } catch (error) {
      // 静默处理整体错误，不影响其他功能
    }
    
    return transactions;
  }

  // 主要的数据获取方法
  async fetchBlockchainData(forceRefresh = false): Promise<{
    blocks: BlockchainBlock[];
    transactions: BlockchainTransaction[];
  }> {
    const now = Date.now();

    // 如果不是强制刷新，且距离上次获取不到20秒，返回缓存数据（避免过于频繁的请求）
    if (!forceRefresh && now - this.lastFetchTime < 20000 && this.cachedData.blocks.length > 0) {
      /*
      if (process.env.NODE_ENV === 'development') {
        console.log('[BlockchainService] 使用缓存数据，距离上次获取:', Math.floor((now - this.lastFetchTime) / 1000), '秒');
      }
      */
      // 更新时间显示但不重新获取数据
      return {
        blocks: this.cachedData.blocks.map(block => ({
          ...block,
          timeAgo: this.formatTimeAgo(block.timestamp)
        })),
        transactions: this.cachedData.transactions.map(tx => ({
          ...tx,
          timeAgo: this.formatTimeAgo(tx.timestamp)
        }))
      };
    }

    try {
      /*
      if (process.env.NODE_ENV === 'development') {
        const caller = new Error().stack?.split('\n')[2]?.trim() || 'unknown';
        console.log('[BlockchainService] 开始获取新的区块链数据...',
          { caller, time: new Date().toISOString() }
        );
      }
      */

      // 并行获取区块和交易数据
      const [blocks, recentTransactions] = await Promise.all([
        this.getLatestBlocks(),
        this.getLatestTransactions()
      ]);

      // 只使用真实的转账数据
      const allTransactions = recentTransactions
        .map(tx => ({
          ...tx,
          timeAgo: this.formatTimeAgo(tx.timestamp) // 更新时间显示
        }))
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 5); // 显示5笔交易

      this.cachedData = {
        blocks: blocks.length > 0 ? blocks.map(block => ({
          ...block,
          timeAgo: this.formatTimeAgo(block.timestamp) // 更新区块时间显示
        })) : this.cachedData.blocks,
        transactions: allTransactions
      };

      this.lastFetchTime = now;
      /*
      if (process.env.NODE_ENV === 'development') {
        console.log('[BlockchainService] 数据更新完成，获取到', blocks.length, '个区块，', allTransactions.length, '笔交易');
      }
      */
      return this.cachedData;
    } catch (error) {
      console.error('[BlockchainService] 获取区块链数据失败:', error);

      // 返回缓存数据作为回退
      return this.cachedData;
    }
  }
}

export default BlockchainService;
