// 代币页面中文语言包
export const tokensLocale = {
  // 代币页面
  page: {
    title: "代币总览",
    subtitle: "查看平台代币的详细信息和统计数据",
    description: "深入了解 PXT 和 PAT 双代币机制，实时监控代币价格、供应量、销毁机制和生态系统数据",
    loading: "正在加载代币信息...",
  },

  // 代币描述
  tokenDescriptions: {
    pxt: "论文x代币，用于质押和治理投票",
    pat: "论文作者代币，通过质押 PXT 获得"
  },

  // 代币统计标签
  stats: {
    totalSupply: "总供应量",
    circulatingSupply: "流通供应量", 
    lockedSupply: "锁定供应量",
    burnedSupply: "销毁供应量",
    currentSupply: "当前供应量",
    inflationRate: "通胀率",
    distributedRewards: "已分发奖励",
    rewardPool: "奖励池余额",
    holders: "持有者数量",
    stakingRate: "质押率"
  },

  // 代币卡片
  tokenCard: {
    contractInfo: "合约信息",
    contractAddress: "合约地址",
    tokenType: "代币类型",
    supplyModel: "供应模型",
    tokenStats: "代币统计",
    economicModel: "经济模型",
    deflationMechanism: "通缩机制",
    inflationModel: "通胀模型",
    holdersRanking: "持有者排行",
    rewardsHistory: "奖励历史",
    expandDetails: "展开详情",
    collapseDetails: "收起详情",
    doubleClickTip: "双击启用展开/收起功能"
  },

  // 快速操作
  quickActions: {
    title: "快速操作",
    actions: {
      stakePxt: {
        title: "质押 PXT",
        description: "质押获得 PAT 奖励"
      },
      governance: {
        title: "参与治理",
        description: "使用 PXT 参与投票"
      },
      viewStats: {
        title: "查看统计",
        description: "查看 PXT 详细统计"
      },
      claimRewards: {
        title: "领取奖励",
        description: "领取质押 PAT 奖励"
      },
      tradePat: {
        title: "交易 PAT",
        description: "在 DEX 上交易 PAT"
      },
      rewardsHistory: {
        title: "奖励历史",
        description: "查看奖励分发历史"
      }
    }
  },

  // 相关链接
  relatedLinks: {
    title: "相关链接",
    links: {
      website: {
        title: "官方网站",
        description: "访问项目官方网站"
      },
      whitepaper: {
        title: "白皮书",
        description: "查看项目白皮书"
      },
      sourceCode: {
        title: "源代码",
        description: "查看智能合约代码"
      },
      blockExplorer: {
        title: "区块浏览器",
        description: "在区块浏览器查看"
      }
    }
  },

  // 持有者表格
  holdersTable: {
    rank: "排名",
    address: "地址",
    balance: "余额",
    percentage: "占比"
  },

  // 经济模型
  economicModel: {
    supplyType: "供应类型",
    totalSupply: "总供应量",
    mechanismDescription: "机制说明"
  },

  // 通缩模型
  deflationModel: {
    burnMechanism: "销毁机制",
    blackHoleAddress: "黑洞地址",
    revenueRepurchaseBurn: "收益回购销毁",
    mechanismDescription: "机制说明"
  },

  // 代币详细信息
  tokenInfo: {
    decimals: "精度",
    tokenType: "代币类型", 
    supplyModel: "供应模型"
  },

  // 复制按钮
  copyButton: {
    copy: "复制",
    copied: "已复制!",
    clickToCopy: "点击复制"
  },

  // 代币头部
  tokenHeader: {
    contractAddress: "合约地址",
    governanceToken: "治理代币"
  },

  // 相关链接详情页
  relatedLinksDetail: {
    title: "相关链接",
    links: {
      website: {
        title: "官方网站",
        description: "访问项目官方网站"
      },
      whitepaper: {
        title: "白皮书", 
        description: "查看项目白皮书"
      },
      sourceCode: {
        title: "源代码",
        description: "查看智能合约源代码"
      }
    }
  },

  // 系统统计
  systemStats: {
    title: "系统统计",
    totalHolders: "总持币者",
    pxtHolders: "PXT 持币者",
    patHolders: "PAT 持币者"
  },

  // 概览快速操作
  overviewQuickActions: {
    startStaking: {
      title: "开始质押",
      description: "质押 PXT 获得 PAT 奖励"
    },
    participateGovernance: {
      title: "参与治理",
      description: "创建提案或参与投票"
    },
    viewStatistics: {
      title: "查看统计",
      description: "查看详细的代币统计"
    }
  },

  // 代币详情快速操作
  tokenDetailQuickActions: {
    pxt: {
      stakePxt: {
        title: "质押 PXT",
        description: "质押获得 PAT 奖励"
      },
      governance: {
        title: "参与治理",
        description: "使用 PXT 参与投票"
      },
      viewStats: {
        title: "查看统计",
        description: "查看 PXT 详细统计"
      }
    },
    pat: {
      claimRewards: {
        title: "领取奖励",
        description: "领取质押 PAT 奖励"
      },
      tradePat: {
        title: "交易 PAT",
        description: "在 DEX 上交易 PAT"
      },
      rewardsHistory: {
        title: "奖励历史",
        description: "查看奖励分发历史"
      }
    }
  },

  // 代币详细信息
  tokenDetails: {
    pxt: {
      name: "论文x代币",
      symbol: "PXT",
      description: "论文x代币",
      type: "治理代币",
      supplyModel: "固定供应",
      economicModel: {
        supplyType: "固定供应",
        totalSupply: "100M PXT",
        description: [
          "PXT 采用固定总量设计，永不增发",
          "总量恒定 1 亿枚，确保稀缺性",
          "通过质押挖矿逐步释放到市场",
          "多重通缩机制维护长期价值"
        ]
      },
      deflationModel: {
        burnRate: "多重销毁",
        mechanisms: ["交易手续费销毁", "提前解锁惩罚", "治理提案押金"],
        description: [
          "交易手续费的 30% 将被永久销毁",
          "提前解除质押的惩罚部分销毁",
          "未通过的治理提案押金部分销毁",
          "平台收入 30% 用于回购销毁"
        ]
      }
    },
    pat: {
      name: "论文作者代币",
      symbol: "PAT",
      description: "论文作者代币",
      type: "奖励代币",
      supplyModel: "通胀模型",
      economicModel: {
        supplyType: "通胀模型",
        totalSupply: "动态供应",
        description: [
          "PAT 采用动态通胀模型，支持生态发展",
          "初始供应 3 亿枚，通过通胀机制增发",
          "主要用于质押奖励和生态激励",
          "通胀率根据网络活跃度动态调整"
        ]
      },
      inflationModel: {
        currentRate: "1.5%",
        maxRate: "2.0%",
        description: [
          "PAT 采用通胀模型，主要通过质押奖励发放",
          "当前年通胀率为 1.5%，最大不超过 2.0%",
          "新增代币主要用于质押奖励分发",
          "通胀率可通过治理投票进行调整"
        ]
      }
    }
  }
};
