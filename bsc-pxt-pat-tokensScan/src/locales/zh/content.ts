// 内容上链页面中文语言包
export const contentLocale = {
  // 内容管理页面
  page: {
    title: "上链管理",
    subtitle: "查看已上链的内容记录和详细信息",
    description: "浏览所有已上链的内容记录，包括电影、电视剧、音乐等多媒体内容的区块链存证信息和状态",
    loading: "正在加载内容信息...",
  },

  // 统计卡片
  stats: {
    totalContent: "总内容数",
    confirmedContent: "已确认",
    pendingContent: "待确认", 
    totalFees: "总费用",
    realtime: "实时"
  },

  // 筛选器
  filters: {
    title: "筛选条件",
    contentType: {
      label: "内容类型",
      allTypes: "全部类型"
    },
    status: {
      label: "状态",
      allStatuses: "全部状态"
    },
    clearFilters: "清除筛选"
  },

  // 内容类型选项
  contentTypes: {
    video: "视频",
    novel: "小说", 
    short_drama: "短剧",
    anime: "动漫",
    manga: "漫画",
    music: "音乐",
    article: "文章",
    short_video: "短视频"
  },

  // 状态选项
  statuses: {
    pending: "待确认",
    confirmed: "已确认",
    failed: "失败"
  },

  // 表格
  table: {
    // 移动端表头
    mobile: {
      contentInfo: "内容信息",
      status: "状态",
      actions: "操作"
    },
    // PC端表头
    desktop: {
      contentInfo: "内容信息",
      type: "类型",
      status: "状态", 
      fees: "费用",
      onChainTime: "上链时间",
      actions: "操作"
    },
    // 操作
    actions: {
      viewDetails: "查看详情",
      details: "详情"
    },
    // 空状态
    empty: {
      loading: "加载中...",
      noRecords: "暂无内容记录"
    }
  },

  // 分页
  pagination: {
    pageInfo: "第 {current} 页，共 {total} 页",
    previousPage: "上一页",
    nextPage: "下一页"
  },

  // 详情页面
  detail: {
    // 导航
    navigation: {
      backToList: "← 返回内容列表",
      detailSuffix: " - 上链详情"
    },

    // 错误状态
    error: {
      loading: "加载中...",
      notFound: "内容记录不存在或加载失败",
      contentNotExists: "内容不存在",
      backToList: "返回内容列表"
    },

    // 基本信息
    basicInfo: {
      title: "基本信息",
      contentId: "内容ID",
      onChainId: "上链ID", 
      licenseNumber: "许可证号",
      director: "导演",
      publisher: "发布者",
      publisherKsuid: "发布者KSUID",
      releaseDate: "发布日期",
      onChainDate: "上链日期",
      none: "无"
    },

    // 内容描述
    contentDescription: {
      title: "内容描述"
    },

    // 演员列表
    castList: {
      title: "演员列表"
    },

    // 审核员
    reviewers: {
      title: "审核员"
    },

    // 区块链信息
    blockchainInfo: {
      title: "区块链信息",
      contentHash: "内容哈希",
      transactionHash: "交易哈希",
      blockNumber: "区块号",
      onChainFee: "上链费用"
    },

    // 时间信息
    timeInfo: {
      title: "时间信息",
      createdAt: "创建时间",
      updatedAt: "更新时间"
    },

    // 操作
    actions: {
      title: "操作",
      copyContentHash: "复制内容哈希",
      copyTransactionHash: "复制交易哈希",
      backToList: "返回列表"
    }
  },

  // 通用文本
  common: {
    loading: "正在加载...",
    error: "加载失败",
    retry: "重试",
    confirm: "确认",
    cancel: "取消",
    close: "关闭",
    save: "保存",
    edit: "编辑",
    delete: "删除",
    copy: "复制",
    copied: "已复制",
    viewMore: "查看更多",
    viewAll: "查看全部"
  },

  // 代币生态系统部分
  tokenEcosystem: {
    title: "代币生态系统",
    subtitle: "探索PXT和PAT代币的实时数据和统计信息",
    cards: {
      pxt: {
        title: "PXT Token",
        totalSupply: "总供应量"
      },
      pat: {
        title: "PAT Token",
        totalSupply: "总供应量"
      },
      staking: {
        title: "质押统计",
        stakingRate: "质押率"
      },
      activeStakers: {
        title: "活跃质押者",
        currentOnline: "当前在线"
      }
    }
  },

  // 系统特色部分
  systemFeatures: {
    title: "系统特色",
    subtitle: "了解PXPAT平台的核心功能和双代币经济模型",
    pxtToken: {
      title: "PXT 论文x代币",
      description: "论文x代币\n用于质押和投票\n获得治理权益",
      totalSupply: "总供应量"
    },
    patToken: {
      title: "PAT 论文作者代币",
      description: "论文作者代币\n通过质押PXT获得\n可用于平台消费",
      inflationRate: "通胀率"
    },
    stakingSystem: {
      title: "7级质押系统",
      description: "多层级质押奖励\n从丁级到至尊\n最高5倍奖励倍数",
      maxMultiplier: "最高倍数"
    },
    governance: {
      title: "社区治理",
      description: "去中心化治理\n提案投票机制\n社区共同决策",
      minVotingPower: "最低投票权"
    }
  },

  // 首页英雄区域
  hero: {
    title: "PXPAT 区块链浏览器",
    subtitle: "专为 PXT/PAT 生态系统设计的现代化区块链浏览器",
    description: "提供实时数据查询、智能合约交互、质押管理和治理参与等全方位服务",
    features: [
      "✨ 实时数据",
      "📊 代币分析",
      "🔍 交易跟踪",
      "🏆 质押奖励"
    ],
    badges: {
      network: "BSC网络",
      block: "最新区块",
      realtime: "实时"
    },
    cta: {
      explore: "开始探索",
      staking: "立即质押",
      governance: "参与治理"
    },
    tokens: {
      pxt: "PXT",
      pat: "PAT"
    },
    stats: {
      totalAccounts: "总账户数",
      totalStaked: "总质押量",
      activeStakers: "活跃质押者",
      blockHeight: "区块高度",
      realtime: "实时",
      syncing: "同步中"
    }
  },

  // 快速操作区域
  quickActions: {
    title: "快速操作",
    subtitle: "探索PXPAT平台的核心功能和服务",
    actions: {
      transfer: {
        title: "转账测试",
        description: "测试代币转账功能",
        button: "开始转账"
      },
      tokens: {
        title: "查看代币",
        description: "浏览PXT和PAT代币信息",
        button: "查看详情"
      },
      staking: {
        title: "开始质押",
        description: "质押PXT获得奖励",
        button: "立即质押"
      },
      governance: {
        title: "参与治理",
        description: "参与社区治理投票",
        button: "参与治理"
      },
      explorer: {
        title: "区块浏览器",
        description: "浏览区块和交易",
        button: "开始浏览"
      }
    }
  },

  // 实时统计区域
  liveStats: {
    title: "实时统计",
    subtitle: "查看平台的实时数据和活跃度指标",
    cards: {
      totalStaked: {
        title: "总质押金额",
        realTimeUpdate: "实时更新"
      },
      totalRewards: {
        title: "总奖励金额",
        distributedRewards: "已分发奖励"
      },
      latestActivity: {
        title: "最新活动",
        systemStatus: "系统状态",
        normalOperation: "正常运行",
        dataSync: "数据同步",
        realTime: "实时",
        lastUpdate: "最后更新"
      },
      leaderboard: {
        title: "质押排行榜",
        viewFullLeaderboard: "查看完整排行榜",
        level: "等级",
        noData: "暂无数据"
      }
    }
  },

  // 网络状态区域
  networkStatus: {
    title: "网络状态",
    subtitle: "查看PXPAT网络的实时状态和性能指标",
    performance: {
      title: "网络性能",
      tps: "TPS",
      avgConfirmTime: "平均确认时间",
      gasPrice: "Gas价格",
      seconds: "秒"
    },
    stakingStats: {
      title: "质押统计",
      totalStaked: "总质押量",
      stakingUsers: "质押用户",
      avgApy: "平均APY"
    },
    governance: {
      title: "治理活动",
      activeProposals: "活跃提案",
      votingCount: "投票数量",
      votingRate: "投票率"
    }
  },

  // 实时数据区域
  realTimeData: {
    title: "实时数据",
    subtitle: "查看最新的区块和交易信息",
    status: {
      connected: "已连接",
      updating: "更新中",
      refreshing: "刷新中",
      refresh: "刷新"
    },
    sections: {
      blocks: "最新区块",
      transactions: "最新交易"
    }
  },

  // 搜索功能
  search: {
    placeholder: "搜索地址、交易哈希或区块号...",
    button: "搜索",
    failed: "搜索失败",
    noResults: "未找到结果",
    searching: "搜索中..."
  },

  // 网络统计
  networkStats: {
    title: "网络统计",
    subtitle: "实时网络数据概览",
    cards: {
      totalAccounts: {
        title: "总账户数",
        change24h: "24h"
      },
      totalStaked: {
        title: "总质押量 (PXT)",
        change24h: "24h"
      },
      activeStakers: {
        title: "活跃质押者",
        change24h: "24h"
      },
      blockHeight: {
        title: "当前区块高度",
        realTimeSync: "实时同步",
        syncing: "同步中..."
      }
    },
    errors: {
      loadFailed: "数据加载失败，请稍后重试"
    }
  },

  // 页脚
  footer: {
    title: "PXPAT 区块链浏览器 - 探索去中心化生态系统",
    subtitle: "© 2024 PXPAT Platform. 基于BSC测试网络构建的双代币生态系统。",
    status: {
      currentTime: "当前时间",
      networkStatus: "网络状态",
      version: "版本",
      latestBlock: "最新区块",
      normal: "正常",
      connecting: "连接中",
      loading: "加载中"
    }
  },

  // 生态系统概览
  ecosystemOverview: {
    title: "生态系统概览",
    subtitle: "PXPAT平台的全面数据统计和分析",
    features: {
      security: {
        title: "安全保障",
        description: "基于区块链技术的去中心化安全保障机制"
      },
      growth: {
        title: "持续增长",
        description: "平台生态系统的持续发展和用户增长"
      },
      community: {
        title: "社区治理",
        description: "去中心化的社区治理和决策机制"
      },
      innovation: {
        title: "技术创新",
        description: "前沿的区块链技术和创新应用"
      }
    }
  }
};
