// Tokens page English language pack
export const tokensLocaleEn = {
  // Tokens page
  page: {
    title: "Token Overview",
    subtitle: "View detailed information and statistics of platform tokens",
    description: "Deep dive into the PXT and PAT dual token mechanism, real-time monitoring of token prices, supply, burning mechanism and ecosystem data",
    loading: "Loading token information...",
  },

  // Token descriptions
  tokenDescriptions: {
    pxt: "Platform governance token for staking and governance voting",
    pat: "Activity reward token obtained through PXT staking"
  },

  // Token statistics labels
  stats: {
    totalSupply: "Total Supply",
    circulatingSupply: "Circulating Supply", 
    lockedSupply: "Locked Supply",
    burnedSupply: "Burned Supply",
    currentSupply: "Current Supply",
    inflationRate: "Inflation Rate",
    distributedRewards: "Distributed Rewards",
    rewardPool: "Reward Pool Balance",
    holders: "Holders",
    stakingRate: "Staking Rate"
  },

  // Token card
  tokenCard: {
    contractInfo: "Contract Information",
    contractAddress: "Contract Address",
    tokenType: "Token Type",
    supplyModel: "Supply Model",
    tokenStats: "Token Statistics",
    economicModel: "Economic Model",
    deflationMechanism: "Deflation Mechanism",
    inflationModel: "Inflation Model",
    holdersRanking: "Holders Ranking",
    rewardsHistory: "Rewards History",
    expandDetails: "Expand Details",
    collapseDetails: "Collapse Details",
    doubleClickTip: "Double click to enable expand/collapse functionality"
  },

  // Quick actions
  quickActions: {
    title: "Quick Actions",
    actions: {
      stakePxt: {
        title: "Stake PXT",
        description: "Stake to earn PAT rewards"
      },
      governance: {
        title: "Participate in Governance",
        description: "Use PXT to participate in voting"
      },
      viewStats: {
        title: "View Statistics",
        description: "View detailed PXT statistics"
      },
      claimRewards: {
        title: "Claim Rewards",
        description: "Claim staking PAT rewards"
      },
      tradePat: {
        title: "Trade PAT",
        description: "Trade PAT on DEX"
      },
      rewardsHistory: {
        title: "Rewards History",
        description: "View reward distribution history"
      }
    }
  },

  // Related links
  relatedLinks: {
    title: "Related Links",
    links: {
      website: {
        title: "Official Website",
        description: "Visit official website"
      },
      whitepaper: {
        title: "Whitepaper",
        description: "Read technical documentation"
      },
      sourceCode: {
        title: "Source Code",
        description: "View GitHub repository"
      },
      blockExplorer: {
        title: "Block Explorer",
        description: "View on BSCScan"
      }
    }
  },

  // Holders table
  holdersTable: {
    rank: "Rank",
    address: "Address",
    balance: "Balance",
    percentage: "Percentage"
  },

  // Economic model
  economicModel: {
    supplyType: "Supply Type",
    totalSupply: "Total Supply",
    mechanismDescription: "Mechanism Description"
  },

  // Deflation model
  deflationModel: {
    burnMechanism: "Burn Mechanism",
    blackHoleAddress: "Black Hole Address",
    revenueRepurchaseBurn: "Revenue Repurchase & Burn",
    mechanismDescription: "Mechanism Description"
  },

  // Supply information
  supplyInfo: {
    title: "Supply Information",
    subtitle: "Detailed token supply breakdown",
    totalSupply: "Total Supply",
    circulatingSupply: "Circulating Supply",
    lockedSupply: "Locked Supply",
    burnedSupply: "Burned Supply",
    percentage: "Percentage",
    amount: "Amount"
  },

  // Distribution chart
  distributionChart: {
    title: "Token Distribution",
    subtitle: "Current token allocation breakdown",
    legends: {
      circulating: "Circulating",
      locked: "Locked",
      burned: "Burned",
      treasury: "Treasury",
      rewards: "Rewards Pool"
    }
  },

  // Price information
  priceInfo: {
    title: "Price Information",
    subtitle: "Current market data",
    currentPrice: "Current Price",
    priceChange24h: "24h Change",
    volume24h: "24h Volume",
    marketCap: "Market Cap",
    fullyDilutedValue: "Fully Diluted Value"
  },

  // Staking statistics
  stakingStats: {
    title: "Staking Statistics",
    subtitle: "Platform staking overview",
    totalStaked: "Total Staked",
    stakingRatio: "Staking Ratio",
    averageStakingTime: "Average Staking Time",
    totalStakers: "Total Stakers",
    stakingRewards: "Staking Rewards",
    apr: "APR"
  },

  // Token metrics
  metrics: {
    title: "Token Metrics",
    subtitle: "Key performance indicators",
    holders: "Token Holders",
    transactions: "Total Transactions",
    transfers: "Total Transfers",
    contracts: "Smart Contracts",
    dailyActiveUsers: "Daily Active Users",
    weeklyActiveUsers: "Weekly Active Users"
  },

  // Recent transactions
  recentTransactions: {
    title: "Recent Transactions",
    subtitle: "Latest token transfer activities",
    viewAll: "View All",
    noTransactions: "No recent transactions",
    columns: {
      hash: "Transaction Hash",
      from: "From",
      to: "To", 
      amount: "Amount",
      time: "Time"
    }
  },

  // Token utilities
  utilities: {
    title: "Token Utilities",
    subtitle: "How tokens are used in the ecosystem",
    pxt: {
      governance: "Governance Voting",
      staking: "Staking for Rewards",
      fees: "Fee Discounts",
      access: "Premium Features"
    },
    pat: {
      rewards: "Staking Rewards",
      payments: "Transaction Fees",
      incentives: "Ecosystem Incentives",
      community: "Community Programs"
    }
  },

  // Tokenomics
  tokenomics: {
    title: "Tokenomics",
    subtitle: "Economic model and token mechanics",
    initialSupply: "Initial Supply",
    maxSupply: "Maximum Supply",
    mintingRate: "Minting Rate",
    burningMechanism: "Burning Mechanism",
    rewardDistribution: "Reward Distribution",
    vestingSchedule: "Vesting Schedule"
  },

  // Actions
  actions: {
    stake: "Stake Now",
    trade: "Trade",
    addToWallet: "Add to Wallet",
    viewContract: "View Contract",
    documentation: "Documentation",
    whitepaper: "Whitepaper"
  },

  // Loading states
  loading: {
    tokenData: "Loading token data...",
    priceData: "Loading price data...",
    stakingData: "Loading staking data...",
    transactions: "Loading transactions..."
  },

  // Error states
  errors: {
    loadFailed: "Failed to load token data",
    priceUnavailable: "Price data unavailable",
    stakingUnavailable: "Staking data unavailable",
    retry: "Retry"
  },

  // Common text
  common: {
    loading: "Loading...",
    error: "Error",
    retry: "Retry",
    viewMore: "View More",
    viewAll: "View All",
    noData: "No data available"
  },

  // Token detailed information
  tokenDetails: {
    pxt: {
      name: "Paper x Token",
      symbol: "PXT",
      description: "Paper x Token",
      type: "Governance Token",
      supplyModel: "Fixed Supply",
      economicModel: {
        supplyType: "Fixed Supply",
        totalSupply: "100M PXT",
        description: [
          "PXT adopts a fixed total supply design with no additional issuance",
          "Total supply is fixed at 100 million tokens to ensure scarcity",
          "Gradually released to the market through staking mining",
          "Multiple deflationary mechanisms maintain long-term value"
        ]
      },
      deflationModel: {
        burnRate: "Multiple Burn",
        mechanisms: ["Transaction Fee Burn", "Early Unlock Penalty", "Governance Proposal Deposit"],
        description: [
          "30% of transaction fees will be permanently burned",
          "Penalty portion from early unstaking is burned",
          "Deposit portion from failed governance proposals is burned",
          "30% of platform revenue is used for buyback and burn"
        ]
      }
    },
    pat: {
      name: "PX Activity Token",
      symbol: "PAT",
      description: "PX Activity Token",
      type: "Reward Token",
      supplyModel: "Inflation Model",
      economicModel: {
        supplyType: "Inflation Model",
        totalSupply: "Dynamic Supply",
        description: [
          "PAT adopts a dynamic inflation model to support ecosystem development",
          "Initial supply of 300 million tokens, increased through inflation mechanism",
          "Mainly used for staking rewards and ecosystem incentives",
          "Inflation rate dynamically adjusts based on network activity"
        ]
      },
      inflationModel: {
        currentRate: "1.5%",
        maxRate: "2.0%",
        description: [
          "PAT adopts an inflation model, mainly distributed through staking rewards",
          "Current annual inflation rate is 1.5%, maximum not exceeding 2.0%",
          "New tokens are mainly used for staking reward distribution",
          "Inflation rate can be adjusted through governance voting"
        ]
      }
    }
  }
};
