jun<PERSON><PERSON><PERSON><PERSON>@jzy local-bsc-chain % 
jun<PERSON><PERSON><PERSON><PERSON>@jzy local-bsc-chain % clear
jun<PERSON><PERSON><PERSON><PERSON>@jzy local-bsc-chain % ./start-interactive.sh
🚀 启动BSC链（交互式）...
🧹 清理旧数据...
🔑 生成验证者节点密钥...


[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = ******************************************
BLS Public key       = 0x85eea401554fa4ffdbe74e3d41fd869e306b09052066bfdae4bd853c81f79e454f4c34e85abb7acfccd68c903ad12e96
Node ID              = 16Uiu2HAkx1wVEaNGucpaYFM3miumpxYc7Ctmx5Ym4z1yaX1VuUFb



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x57aa1e488E04674899fC8d1aAfEbCFD84d76C0e8
BLS Public key       = 0xb6e903023a345604f6a88c3c0c88dd4e3a7bc64811016766b3f5a9038ea61bd4077aa77306d29d262f2a5a10817ea7db
Node ID              = 16Uiu2HAmGKNt6XJaMC7QT1Nuh7wWV5mN8gJMP3CGuBeEByYxJHtW



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x92241D8fA025a5AdadF22496F984Dd51f7044135
BLS Public key       = 0x9990ed130681c08fb8cab3eb518b3a28ca513ab5d72677fbbd85aea58b9ff42214a334033df0d8f734d6fdd2e8937f00
Node ID              = 16Uiu2HAmMQpX2674svcnn1YjLNXKYF3psfDtk6aBdpqbBYQbLmhg



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x25dC0484fe8b02f4cC5C813bD98A1111988f40AA
BLS Public key       = 0xb658a3d7211988561f9627e17f8997abcfe5f96ee2f106aa42d782cc33bcf64fc2f084f3127b523ac67ba7ec577cb256
Node ID              = 16Uiu2HAm7zevGbrpjhcEQQiXvabJAVHj9itE7DCgJ3DYQ3FdFdLm

🔑 获取节点、公钥和私钥...
✅ 节点ID: 16Uiu2HAkx1wVEaNGucpaYFM3miumpxYc7Ctmx5Ym4z1yaX1VuUFb
✅ 预分配地址 (公钥): ******************************************
🔑 私钥 (用于MetaMask导入): d9d887f848c5bfd9caaf35fec8438777b93103f1854a802ea3ffa2ac660391b5
   [警告] 这是一个开发密钥，请勿在主网使用!
🔧 更新主.env文件中的部署者私钥...
✅ 已更新主.env文件中的DEPLOYER_PRIVATE_KEY
✅ 已更新bsc-local.env文件
💡 提示: BSC本地链私钥已同步到主.env文件，hardhat将使用正确的私钥
📜 创建创世文件...
✅ 创世文件已创建（包含预分配余额）
🔥 启动BSC节点...
▶️ 启动主节点...
▶️ 启动节点2...
▶️ 启动节点3...
▶️ 启动节点4...
✅ 所有节点已启动
🌐 主节点 RPC: http://127.0.0.1:18545
💰 预分配地址: ******************************************
⏳ 等待链启动...
🧪 测试链状态...
✅ 链状态正常，当前区块高度: 0x3
💰 检查预分配账户余额...
💰 预分配账户余额: 10000.0000 BNB
====================================
🎉 BSC链启动成功！
====================================
MetaMask网络配置:
网络名称: BSC Local Chain
RPC URL: http://127.0.0.1:18545
链ID: 97
货币符号: BNB

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost

=== 开始部署核心代币系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署账户余额: 10000.0 ETH

=== 创建池子账户 ===
中国大陆池子地址: ******************************************
国际池子地址: ******************************************
质押池地址: ******************************************
跨链池地址: ******************************************

=== 1. 部署 PXT 治理代币 ===
✅ PXT代币部署成功: ******************************************

=== 2. 部署 PAT 功能代币 ===
✅ PAT代币部署成功: ******************************************

=== 3. 部署代币注册表 ===
✅ 代币注册表部署成功: ******************************************

=== 4. 配置代币注册表 ===
注册表合约owner: ******************************************
部署者地址: ******************************************
是否为owner: true
正在添加工厂权限...
✅ 部署者已添加为工厂
验证工厂权限: true
准备注册PXT代币:
- 地址: ******************************************
- 名称: Platform Governance Token
- 符号: PXT
- 小数位: 18n
- 总供应量: 100000000.0
✅ PXT代币已注册到注册表
准备注册PAT代币:
- 地址: ******************************************
- 名称: PX Activity Token
- 符号: PAT
- 小数位: 18n
- 总供应量: 300000000.0
✅ PAT代币已注册到注册表

=== 5. 验证部署结果 ===
PXT总供应量: 100000000.0
PAT总供应量: 300000000.0
部署者PXT余额: 55002000.0
部署者PAT余额: 0.0

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/core-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost

=== 开始部署质押系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
✅ 已加载核心合约地址
- PXT代币: ******************************************
- PAT代币: ******************************************
- 代币注册表: ******************************************
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署质押工厂 ===
✅ 质押工厂部署成功: 0x7Ef2Bcf82405E77a369Fe7665A9A7E935e99686D

=== 2. 部署质押池 ===
✅ 质押池部署成功: ******************************************

=== 3. 初始化质押池 ===
✅ 质押池初始化完成

=== 4. 部署奖励分配器 ===
✅ 奖励分配器部署成功: 0xA1d269bF8757257cd2D5c69e59eb990f0fA01Fb6

=== 5. 配置质押系统关系 ===
✅ 质押池已添加到奖励分配器
✅ 已设置奖励分配器
✅ 已设置质押池实现合约

=== 6. 质押等级已预配置 ===
质押等级在合约构造函数中已预设：
- 丁级: 100 PXT
- 丙级: 1,000 PXT
- 乙级: 5,000 PXT
- 甲级: 20,000 PXT
- 十绝: 100,000 PXT
- 双十绝: 250,000 PXT
- 至尊: 500,000 PXT

=== 7. 验证质押系统 ===
最小质押金额: 1.0 PXT
基础年化收益率: 500 基点

=== 质押系统部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/staking-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost

=== 开始部署治理系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
调试 - 质押部署数据: {
  "network": "localhost",
  "chainId": 97,
  "timestamp": "2025-08-04T07:00:00.371Z",
  "deployer": "******************************************",
  "treasury": "******************************************",
  "contracts": {
    "StakingFactory": "0x7Ef2Bcf82405E77a369Fe7665A9A7E935e99686D",
    "StakingPool": "******************************************",
    "RewardDistributor": "0xA1d269bF8757257cd2D5c69e59eb990f0fA01Fb6"
  },
  "configuration": {
    "minStakeAmount": "1000000000000000000",
    "baseAPY": "500",
    "stakeLevels": 7
  }
}
调试 - 读取的质押池地址: ******************************************
✅ 已加载依赖合约地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************
质押池地址: ******************************************

=== 1. 部署投票合约 ===
✅ 投票合约部署成功: 0x1e811A5C4A80f428B798CDdD75f59F85DDfff3Cf
✅ 投票合约初始化完成

=== 2. 部署提案管理器 ===
✅ 提案管理器部署成功: 0xbe0630899c07fd34A3a95F1ee077A8f4fb48694F
✅ 提案管理器初始化完成

=== 3. 部署DAO主合约 ===
✅ DAO主合约部署成功: 0x46a6042aC63C0A1Fde206117002403D01ed5Ff05
✅ DAO主合约初始化完成

=== 4. 部署国库 ===
✅ 国库合约部署成功: ******************************************

=== 5. 配置治理系统关系 ===
✅ Treasury DAO地址更新完成
✅ 提案管理器配置完成

=== 6. 验证治理系统 ===
投票合约PXT代币地址: ******************************************
投票合约质押池地址: ******************************************
最低参与率: 10 %

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/governance-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost

=== 开始部署内容上链系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
部署者余额: 9999.96 ETH
✅ 已加载依赖合约地址
- PAT代币: ******************************************
- 国库合约: ******************************************

=== 1. 部署ContentRegistry合约 ===
正在部署ContentRegistry...
✅ ContentRegistry部署成功: ******************************************
验证ContentRegistry配置...
  PAT代币地址: ******************************************
  国库地址: ******************************************

=== 2. 部署ContentCharacter合约 ===
正在部署ContentCharacter...
✅ ContentCharacter部署成功: ******************************************

=== 3. 部署ContentMint合约 ===
正在部署ContentMint...
✅ ContentMint部署成功: ******************************************
验证ContentMint配置...
  ContentRegistry地址: ******************************************
  国库地址: ******************************************

=== 4. 测试基础功能 ===
测试内容类型费用...
  视频费用: 1.0 PAT
  文章费用: 0.05 PAT
测试统计信息...
  总内容数: 0
  活跃内容数: 0
  总PAT消耗: 0.0 PAT

=== 5. 生成验证命令 ===
ContentRegistry验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"
ContentCharacter验证命令:
  npx hardhat verify --network bscTestnet ******************************************
ContentMint验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"

=== 6. 保存部署信息 ===
✅ 部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/content-deployment.json

=== 内容上链系统部署完成 ===
ContentRegistry: ******************************************
ContentCharacter: ******************************************
ContentMint: ******************************************

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost

🌉 开始部署BSC端TokenBridge合约
================================================
网络: localhost
链ID: 97
时间: 2025-08-04T07:05:14.378Z
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
✅ 已加载核心代币地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署TokenBridge合约 ===
✅ TokenBridge部署成功: 0x04e10f90D5D0C5A59A4Ed3a11a9081bB7486eEb8

=== 2. 设置管理员权限 ===
✅ 已设置部署者为管理员

=== 3. 配置PXA链支持 ===
✅ 已设置PXA链名称: PXA
正在添加PXA链支持...
等待交易确认...
交易确认成功，区块: 180
✅ 已添加PXA链支持
  链ID: 327
  验证者数量: 3
  需要确认数: 2
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 4. 验证配置 ===
PXA链支持状态: ✅ 支持
PXA链验证者数量: 3
验证者地址:
  1. ******************************************
  2. ******************************************
  3. ******************************************
PXA链费用配置:
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/bridge-deployment.json

📋 重要信息:
TokenBridge地址: 0x04e10f90D5D0C5A59A4Ed3a11a9081bB7486eEb8
支持的目标链: PXA (链ID: 327)
验证者数量: 3
需要确认数: 2
费用接收地址: ******************************************

junziliuyi@jzy pxa-chain % npx hardhat compile
Compiled 51 Solidity files successfully (evm target: paris).
junziliuyi@jzy pxa-chain % npx hardhat run scripts/deployment/complete-deployment.js --network localhost

Compiled 51 Solidity files successfully (evm target: paris).
🚀 PXA链完整部署和测试流程
=====================================
⏰ 开始时间: 2025/8/4 15:33:05

🔍 第二阶段: 检查PXA链状态
=====================================
🔗 正在连接到: http://127.0.0.1:8545
✅ PXA链运行正常，当前区块: 475
🔑 使用Hardhat配置的账户进行部署
💰 部署账户: ******************************************
💰 账户余额: 50000000.0 PXA

🏗️ 第三阶段: 部署核心合约
=====================================
📦 部署GasFeeManager... 
⏳ 等待交易确认: GasFeeManager部署
✅ 交易确认: GasFeeManager部署 (Gas: 1255872)
✅ GasFeeManager部署完成: 0x05f30452BC7A5aa0cEba2592395f4A42E1B3b0A7
📦 部署TokenFactory... 
⏳ 等待交易确认: TokenFactory部署
✅ 交易确认: TokenFactory部署 (Gas: 3287598)
✅ TokenFactory部署完成: 0xc7ad5BCCdaD011B55c5656103f338C1BBE1Fbaa6

🌉 第四阶段: 部署跨链桥合约
=====================================
📦 部署ValidatorRegistry... 
⏳ 等待交易确认: ValidatorRegistry部署
✅ 交易确认: ValidatorRegistry部署 (Gas: 3099794)
✅ ValidatorRegistry部署完成: 0x4407A89e201B2F34d2E2959A3dEEb01aE9968ebB
📦 部署BridgeReceiver... 
⏳ 等待交易确认: BridgeReceiver部署
✅ 交易确认: BridgeReceiver部署 (Gas: 4836504)
✅ BridgeReceiver部署完成: ******************************************
📋 部署记录已保存: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/complete-deployment-1754292785619.json
📋 跨链桥配置已保存: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/bridge-config.json

🎉 部署完成!
=====================================
📋 部署记录: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/complete-deployment-1754292785619.json
⏰ 完成时间: 2025/8/4 15:34:12

junziliuyi@jzy pxa-chain % npx hardhat run scripts/fix-all-scripts.js --network localhost

🔧 开始自动修复所有脚本...

📄 读取部署记录: complete-deployment-1754292785619.json
📍 使用TokenFactory地址: 0xc7ad5BCCdaD011B55c5656103f338C1BBE1Fbaa6

🔧 修复 test-token-factory.js...
✅ test-token-factory.js 修复完成
🔧 修复 create-token.js...
✅ create-token.js 修复完成
🔧 修复 list-tokens.js...
✅ list-tokens.js 修复完成
🔧 修复 test-gas-fees-collection.js...
✅ test-gas-fees-collection.js 修复完成
🔧 修复 test-governance-ecosystem.js...
✅ test-governance-ecosystem.js 修复完成
🔧 修复 test-bridge-scenarios.js...
✅ test-bridge-scenarios.js 修复完成

🎉 所有脚本修复完成!

📋 现在可以按顺序执行以下命令:
1. npx hardhat run scripts/deployment/complete-deployment.js --network <network>
2. npx hardhat run scripts/testing/test-token-factory.js --network <network>
3. npx hardhat run scripts/query/chain-status.js --network <network>
4. npx hardhat run scripts/create-token.js --network <network>
5. npx hardhat run scripts/query/list-tokens.js --network <network>
6. npx hardhat run scripts/testing/test-bridge-scenarios.js --network <network>
7. npx hardhat run scripts/testing/test-gas-fees-collection.js --network <network>
8. npx hardhat run scripts/testing/test-governance-ecosystem.js --network <network>

💡 将 <network> 替换为 localhost 或 production
💡 模拟和测试相关脚本已删除，只保留真实功能
junziliuyi@jzy pxa-chain % npx hardhat run scripts/deploy/deploy-wpat-token.js --network localhost

🪙 部署PXPAC链wPAT代币合约
================================================
网络: localhost
时间: 2025-08-04T07:35:15.720Z
部署账户: ******************************************
部署账户余额: 49999999.991875898 ETH

=== 1. 读取BSC链PAT代币信息 ===
✅ BSC链PAT代币信息:
   地址: ******************************************
   链ID: 97
   符号: PAT
   精度: 18

=== 2. 读取PXPAC链桥接合约信息 ===
✅ PXPAC链桥接合约:
   BridgeReceiver: ******************************************
   部署文件: complete-deployment-1754292785619.json

=== 3. 部署wPAT代币合约 ===
wPAT代币参数:
   名称: Wrapped PX Activity Token
   符号: wPAT
   精度: 18
   原生合约: ******************************************
   原生链ID: 97
   原生符号: PAT
   桥接合约: ******************************************
   管理员: ******************************************

正在部署wPAT代币合约...
✅ wPAT代币部署成功!
   合约地址: ******************************************
   交易哈希: 0x538d60343c44b9cf7b4a2be4ce1509b1ae0ef8846fccf6a518dcdb7240246746

=== 4. 验证部署结果 ===
合约验证:
   名称: Wrapped PX Activity Token
   符号: wPAT
   精度: 18n
   总供应量: 0.0
原生代币信息:
   原生合约: ******************************************
   原生链ID: 97
   原生符号: PAT
   原生精度: 18n
权限验证:
   桥接权限: ✅
   铸造权限: ✅

=== 5. 保存部署信息 ===
✅ 部署信息已保存: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/wpat-deployment-1754292933662.json

🎉 wPAT代币部署完成!
================================================
✅ 合约地址: ******************************************
✅ 代币名称: Wrapped PX Activity Token
✅ 代币符号: wPAT
✅ 桥接功能: 已启用
✅ 跨链映射: BSC PAT ↔ PXPAC wPAT

junziliuyi@jzy pxa-chain % npx hardhat run scripts/deployment/deploy-content-system.js --network localhost

📝 部署PXPAC链内容系统
================================================
网络: localhost
时间: 2025-08-04T07:37:36.600Z
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
✅ 使用Test Token作为支付代币: 0xb53892B96C910AB39f64f7B3D848650A03dc3B24
✅ 代币名称: Test Token (TEST)
💡 这个代币有充足的供应量，适合测试内容系统

=== 1. 部署ContentCharacter合约 ===
正在部署ContentCharacter...
✅ ContentCharacter部署成功: 0x62C6b6DE32c336067a96Ae1e269B168A8cc1a97A

=== 2. 部署ContentRegistry合约 ===
正在部署ContentRegistry...
✅ ContentRegistry部署成功: 0xe438DFa7299C43667357d1F5570f4A4Ab3e5c27D

=== 3. 配置内容类型和费用 ===
配置内容类型和费用...
- 添加类型: article, 费用: 0.05 TEST
- 添加类型: video, 费用: 1.0 TEST
- 添加类型: audio, 费用: 0.25 TEST
- 添加类型: image, 费用: 0.01 TEST
- 添加类型: document, 费用: 0.05 TEST
✅ 内容类型配置完成

=== 4. 配置角色权限 ===
配置内容角色...
- 添加角色: content_moderator
- 添加角色: education_expert
- 添加角色: technical_reviewer
- 添加角色: community_manager
- 添加角色: creator
✅ 角色配置完成

=== 5. 部署ContentMint合约 ===
正在部署ContentMint...
✅ ContentMint部署成功: 0xb546d6155AF23CC924a0E7e9FF15620601cA55b5

=== 6. 配置ContentMint价格 ===
配置铸造价格...
- 配置 article: 基础 0.01 TEST, 递增 0.005 TEST, 最大 0.5 TEST
- 配置 video: 基础 0.1 TEST, 递增 0.05 TEST, 最大 2.0 TEST
- 配置 audio: 基础 0.05 TEST, 递增 0.01 TEST, 最大 1.0 TEST
- 配置 image: 基础 0.002 TEST, 递增 0.001 TEST, 最大 0.1 TEST
- 配置 document: 基础 0.01 TEST, 递增 0.005 TEST, 最大 0.5 TEST
✅ 铸造价格配置完成

=== 7. 设置权限 ===
设置ContentRegistry为ContentCharacter的铸造者...
设置ContentMint为ContentCharacter的铸造者...
✅ 权限设置完成

=== 8. 验证部署 ===
激活的内容类型: Result(5) [ 'article', 'video', 'audio', 'image', 'document' ]
video内容费用: 1.0 TEST
video铸造基础价格: 0.1 TEST

=== 9. 保存部署信息 ===
✅ 部署信息已保存: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/localhost/content-deployment.json

🎉 PXA链内容系统部署完成！
================================================
✅ 支付代币: 0xb53892B96C910AB39f64f7B3D848650A03dc3B24 (Test Token)
✅ ContentCharacter: 0x62C6b6DE32c336067a96Ae1e269B168A8cc1a97A
✅ ContentRegistry: 0xe438DFa7299C43667357d1F5570f4A4Ab3e5c27D
✅ ContentMint: 0xb546d6155AF23CC924a0E7e9FF15620601cA55b5
✅ 支持内容类型: article, video, audio, image, document
✅ 铸造功能: 已配置

junziliuyi@jzy pxa-chain % npx hardhat run scripts/fix-content-scripts.js --network localhost


🔧 修复内容系统脚本地址引用
=====================================
🌐 网络: localhost
✅ 已加载内容系统部署记录

📋 内容系统合约地址:
- wPAT代币: 0xb53892B96C910AB39f64f7B3D848650A03dc3B24
- ContentRegistry: 0xe438DFa7299C43667357d1F5570f4A4Ab3e5c27D
- ContentCharacter: 0x62C6b6DE32c336067a96Ae1e269B168A8cc1a97A
- ContentMint: 0xb546d6155AF23CC924a0E7e9FF15620601cA55b5

🔧 开始修复脚本...
✅ test/pxpac-ipfs-content-upload.js 已存在，无需修复
✅ test/test-content-mint.js 已存在，无需修复
✅ query/content-stats.js 已存在，无需修复

🎉 内容系统脚本地址修复完成!
=====================================


junziliuyi@jzy pxa-chain % npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost
🌉 配置BSC本地链跨PXA本地链
================================================
时间: 2025-08-04T08:19:14.235Z
部署者账户: ******************************************
账户余额: 49999999.983524581 PXA
BSC本地链配置:
- 链ID: 97
- TokenBridge: 0x4815cD2986229a7633f66DC358262c05325E7128
- PAT代币: 0xe5EC13184D7184E0923453c1CD37600D7B97A387
- 验证者数量: 3

✅ 已加载PXA链部署信息
- BridgeReceiver: ******************************************
- wPAT代币: ******************************************

=== 1. 检查当前配置状态 ===
当前配置:
- 总验证者数量: 0
- 需要验证者数量: 1
- 源链ID: 97
- PAT代币支持: 否
- PAT映射地址: 0x0000000000000000000000000000000000000000
- 映射是否正确: 否

=== 2. 配置源链ID ===
✅ 源链ID配置正确

=== 3. 配置验证者 ===
添加验证者: 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121
✅ 验证者 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121 已添加
添加验证者: ******************************************
✅ 验证者 ****************************************** 已添加
添加验证者: ******************************************
✅ 验证者 ****************************************** 已添加
最终验证者数量: 3

=== 4. 配置代币映射 ===
设置新的代币映射...
- BSC PAT: 0xe5EC13184D7184E0923453c1CD37600D7B97A387
- PXA wPAT: ******************************************
✅ 代币映射配置完成
最终映射状态:
- 映射地址: ******************************************
- 代币支持: 是

=== 5. 检查wPAT权限 ===
wPAT权限检查:
- BridgeReceiver地址: ******************************************
- 是否有桥接权限: 是

=== 6. 最终配置验证 ===
✅ 最终配置状态:
- 总验证者数量: 3
- 需要验证者数量: 1
- 源链ID: 97 ✅
- PAT代币支持: ✅
- PAT映射正确: ✅

🎉 跨链桥配置完成！可以开始跨链测试

=== 7. 保存配置信息 ===
✅ 跨链桥配置已保存: /Users/<USER>/Desktop/PXA/pxa-chain/deployments/bsc-pxa-local-bridge-config.json

🎯 下一步操作:
1. 测试BSC到PXA跨链:
   npx hardhat run scripts/bridge/test-bsc-to-pxa-bridge.js --network localhost
2. 处理BSC跨链请求:
   npx hardhat run scripts/bridge/process-bsc-bridge-request.js --network localhost
3. 检查wPAT余额:
   npx hardhat run scripts/bridge/check-wPAT-balance.js --network localhost

📊 跨链流程说明:
BSC本地链: PAT锁定 → 事件监听 → PXA本地链: wPAT铸造
费用: BSC Gas + 跨链费用 + PXA处理费用

🎉 BSC本地链跨PXA本地链配置完成！
junziliuyi@jzy pxa-chain % 

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/bridge/bridge-10k-pat-from-pool.js --network localhost

🌉 使用跨链池直接执行10,000 PAT跨链到PXA链
================================================
🌐 当前网络: localhost
📝 部署者地址: ******************************************
⏰ 时间: 2025-08-04T08:21:43.222Z
🎯 跨链参数:
- 跨链金额: 10000.0 PAT
- 目标链ID: 327
- 接收地址: ******************************************
✅ 已加载部署信息
📝 合约地址:
- PAT代币: ******************************************
- 跨链桥: 0x04e10f90D5D0C5A59A4Ed3a11a9081bB7486eEb8
- 跨链池: ******************************************
- 跨链池钱包: ******************************************

=== 1. 检查跨链池余额和Gas费 ===
跨链池PAT余额: 100000000.0 PAT
跨链池BNB余额: 2.01 BNB

=== 2. 计算跨链费用 ===
跨链手续费: 0.1 BNB

=== 3. 授权PAT代币给跨链桥 ===
当前授权额度: 0.0 PAT
授权额度不足，正在授权...
⏳ 等待授权交易确认...
✅ 授权成功，新授权额度: 10000.0 PAT

=== 4. 执行跨链操作 ===
🚀 开始跨链转账...
参数确认:
- PAT代币地址: ******************************************
- 跨链金额: 10000.0 PAT
- 目标链ID: 327
- 接收地址: ******************************************
- 手续费: 0.1 BNB
⏳ 等待跨链交易确认...
交易哈希: 0x4b7c181f7b83c7bf7f2e2ee32b7e5881a485cb6136e03e960c6dbebfbdbf64b9
✅ 跨链交易成功!
- 区块号: 1710
- Gas使用: 410826

💰 跨链后余额:
- 跨链池PAT余额: 99990000.0 PAT
- 跨链池BNB余额: 1.91 BNB

🎉 跨链操作完成!
=====================================
✅ 已从BSC链跨链 10000.0 PAT
✅ 目标链: PXA (链ID: 327 )
✅ 接收地址: ******************************************
✅ 交易哈希: 0x4b7c181f7b83c7bf7f2e2ee32b7e5881a485cb6136e03e960c6dbebfbdbf64b9

📋 下一步:
1. 等待PXA链验证者处理跨链请求
2. 检查PXA链上的wPAT余额变化
3. 验证跨链是否成功完成

junziliuyi@jzy pxa-chain % npx hardhat run scripts/bridge/add-bsc-validator-to-pxa.js --network localhost
🔧 添加BSC验证者到PXA链BridgeReceiver
================================================
时间: 2025-08-04T08:57:16.562Z
部署者账户: ******************************************
✅ 已加载跨链桥配置
合约地址:
- BridgeReceiver: ******************************************
BSC验证者列表:
  1. 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121
  2. ******************************************
  3. ******************************************

=== 1. 检查当前验证者状态 ===
当前验证者配置:
- 总验证者数量: 3
- 需要验证者数量: 1

BSC验证者在PXA链的状态:
  0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121: ✅ 已是验证者
  ******************************************: ✅ 已是验证者
  ******************************************: ✅ 已是验证者

=== 2. 添加BSC验证者到PXA链 ===
⏭️ 验证者 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121 已存在，跳过
⏭️ 验证者 ****************************************** 已存在，跳过
⏭️ 验证者 ****************************************** 已存在，跳过

=== 3. 验证添加结果 ===
最终验证者数量: 3
最终BSC验证者状态:
  0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121: ✅ 已是验证者
  ******************************************: ✅ 已是验证者
  ******************************************: ✅ 已是验证者

✅ 所有BSC验证者都已经在PXA链中
可以直接处理跨链请求

🎉 BSC验证者添加完成！

junziliuyi@jzy pxa-chain % npx hardhat run scripts/bridge/add-token-mapping.js --network localhost
🔗 添加BSC PAT → PXA wPAT代币映射
================================================
时间: 2025-08-04T09:17:01.155Z
部署者账户: ******************************************
✅ 已加载跨链桥配置
合约地址:
- BridgeReceiver: ******************************************
- BSC PAT代币: ******************************************
- PXA wPAT代币: ******************************************

=== 1. 检查当前代币映射状态 ===
BSC PAT代币支持状态: ❌ 不支持
当前映射地址: 0x0000000000000000000000000000000000000000
映射是否正确: ❌ 错误

=== 2. 添加代币映射 ===
正在添加代币映射...
- 源代币 (BSC PAT): ******************************************
- 目标代币 (PXA wPAT): ******************************************
⏳ 等待交易确认...
交易哈希: 0x8704a45996386da7782246b278736fb711c9d20583ff4f7d5aef4f6084254a88
✅ 代币映射添加成功!
- Gas使用: 66428
- 区块号: 1170

=== 3. 验证映射结果 ===
最终代币支持状态: ✅ 已支持
最终映射地址: ******************************************
最终映射是否正确: ✅ 正确

🎉 代币映射配置完成!
=====================================
✅ BSC PAT代币已支持跨链
✅ 映射到PXA wPAT代币
✅ 现在可以处理跨链请求

📋 下一步操作:
npx hardhat run scripts/bridge/process-bsc-local-with-validator.js --network localhost

🎉 代币映射配置完成！
junziliuyi@jzy pxa-chain % npx hardhat run scripts/bridge/process-bsc-local-with-validator.js --network localhost
🌉 使用BSC验证者处理跨链请求
================================================
时间: 2025-08-04T09:17:24.206Z
验证者账户: 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121
账户余额: 1.0 PXA
✅ 已加载跨链桥配置
合约地址:
- BridgeReceiver: ******************************************
- wPAT代币: ******************************************

=== 1. 使用BSC跨链记录中的真实数据 ===
BSC跨链请求信息:
- 源代币: ******************************************
- 接收者: ******************************************
- 数量: 10000.0 PAT
- 源链ID: 97
- BSC交易哈希: 0x4b7c181f7b83c7bf7f2e2ee32b7e5881a485cb6136e03e960c6dbebfbdbf64b9

=== 2. 检查接收者余额（处理前） ===
接收者当前wPAT余额: 0.0 wPAT

=== 3. 生成转移ID并检查状态 ===
转移ID: 0x0d8e51ec64cef86b41355c0d2e1a797b886e2cec19af5045b1869195e24c11e7
转移是否已处理 (复杂版): ❌ 未处理

=== 4. 检查验证者权限 ===
验证者权限检查:
- 当前账户: 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121
- 总验证者数量: 3
- 需要验证者数量: 1
- 是否为验证者 (hasRole): ✅ 是

=== 5. 处理跨链转移 ===
🚀 开始处理跨链转移...
尝试使用简化版本处理...
简化版本失败，尝试复杂版本...
⏳ 等待交易确认...
交易哈希: 0x3d45a0404cd3e06efdfe9fcf64f55dace5fd077a07d2bf980fedc8b46e71d04f
✅ 跨链转移处理成功!
- Gas使用: 612087
- 区块号: 1173

=== 6. 验证处理结果 ===
处理结果:
- 接收者处理前余额: 0.0 wPAT
- 接收者处理后余额: 9999.0 wPAT
- 余额增加: 9999.0 wPAT
- 转移最终状态 (复杂版): ❌ 未处理
- wPAT总供应量: 10000.0 wPAT
❌ 跨链处理可能失败，请检查结果

🎉 BSC本地链跨链请求处理完成！

junziliuyi@jzy pxa-chain % npx hardhat run scripts/bridge/check-bsc-pxa-bridge-status.js --network localhost

📊 检查BSC本地链跨PXA本地链状态
================================================
时间: 2025-08-04T09:32:07.308Z
查询账户: ******************************************
✅ 已加载跨链桥配置

=== 1. 跨链桥配置状态 ===
跨链桥配置:
- 总验证者数量: 3
- 需要验证者数量: 1
- 源链ID: 97
- PAT代币支持: ✅ 是
- PAT映射地址: ******************************************
- 映射正确: ✅ 是
配置状态: ✅ 完整

=== 2. wPAT代币状态 ===
wPAT代币信息:
- 名称: Wrapped PX Activity Token
- 符号: wPAT
- 精度: 18
- 总供应量: 10000.0 wPAT
- 桥接权限: ✅ 正常

=== 3. 主要账户余额 ===
账户wPAT余额:
- 部署者: 0.0 wPAT
- BSC部署者: 0.0 wPAT
- BSC国库: 0.0 wPAT
- BSC操作员: 0.0 wPAT
- BSC跨链池: 0.0 wPAT

=== 4. 跨链历史记录 ===
查询区块范围: 269 - 1269
❌ 查询跨链历史失败: bridgeReceiver.filters.CrossChainTransferProcessed is not a function

=== 5. 验证者状态 ===
验证者状态:
- 0xCbc4AB4f4dF2A860079caAB2a18Db7DFA65c0121: ✅ 活跃
- ******************************************: ✅ 活跃
- ******************************************: ✅ 活跃

=== 6. 网络连接状态 ===
PXA本地链状态:
- RPC URL: http://127.0.0.1:8545
- 当前区块: 1269
- 查询账户余额: 49999997.983838638988982434 PXA
- 连接状态: ✅ 正常

BSC本地链配置:
- RPC URL: http://127.0.0.1:18485
- 链ID: 97
- TokenBridge: 0x4815cD2986229a7633f66DC358262c05325E7128
- PAT代币: 0xe5EC13184D7184E0923453c1CD37600D7B97A387

=== 7. 跨链统计总结 ===
📊 跨链统计:
- wPAT总铸造量: 10000.0 wPAT
- 活跃验证者数: 3
- 配置状态: ✅ 就绪
- 跨链状态: ✅ 已有跨链记录

🎉 BSC本地链跨PXA本地链状态检查完成！